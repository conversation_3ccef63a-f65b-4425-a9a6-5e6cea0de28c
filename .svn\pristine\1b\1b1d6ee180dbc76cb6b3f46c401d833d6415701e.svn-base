<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SubQuickSetMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_subquickset(
			item,
			quickcode,
			<if test="accsubject_id != null and accsubject_id !=''">accsubject_id,</if>
			createby,createtime
		) values (
			#{item},
			#{quickcode},
			<if test="accsubject_id != null and accsubject_id !=''">#{accsubject_id},</if>
			#{createby},now()
		)
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_subquickset where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_subquickset
		set 
			item = #{item},
			quickcode = #{quickcode},
			<choose>
				<when test="accsubject_id != null and accsubject_id !=''">accsubject_id = #{accsubject_id},</when>
				<otherwise>accsubject_id = null,</otherwise>
			</choose>
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.item,
			a.quickcode,
			a.accsubject_id,
			t.name accsubject_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_subquickset as a
			left join ${ database }.c_accsubjects as t on a.accsubject_id = t.id
		where 
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select 
			a.item,
			a.quickcode,
			a.accsubject_id,
			t.name accsubject_name,
			t.auxbus,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_subquickset as a
			left join ${ pd.database }.c_accsubjects as t on a.accsubject_id = t.id
		where 
			a.closestatus = 0
			<if test="pd.q != null and pd.q !=''">
				and (a.item like '%${ pd.q }%' or a.quickcode like '%${ pd.q }%')
			</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select 
			a.item,
			a.quickcode,
			a.accsubject_id,
			t.name accsubject_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_subquickset as a
			left join ${ database }.c_accsubjects as t on a.accsubject_id = t.id
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	</select>
</mapper>