<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
</head>
<body style="background: #ebedf3;">
	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<!-- 顶部功能栏 -->
		<div id="eastToolDiv" data-options="region:'north'" style="background-color: #f4f4f4; background: -webkit-linear-gradient(top,#fff,#f4f4f4);background: -moz-linear-gradient(top,#fff,#f4f4f4);background: -o-linear-gradient(top,#fff,#f4f4f4);
			background: linear-gradient(to bottom,#fff,#f4f4f4); background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			
			<a class="easyui-linkbutton jz_btns" style="margin: 8px 0 0 0;color: #6d62af;" id="tallyBtn" href="javascript:void(0)" onclick="checkInitialbalance({position:'#gridlist',code:'initialstore',estatus:'1'})"><i></i></a>
			<a class="easyui-linkbutton cz_btns" style="margin: 8px 0 0 0;color: #6d62af;" id="untallyBtn" href="javascript:void(0)" onclick="checkInitialbalance({position:'#gridlist',code:'initialstore',estatus:'0'})"><i></i></a>
			<a class="easyui-linkbutton dc_btns" style="margin: 8px 0 0 0;color: #6d62af;" href="javascript:void(0)" onclick="beforeVoucherRevoke({position:'#gridlist',code:'initialstore'})"><i></i></a>
			<a class="easyui-linkbutton gb_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="closeIndexTabs({title:'期初库存'})"><i></i></a>
		    <p class="refreshbtns" id="refreshbtn" onclick="refresh()"></p>
		</div>
		
		<!-- 主面板 -->
		<div style="margin: 54px 0 0 0;border:1px #cedae4 solid;" id="parcel_southToolDiv">
			<input type="hidden" id="estatus">
			<input type="hidden" id="isaccount">
		    <div id="gridlist" data-options="region:'center'" ></div>
		    
		   <!-- 底部功能栏 -->
			<div id="southToolDiv" data-options="region:'south'" style="margin: -6px 0 0 -8px;position: absolute;">
				<a href="javascript:void(0)" class="easyui-linkbutton bc_btn" onclick="save()">保存</a>
			</div>
		    
		</div>
		
		<!-- 导入弹出框 -->
		<div id="import_panel" class="easyui-window" style="width: 400px;height:200px;display:none" title="导入" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="bgdivtit" style="height: 117px;border: 0;">
				<form id="importFileForm" method="post" enctype="multipart/form-data" data-options="novalidate:true">
					<input type="hidden" id="type" name="type" >
					<ul class="tolead_ul">
					    <li>
					    	<p>选择文件：</p>
					        <input class="easyui-filebox" id="file_name" name="file_name" data-options="buttonText:'浏览'" style="width:200px;height:27px">
					    </li>
					</ul>
				</form>
			</div>
			<div style="height:43px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" style="margin: 0px 13px 0 0;" onclick="$('#import_panel').window('close')">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" style="margin: 0px 13px 0 0;" onclick="importExcel()">确定</a>
		    </div>
		</div>
		
	</div>

<script type="text/javascript">
var width = $(window).width()-60;
var w1 = width/30;
var heights = $(window).height();
var mxData = {},mainIndex,pre_val='';

$(function(){
	$("#parcel_southToolDiv").height(heights-54);
	$("#gridlist").height(heights-100);
	//$("#gridlist").width(w1*14+70);
	var obj = new Object();
	obj["position"] = '#gridlist';
	obj["idField"] = 'id';
	obj["url"] = 'initialstore/list';
	obj["pagination"] = false;
	obj["columns"] = [[ 
						{field:'',checkbox:true},
						{field:'goodsid',title:'商品类别',hidden:true},
						{field:'goodstype_name',title:'商品类别',align: 'left', halign: 'center',width:w1*3},
						{field:'goods_name',title:'商品名称',align: 'left', halign: 'center',width:w1*4},
						{field:'qmsl',title:'期初数量',align: 'right', halign: 'center',width:w1*3+5,
							editor:{type:'numberbox',
								options: {
									precision:2
								}
							},
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'qmje',title:'期初金额',align: 'right', halign: 'center',width:w1*3+5,
							editor:{type:'numberbox',
								options: {
									precision:2
								}
							},
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						}
					]];
	
	//生成可编辑表格
	Grid.edit_row_grid(obj);
	//启动行编辑
	$('#gridlist').datagrid({
		 onClickCell: function(index, field){
		    	debugger;
				if ($("#gridlist").datagrid('options').editIndex == undefined){
					$("#gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
					$("#gridlist").datagrid('options').editIndex = index;
					var ed = $("#gridlist").datagrid('getEditor', {index:index,field:field});  
					if($(ed.target).next().find(".textbox-text").length>0){
						$(ed.target).next().find(".textbox-text").focus();
					}else{
						$(ed.target).focus();
					}
				}else if ($("#gridlist").datagrid('validateRow', $("#gridlist").datagrid('options').editIndex)){
					$("#gridlist").datagrid('endEdit', $("#gridlist").datagrid('options').editIndex);
					$("#gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
					$("#gridlist").datagrid('options').editIndex = index;
					var ed = $("#gridlist").datagrid('getEditor', {index:index,field:field});
					if($(ed.target).next().find(".textbox-text").length>0){
						$(ed.target).next().find(".textbox-text").focus();
					}else{
						$(ed.target).focus();
					}
				}
			},
			onLoadSuccess: function(data){
				if(!checkEm(data.rows)){
					var estatus = data.rows[0].estatus;
					if(parseInt(estatus) == 0){
						enableControl(['#tallyBtn']);
						disableControl(['#untallyBtn']);
					}else{
						enableControl(['#untallyBtn']);
						disableControl(['#tallyBtn']);
					} 
				}
			}
	
	});
	
	//初始状态
	/* var accinfo = findAccInfo();
	var isaccount = accinfo.initbalanceisacc;
	$('#isaccount').val(isaccount);
	if($("#isaccount").val() == 0){
		enableControl(['#tallyBtn']);
		disableControl(['#untallyBtn']);
	}else{
		enableControl(['#untallyBtn']);
		disableControl(['#tallyBtn']);
	} */
	
});

function save(){
	debugger
	var indexs=$('#gridlist').datagrid('getEditingRowIndexs');
	if(indexs.length>0){
		for(var i=0;i<indexs.length;i++){
			 $('#gridlist').datagrid('refreshRow', indexs[i]);
			$('#gridlist').datagrid('endEdit',indexs[i]);
		}
	}
	var data = $('#gridlist').datagrid('getData');
	var rows=data.rows;
	for(var i=0;i<rows.length;i++){
		//删除空行
		if(checkEm(rows[i].qmsl) && checkEm(rows[i].qmje)){
			$('#gridlist').datagrid('deleteRow',i);
			i--;
			continue;
		}
	
	}
	
	if(rows.length==0){
		$.messager.alert('提示','<span class="hintsp_w">提示</span>明细不能为空！','warning');  
		return false;
	}
	$.ajax({
	    url:getUrl('initialstore/save.do'),
	    type:'post',
	    async:false,
	    data: {"initialstoremx":JSON.stringify(rows)},
	    dataType:'json',
	    success:function(data){
    			if(data.state == 'success'){
	    			promptbox('success','操作成功！');
	    			reloadData('#gridlist',2);
				}else if(data.state == 'error'){
					$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
				}
	    },
	    error : function(data){
	    	$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败，请联系管理员','error');
	    }
	    
    });
	
}

function checkInitialbalance (obj){
	$.ajax({
	    url:getUrl('initialstore/accountAll.do'),
	    type:'post',
	    async:false,
	    data: {"type":obj.estatus},
	    dataType:'json',
	    success:function(data){
    			if(data.state == 'success'){
	    			promptbox('success','操作成功！');
	    			reloadData('#gridlist',2);
				}else if(data.state == 'error'){
					$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
				}
	    },
	    error : function(data){
	    	$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败，请联系管理员','error');
	    }
	    
    });
}
/**
 * 表单验证
 */
function formVailate(){
	var isaccount_1 = $('#isaccount').val();
	if(isaccount_1 == 1){
		return true;
	}else{
		return false;
	}
}


/**
 * 撤账
 */
function beforeVoucherRevoke(obj){
	voucherRevoke(obj);		//撤账
}

//导入excel
function importExcel(){
	$.messager.progress();	// 显示进度条
	$('#importFileForm').form('submit', {
			url: getUrl('initialstore/importExcel'),
			onSubmit: function(){
				var file_name = $('#file_name').filebox('getText');
				if(file_name == ''){
					$.messager.alert('提示','<span class="hintsp_e">提示</span>请选择要导入的文件！','error');
					$.messager.progress('close');
					return false;
				}
			},
			success: function (result) {
				var res = JSON.parse(result);
				if(res.state == 'success'){
					var rows = res.rows;
					for(var i in rows){
						$('#editgridlist').datagrid('appendRow',rows[i]);
					}
					$.messager.alert('提示','<span class="hintsp_w">提示</span>导入成功！','success');
					$('#import_panel').window('close');
				}else{
					$.messager.alert('提示','<span class="hintsp_w">提示</span>'+res.message,'success');
				}
				$('#gridlist').datagrid('load');
				$.messager.progress('close');// 如果表单是无效的则隐藏进度条
			}
	});
}

</script>

</body>
</html>