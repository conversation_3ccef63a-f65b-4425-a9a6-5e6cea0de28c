package org.newstanding.test;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.junit.Before;
import org.junit.Test;
import org.newstanding.common.utils.data.JedisUtil;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

public class testOne {
	
	/**
	 * 单机版测试
	 * <p>Title: testSpringJedisSingle</p>
	 * <p>Description: </p>
	 */
	@Test
	public void testSpringJedisSingle() {
		ApplicationContext applicationContext = new ClassPathXmlApplicationContext("classpath:ApplicationContext.xml");
		JedisPool pool = (JedisPool) applicationContext.getBean("redisClient");
		Jedis jedis = pool.getResource();
		jedis.set("key1","100");
		String string = jedis.get("key1");
		System.out.println(string);
		jedis.close();
		pool.close();
	}

	String[] strs = null;
	@Before
    public void before(){                                                                   
        @SuppressWarnings("resource")
        ApplicationContext context = new ClassPathXmlApplicationContext(new String[]{"classpath:ApplicationContext.xml"});
    }
     
	@Test
	public void testOne() throws InterruptedException {
		Jedis jedis = JedisUtil.getInstance().getJedis("**************", 6379);
	    System.out.println(jedis.ping());
	   /* System.out.println("清空数据："+jedis.flushDB());
		System.out.println("判断某个键是否存在："+jedis.exists("username"));
		System.out.println("新增<'username','zzh'>的键值对："+jedis.set("username", "zzh1231"));
		System.out.println(jedis.exists("name"));
		System.out.println("新增<'password','password'>的键值对："+jedis.set("password", "password"));
		System.out.print("系统中所有的键如下：");
		Set<String> keys = jedis.keys("*");
		System.out.println(keys);
		System.out.println("删除键password:"+jedis.del("password"));
		System.out.println("得到键username:"+jedis.get("username"));*/
		jedis.close();
		/*System.out.println("判断键password是否存在："+jedis.exists("password"));
		System.out.println("设置键username的过期时间为5s:"+jedis.expire("username", 5));
		TimeUnit.SECONDS.sleep(2);
		System.out.println("查看键username的剩余生存时间："+jedis.ttl("username"));
		System.out.println("移除键username的生存时间："+jedis.persist("username"));
		System.out.println("查看键username的剩余生存时间："+jedis.ttl("username"));
		System.out.println("查看键username所存储的值的类型："+jedis.type("username"));*/
		
		jedis.del("cs2000116297_accountperiod");
	}
	
	@Test
	public void testDate(){
		String datestr= "Nov 28, 2017 11:34:52 AM";//Date的默认格式显示
		Date date = null;
		try {
			date = new SimpleDateFormat("EEE MMM dd HH:mm:ss Z yyyy", Locale.UK).parse(datestr);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		//格式化
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
		String sDate=sdf.format(date);
		System.out.println(sDate);
	}
}
