<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="PayrollnewMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.t_calpayrollnew(
			 payrollmonth,
			 totalmoney,
			 manage_totalmoney,
			 sales_totalmoney,
			 shouldpay_money,
			 insurance_money,
			 personaltax_money,
			 createdate,
			 payrollmx,
			  payrollmxdata,
			 empCount,
			 createby,createtime
		) values (
			 #{payrollmonth},
			 #{totalmoney},
			 #{manage_totalmoney},
			 #{sales_totalmoney},
			 #{shouldpay_money},
			 #{insurance_money},
			 #{personaltax_money},
			 #{createdate},
			 #{payrollmx},
			  #{payrollmxdata},
			  #{empCount},
			 #{createby},now()
		)
	</insert>

	<!-- 新增 明细 -->
	<insert id="savePayrollmx" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.t_calpayrollnewmx(
			 payrollid,
			 empdocid,
			 item,
			 money
		) values
		<foreach item="item" index="index" collection="mxList" separator=",">
			(
				#{item.payrollid},
				#{item.empdocid},
				#{item.item},
				<choose>
					<when test="item.money !=null and item.money !=''">#{item.money}</when>
					<otherwise>0</otherwise>
				</choose>
			)
		</foreach>
	</insert>

	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.t_calpayrollnew
		where
		id = #{ DATA_IDS} and estatus = 1 and closestatus = 0
	</delete>

	<!-- 删除 明细 -->
	<delete id="deletePayrollmx" parameterType="pd">
		delete from ${ database }.t_calpayrollnewmx where payrollid = #{DATA_IDS}
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.t_calpayrollnew
			set
				payrollmonth = #{payrollmonth},
				createdate = #{createdate},
				totalmoney = #{totalmoney},
				manage_totalmoney = #{manage_totalmoney},
				sales_totalmoney = #{sales_totalmoney},
				shouldpay_money = #{shouldpay_money},
				insurance_money = #{insurance_money},
				personaltax_money = #{personaltax_money},
			    createdate = #{createdate},
			    payrollmx=#{payrollmx},
				empCount = #{empCount},
			    payrollmxdata=#{payrollmxdata}
			where
				id =  #{id}
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
			a.payrollmonth,
			ifnull(a.totalmoney,0) total_money,
			ifnull(a.manage_totalmoney,0) manage_totalmoney,
			ifnull(a.sales_totalmoney,0) sales_totalmoney,
			ifnull(a.insurance_money,0) insurance_money,
			ifnull(a.personaltax_money,0) personaltax_money,
			ifnull(a.shouldpay_money,0) shouldpay_money,

			a.voucher_id,
			b.code as vouchercode,
			a.createdate,
			a.payrollmx,
			a.payrollmxdata,
			a.isaudit,
			a.isaccount,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from
			${ database }.t_calpayrollnew as a
			left join ${ database }.t_fillvouchers as b on a.voucher_id=b.id
		where
			a.id = #{ id }
	</select>

	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.payrollmonth,
			a.totalmoney,
			a.voucher_id,
			b.code as vouchercode,
			a.createdate,
			a.isaudit,
			a.isaccount,
			a.payrollmx,
			a.payrollmxdata,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from
			${ pd.database }.t_calpayrollnew as a
			left join ${ pd.database }.t_fillvouchers as b on a.voucher_id=b.id
		where
			left(a.createdate,4) = left(#{ pd.accountperiod },4)
	</select>

	<select id="listAll" parameterType="pd" resultType="pd">
		select
			a.payrollmonth,
			a.totalmoney,
			a.voucher_id,
			b.code as vouchercode,
			a.createdate,
			a.isaudit,
			a.isaccount,
			a.payrollmx,
			a.payrollmxdata,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from
			${ database }.t_calpayrollnew as a
			left join ${ database }.t_fillvouchers as b on a.voucher_id=b.id
		where
			a.payrollmonth = #{ accountperiod }
	</select>

	<select id="findMaxCodeAndEtc" parameterType="pd" resultType="pd">
		select
			a.code,
			#{createby} as createby,
			0 as attachcount,
			a.id
		from
			${database }.t_calpayrollnew as a
		order by substring_index(a.code,'-', -1)+0 desc limit 1
	</select>

	<select id="findMaxMonth" parameterType="pd" resultType="pd">
		select
			a.payrollmonth,
			a.id
		from
			${database }.t_calpayrollnew as a
		order by a.payrollmonth desc limit 1
	</select>
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.accountperiod,
			a.code,
			a.voucherdate,
			a.attachcount,
			a.isaudit,
			a.isaccount,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from
			${ database }.t_calpayrollnew as a
		where
			a.accountperiod = #{ pd.accountperiod }
	</select>

	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">

	</select>

	<!-- 未关闭的职员 -->
	<select id="empdoclist" parameterType="pd" resultType="pd">
		select
			distinct
			a.id as empdocid,
			a.name as empdoc_name,
			a.deptid as deptid,
			b.name as dept_name,
			b.dept_type,
			a.salarysubid,
			a.insurancesubid,
			ifnull(a.specialdeducate,0) code_110

		from  ${ database }.c_empdoc as a
			left join ${ database }.c_deptdoc as b on a.deptid=b.id
		where a.closestatus=0 and ifnull(a.is_personal_declare,0) = 0
	</select>

		<!-- 查询所有的薪资项目 -->
	<select id="findItems" parameterType="pd" resultType="pd">
	select t.* from (
		select
			a.code,
			a.name,
			a.datasource as datasourceid,
			case a.datasource when 0 then '' when 1 then '固定薪资' when 2 then '手工录入'
			when 3 then '保险费扣除' when 4 then '个税计算' when 5 then '计算公式' else '' end as datasource,
			a.calformula,
			a.subjectid,
			a.sub_direction as sub_directionid,
			case a.sub_direction when 0 then '' when 1 then '借' when 2 then '贷' else '' end as sub_direction,
			a.isdefault,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from
			${ database }.t_salaryitems a
		where
			a.closestatus=0
		<!-- 专项扣除 -->
	<!-- union all
    select
        '110' as code,
        '专项扣除'  as name,
        0 as datasourceid,
        '' as datasource,
        '' as calformula,
        0 as subjectid,
        0 as sub_directionid,
        '' as sub_direction,
        0 isdefault,
        1 estatus,
        0 closestatus,
        1 version,
        0  as id
    from
        dual-->) t
     order by t.code+0 asc
</select>

<!-- 根据职员id 查询 维护的固定薪资项 -->
	<select id="findFixedsalary" parameterType="pd" resultType="pd">
		select
				a.empdocid,
				b.name as empdoc_name,
				ifnull(a.money,0) as money,
				a.salaryitemscode,
				a.salaryitemsname
			from
				${ database }.t_fixedsalary as a
				left join ${ database }.c_empdoc as b on a.empdocid=b.id
			where
				a.empdocid = #{empdocid}  and a.salaryitemscode=#{code}
	</select>
	<!-- 查询计算的社保 -->
	<select id="findNorInsurByPayrollMonth" parameterType="pd" resultType="pd">
		select
			a.insurancemonth,
			a.totalmoney,
			a.vouchercode,
			a.createdate,
			a.isaudit,
			a.isaccount,
			a.calnorinsurancemxdata as calnorinsurancemx,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id

		from
			${ database }.t_calnorinsurance as a
		where
			a.insurancemonth =#{payrollmonth } and a.closestatus=0
	</select>
	<!-- 查询计算的外包保险 -->
		<select id="findOutInsurByPayrollMonth" parameterType="pd" resultType="pd">
		select
			a.insurancemonth,
			a.totalmoney,
			a.vouchercode,
			a.createdate,
			a.isaudit,
			a.isaccount,
			a.caloutsourcemxdata as caloutsourcemx,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id

		from
			${ database }.t_caloutsource as a
		where
			a.insurancemonth =#{payrollmonth } and a.closestatus=0
	</select>
	<!-- 查询计算的公积金-->
		<select id="findHouseFundByPayrollMonth" parameterType="pd" resultType="pd">
		select
			a.insurancemonth,
			a.totalmoney,
			a.vouchercode,
			a.createdate,
			a.isaudit,
			a.isaccount,
			a.calhousefundmxdata as calhousefundmx,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id

		from
			${ database }.t_calhousefund as a
		where
			a.insurancemonth =#{payrollmonth } and a.closestatus=0
	</select>
	<select id="findPersonalTaxSetBaseMoney" parameterType="pd" resultType="pd">
		select
			ifnull(a.basemoney,0) as basemoney,
			a.id
		from
			${ database }.t_personaltaxset as a
			where  a.closestatus=0
			limit 1
		</select>



	<select id="findYearAllSalaryEtc" parameterType="pd" resultType="pd">
	select * from
		((select
			ifnull(sum(ifnull(b.money,0)),0) as yfgzze
		from
			${ database }.t_calpayrollnew as a
			left join ${ database }.t_calpayrollnewmx as b on a.id = b.payrollid
			where  left(a.payrollmonth,4) = left(#{payrollmonth},4) and a.payrollmonth &lt;#{payrollmonth}  and b.empdocid = #{empdocid} and b.item=51) as yfgzze,
		(select
			ifnull(sum(ifnull(b.money,0)),0) as jbkcze
		from
			${ database }.t_calpayrollnew as a
			left join ${ database }.t_calpayrollnewmx as b on a.id = b.payrollid
			where  left(a.payrollmonth,4) = left(#{payrollmonth},4) and a.payrollmonth &lt;#{payrollmonth}  and b.empdocid = #{empdocid} and b.item=81) as jbkcze,
		(select
			ifnull(sum(ifnull(b.money,0)),0) as qnljze
		from
			${ database }.t_calpayrollnew as a
			left join ${ database }.t_calpayrollnewmx as b on a.id = b.payrollid
			where  left(a.payrollmonth,4) = left(#{payrollmonth},4) and a.payrollmonth &lt;#{payrollmonth}  and b.empdocid = #{empdocid} and b.item=82) as qnljze,
		(select
			ifnull(sum(ifnull(b.money,0)),0) as zxkcze
		from
			${ database }.t_calpayrollnew as a
			left join ${ database }.t_calpayrollnewmx as b on a.id = b.payrollid
			where  left(a.payrollmonth,4) = left(#{payrollmonth},4) and a.payrollmonth &lt;#{payrollmonth}  and b.empdocid = #{empdocid} and b.item='code_110') as zxkcze)

	</select>
	<!-- 查询个税设定中的扣税基数  以及各级税率 -->
	<select id="findPersonalTaxset" parameterType="Double" resultType="pd">
		select
			ifnull(a.basemoney,0) as basemoney,
			b.id as personaltaxsetmxid,
			b.personaltaxsetid,
			b.logicalsymbol,
			cast(CONCAT(#{taxmoney},case  b.logicalsymbol when 1 then '&lt;' when 2 then '>=' when 3 then '&lt;='  when 4 then '>' else '' end,b.taxlevel) as char) as speaf,
			b.taxlevel,
			b.taxrate,
			b.deducationmoney,
			a.id
		from
			${ database }.t_personaltaxset as a
			left join ${ database }.t_personaltaxsetmx as b on a.id=b.personaltaxsetid
			where a.closestatus=0
			order by taxlevel asc

	</select>
	<!-- 生成凭证后，更新凭证号码和状态 -->
	<update id="updateVouchercodeAndIsAccount" parameterType="pd">
		update ${ database }.t_calpayrollnew
			set
				voucher_id = #{fillvouchersid},
			    vouchercode = #{code},
			  	isaudit=1
			where
				id =  #{id}
	</update>



	<select id="findVoucherIdByCode" parameterType="pd" resultType="pd">
		select
			a.code,
			ifnull(a.isaudit,0) as isaudit,
			ifnull(a.isaccount,0) as isaccount,
			a.id
		from
			${ database }.t_fillvouchers a
		where
			a.closestatus=0 and a.id=#{voucher_id}
	</select>
	<!-- 撤帐 -->
	<update id="updateVouchercodeAndIsAccount1" parameterType="pd">
		update ${ database }.${table}
			set
				voucher_id=null,
			    vouchercode = null,
			    perssubjectid =null,
			  	isaudit=0
			where
				id =  #{id}
	</update>
	<select id="findHaveSubjectItems" parameterType="pd" resultType="pd">
		select
			a.code,
			a.name,
			a.datasource as datasourceid,
			case a.datasource when 0 then '' when 1 then '固定薪资' when 2 then '手工录入'
			when 3 then '保险费扣除' when 4 then '个税计算' when 5 then '计算公式' else '' end as datasource,
			a.calformula,
			a.subjectid,
			a.sub_direction as sub_directionid,
			case a.sub_direction when 0 then '' when 1 then '借' when 2 then '贷' else '' end as sub_direction,
			a.isdefault,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from
			${ database }.t_salaryitems a
		where
			a.closestatus=0 and a.subjectid is not null and a.sub_direction is not null
		   order by a.code+0 asc
	</select>

	<select id="findInsuranceSubjectByMonth" parameterType="pd" resultType="pd">
		select
			'社保' as lx,
			a.perssubjectid,
			ifnull(a.persontotalmoney,0) as persontotalmoney,
			a.id
		from
			${ database }.t_calnorinsurance as a
		where
			a.insurancemonth =#{payrollmonth } and a.closestatus=0
			and a.perssubjectid is not null and a.voucher_id is not null

		union
		select
			'公积金' as lx,
			a.perssubjectid,
			ifnull(a.persontotalmoney,0) as persontotalmoney,
			a.id
		from
			${ database }.t_calhousefund as a
		where
			a.insurancemonth =#{payrollmonth } and a.closestatus=0
			and a.perssubjectid is not null and a.voucher_id is not null

		union

		select
			'外包保险' as lx,
			a.perssubjectid,
			ifnull(a.persontotalmoney,0) as persontotalmoney,
			a.id
		from
			${ database }.t_caloutsource as a
		where
			a.insurancemonth =#{payrollmonth } and a.closestatus=0
			and a.perssubjectid is not null and a.voucher_id is not null
	</select>




	<select id="dataYeartreelist" parameterType="pd" resultType="pd">
		select
			distinct
			left(a.payrollmonth,4) id,
			left(a.payrollmonth,4) name,
			cast(CONCAT(left(a.payrollmonth,4),'年') as char) as text,
			null as parentId
		from
			${ pd.database }.t_calpayrollnew as a order by left(a.payrollmonth,4) desc
	</select>


	<select id="dataMonthtreelist" parameterType="pd" resultType="pd">
		select
			a.payrollmonth,
			left(a.payrollmonth,4) parentId,
			right(a.payrollmonth,2) as name,
			cast(CONCAT(right(a.payrollmonth,2),'月') as char) as text,
			a.id

		from
			${ pd.database }.t_calpayrollnew as a order by a.payrollmonth
	</select>

	<select id="findByInsuranceMonth" parameterType="pd" resultType="pd">
		select
			a.totalmoney,
			a.manage_totalmoney,
			a.sales_totalmoney,
			a.persontotalmoney,
			a.payrollmx,
			a.payrollmxdata
		from
			${ database }.t_calpayrollnew as a
		where
			a.payrollmonth = #{payrollmonth}
	</select>


	<select id="findAllByMonth" parameterType="pd" resultType="pd">
		select
			a.payrollmonth,
			ifnull(a.totalmoney,0) total_money,
			ifnull(a.manage_totalmoney,0) manage_totalmoney,
			ifnull(a.sales_totalmoney,0) sales_totalmoney,
			ifnull(a.insurance_money,0) insurance_money,
			ifnull(a.personaltax_money,0) personaltax_money,
			ifnull(a.shouldpay_money,0) shouldpay_money,

			a.voucher_id,
			b.code as vouchercode,
			a.createdate,
			a.payrollmx,
			a.payrollmxdata,
			ifnull(a.empCount,0) empCount,
			a.isaudit,
			a.isaccount,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id

		from
			${ database }.t_calpayrollnew as a
			left join ${ database }.t_fillvouchers as b on a.voucher_id = b.id
		where a.payrollmonth = #{payrollmonth}

	</select>




	<select id="findLizhiEmpList" parameterType="pd" resultType="pd">
		select * from ${ database }.c_empdoc as a where ifnull(a.closestatus,0) =1
	</select>
</mapper>
