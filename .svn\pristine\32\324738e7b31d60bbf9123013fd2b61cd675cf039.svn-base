<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html>
<html lang="en">
	<head>
		<title>新屹--财税系统</title>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<link rel="shortcut icon" href="static/login/img/top.ico" type="image/x-icon" />
		<link rel="stylesheet" type="text/css" href="static/css/login.css">
		<script type="text/javascript" src="static/common/js/jquery-3.2.1.min.js"></script>
		<script type="text/javascript" src="static/common/js/jquery.tips.js"></script>
		<script type="text/javascript" src="static/common/js/jquery.cookie.js"></script>
		<style>
		   #recordCode{position:fixed;width:100%; left:0;bottom:30px;text-align:center;font-size:12px;}
		   #recordCode a{color:#999;text-decoration:none}
		</style>
	</head>
<body class="denldbody">
	<div class="denldic">
		  <input type="hidden" name="ipaddress" id="ipaddress"/>
	      <div class="formdivs">
	          <form action="" method="post" name="loginForm" id="loginForm" class="loginForm">
	                <h1>财务系统登录</h1>
	                <div class="formldiv">
	                     <p class="formldiv_title">登录</p>
						 <div class="controlsdiv" style="margin-top:21px">
							<span>用户名</span>
							<input type="text" name="loginname" id="loginname" value="" placeholder="用户名" />
						 </div>
	                     <div class="controlsdiv">
							<span>密码</span>
							<input type="password" name="password" id="password" placeholder="密码" value="" />
						 </div>
						 <div class="controlsdiv">
							<span>账套</span>
							<input type="text" name="database_name" id="database_name" placeholder="账套名称" value="${pd.database_name}" />
						 </div>
			             <div class="controlsdiv">
			                <span>验证码</span>
						    <input type="text" name="code" id="code" class="login_codes"  style="text-indent: 15px;"/>
							<i><img class="clickimg" id="codeImg" alt="点击更换" title="点击更换" src="" /></i>
						 </div>
						 <div class="controlsdiv controlsdivs">
							 <font>记住密码</font>
							 <input name="form-field-checkbox" id="saveid" class="checkdica" onclick="savePaw();" checked="checked" type="checkbox">
						     <a onclick="severCheck();" class="flip-linkbtn" id="to-recover">登录</a>
						 </div>
	                </div>
	          </form>
	      </div>
	      
	      <div id="recordCode"> 
			<a target="_blank" href="https://beian.miit.gov.cn/">沪ICP备17047832号-1</a>
		 </div>
	</div>
	<script type="text/javascript">
		$(function(){
		});
		var ip = document.getElementById('ipaddress');
		if(ip != null){
			$.ajax({
			    url: "http://ip.chinaz.com/getip.aspx",
			    type: "GET",
			    dataType: 'JSONP',
			    success: function(result){
					$('#ipaddress').val(result.ip);
			    }
			});
		}
		
	    $('#get_signature').click(function(){
	    	var gsdm = $('#gsdm').val(),
		    	mac = $('#mac').val(),
		    	signature = $('#signature').val();
	    	$.post('web2/get_signature',{gsdm:gsdm,mac:mac,signature:signature},function(data){
	    		if(data != null){
	    			if(data.state == 'success'){
	    				$('#signature').val(data.msg);
	    			}else{
	    				alert(data.msg);
	    			}
	    		}
			});
	    })
	    
	    $('#check_signature').click(function(){
	    	var gsdm = $('#gsdm').val(),
	    	mac = $('#mac').val(),
	    	signature = $('#signature').val();
	    	$.post('web2/check_signature',{gsdm:gsdm,mac:mac,signature:signature},function(data){
	    		if(data != null){
    				alert(data.msg);
	    		}
			});
	    })
		
		//服务器校验
		function severCheck(){
			if(check()){
				var loginname = $("#loginname").val();
				var password = $("#password").val();
				var code = "#org.newstanding#"+loginname+",fh,"+password+"#org.newstanding#"+",fh,"+$("#code").val();
				
				var gsdm = $('#gsdm').val(),
		    		mac = $('#mac').val(),
		    		signature = $('#signature').val(),
		    		ip = $('#ipaddress').val(),
		    		database = '',
		    		database_name = $('#database_name').val();
				
				$.ajax({
					type: "POST",
					url: 'login_login',
			    	data: {	KEYDATA:code,gsdm:gsdm,mac:mac,signature:signature,ip:ip,database:database,
			    			database_name:database_name,tm:new Date().getTime()},
					dataType:'json',
					cache: false,
					success: function(data){
						if("success" == data.result){
							saveCookie();
							window.location.href="main.do?version_="+data.version_;
						}else if("usererror" == data.result){
							$("#loginname").tips({
								side : 1,
								msg : "用户名或密码有误",
								bg : '#FF5080',
								time : 15
							});
							$("#loginname").focus();
						}else if("codeerror" == data.result){
							$("#code").tips({
								side : 1,
								msg : "验证码输入有误",
								bg : '#FF5080',
								time : 15
							});
							$("#code").focus();
						}else{
							$("#loginname").tips({
								side : 1,
								msg : data.result,
								bg : '#FF5080',
								time : 15
							});
							$("#loginname").focus();
						}
					},error:function(){
					}
				});
			}
		}
	
		$(document).ready(function() {
			changeCode();
			$("#codeImg").bind("click", changeCode);
		});

		$(document).keyup(function(event) {
			if (event.keyCode == 13) {
				$("#to-recover").trigger("click");
			}
		});

		function genTimestamp() {
			var time = new Date();
			return time.getTime();
		}

		function changeCode() {
			$("#codeImg").attr("src", "code.do?t=" + genTimestamp());
		}

		//客户端校验
		function check() {

			if ($("#loginname").val() == "") {

				/**
				* jquery tips 提示插件 jquery.tips.js v0.1beta
				*
				* 使用方法
				* $(selector).tips({   //selector 为jquery选择器
				*  msg:'your messages!',    //你的提示消息  必填
				*  side:1,  //提示窗显示位置  1，2，3，4 分别代表 上右下左 默认为1（上） 可选
				*  color:'#FFF', //提示文字色 默认为白色 可选
				*  bg:'#F00',//提示窗背景色 默认为红色 可选
				*  time:2,//自动关闭时间 默认2秒 设置0则不自动关闭 可选
				*  x:0,//横向偏移  正数向右偏移 负数向左偏移 默认为0 可选
				*  y:0,//纵向偏移  正数向下偏移 负数向上偏移 默认为0 可选
				* })
				*
				*
				*/
				$("#loginname").tips({
					side : 2,
					msg : '用户名不得为空',
					bg : '#AE81FF',
					time : 3
				});

				$("#loginname").focus();
				return false;
			} else {
				$("#loginname").val(jQuery.trim($('#loginname').val()));
			}

			if ($("#password").val() == "") {

				$("#password").tips({
					side : 2,
					msg : '密码不得为空',
					bg : '#AE81FF',
					time : 3
				});

				$("#password").focus();
				return false;
			}
			if ($("#code").val() == "") {

				$("#code").tips({
					side : 1,
					msg : '验证码不得为空',
					bg : '#AE81FF',
					time : 3
				});

				$("#code").focus();
				return false;
			}

			$("#loginbox").tips({
				side : 1,
				msg : '正在登录 , 请稍后 ...',
				bg : '#68B500',
				time : 10
			});

			return true;
		}
		
		/**
			删除cookie,清空用户名，密码
		**/
		function savePaw() {
			if (!$("#saveid").attr("checked")) {
				$.cookie('loginname', '', {
					expires : -1
				});
				$.cookie('password', '', {
					expires : -1
				});
				/* $("#loginname").val('');
				$("#password").val(''); */
			}
		}

		function saveCookie() {
			if ($("#saveid").attr("checked")) {
				$.cookie('loginname', $("#loginname").val(), {
					expires : 7
				});
				$.cookie('password', $("#password").val(), {
					expires : 7
				});
			}
		}
		function quxiao() {
			$("#loginname").val('');
			$("#password").val('');
		}
		
		jQuery(function() {
			var loginname = $.cookie('loginname');
			var password = $.cookie('password');
			if (typeof(loginname) != "undefined"
					&& typeof(password) != "undefined") {
				$("#loginname").val(loginname);
				$("#password").val(password);
				$("#saveid").attr("checked", true);
				$("#code").focus();
			}
		});
	</script>
	<script>
		//TOCMAT重启之后 点击左侧列表跳转登录首页 
		if (window != top) {
			top.location.href = location.href;
		}
	</script>

</body>

</html>