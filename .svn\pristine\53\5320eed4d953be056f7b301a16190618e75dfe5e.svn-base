<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="AssetstypeMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_assetstype(
			code,
			name,
			createby,createtime
		) values (
			#{code},
			#{name},
			#{createby},now()
		)
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_assetstype where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_assetstype
		set 
			code = #{code},
			name = #{name},
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.code,
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_assetstype as a
		where 
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select 
			a.code,
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_assetstype as a
		where 
			a.closestatus = 0
			<if test="pd.q != null and pd.q !=''">
				and (a.name like '%${ pd.q }%' or a.code like '%${ pd.q }%')
			</if>
	</select>
	
	<!-- 列表 -->
	<select id="gridlistPage" parameterType="pd" resultType="pd">
		select 
			a.code,
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_assetstype as a
		where 
			a.closestatus = 0
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select 
			a.code,
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_assetstype as a
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	
	select sum(t.num) s_num from (
			select count(0) num from ${ database }.t_fixedassetscard a where a.assetstypeid in (${DATA_IDS}) 
		) t
	</select>
</mapper>