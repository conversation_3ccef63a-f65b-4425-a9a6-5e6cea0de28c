<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Basic ValidateBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Basic ValidateBox</h2>
	<p>It's easy to add validate logic to a input box.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" title="Register" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<label for="username" class="label-top">User Name:</label>
			<input id="username" class="easyui-validatebox tb" data-options="required:true,validType:'length[3,10]'">
		</div>
		<div style="margin-bottom:20px">
			<label for="email" class="label-top">Email:</label>
			<input id="email" class="easyui-validatebox tb" data-options="required:true,validType:'email'">
		</div>
		<div style="margin-bottom:20px">
			<label for="url" class="label-top">Url:</label>
			<input id="url" class="easyui-validatebox tb" data-options="required:true,validType:'url'">
		</div>
		<div style="margin-bottom:20px">
			<label for="phone" class="label-top">Phone:</label>
			<input id="phone" class="easyui-validatebox tb" data-options="required:true">
		</div>
	</div>
	<style scoped="scoped">
		.tb{
			width:100%;
			margin:0;
			padding:5px 4px;
			border:1px solid #ccc;
			box-sizing:border-box;
		}
	</style>

</body>
</html>