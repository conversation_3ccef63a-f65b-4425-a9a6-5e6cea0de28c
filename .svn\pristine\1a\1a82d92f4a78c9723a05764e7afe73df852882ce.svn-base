package org.newstanding.controller.salary;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.invoicing.PurchaseInvoiceService;
import org.newstanding.service.salary.CalhousefundService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;
/**
 * deptdoc
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "calhousefund")
public class CalhousefundController extends BaseController {
	@Resource(name = "calhousefundService")
	private CalhousefundService calhousefundService;
	@Resource(name = "purchaseInvoiceService")
	private PurchaseInvoiceService purchaseInvoiceService;
	/**
	 * 新增
	 * 
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/save")
	@ResponseBody
	public PageData save() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return calhousefundService.save(pd);
	}

	/**
	 * 删除
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/delete")
	@ResponseBody
	public Object delete() throws Exception {
		PageData pd = this.getPageData();
		return calhousefundService.delete(pd);
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit")
	@ResponseBody
	public Object edit() throws Exception {
		PageData pd = this.getPageData();
		return calhousefundService.edit(pd);
	}
	/**
	 * 通过ID获取数据
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/findById")
	public Object findById() throws Exception {
		PageData pd = this.getPageData();
		PageData tmp = calhousefundService.findById(pd);
		pd.putAll(tmp);
		//pd.put("itemNames",JsonUtils.PageDataToJSONArray(calhousefundService.findItems(pd)));
		if(pd.get("itemNames") ==null || "".equals(pd.get("itemNames"))){
			pd.put("itemNames",JsonUtils.PageDataToJSONArray(calhousefundService.findItems(pd)));
		}
		pd.put("accountperiod",calhousefundService.getAccountperiod(pd));
		return pd;
	}
	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String accountperiod="";
		if(pd.get("accountperiod") == null){
			accountperiod = calhousefundService.getAccountperiod(pd);
			pd.put("accountperiod", accountperiod);
			resultMap.put("year", accountperiod.split("-")[0]);
		}else{
			accountperiod = pd.get("accountperiod").toString();
			resultMap.put("year", accountperiod);
		}
		setPage(pd,page);
	
		page.setPd(pd);
		resultPageData =  calhousefundService.list(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			for(PageData varPd : varList){
				//varPd.put("itemNames",JsonUtils.PageDataToJSONArray(calhousefundService.findItems(pd)));
				if(varPd.get("itemNames") ==null || "".equals(varPd.get("itemNames"))){
					varPd.put("itemNames",JsonUtils.PageDataToJSONArray(calhousefundService.findItems(pd)));
				}
				varPd.put("accountperiod",calhousefundService.getAccountperiod(pd));
			}
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
			resultMap.put("accountperiod", calhousefundService.getAccountperiod(pd));
			//resultMap.put("itemNames",calhousefundService.findItems(pd));
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return resultMap;
	}

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		mv.setViewName("system/salary/calhousefund");
		return mv;
	}

	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "c_calhousefund");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = calhousefundService.deleteAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 验证重复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkRepeatByParam")
	public boolean checkRepeatByParam() throws Exception {
		PageData pd = this.getPageData();
		if( calhousefundService.checkRepeatByParam2(pd) ){
			return false;
		}
		return true;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "c_calhousefund");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = calhousefundService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要关闭的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 搜索 导出 查询 部门列表
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = calhousefundService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	/**
	 * 新增时获取  保险月份  制单人 等信息
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value="/findMaxCodeAndEtc")
	public Object findMaxCodeAndEtc() throws Exception {
		PageData pd = this.getPageData();
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
		pd.put("createby", getCurrentUserByCatch().getUsername());
		PageData monthPd = calhousefundService.findMaxMonth(pd);
		String insurmonth="";
		if(monthPd != null){
			String yearmonth=monthPd.get("insurancemonth").toString();
			insurmonth= DateUtil.addMonth(yearmonth, 1);
		}else{
			insurmonth=sdf.format(new Date());
		}
		pd.put("accountperiod", calhousefundService.getAccountperiod(pd));
		pd.put("insurancemonth",insurmonth);
		pd.put("createdate", DateUtil.getMaxDayOfMonth(calhousefundService.getAccountperiod(pd)));
		return pd;
	}
	/**
	 * 计算社保
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/calinsurance")
	public PageData calucationvatax() throws Exception {
		PageData pd = this.getPageData();
		 PageData  resPd =  calhousefundService.calinsurance(pd);
		 resPd.put("itemNames",calhousefundService.findItems(pd));
		return resPd;
	}

	/**
	 * 检查人员下是否被使用
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value="/checkIsUsed")
	public Object checkSubAndChild() throws Exception {
		PageData pd = this.getPageData();
		List<PageData> list = calhousefundService.checkIsUsed(pd);
		if(list.size()>0){
			pd.put("state", "error");
		}else{
			pd.put("state", "success");
		}
		return pd;
	}
	/**
	 * 生成凭证
	 */
	@ResponseBody
	@RequestMapping(value = "/createvoucher")
	public PageData createvoucher() throws Exception {
		PageData pd = this.getPageData();
		 PageData  resPd =  calhousefundService.createVoucher(pd);
		return resPd;
	}
}




