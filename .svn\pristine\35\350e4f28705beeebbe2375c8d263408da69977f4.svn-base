<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="VouchertemplateMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_vouchertemplate(
				 code,
				 name,
				createby,createtime
		) values (
				 #{code},
				 #{name},
				#{createby},now()
		)
	</insert>
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_vouchertemplate
		where
		id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		<foreach item="item" index="index" collection="modList" separator=";">
			update ${ database }.c_vouchertemplatemx
				set 
					vouchertemplateid = #{item.vouchertemplateid},
					direction = #{item.direction},
					businessmatters = #{item.businessmatters},
					accsubjectsid = #{item.accsubjectsid},
					assistaccounting = #{item.assistaccounting},
					defaulttranmemo = #{item.defaulttranmemo},
					subject_edit = #{subject_edit}
				where 
					id = #{item.id}
		</foreach>	
			
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.code,	
			a.name,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_vouchertemplate as a
		where 
			a.id = #{ id }
	</select>
	
	<!-- 通过模板ID获取明细数据 -->
	<select id="findVouchertemplatemxByVouchertemplateid" parameterType="pd" resultType="pd">
		select 
			a.id,
			a.businessmatters,
			a.accsubjectsid,
			t.aliasname as accsubjects_name,
			a.direction direction,
			c.auxbus assistaccounting,
			a.defaulttranmemo,
			a.vouchertemplateid,
			a.subject_edit
		from 
			${ database }.c_vouchertemplatemx as a
			left join ${ database }.c_accsubjects c on a.accsubjectsid = c.id
			left join ${ database }.c_accsubjects  as t on a.accsubjectsid=t.id
			
		where 
			a.vouchertemplateid = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.code,	
			a.name,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_vouchertemplate a
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.code,	
			a.name,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_vouchertemplate a
	</select>
	
</mapper>