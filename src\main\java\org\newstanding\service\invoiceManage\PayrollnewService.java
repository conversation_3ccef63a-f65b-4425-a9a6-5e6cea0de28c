package org.newstanding.service.invoiceManage;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.financialhandle.FillvouchersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Service("payrollnewService")
public class PayrollnewService extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private FillvouchersService fillvouchersService;
	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			//2024-05-16新增，保存时候验证社保扣款是否和社保、公积金单据中的合计相等
			double insurance_money = Double.parseDouble(pd.get("insurance_money").toString());
			double v = reCaculationInsuranceMoneyBeforeSaveOrCreateVoucher(pd);
			if(insurance_money !=v){
				resPd.put("state", "error");
				resPd.put("message", "检测到当前工资单中的社保扣款金额与社保单据的金额不一致，请检查后重试！");
				return resPd;
			}

			int count = dao.save("PayrollnewMapper.save", pd);
			String id = pd.getString("id");

			String mxdata = pd.get("payrollmxdata").toString();
			List<PageData> mxdatalist = JsonUtils.strToListPd(mxdata);

			String data = pd.get("payrollmx").toString();
			Map<String, Object> map = JsonUtils.strToMap(data);

			List<PageData> itemlist = JsonUtils.strToListPd(map.get("itemNames").toString());

			List<PageData> mxlist = new ArrayList<PageData>();
			for (PageData mx : mxdatalist) {
				for (PageData item : itemlist) {
					String code = item.get("code").toString();
					PageData tmp = new PageData();
					tmp.put("payrollid", id);
					tmp.put("empdocid", mx.get("empdocid").toString());
					tmp.put("item", code);
					if(mx.get(code) != null){
						tmp.put("money", mx.get(code).toString());
					}else{
						tmp.put("money", 0);
					}
					mxlist.add(tmp);
				}
			}

			pd.put("mxList", mxlist);
			dao.save("PayrollnewMapper.savePayrollmx", pd);

			if (count > 0) {
				resPd.put("id", id);
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}


	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.delete("PayrollnewMapper.delete", pd);
			dao.delete("PayrollnewMapper.deletePayrollmx", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			//2024-05-16新增，保存时候验证社保扣款是否和社保、公积金单据中的合计相等
			double insurance_money = Double.parseDouble(pd.get("insurance_money").toString());
			double v = reCaculationInsuranceMoneyBeforeSaveOrCreateVoucher(pd);
			if(insurance_money != v){
				resPd.put("state", "error");
				resPd.put("message", "检测到当前工资单中的保险扣款金额与社保单据金额不一致，请检查后重试！");
				return resPd;
			}

			String id = pd.getString("id");
			int count = dao.update("PayrollnewMapper.edit", pd);

			pd.put("DATA_IDS", id);
			dao.delete("PayrollnewMapper.deletePayrollmx", pd);

			String mxdata = pd.get("payrollmxdata").toString();
			List<PageData> mxdatalist = JsonUtils.strToListPd(mxdata);

			String data = pd.get("payrollmx").toString();
			Map<String, Object> map = JsonUtils.strToMap(data);

			List<PageData> itemlist = JsonUtils.strToListPd(map.get("itemNames").toString());

			List<PageData> mxlist = new ArrayList<PageData>();
			for (PageData mx : mxdatalist) {
				for (PageData item : itemlist) {
					String code = item.get("code").toString();
					PageData tmp = new PageData();
					tmp.put("payrollid", id);
					tmp.put("empdocid", mx.get("empdocid").toString());
					tmp.put("item", code);
					if(mx.get(code) != null){
						tmp.put("money", mx.get(code).toString());
					}else{
						tmp.put("money", 0);
					}
					mxlist.add(tmp);
				}
			}

			pd.put("mxList", mxlist);
			dao.save("PayrollnewMapper.savePayrollmx", pd);

			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PayrollnewMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}

		return resPd;
	}

	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PayrollnewMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	/**
	 * 查找最大月份
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findMaxMonth(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PayrollnewMapper.findMaxMonth", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	/**
	 * 计算工资
	 * @param pd
	 * @return
	 */
	public PageData calpayroll(PageData pd){
		ScriptEngine ScrEngin = new ScriptEngineManager().getEngineByName("JavaScript");
		PageData resPd=new PageData();
		try {
			//获取未关闭的职员
			//2023-11-06修改，职员档案设定中  是否申报个人经营所得   如果勾选了，此人员在工资单中不出现。
			List<PageData> emplist =  (List<PageData>)dao.findForList("PayrollnewMapper.empdoclist", pd);
			//获取 所有的工资项
			List<PageData> itemsList = findItems(pd);
			//在单个职员pd中插入此职员的所有  工资项目
			if(emplist.size()>0){
				for (PageData empd : emplist) {
					empd.put("database", getDatabase(pd));
					for(PageData itemPd : itemsList){
						itemPd.put("database", getDatabase(pd));
						if("固定薪资".equals(itemPd.get("datasource").toString())){
							itemPd.put("empdocid", empd.get("empdocid"));
							PageData fixedSalaryPd= (PageData) dao.findForObject("PayrollnewMapper.findFixedsalary", itemPd);
							if(fixedSalaryPd !=null){
								empd.put(itemPd.get("code"),fixedSalaryPd.get("money"));
							 }else{
								empd.put(itemPd.get("code"),0);
							}
						}else if("保险费扣除".equals(itemPd.get("datasource").toString())){
							//社保，公积金，外包保险的个人部分
							empd.put("payrollmonth", pd.get("payrollmonth"));
							double personInsurTotal= findInsurInfoByMonthAndEmpdocid(empd);
							empd.put(itemPd.get("code"),personInsurTotal);
						}else if("个税计算".equals(itemPd.get("datasource").toString())){
							if("张庆红".equals(empd.get("empdoc_name"))) {
								System.err.println("aaaaaaaa");
							}
							String calformula=itemPd.getString("calformula");//([51]-[82])*税率
							if(calformula==null || "".equals(calformula)){
								resPd.put("state","error");
								resPd.put("message","计算存在错误,薪资项："+itemPd.get("name")+",其数据来源为计算公式，没有设置计算公式！");
								return resPd;
							}
							//将后面税率截掉，先处理括号内的算式
							String calformula1=getKuohaoContent(calformula);
							//查询  计税基数
							PageData baseMoneyPd=(PageData) dao.findForObject("PayrollnewMapper.findPersonalTaxSetBaseMoney", empd);
							if(baseMoneyPd !=null){
								empd.put("basemoney", baseMoneyPd.get("basemoney"));

							}else{
								resPd.put("state","error");
								resPd.put("message","计算存在错误,薪资项："+itemPd.get("name")+",没有找到个人所得税设置！");
								return resPd;
							}

							//查询工资月份之前的  本年度的全年计税工资总额-全年专项扣除总额-全年累计基本扣除额-全年累计扣除额
							empd.putAll(pd);
							PageData yearAllPd=(PageData) dao.findForObject("PayrollnewMapper.findYearAllSalaryEtc", empd);
							Double pre_yfgzze,pre_jbkcze,pre_qnljze,basemoney_total,pre_zxkcze=0d;
							Double now_yfgz,now_jbkc,now_zxkc=0d;
							if(yearAllPd !=null) {
								String[] result = DateUtil.getAllMonths(empd.get("payrollmonth").toString().substring(0,5)+"01", empd.get("payrollmonth").toString());
								basemoney_total = CalculateUtil.multiply(result.length, Double.parseDouble(empd.get("basemoney").toString()));//累计扣税基数  5000 * n
								pre_yfgzze = Double.parseDouble(yearAllPd.get("yfgzze").toString());
								pre_jbkcze = Double.parseDouble(yearAllPd.get("jbkcze").toString());
								pre_qnljze = Double.parseDouble(yearAllPd.get("qnljze").toString());
								pre_zxkcze = Double.parseDouble(yearAllPd.get("zxkcze").toString());

								//本月：
								now_yfgz = Double.parseDouble(empd.get("51").toString());
								now_jbkc = Double.parseDouble(empd.get("81").toString());
								now_zxkc = Double.parseDouble(empd.get("code_110").toString());

								double taxmoney = CalculateUtil.subtract( CalculateUtil.subtract(
										CalculateUtil.subtract(CalculateUtil.add(pre_yfgzze, now_yfgz)
										, CalculateUtil.add(pre_jbkcze, now_jbkc))
										,CalculateUtil.add(pre_zxkcze, now_zxkc)), basemoney_total);
								if(taxmoney<0){
									empd.put(itemPd.get("code"),0);
									continue;
								}
								pd.put("taxmoney", taxmoney);
								pd.put("qnljze", pre_qnljze);//减去全年已交税额
								double aa =calSalaryTaxBySalary(pd);
								if(aa<0) {
									aa=0;
								}
								empd.put(itemPd.get("code"),aa);
							}

							/**
							 * （[51]-[81]）  表示计税工资总额

								个税：（全年计税工资总额-全年专项扣除总额-全年累计基本扣除额-全年累计扣除额） *适用税率

								如
								张三1月份计税工资 25000，月专项扣除2000，月基本扣除5000
								 则个税=25000-2000-5000=18000，适用税率3%   个税=18000*3%=540

								2月份同上，则
								个税=25000*2-2000*2-5000*2=36000，适用税率3%，个税=36000*3%=1080-540=540

							     3月份收入同上，则
								  个税=25000*3-2000*3-5000*3=54000，适用税率10%，个税=54000*10%-2520（速算扣除）-1080（已交）=1800

								 4月份收入26000，月专项扣除3000，月基本扣除50000
								  个税=（25000*3+26000）-（2000*3+3000）-5000*4=72000  个税=72000*10%-2520-2880=1800
							 */
				/*			String val="";
							for(PageData itemsPd1: itemsList){
								if(empd.get(itemsPd1.get("code").toString()) !=null){
									val=empd.get(itemsPd1.get("code").toString()).toString();
								}else{
									val="0";
								}
								 String key="["+itemsPd1.get("code").toString()+"]";
									if(calformula1.contains(key)){
										calformula1=calformula1.replace(key, val);
									}

							}
							//如果计算公式中有值   但是没有此工资项目时，将此工资项目值替换为0
							if(calformula1.contains("[") && calformula1.contains("]")){
								calformula1=calformula1.replaceAll("\\[(.*?)\\]", "0");
							}
							calformula1=calformula1+"-"+baseMoneyPd.get("basemoney");*/
							//计算出括号内的值  [51]-[basemoney]
						/*	double taxmoney=new BigDecimal( Double.parseDouble( ScrEngin.eval(calformula1).toString() ) ).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
							if(taxmoney<0){
								empd.put(itemPd.get("code"),0);
								continue;
							}
							pd.put("taxmoney", taxmoney);
							pd.put("qnljze", qnljze);//减去全年已交税额
							double aa =calSalaryTaxBySalary(pd);
							empd.put(itemPd.get("code"),aa);*/
						}else if("计算公式".equals(itemPd.get("datasource").toString())){
							String calformula=itemPd.getString("calformula");
							if(calformula==null || "".equals(calformula)){
								resPd.put("state","error");
								resPd.put("message","计算存在错误,薪资项："+itemPd.get("name")+",其数据来源为计算公式，没有设置计算公式！");
								return resPd;
							}
							String val="";
							for(PageData itemsPd1: itemsList){
								if(empd.get(itemsPd1.get("code").toString()) !=null){
									val=empd.get(itemsPd1.get("code").toString()).toString();
								}else{
									val="0";
								}
								 String key="["+itemsPd1.get("code").toString()+"]";
									if(calformula.contains(key)){
										calformula=calformula.replace(key, val);
									}
							}
							//如果计算公式中有值   但是没有此工资项目时，将此工资项目值替换为0
							if(calformula.contains("[") && calformula.contains("]")){
								calformula=calformula.replaceAll("\\[(.*?)\\]", "0");
							}
							calformula=calformula.replaceAll("\\--", "+").replaceAll("\\++", "+").replaceAll("\\+-", "-").replaceAll("\\-+", "-");
							empd.put(itemPd.get("code"),new BigDecimal( Double.parseDouble( ScrEngin.eval(calformula).toString() ) ).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
						}else{//手工录入

						}
					}
				}
			}else{
				resPd.put("state","error");
				resPd.put("message","计算存在错误,没有符合条件的人员,请检查！");
				return resPd;
			}
			//合计行  数据构建
			List<PageData> footerList=new ArrayList<PageData>();
			PageData footerPd=new PageData();
			footerPd.put("empdoc_name", "合计");
			for(Map<String,String> empPd :emplist){
				for (String key : empPd.keySet()) {
					  if(key.matches("^[0-9]*$") || key.equals("basemoney") || key.contains("code_")){
						 if(footerPd.get(key) !=null){
							 String footer=footerPd.get(key).toString();
							 String em=String.valueOf(empPd.get(key));
							 footerPd.put(key,
									 CalculateUtil.add(footer
											 , em));

						 }else{
							 footerPd.put(key, empPd.get(key));
						 }
					  }

				}
			}
			footerList.add(footerPd);
			resPd.put("footer", footerList);
			resPd.put("rows", emplist);
			resPd.put("total", emplist.size());
			resPd.put("state","success");
			resPd.put("message","计算成功");
		} catch (Exception e) {
			e.printStackTrace();
			resPd.put("state","error");
			resPd.put("message","计算存在错误,请联系管理员");
		}

		return resPd;
	}
	/**
	 * 获取括号内的内容 一个  多个用list接收
	 * @param managers
	 * @return
	 */
	public String getKuohaoContent(String managers){
        Pattern pattern = Pattern.compile("(?<=\\()(.+?)(?=\\))");
        Matcher matcher = pattern.matcher(managers);
        while(matcher.find())
            return matcher.group(0);
        return "";
    }
	/**
	 * 根据员工应付工资  和设置的税率  计算应交个人所得税
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public Double calSalaryTaxBySalary(PageData pd) throws Exception{
		ScriptEngine ScrEngin = new ScriptEngineManager().getEngineByName("JavaScript");
		//查询个税设定的缴费基数
		List<PageData> baseTaxList=(List<PageData>) dao.findForList("PayrollnewMapper.findPersonalTaxset", pd);
		if(baseTaxList.size()>0){
			for(PageData baseTax : baseTaxList){
				if((boolean) ScrEngin.eval(baseTax.get("speaf").toString())){
					//72000（应纳税额）*10%（税率）-2520（速算扣除）-2880（全年已交税总额）
					return  CalculateUtil.subtract(CalculateUtil.subtract(
								CalculateUtil.divide(CalculateUtil.multiply( Double.parseDouble(pd.get("taxmoney").toString())
									, Double.parseDouble(baseTax.get("taxrate").toString()),2), 100, 2)
									,Double.parseDouble(baseTax.get("deducationmoney").toString())), Double.parseDouble(pd.get("qnljze").toString()));
				}
			}
		}
		return 0d;
	}



		public Double calSalaryTaxBySalary1(PageData pd) throws Exception{

			String[] result = DateUtil.getAllMonths(pd.get("payrollmonth").toString().substring(0,5)+"01", pd.get("payrollmonth").toString());
			Double basemoney_total = CalculateUtil.multiply(result.length, Double.parseDouble(pd.get("basemoney").toString()));//累计扣税基数  5000 * n
			PageData yearAllPd=(PageData) dao.findForObject("PayrollnewMapper.findYearAllSalaryEtc", pd);
			Double pre_yfgzze,pre_jbkcze,pre_qnljze,pre_zxkcze=0d;
			Double now_yfgz,now_jbkc,now_zxkc=0d;
			if(yearAllPd !=null) {
				pre_yfgzze = Double.parseDouble(yearAllPd.get("yfgzze").toString());
				pre_jbkcze = Double.parseDouble(yearAllPd.get("jbkcze").toString());
				pre_qnljze = Double.parseDouble(yearAllPd.get("qnljze").toString());
				pre_zxkcze = Double.parseDouble(yearAllPd.get("zxkcze").toString());

				//本月：
				now_yfgz = Double.parseDouble(pd.get("51").toString());
				now_jbkc = Double.parseDouble(pd.get("81").toString());
				now_zxkc = Double.parseDouble(pd.get("code_110").toString());

				double taxmoney = CalculateUtil.subtract(CalculateUtil.subtract(
						CalculateUtil.subtract(CalculateUtil.add(pre_yfgzze, now_yfgz)
						, CalculateUtil.add(pre_jbkcze, now_jbkc))
						,CalculateUtil.add(pre_zxkcze, now_zxkc)),basemoney_total);
				if(taxmoney<0){
					return 0d;
				}
				pd.put("taxmoney", taxmoney);
				pd.put("qnljze", pre_qnljze);//减去全年已交税额
			}

			return calSalaryTaxBySalary(pd);
		}
	/**
	 * 根据员工id和工资月份  获取社保 + 外包保险 + 公积金的个人部分合计
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public Double findInsurInfoByMonthAndEmpdocid(PageData pd) throws Exception{
		double norinsurPersonMoney=0d;
		double outinsurPersonMoney=0d;
		double housePersonMoney=0d;
		//社保，公积金，外包保险的个人部分
		PageData norInsurPd=(PageData) dao.findForObject("PayrollnewMapper.findNorInsurByPayrollMonth", pd);
		if(norInsurPd !=null){
			if (norInsurPd.get("calnorinsurancemx")!=null && !"".equals(norInsurPd.getString("calnorinsurancemx"))) {
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(norInsurPd.getString("calnorinsurancemx"));
				for (Object o : jay) {
					PageData calnorinsurancemx = new PageData(JsonUtils.strToMap(o.toString()));
					if(calnorinsurancemx.get("empdocid").toString().equals(pd.get("empdocid").toString())){
						norinsurPersonMoney=Double.parseDouble(calnorinsurancemx.get("personparttotal").toString());
					}
				}
			}
		}
		PageData outInsurPd=(PageData) dao.findForObject("PayrollnewMapper.findOutInsurByPayrollMonth", pd);
		if(outInsurPd !=null){
			if (outInsurPd.get("caloutsourcemx") !=null && !"".equals(outInsurPd.getString("caloutsourcemx"))) {
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(outInsurPd.getString("caloutsourcemx"));
				for (Object o : jay) {
					PageData caloutsourcemx = new PageData(JsonUtils.strToMap(o.toString()));
					if(caloutsourcemx.get("empdocid").toString().equals(pd.get("empdocid").toString())){
						outinsurPersonMoney=Double.parseDouble(caloutsourcemx.get("personparttotal").toString());
					}
				}
			}
		}
		PageData housefundPd=(PageData) dao.findForObject("PayrollnewMapper.findHouseFundByPayrollMonth", pd);
		if(housefundPd !=null){
			if (housefundPd.get("calhousefundmx")!=null && !"".equals(housefundPd.getString("calhousefundmx"))) {
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(housefundPd.getString("calhousefundmx"));
				for (Object o : jay) {
					PageData caloutsourcemx = new PageData(JsonUtils.strToMap(o.toString()));
					if(caloutsourcemx.get("empdocid").toString().equals(pd.get("empdocid").toString())){
						housePersonMoney=Double.parseDouble(caloutsourcemx.get("personparttotal").toString());
					}
				}
			}
		}
		return CalculateUtil.add(CalculateUtil.add(norinsurPersonMoney, outinsurPersonMoney), housePersonMoney);
	}
	/**
	 * 获取设定的所有的薪资项目
	 * @return
	 * @throws Exception
	 */
	public List<PageData> findItems(PageData pd)throws Exception{
		List<PageData> list = (List<PageData>)dao.findForList("PayrollnewMapper.findItems", pd);
		for(PageData pd1 :list){
			if("99".equals(pd1.get("code").toString())){//实发工资
				pd1.put("code", "code_99");
			}
			if("110".equals(pd1.get("code").toString())){//实发工资
				pd1.put("code", "code_110");
			}
		}
		return list;
	}

	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PayrollnewMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}

		return resPd;
	}
	/**
	 * 查询是否被其他单据引用
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public List<PageData> checkIsUsed(PageData pd) {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PayrollnewMapper.checkIsUsed", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	public boolean checkRepeatByParam2(PageData pd) throws Exception {
		String check_param = pd.get("check_param").toString();
		if (StringUtils.getEncoding(check_param).equals("GB2312")) {
			check_param = URLDecoder.decode(URLDecoder.decode(check_param,"UTF-8"),"UTF-8");
		}
		String[] check_params = {check_param};
		PageData param = new PageData();
		param.put("database", pd.get("database").toString());
		param.put("table_name", pd.get("table_name").toString());
		param.put("id", pd.get("id").toString());
		PageData p = new PageData();
		for (String str : check_params) {
			String[] strs = str.split(";");
			param.put("str", strs[0]);
			param.put("value", strs[1]);
			p = (PageData)dao.findForObject("CommonMapper.checkRepeatByParam", param);
			if(p != null && Integer.parseInt(p.get("num").toString()) != 0){
				return true;
			}
		}
		return false;
	}

	public List<PageData> getExcelData(PageData pd) throws Exception {
		List<PageData> list = (List<PageData>) dao.findForList("PayrollnewMapper.listAll", pd);
		PageData p = list.get(0);
		String data = p.get("payrollmxdata").toString();
		List<PageData> datalist = JsonUtils.strToListPd(data);
		return datalist;
	}

	/**
	 * 生成凭证
	 * @param pd
	 * @return
	 */
	public PageData createVoucher(PageData pd) {
		PageData resPd = new PageData();
		String database=pd.get("database").toString();
		if( org.springframework.util.StringUtils.isEmpty(pd.get("code"))){
			resPd.put("state","error");
			resPd.put("message",  "生成凭证失败，无法获取单据的编号。！");
			return resPd;
		}
		if( org.springframework.util.StringUtils.isEmpty(pd.get("accountperiod"))){
			resPd.put("state","error");
			resPd.put("message",  "生成凭证失败，无法获取单据的会计期间！");
			return resPd;
		}
		try {
			PageData dataPd = findById(pd);
			dataPd.put("database",database);
			//2024-05-16增加验证社保扣款和社保单据的金额是否一致
			double insurance_money = Double.parseDouble(dataPd.get("insurance_money").toString());
			double v = reCaculationInsuranceMoneyBeforeSaveOrCreateVoucher(dataPd);
			if(insurance_money !=v){
				resPd.put("state", "error");
				resPd.put("message", "检测到当前工资单中的保险扣款金额与社保单据金额不一致，请检查后重试！");
				return resPd;
			}

			//取凭证模板
			List<PageData> vmlist = getVoucherteMplateNew(pd.get("code").toString(),pd);
			//构造凭证数据
			PageData mainPd = new PageData();
			List<PageData> mx_list = new ArrayList<PageData>();
			mainPd.put("accountperiod", pd.get("accountperiod"));
			mainPd.put("createby", getCurrentUserByCatch(pd).getUsername());
			mainPd .put("source", pd.get("code").toString());
			mainPd .put("source_ids", pd.get("id"));
			mainPd .put("attachcount", "0");
			mainPd.put("database", database);
			mainPd.put("voucherdate", org.newstanding.common.utils.date.DateUtil.getMaxDayOfMonth(pd.get("accountperiod").toString()));
			PageData codePd=fillvouchersService.findMaxCodeAndEtc(mainPd);
			if(codePd != null && codePd.get("code")!=null){
				mainPd.put("code", codePd.get("code"));
			}


			for (PageData vm : vmlist) {
				PageData mxPd = new PageData();
				vm.put("database", pd.getString("database"));
				mxPd.put("rowid",(int)(Math.random()*100000));
				mxPd.put("subjectid",vm.get("accsubjectsid"));
				mxPd.put("abstracta", vm.get("defaulttranmemo"));
				switch (vm.getString("businessmatters")) {
					case "工资费用-管理":
						mxPd.put("debitmoney", dataPd.get("manage_totalmoney"));
						mx_list.add(mxPd);
						break;
					case "工资费用-销售":
						mxPd.put("debitmoney", dataPd.get("sales_totalmoney"));
						mx_list.add(mxPd);
						break;
					case "实发工资":
						mxPd.put("creditmoney", dataPd.get("total_money"));
						mx_list.add(mxPd);
						break;
					case "保险个人部分":
						mxPd.put("creditmoney", dataPd.get("insurance_money"));
						mx_list.add(mxPd);
						break;
					case "代缴个税":
						mxPd.put("creditmoney", dataPd.get("personaltax_money"));
						mx_list.add(mxPd);
						break;
					default:

						break;
				}
			}
			mainPd.put("fillvouchersmx", JsonUtils.PageDataToJSONArray(mx_list).toString());
			//生成凭证
			resPd=fillvouchersService.save(mainPd);

			if(resPd .get("state") !=null && "success".equals(resPd .get("state").toString())){
				resPd.put("message",  "已生成凭证,凭证号码："+mainPd.get("code").toString());
				mainPd.put("id", pd.get("id"));
				mainPd.put("fillvouchersid", resPd.get("id"));
				resPd.put("voucher_id", resPd.get("id"));
				resPd.put("vouchercode", mainPd.get("code").toString());
				dao.update("PayrollnewMapper.updateVouchercodeAndIsAccount", mainPd);
			}else{
				resPd.put("message", resPd.get("message"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}




	/*
		生成凭证之前要重新统计下社保扣款金额是否和单据的金额一致，不一致的话需要报错。
	 */
	public Double reCaculationInsuranceMoneyBeforeSaveOrCreateVoucher(PageData pd) throws Exception{

		List<PageData> payrollmx = new ArrayList<>();
		List<PageData> norinsurancemx = new ArrayList<>();
		List<PageData> housefundmx = new ArrayList<>();
		List<PageData> outsourcemx = new ArrayList<>();
		double norinsurPersonMoney=0d;
		double outinsurPersonMoney=0d;
		double housePersonMoney=0d;
		if(pd.get("payrollmxdata") !=null && !"".equals(pd.getString("payrollmxdata"))){
			 payrollmx = JsonUtils.jsonArrayStr2PageDataList(pd.getString("payrollmxdata"));
		}

		//社保，公积金，外包保险的个人部分
		PageData norInsurPd=(PageData) dao.findForObject("PayrollnewMapper.findNorInsurByPayrollMonth", pd);
		if(norInsurPd !=null && norInsurPd.get("calnorinsurancemx") !=null && !"".equals(norInsurPd.getString("calnorinsurancemx"))){
			norinsurancemx = JsonUtils.jsonArrayStr2PageDataList(norInsurPd.getString("calnorinsurancemx"));
		}
		//外包保险
		PageData outInsurPd=(PageData) dao.findForObject("PayrollnewMapper.findOutInsurByPayrollMonth", pd);
		if(outInsurPd !=null && outInsurPd.get("caloutsourcemx") !=null && !"".equals(outInsurPd.getString("caloutsourcemx"))){
			outsourcemx = JsonUtils.jsonArrayStr2PageDataList(outInsurPd.getString("caloutsourcemx"));
		}
		//公积金
		PageData housefundPd=(PageData) dao.findForObject("PayrollnewMapper.findHouseFundByPayrollMonth", pd);

		if(housefundPd !=null && housefundPd.get("calhousefundmx") !=null && !"".equals(housefundPd.getString("calhousefundmx"))){
			housefundmx = JsonUtils.jsonArrayStr2PageDataList(housefundPd.getString("calhousefundmx"));
		}

		//循环工资单中的人员明细，合计当前人员的社保  外包保险，公积金合计

		for(PageData payrollPd : payrollmx){
			String empdocid = payrollPd.get("empdocid").toString();
			double norinsuranceSum = norinsurancemx.stream().filter((PageData insurancePd) -> empdocid.equals(insurancePd.get("empdocid").toString()))
					.mapToDouble((PageData insuracePd) -> Double.parseDouble(insuracePd.get("personparttotal").toString())).sum();
			norinsurPersonMoney = CalculateUtil.add(norinsurPersonMoney,norinsuranceSum);

			double housefundSum = housefundmx.stream().filter((PageData housefundData) -> empdocid.equals(housefundData.get("empdocid").toString()))
					.mapToDouble((PageData housefundData) -> Double.parseDouble(housefundData.get("personparttotal").toString())).sum();
			housePersonMoney = CalculateUtil.add(housePersonMoney,housefundSum);

			double outsourceSum = outsourcemx.stream().filter((PageData outsourceData) -> empdocid.equals(outsourceData.get("empdocid").toString()))
					.mapToDouble((PageData outsourceData) -> Double.parseDouble(outsourceData.get("personparttotal").toString())).sum();
			outinsurPersonMoney = CalculateUtil.add(outinsurPersonMoney,outsourceSum);

		}
		return CalculateUtil.add(CalculateUtil.add(norinsurPersonMoney, outinsurPersonMoney), housePersonMoney);
	}



	public List<PageData> findHaveSubjectItems(PageData pd)throws Exception{
		List<PageData> list = (List<PageData>)dao.findForList("PayrollnewMapper.findHaveSubjectItems", pd);
		for(PageData pd1 :list){
			if("99".equals(pd1.get("code").toString())){//实发工资
				pd1.put("code", "code_99");
			}
		}
		return list;
	}
	/**
	 * 撤帐
	 * @param pd
	 * @return
	 */
	public PageData unaccount(PageData pd) {
		PageData resPd = new PageData();
		try {
			if(pd.get("voucher_id") == null || "".equals(pd.get("voucher_id").toString())){
				resPd.put("state", "error");
				resPd.put("message", "撤帐失败！没有找到凭证数据！");
				return resPd;
			}else{
				pd.put("DATA_IDS", pd.get("voucher_id"));
			}
			String id = pd.get("id").toString();
			//检查凭证  是否已经审核  或者是过账
			PageData checkPd=findVoucherIdByCode(pd);
			if(checkPd !=null && checkPd.get("isaudit") !=null){
				if(Integer.parseInt(checkPd.get("isaudit").toString())==1){
					resPd.put("state", "error");
					resPd.put("message", "要撤审的数据生成的凭证已经审核,不能撤审！");
					return resPd;
				}
			}
			resPd=fillvouchersService.delete(pd);
			if(resPd .get("state") !=null && "success".equals(resPd .get("state").toString())){
				pd.put("id",id);
				dao.update("PayrollnewMapper.updateVouchercodeAndIsAccount1", pd);
				resPd.put("message",  "单据撤帐成功！");
			}else{
				resPd.put("message",  "撤帐失败，请联系管理员！");
			}

		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}


	private PageData findVoucherIdByCode(PageData pd) throws Exception {
		PageData voucherPd=(PageData) dao.findForObject("PayrollnewMapper.findVoucherIdByCode", pd);
		return voucherPd;
	}

	public PageData treelist(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> yearList=new ArrayList<PageData>();
		try {
			yearList=(List<PageData>) dao.findForList("PayrollnewMapper.dataYeartreelist", page);
			List<PageData> monthList=(List<PageData>) dao.findForList("PayrollnewMapper.dataMonthtreelist", page);
			yearList.addAll(monthList);
			resPd.put("state","success");
			resPd.put("list",yearList);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}

		return resPd;
	}


	public PageData copyPrevMonth(PageData pd){
		PageData resPd=new PageData();
		try {
			resPd=(PageData) dao.findForObject("PayrollnewMapper.findByInsuranceMonth", pd);
			//2024-05-16修改，检测是否有离职人员，若有将离职人员移除
			List<PageData> empdocList =(List<PageData>) dao.findForList("PayrollnewMapper.findLizhiEmpList", pd);
			if(empdocList.size()>0 && resPd!=null && resPd.get("payrollmx")!=null && !"".equals(resPd.getString("payrollmx"))){
				Map<String, Object> map = JsonUtils.strToMap(resPd.getString("payrollmx"));
				List<PageData> payrollmx = JsonUtils.jsonArrayStr2PageDataList(map.get("rows").toString());
				for(PageData empdocPd : empdocList){
					String empdocid = empdocPd.get("id").toString();
					payrollmx.removeIf((PageData payrollPd) -> empdocid.equals(payrollPd.get("empdocid").toString()));
				}
				//合计行  数据构建
				List<PageData> footerList=new ArrayList<PageData>();
				PageData footerPd=new PageData();
				footerPd.put("empdoc_name", "合计");
				for(Map<String,String> empPd :payrollmx){
					for (String key : empPd.keySet()) {
						if(key.matches("^[0-9]*$") || key.equals("basemoney") || key.contains("code_")){
							if(footerPd.get(key) !=null){
								String footer=(footerPd.get(key)==null || "".equals(footerPd.get(key).toString()))?"0":footerPd.get(key).toString();
								String em=String.valueOf((empPd.get(key)==null || "".equals(empPd.get(key)))?"0":empPd.get(key));
								footerPd.put(key,
										CalculateUtil.add(footer
												, em));

							}else{
								footerPd.put(key, empPd.get(key));
							}
						}

					}
				}
				footerList.add(footerPd);
				map.put("footer", JsonUtils.PageDataToJSONArray(footerList));
				map.put("rows",JsonUtils.PageDataToJSONArray(payrollmx));
				resPd.put("payrollmx",JsonUtils.mapToString(map));
				resPd.put("total",payrollmx.size());
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	public PageData findAllByMonth(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PayrollnewMapper.findAllByMonth", pd);
			if(resPd == null){
				resPd = new PageData();
				resPd.putAll(pd);
			}
		} catch (Exception e) {
			e.printStackTrace();
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
}
