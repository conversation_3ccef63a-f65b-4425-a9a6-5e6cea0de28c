package org.newstanding.controller.base;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.management.RuntimeErrorException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.newstanding.common.entity.Logger;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.entity.User;
import org.newstanding.common.utils.PageDataSingleton;
import org.newstanding.common.utils.data.JedisUtil;
import org.newstanding.service.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;

import redis.clients.jedis.Jedis;

@SuppressWarnings({ "unchecked", "unused","resource"})
public class BaseController {
	@Autowired
	private BaseServiceImpl baseServiceImpl;
	private static final long serialVersionUID = 6357869213649815390L;
	protected Logger logger = Logger.getLogger(this.getClass());
	protected HttpServletRequest request;  
    protected HttpServletResponse response;  
    protected HttpSession session;  
    public String separator = File.separator;
    protected PageData parmsPd = new PageData();
    protected PageDataSingleton pageDataSingleton = PageDataSingleton.getInstance();
    
    
    public BaseController(){
    	
    }
    public static void logBefore(Logger logger, String interfaceName){
		logger.info("");
		logger.info("start");
		logger.info(interfaceName);
	}
	
	public static void logAfter(Logger logger){
		logger.info("end");
		logger.info("");
	}
	
	public static User getCurrentUser(){
		Subject currentUser = SecurityUtils.getSubject();  
		Session session = currentUser.getSession();
		return (User)session.getAttribute("sessionUser");
	}
	
	/**
	 * 从缓存中得到 登录用户
	 * @return
	 */
	public User getCurrentUserByCatch(){
		PageData pd = getPageData();
		if (pd.get("version_") != null) {
			String version_id = pd.get("version_").toString();
			User user = (User) pageDataSingleton.get(version_id);
			return user;
		}else {
			return null;
		}
	}
	
	@ModelAttribute
    public void setReqAndRes(HttpServletRequest request, HttpServletResponse response){  
        this.request = request;  
        this.response = response;  
        this.session = request.getSession();  
    }  
	
	/**
	 * 得到PageData
	 */
	public PageData getPageData(){
		//获取 request 参数数据
		PageData resultPd = new PageData(this.getRequest());
		if(resultPd.get("database") == null){
			if (resultPd.get("version_") != null) {
				String version_id = resultPd.get("version_").toString();
				User user = (User) pageDataSingleton.get(version_id);
				resultPd.put("database", user.getDatabase_());
			}else {
				resultPd.put("database", "");
			}
		}
		/*if (resultPd != null) {
			String database = "";
			//获取缓存服务器
			String session_id = this.session.getId();
			JedisUtil jedisUtil= null;
			Jedis jedis = null;
			//如果request 中不存在 database 从缓存服务器获取
			if (resultPd.get("database")==null) {
				try {
					jedisUtil = JedisUtil.getInstance();
					jedis = jedisUtil.getJedis("", 0);
					database = jedis.get(session_id + "_database");
					if (database != null && !database.equals("")) {
						resultPd.put("database", database);
					}
				} catch (Exception e) {
					e.printStackTrace();
				}finally{
					if (jedisUtil != null && jedis!= null) {
						jedisUtil.closeJedis(jedis);
					}
				}
			}
			
		}*/
		return resultPd;
	}
	
	/**
	 * 得到ModelAndView
	 */
	public ModelAndView getModelAndView(){
		return new ModelAndView();
	}
	
	/**
	 * 设置 分页参数
	 * @param pd
	 * @param page
	 * @return
	 */
	public Page setPage(PageData pd,Page page) {
		if (pd.get("page") != null && pd.get("rows") != null) {
			page.setCurrentPage((String) pd.get("page"));
			pd.put("start", (Integer.parseInt(pd.getString("page"))-1)*Integer.parseInt(pd.getString("rows")));
			pd.put("limit", pd.getString("rows"));
			page.setCurrentResult(String.valueOf(pd.get("start")));
			page.setShowCount((String) pd.get("rows"));
		}
		page.setPd(pd);
		return page;
	}
	
	/**
	 * 得到request对象
	 */
	public HttpServletRequest getRequest() {
		HttpServletRequest request = ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
		return request;
	}
	/**
	 * 根据当前会计制度，获取每一级别code 的总长度数组
	 * eg:4-2-3 --->[4,6,9]
	 * @return
	 */
	public String[] getAccountCodeArray(){
		PageData pd=getPageData();
		String [] atcode=new String[20];
		//获取当前的会计制度
		String accountcode=baseServiceImpl.getAccountcode(pd);
		if(accountcode != "" && accountcode !=null){
			atcode=accountcode.split("-");
		}
		int firsttotallength=0,secondtotallength=0,thirdtotallength=0,fourtotallength=0,fivetotallength=0;
		for(int i=0;i<atcode.length;i++){
			if(i==0){
				firsttotallength=Integer.parseInt(atcode[0]);
				atcode[0]=firsttotallength+"";
			}else if(i==1){
				secondtotallength=firsttotallength+Integer.parseInt(atcode[1]);
				atcode[1]=secondtotallength+"";
			}else if(i==2){
				thirdtotallength =secondtotallength+Integer.parseInt(atcode[2]);
				atcode[2]=thirdtotallength+"";
			}else if(i==3){
				fourtotallength=thirdtotallength+Integer.parseInt(atcode[3]);
				atcode[3]=fourtotallength+"";
			}else if(i==4){
				fivetotallength=fourtotallength+Integer.parseInt(atcode[4]);
				atcode[4]=fivetotallength+"";
			}
		}
		return atcode;
	}
	
}
