.textbox {
  position: relative;
  border: 1px solid #95B8E7;
  background-color: #fff;
  vertical-align: middle;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  margin: 0;
  padding: 0;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.textbox .textbox-text {
  font-size: 14px;
  border: 0;
  margin: 0;
  padding: 0 4px;
  white-space: normal;
  vertical-align: top;
  outline-style: none;
  resize: none;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
  height: 28px;
  line-height: 28px;
}
.textbox textarea.textbox-text {
  line-height: normal;
}
.textbox .textbox-text::-ms-clear,
.textbox .textbox-text::-ms-reveal {
  display: none;
}
.textbox textarea.textbox-text {
  white-space: pre-wrap;
}
.textbox .textbox-prompt {
  font-size: 14px;
  color: #aaa;
}
.textbox .textbox-bgicon {
  background-position: 3px center;
  padding-left: 21px;
}
.textbox .textbox-button,
.textbox .textbox-button:hover {
  position: absolute;
  top: 0;
  padding: 0;
  vertical-align: top;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.textbox .textbox-button-right,
.textbox .textbox-button-right:hover {
  right: 0;
  border-width: 0 0 0 1px;
}
.textbox .textbox-button-left,
.textbox .textbox-button-left:hover {
  left: 0;
  border-width: 0 1px 0 0;
}
.textbox .textbox-button-top,
.textbox .textbox-button-top:hover {
  left: 0;
  border-width: 0 0 1px 0;
}
.textbox .textbox-button-bottom,
.textbox .textbox-button-bottom:hover {
  top: auto;
  bottom: 0;
  left: 0;
  border-width: 1px 0 0 0;
}
.textbox-addon {
  position: absolute;
  top: 0;
}
.textbox-label {
  display: inline-block;
  width: 80px;
  height: 30px;
  line-height: 30px;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
  padding-right: 5px;
}
.textbox-label-after {
  padding-left: 5px;
  padding-right: 0;
}
.textbox-label-top {
  display: block;
  width: auto;
  padding: 0;
}
.textbox-disabled,
.textbox-label-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.textbox-icon {
  display: inline-block;
  width: 18px;
  height: 20px;
  overflow: hidden;
  vertical-align: top;
  background-position: center center;
  cursor: pointer;
  opacity: 0.6;
  filter: alpha(opacity=60);
  text-decoration: none;
  outline-style: none;
}
.textbox-icon-disabled,
.textbox-icon-readonly {
  cursor: default;
}
.textbox-icon:hover {
  opacity: 1.0;
  filter: alpha(opacity=100);
}
.textbox-icon-disabled:hover {
  opacity: 0.6;
  filter: alpha(opacity=60);
}
.textbox-focused {
  border-color: #6b9cde;
  -moz-box-shadow: 0 0 3px 0 #95B8E7;
  -webkit-box-shadow: 0 0 3px 0 #95B8E7;
  box-shadow: 0 0 3px 0 #95B8E7;
}
.textbox-invalid {
  border-color: #ffa8a8;
  background-color: #fff3f3;
}
.form-floating-label.form-field .textbox-text {
  padding: 0;
}
.form-floating-label.form-field .textbox-label {
  position: relative;
  height: 20px;
  line-height: 20px;
  transition: all .3s;
  font-size: 12px;
  z-index: 9;
}
.form-floating-label.form-field-empty .textbox-label {
  cursor: text;
  font-size: 14px;
  transform: translate(0,25px);
}
.form-floating-label.form-field-empty.form-field-focused .textbox-label {
  cursor: default;
  font-size: 12px;
  transform: translate(0,0);
}
