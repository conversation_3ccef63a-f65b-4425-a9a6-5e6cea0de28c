<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/summaryAccount.css">
<style type="text/css">
.datagrid-header-row td{border-bottom: 1px #6f9ec2 solid;border-left: 1px #6f9ec2 solid;}
.datagrid-header-row{ border-bottom: 1px #6f9ec2 solid;} 
#parcel_southToolDiv .datagrid-view2 .datagrid-htable td:last-child{border: 1px #6f9ec2 solid;    border-top: 0;}   
.commontbody{color: #5a666e !important;font-weight: 600;background: #c9defd !important;}  
.commontbody td div{text-align:center !important;}
.commontbody td a{color: #5a666e !important;text-decoration:none;}
</style>
</head>
<body style="background: #ebedf3;">

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
	
		<!-- 顶部功能栏 -->
		<div id="northToolDiv" data-options="region:'north'" style="background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4); background: -moz-linear-gradient(top,#fff,#f4f4f4);background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<a class="easyui-linkbutton gl_btns" style="float: left;" href="javascript:void(0)" onclick="$('#filter_pageId').window('open')"></a>
				<div style="float: left;width: 400px;margin: 0 0 0 100px;">
				<a class="easyui-linkbutton dyy_btns" href="javascript:void(0)" onclick="cross('min')"></a>
				<a class="easyui-linkbutton prevy_btns" href="javascript:void(0)" onclick="cross('b')"></a>
				<a class="easyui-linkbutton nexty_btns" href="javascript:void(0)" onclick="cross('n')"></a>
				<a class="easyui-linkbutton my_btns" href="javascript:void(0)" onclick="cross('max')"></a>
			</div>
			<p class="refreshbtns" id="refreshbtn" onclick="refresh()" style="display:none"></p>
			<div style="float: right;width: 435px;margin: 0 0 0 100px;">
				<!-- <a class="easyui-linkbutton dc_btns" href="javascript:void(0)" onclick="ExportExcel()"></a> -->
				<a class="easyui-menubutton dc_btns" href="javascript:void(0)" data-options="menu:'#createvoucherDiv'"></a>
				<div id="createvoucherDiv" style="width:100px;display:none">
					<div onclick="ExportExcel()">导出EXCEL</div>
					<div onclick="printAndPreview('导出PDF')">导出PDF</div>
				</div>
				<a class="easyui-linkbutton dy_btns" href="javascript:void(0)" onclick="printAndPreview('打印')"></a>
				<a class="easyui-linkbutton yl_btns" href="javascript:void(0)" onclick="printAndPreview('预览')"></a>
				<a class="easyui-linkbutton dypz_btns" href="javascript:void(0)" onclick="printallocation({width:320,height:490,menucode:'SupplierDetail'})"></a>
				<a class="easyui-linkbutton gb_btns" href="javascript:void(0)" onclick="closeIndexTabs({title:'供应商往来明细表'})"></a>
			</div>
			
		</div>
		
		<!-- 主面板 -->
		<div style="margin: 54px 0 0 0;border:1px #6f9ec2 solid;border-bottom:0" id="parcel_southToolDiv">
		    <div class="gridlist_top">
		        <p class="gridlist_top_le">
		        	<span>单位：</span>
		        	<input id="accountname" type="text" name="" class="" readonly="readonly" />
		        </p>
		        <p class="gridlist_top_cent">供应商往来明细账</p>
		        <p class="gridlist_top_cents">
		        	<span>辅助核算：</span>
		        	<span class="towsp" id="assist_now_name"></span>
		        	<input type="hidden" id="assist_now_id" name="assist_now_id" />
		        </p>
		        <p class="gridlist_top_ri">
		        	<span>会计期间：</span>
		        	<span class="towsp" id="period"></span>
		        </p>
		    </div>
		    
		    <!-- 数据面板 -->
			<div id="gridlist" data-options="region:'center'" style="width: 100%"></div>
		</div>
		
		<!--报表过滤弹窗 -->
		<div id="filter_pageId" class="easyui-window" title="过滤" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div class="bgdivtitfixed" style="width: 100%;height: 193px;">		
		    	<ul class="report_ul" style="height: 191px;width: 508px;">
		    		<li style="margin: 49px 0 4px 0;">
		    			<p >会计期间：</p>
	    				<input type='text' id="year" id="year" readonly="readonly" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" value="${year}" onclick="WdatePicker({skin:'whyGreen',startDate:'%y',dateFmt:'yyyy'})" />
						<input type="text" id="month_begin" class="invo_title_year" name="month_begin" style="float: left;margin: 0 3px 0 0;" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM'})" /> 
						<span style="float: left;margin: 6px 3px 0 0;">月&nbsp;&nbsp; 至</span>
						<input type="text" id="month_finish" name="month_finish" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM'})" />
						 <span style="float: left;margin: 6px 0 0 0;">月</span>
		    		</li>
		    		
		    		<li>	
		    			<p style="margin: 0 2px 0 14px;">辅助核算：</p>
		    			<input type="hidden" id="assist_begin_id" name="assist_begin_id"/>
		    			<input type="hidden" id="assist_begin_name" name="assist_begin_name"/>
	    				<input class="easyui-textbox" id="assist_begin" style="width:173px; height:27px;padding:0 24px 0 2px;" name="assist_begin" />
	    				<div class="kjkm_btn" style="right: 52%;top: 29%;" onclick="auxClick('begin')"></div>
	    				<span>至</span>
	    				<input type="hidden" id="assist_finish_id" name="assist_finish_id"/>
		    			<input type="hidden" id="assist_finish_name" name="assist_finish_name"/>
	    				<input class="easyui-textbox" id="assist_finish" style="width:173px; height:27px;padding:0 24px 0 2px;" name="assist_finish" />
	    				<div class="kjkm_btn" style="right: 14%;top: 29%;" onclick="auxClick('finish')"></div>
		    		</li>
		    		<li style="margin: 2px 0 0 0;">
		    			<p>是否包含未过账凭证：</p>
	    				<input type="checkbox" id="isinclude" name="isinclude" class="checkboxbgimg" style="margin: 6px 0 0 0;" />
		    		</li>
		    	</ul>
		    </div>	
		    <div style="height:37px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;    padding: 4px 0 0 0;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" style="margin: 0px 13px 0 0;"onclick="$('#filter_pageId').window('close');">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" style="margin: 0px 9px 0 0;"onclick="filterOk()">确定</a>
		    </div>
		</div>
	<!-- 打印配置 -->
			<div id="printedit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div style="width:316px;border: 1px #cedae4 solid;">
		        <div class="bgdivtit" style="border: 0;height:47px">
			        <span style="margin:0 10px;line-height: 46px;display: inline-block;float: left;">模板名称</span>
					<select id="temp_name"  name="temp_name"  class="easyui-combobox"  data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="width:120px;margin: 0;height: 27px;">
					    
					</select>
					<a class="delelte_fillvouchers" href="javascript:void(0)" onclick="del_temp()">删除</a>
					<a class="add_fillvouchers" href="javascript:void(0)" onclick="add_temp()">新增</a>
				</div>	
		    	<div class="centerpagediv">
		    	     <div class="tablepage">
			    	     <p class="paper_btn paper_btns" onclick="switchoverF(1)">纸张</p>
			    	     <p class="page_btn page_btns" onclick="switchoverF(2)">页面</p>
		    	     </div>
		    	     <div class="paper_div">
		    	     <div id="divMMHeight" style="height: 1mm; width: 1mm; display: none;"></div>
		    	     <input type="hidden" id="menucode" name="menucode"> 
		    	     <input type="hidden" id="tempid" name="tempid">  
		    	      <input type="hidden" id="name" name="name">    
		    	      <input type="hidden" id="content" name="content">
		    	      <input type="hidden" id="html" name="html">  
		    	          <ul>
		    	             <li style="margin: 22px 0 0 0;">
		    	                 <span>纸张类型：</span>
		    	                 <div style="background:#fff;width: 149px;height: 25px;margin: 1px 0 0 62px; border-radius: 3px;">
			    	                 <div id="selectStyle" class="selectStyle">
							             <select class="select" id="pagelx" name="pagelx">
											<option value="A5(横向)">A5(横向)</option>
											<option value="A4">A4</option>
											<option value="自定义">自定义</option>
										</select>
									</div>
									<div class="input-group-addon"><p class="glyphicon actips"></p></div>
		    	                 </div>
		    	             </li>
		    	             <li>
		    	                 <span>页面属性</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>高度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="height" type="text" id="height" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>宽度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="width" type="text" id="width"onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>页边距</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>上边距：</span>
		    	                 <input name="page_top" type="text" id="page_top" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>下边距：</span>
		    	                 <input name="page_bottom" type="text" id="page_bottom"onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>左边距：</span>
		    	                 <input name="page_left" type="text" id="page_left"onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>右边距：</span>
		    	                 <input name="page_right" type="text" id="page_right" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	          </ul>
		    	     </div> 
		    	     <div class="pape_div">
		    	         <ul class="page_t_ul page_t_ulspec">
		    	             <li>
		    	                <span>列项目</span>
		    	                <span>列宽（毫米）</span>
		    	                <span>是否打印</span>
		    	             </li>
		    	             <li>
		    	                <span>日期</span>
		    	                <span>
		    	                    <input  type="text" id="voucherdatewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                <span>
		    	                    <select id="voucherdateSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>凭证字号</span>
		    	                <span>
		    	               		<input   type="text" id="vouchercodewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="vouchercodeSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>摘要</span>
		    	                <span>
		    	                	<input  name="abstractawidth" type="text" id="abstractawidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="abstractaSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	              <li>
		    	                <span>发票号码/付款账户</span>
		    	                <span>
		    	                	<input  name="invoiceoraccountwidth" type="text" id="invoiceoraccountwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="invoiceoraccountSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>借方金额</span>
		    	                <span>
		    	                     <input  name="debitmoneywidth" type="text" id="debitmoneywidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                <span>
		    	                    <select id="debitmoneySel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	              <li>
		    	                <span>贷方金额</span>
		    	                <span>
		    	                    <input  name="creditmoneywidth" type="text" id="creditmoneywidth"onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="creditmoneySel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>余额方向</span>
		    	                <span>
		    	                    <input  name="directionwidth" type="text" id="directionwidth"onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="directionSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>余额</span>
		    	                <span>
		    	                    <input  name="surpluswidth" type="text" id="surpluswidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="surplusSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	         </ul>
		    	         <ul class="page_b_ul" style="margin: 12px 0 14px 19px;height: 63px;">
		    	             <li style="margin: 1px 0 0 0;">
		    	                 <span>行高：</span>
		    	                 <input name="rowheight" type="text" id="rowheight" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>每页循环行数：</span>
		    	                 <input name="hs" type="text" id="hs" onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')" class=""/>
		    	                 <p>（单位：行）</p>
		    	             </li>
		    	         </ul>
		    	     </div>
		    	</div>
		    </div>
		    <div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
				<!-- 底部功能栏 -->
				<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="closeclearF()">取消</a>
				<a class="easyui-linkbutton sure-dialog" href="javascript:void(0)" onclick="templateSure()">确定</a>
			</div>
		</div>
		
		
		<div id="edit_pageIdm" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="overflow: hidden;display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的模板</p>
		    	<select class="easyui-combobox" id="template_name" name="template_name" data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="overflow: hidden;width: 125px;height:30px">
   	       			
				</select>
				<input type="hidden" id="tempurl" />
				<input type="hidden" id="opertype">
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdm')">取消</a>
			    	<a href="javascript:void(0)" id="next_btn" class="easyui-linkbutton cancel-btn sureNew_btn" onclick="nextprint()">下一步</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn sureNew_btn print_btns" style="display:none" onclick="printTemp('edit')">打印</a>
		    	</div>
		    </div>
		</div>
		
		<div id="edit_pageIdprintb" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的操作</p>
		    	<input type="hidden" id="selecttempurl" />
		    	<input type="hidden" id="tempname" />
		    	<!-- <a href="javascript:void(0)" class="print_btn" onclick="exportTemp()">导出</a> -->
		    	<a href="javascript:void(0)" class="print_btn" style="margin:-5px 0 0 28px" onclick="printTemp('edit')">打印</a>
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdprintb')">取消</a>
		    	</div>
		    </div>
		</div>
	</div>
<div style=" position: absolute;top: 0;opacity: 0.3;   background: #ccc;width:100%;height:100%;font-size: 12px;display:none" class="datagridmask">
     <div class="datagrid-mask-msg" style="display: block; left: 50%; height: 16px; margin-left: -47.825px; line-height: 16px;">正在加载...</div>
</div>
<script type="text/javascript">
$(function(){
	$("#month_begin").val('01');
	$('#month_finish').val('12');
	//动态添加科目级次选项option
	var accinfo = findAccInfo();
	$('#accountname').val(accinfo.accountname);
});

var width = $(window).width()-76;
var w1 = width/30;
var heights = $(window).height();
$("#parcel_southToolDiv").height(heights-80);
$("#gridlist").height(heights-101);
//$("#gridlist").width(1093);

var data = JSON.parse('${data}');
var code = data.code;
var columns = data.columns;
//格式化columns
columnsFormat(columns);

var obj = new Object();
obj["position"] = '#gridlist';
obj["url"] = 'report/getGridData_CustomerDetail';
obj["columns"] = [columns];
obj["fitColumns"] = false;
obj["pagination"] = false;
obj["showFooter"] = true;
//obj["queryParams"] = {code:'DetailedAccount',params:'month_begin:2017-01,month_finish:2017-12,subjectid:72',sub_direction:'0'}
obj["listDbClickFun"] = function (){};
//生成报表表格
Grid.list_report(obj);


$('#gridlist').datagrid("options").view.onAfterRender = function (target, rows) {
	debugger;
    var data = $('#gridlist').datagrid('getData');
	var ddata=data.rows;
	for(var i=0;i<ddata.length;i++){
		if(!checkEm(ddata[i].position)){
			$('.datagrid-view2 .datagrid-btable').find('tr').eq(ddata[i].position - 1).addClass('commontbody');
			
		}
	}
};
//定义变量
var year, month_begin, month_finish, isinclude,assistid, params;

//初始化变量 并赋值
function initData(){
	var accinfo = findAccInfo();
	var periodofaccount = accinfo.periodofaccount;
	var periodofaccounts = periodofaccount.split('-');

	year = $("#year").val();
	month_begin = year + '-' + $("#month_begin").val();
	month_finish = year + '-' + $('#month_finish').val();
	assistid= $('#assist_now_id').val();
	if($("#isinclude").is(':checked')){
		isinclude = "2";
	}else{
		isinclude = "1";
	}
	$('#period').text(year + '年' + $("#month_begin").val() + '月至' + $('#month_finish').val() + '月');
	
	if(checkEm(assistid)){
		$('#assist_now_id').val($('#assist_begin_id').val());
		$('#assist_now_name').text($('#assist_begin_name').val());
		assistid=$('#assist_now_id').val();
	}
	
	params = 'month_begin:'+month_begin+',month_finish:'+month_finish
	   		+',!isinclude:'+isinclude +',assistid:'+assistid;
}

//选择辅助核算   开始  和结束
function auxClick(type){
	if(type=='begin'){
		Dialog.archives_supplierdoc('1;filter_pageId;assist_begin_id:id,assist_begin_name:name,assist_begin:name;report');
	}else{
		Dialog.archives_supplierdoc('1;filter_pageId;assist_finish_id:id,assist_finish_name:name,assist_finish:name;report');
	}
}
//过滤弹窗 确定
function filterOk(){
	if(checkEm($('#assist_begin_id').val())){
		$.messager.alert('提示','请选择辅助核算！');
		return;
	}
	//设置表格顶部  辅助核算值
	$('#assist_now_id').val( $('#assist_begin_id').val() );
	$('#assist_now_name').text($('#assist_begin_name').val());
	
	initData();
	queryData();
}
var searchFlag=0;
//查询
function queryData(){
	searchFlag=1;
	initData();
	$('#gridlist').datagrid('load',{code:code, params:params});
	$('#filter_pageId').window('close');
}

//翻页
function cross(flag){
	var assist_begin_id = $('#assist_begin_id').val();
	var assist_finish_id = $('#assist_finish_id').val();
	var assist_now_id = $('#assist_now_id').val();
	switch(flag){
	case 'n':	//下一页
		var pd = {
			assist_begin_id : assist_begin_id, 
			assist_finish_id : assist_finish_id, 
			assist_now_id : assist_now_id,
			auxtype:2,
			flag : "n"
		};
		findAux(pd);
		queryData();
		break;
	case 'b':	//上一页
		var pd = {
			assist_begin_id : assist_begin_id, 
			assist_finish_id : assist_finish_id, 
			assist_now_id : assist_now_id,
			auxtype:2,
			flag : "b"
		};
		findAux(pd);
		queryData();
		break;
	case 'min':	//第一页
		$('#assist_now_id').val( $('#assist_begin_id').val() );
		$('#assist_now_name').text( $('#assist_begin_name').val() );
		queryData();
		break;
	case 'max':	//最末页
		$('#assist_now_id').val( $('#assist_finish_id').val() );
		$('#assist_now_name').text( $('#assist_finish_name').val() );
		queryData();
		break;
	}
}

//查询会计科目
function findAux(pd){
	$.ajax({
		url: getUrl('report/findAux'),
		type: 'post',
		async: false,
		data: pd,
		dataType: 'json',
		success: function(data){
			$('#assist_now_id').val( data.id);
			$('#assist_now_name').text( data.name);
		}
	});
}

function doSupplierSearch(value){
	$('#supplier_gridlist').datagrid('load',{
		q:value
	})
}

//导出
function ExportExcel(){
	var obj=new Object;
	obj['renderid']='#gridlist';
	obj['title'] = '供应商往来明细帐';
	obj['controllername'] = 'report';
	obj['run_method'] = 'getGridData_DetailedAccount';
	obj['method_type'] = '1';
	obj['cs'] = '';
	obj['pms'] = {code:code, params:params};
	toExcel(obj);
}
//打印、预览
function printAndPreview(opertype){
	if(searchFlag==0){
		return;
	}
	var obj=new Object;
	obj['opertype']=opertype;
	if(opertype == '批量打印' || opertype == '导出PDF'){
		obj['table_name']='c_assist';
		obj['begin_id'] = $("#assist_begin_id").val();
		obj['finish_id'] = $("#assist_finish_id").val();
		obj['month_begin'] = month_begin;
		obj['month_finish'] = month_finish;
		obj['isinclude'] = isinclude;
		obj['auxtype'] = 2;
	}
	obj['operpage']='report';
	obj['menucode']='SupplierDetail';
	obj['controllername'] = 'report';
	obj['run_method'] = 'getGridData_DetailedAccount';
	obj['method_type'] = '1';
	obj['cs'] = '';
	obj['pms'] = {code:code, params:params,auxtype : 2};
	previewClick_report(obj);
}

function closeKmye(id){
	$('#'+id).dialog('close');
}
//关闭弹出框
function closeclearF(){
	$('#printedit_pageId').window('close');
}
//temp_name 事件
$("#temp_name").combobox({
	onSelect:function(record){
		$.ajax({
		    url:getUrl('printSet/findByCode.do'),
		    type:'post',
		    async:false,
		    data: {"menucode":"SupplierDetail","name" : record.text},
		    dataType:'json',
		    success:function(data){
  				$("#menucode").val(data.menucode);
  				$('#tempid').val(data.id);
  				$("#height").val(data.height);
  				$("#width").val(data.width);
  				$("#hs").val(data.hs);
  				$("#page_bottom").val(data.page_bottom);
  				$("#page_left").val(data.page_left);
  				$("#page_right").val(data.page_right);
  				$("#page_top").val(data.page_top);
  				$("#pagelx").val(data.pagelx);
  				$("#rowheight").val(data.rowheight);
  				$("#title").val(data.title);
  				
  				$("#content").val(data.content);
  				$("#html").val(data.html);
  				var columnstr=data.columnstr;
  				var cols=columnstr.split(',');
  				for(var i=0;i<cols.length;i++){
  					var str=cols[i].split(':');
  					$("#"+str[0]).val(str[1]);
  				}
		    }
		})
	}
})
//模板设置确定
function templateSure(){
	//将毫米转化成px  
	debugger
	var height=$("#height").val();
	var width=$("#width").val();
	var hs=$("#hs").val();
	var page_bottom=$("#page_bottom").val();
	var page_left=$("#page_left").val();
	var page_right=$("#page_right").val();
	var page_top=$("#page_top").val();
	var pagelx=$("#pagelx").val();
	var rowheight=$("#rowheight").val();
	var title='供应商往来明细账';
	
	
	var pheight = $('#divMMHeight').height(), pwidth = $('#divMMHeight').width();
	var rowheight=$("#rowheight").val();
	
	var voucherdatewidth = $("#voucherdatewidth").val() || 0;
	var vouchercodewidth = $("#vouchercodewidth").val() || 0;
	var abstractawidth=$("#abstractawidth").val() || 0;
	var invoiceoraccountwidth=$("#invoiceoraccountwidth").val() || 0;
	var debitmoneywidth=$("#debitmoneywidth").val() || 0;
	var creditmoneywidth=$("#creditmoneywidth").val() || 0;
	var directionwidth=$("#directionwidth").val() || 0;
	var surpluswidth=$("#surpluswidth").val() || 0;
	
	var voucherdateSel=$("#voucherdateSel").val();
	var vouchercodeSel=$("#vouchercodeSel").val();
	var abstractaSel=$("#abstractaSel").val();
	var invoiceoraccountSel=$("#invoiceoraccountSel").val();
	var debitmoneySel=$("#debitmoneySel").val();
	var creditmoneySel=$("#creditmoneySel").val();
	var directionSel=$("#directionSel").val();
	var surplusSel=$("#surplusSel").val();
	var menucode="SupplierDetail";
	var name=/* $('#name').val() */$("#temp_name").combobox("getValue");
	var tdf=1;
	var widthp = ((parseInt(creditmoneywidth)+parseInt(directionwidth)+parseInt(surpluswidth)+parseInt(voucherdatewidth)+parseInt(vouchercodewidth)+parseInt(abstractawidth)+parseInt(invoiceoraccountwidth)+parseInt(debitmoneywidth)) * pwidth)+parseInt(30)
	
	var columnstr="voucherdatewidth:"+voucherdatewidth+",vouchercodewidth:"+vouchercodewidth
	+",abstractawidth:"+abstractawidth+",debitmoneywidth:"+debitmoneywidth+",creditmoneywidth:"+creditmoneywidth
	+",directionwidth:"+directionwidth+",surpluswidth:"+surpluswidth
	+",voucherdateSel:"+voucherdateSel+",invoiceoraccountwidth:"+invoiceoraccountwidth
	+",vouchercodeSel:"+vouchercodeSel+",abstractaSel:"+abstractaSel
	+",debitmoneySel:"+debitmoneySel+",creditmoneySel:"+creditmoneySel
	+",directionSel:"+directionSel+",surplusSel:"+surplusSel+",invoiceoraccountSel:"+invoiceoraccountSel;
	
	var contentstr='<style type="text/css"></style>'
		+'<p style="text-align: center;">'
		+'    <span style="font-size: 20px;">供应商往来明细账</span>'
		+'</p>'
		+'<p style="width:'+widthp+'px;margin: 0 auto;">'
		+'    <span style="font-size: 12px;"></span><span style="font-size: 12px;">'
		+'<span style="font-size: 12px;display: inline-block;float:left">'
		+'    单位：<textarea class="laowang" readonly="readonly" id="companyname" '
		+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 190px; height: 20px; border: 1px dashed rgb(0, 0, 0); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 2px 0 0 0;" '
		+'    title="~{main.companyname}">公司名称</textarea>'
		+'</span><span style="font-size: 12px;display: inline-block;float:right">'
		+'    辅助核算：<textarea class="laowang" readonly="readonly" id="assist_now_name" '
		+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 180px; height: 20px; border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
		+'    font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 2px 0 0 0;" title="~{main.assist_now_name}">辅助核算</textarea>'
		+'</span><span style="font-size: 12px;display: inline-block;float:right">'
		+'    会计期间：<textarea class="laowang" readonly="readonly" id="accountperioda" '
		+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 200px; height: 20px; border: 1px dashed rgb(0, 0, 0); font-size: 12px;'
		+'font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 2px 0 0 0;" title="~{main.accountperioda}">会计期间</textarea></span>'
		+'</p>'
		+'<hr/>'
		+'<table>'
		+'    <tbody>'
		+'        <tr class="firstRow">';
		
		var firstTr='';
		var secondtr='';
		var hjtr='';
		
		if(voucherdateSel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">日期</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="voucherdate" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.voucherdate}">日期</textarea>'
			+'            </td>';
			hjtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px; word-break: break-all;" valign="top">'
			+'               &nbsp; &nbsp; &nbsp;合计'
			+'            </td>';
		}
		if(vouchercodeSel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">凭证字号</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="vouchercode" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.vouchercode}">凭证字号</textarea>'
			+'            </td>';
			hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
		}
		if(abstractaSel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">摘要</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="abstracta" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.abstracta}">摘要</textarea>'
			+'            </td>';
			hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
		}
		if(invoiceoraccountSel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+invoiceoraccountwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">发票号码/收款账户</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+invoiceoraccountwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="invoiceoraccount" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+invoiceoraccountwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.invoiceoraccount}">发票号码/收款账户</textarea>'
			+'            </td>';
			hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+invoiceoraccountwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
		}
		if(debitmoneySel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">借方金额</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="debitmoney" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.debitmoney}">借方金额</textarea>'
			+'            </td>';
			hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="debitmoney" '
				+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
				+'		title="~{mx.debitmoney}">借方金额</textarea>'
				+'            </td>'
		}
		if(creditmoneySel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">贷方金额</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="creditmoney" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.creditmoney}">贷方金额</textarea>'
			+'            </td>';
			hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="creditmoney" '
				+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
				+'		title="~{mx.creditmoney}">贷方金额</textarea>'
				+'            </td>'
		}
		
		if(directionSel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">余额方向</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="direction" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.direction}">余额方向</textarea>'
			+'            </td>';
			hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>';
		}
		if(surplusSel=='是'){
			firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">余额</td>';
			secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="surplus" '
			+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
			+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.surplus}">余额</textarea>'
			+'            </td>';
			hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="surplus" '
				+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
				+'		title="~{mx.surplus}">余额</textarea>'
				+'            </td>'
				firstTr +='</tr><tr>';
			secondtr +='</tr><tr>';
			hjtr +='</tr>'
				+'</tbody>'
				+'</table>'
				+'<hr/>'
				+'<p>'
				+'<span style="font-size: 12px;"></span><br/>'
				+'</p>';
		}else{
			firstTr +='</tr><tr>';
			secondtr +='</tr><tr>';
			hjtr +='</tr>'
				+'</tbody>'
				+'</table>'
				+'<hr/>'
				+'<p>'
				+'<span style="font-size: 12px;"></span><br/>'
				+'</p>';
		};
		var content=contentstr + firstTr +secondtr + hjtr;
	//保存
	if(name == ''){
		var name = prompt('请输入文件名', '');
    	if(name != null && name != ''){
    		//保存模板
    		$.post(getUrl('printSet/save'),{menucode:menucode,name:name,title:title,content:content,
    									   pagelx:pagelx,width:width,height:height,
    									   page_top:page_top,page_bottom:page_bottom,
    									   page_left:page_left,page_right:page_right,rowheight:rowheight,pagegs:7,
    									   hs:hs,columnstr:columnstr},function(data){
										    if(data.state == 'success'){
										    	$('#printcode').val(data.code);
							    				promptbox('success','操作成功！')
							    			}else{
							    				alert(data.msg);
							    				promptbox('error',data.msg)
							    			}
    		})
    	}
	}else{
		$.post(getUrl('printSet/edit'),{menucode:menucode,name:name,title:title,content:content,
			   pagelx:pagelx,width:width,height:height,
			   page_top:page_top,page_bottom:page_bottom,
			   page_left:page_left,page_right:page_right,rowheight:rowheight,pagegs:7,
			   hs:hs,columnstr:columnstr},function(data){
			    if(data.state == 'success'){
			    	$('#printcode').val(data.code);
    				promptbox('success','操作成功！')
    			}else{
    				alert(data.msg);
    				promptbox('error',data.msg)
    			}
})
	}
	$('#printedit_pageId').window('close');
}
$("#pagelx").change(function(e){
	var lx= $("#pagelx").val();
	if(lx=="A5(横向)"){
		$("#height").val(148);
		$("#width").val(210);
		$("#height").attr("readOnly",true);
		$("#width").attr("readOnly",true);
	}else{
		$("#height").val(297);
		$("#width").val(210);
		if(lx=="A4"){
			$("#height").attr("readOnly",true);
			$("#width").attr("readOnly",true);
		}else{
			$("#height").attr("readOnly",false);
			$("#width").attr("readOnly",false);
		}
	}
	
})
</script>

</body>
</html>