package org.newstanding.controller.invoicing;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.excel.XLSXCovertCSVReader;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.invoicing.PurchaseInvoiceService;
import org.newstanding.service.invoicing.PurchaselistService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import org.springframework.web.servlet.ModelAndView;
/**
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "/purchaseinvoice")
public class PurchaseInvoiceController extends BaseController {
	@Resource(name = "purchaseInvoiceService")
	private PurchaseInvoiceService purchaseInvoiceService;
	
	/**
	 * 删除
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/delete")
	@ResponseBody
	public Object delete() throws Exception {
		PageData pd = this.getPageData();
		return purchaseInvoiceService.delete(pd);
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit")
	@ResponseBody
	public Object edit() throws Exception {
		PageData pd = this.getPageData();
		return purchaseInvoiceService.edit(pd);
	}

	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		if( pd.get("accountperiod") == null || "".equals(pd.get("accountperiod").toString()) ){
			pd.put("accountperiod",purchaseInvoiceService.getAccountperiod(pd));
		}
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		page.setPd(pd);
		resultPageData = purchaseInvoiceService.list(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	/**
	 * 	选择采购入库明细数据 方法
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/listPurchaselistmx")
	public PageData listPurchaselistInvoicemx() throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		PageData resultMap = new PageData();
		List<PageData> varList = null;
		if(pd.get("accountperiod")==null || "".equals(pd.get("accountperiod").toString())) {
			pd.put("accountperiod", purchaseInvoiceService.getAccountperiod(pd));
		}
		resultPageData =  purchaseInvoiceService.listPurchaselistmx(pd);
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total",varList.size());
			resultMap.put("rows", varList);
		}
		return resultMap;
	}
	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		PageData pd=this.getPageData();
		mv.setViewName("system/invoicing/purchaseinvoice");
		String accountperiod = purchaseInvoiceService.getAccountperiod(pd);
		String[] pfs = accountperiod.split("-");
		mv.addObject("year", pfs[0]);
		mv.addObject("month", pfs[1]);
		return mv;
	}

	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "t_purchaseinvoice");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = purchaseInvoiceService.deleteAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 验证重复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkRepeatByParam")
	public boolean checkRepeatByParam() throws Exception {
		PageData pd = this.getPageData();
		if( purchaseInvoiceService.checkRepeatByParam(pd) ){
			return false;
		}
		return true;
	}
	
	/**
	 * 检查客户档案是否被引用
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value="/checkIsUsed")
	public Object checkSubAndChild() throws Exception {
		PageData pd = this.getPageData();
		List<PageData> list = purchaseInvoiceService.checkIsUsed(pd);
		if(list.size()>0){
			pd.put("state", "error");
		}else{
			pd.put("state", "success");
		}
		return pd;
	}
	
	/**
	 * 搜索 导出 查询 
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = purchaseInvoiceService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	/**
	 * 明细供应商增加
	 */
	@RequestMapping(value = "/addRowSupplier")
	@ResponseBody
	public Object rowSupplierAdd() throws Exception {
		PageData pd = this.getPageData();
		return purchaseInvoiceService.addRowSupplier(pd);
	}
	
	/**
	 * 导入
	 * @param file
	 * @param templet
	 * @return
	 */
	@RequestMapping(value="/importExcel")
	@ResponseBody
	public String importExcel(	@RequestParam(value="file_name") MultipartFile file, 
								@RequestParam(value="acc_time") String acc_time,
								@RequestParam(value="is_in_archives") String is_in_archives) {
		PageData pd = this.getPageData();
		try {
			pd.put("code", "purchaseinvoice");
			pd.put("acc_time", acc_time);
			if("1".equals(is_in_archives)){
				pd.put("is_in_archives", is_in_archives);
				pd.put("in_archives_info", "t_purchaseinvoice,tmp_supplier,supplier_id,c_supplier,name,id");
			}
			pd = purchaseInvoiceService.importExcel(pd,file);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		String str = "err!~!" + purchaseInvoiceService.string2Unicode("未知错误，请联系管理员！");
		if( (pd.get("state") != null && !"".equals(pd.get("state").toString())) 
				|| (pd.get("message") != null && !"".equals(pd.get("message").toString())) ){
			if("success".equals(pd.get("state").toString())){
				str = pd.get("state").toString();
			}else{
				str = pd.get("state").toString() + "!~!" + purchaseInvoiceService.string2Unicode(pd.get("message").toString());
			}
		}
		return str;
	}

	/**
	 * 生成凭证
	 * @return
	 */
	@RequestMapping(value="/voucherCreate")
	@ResponseBody
	public Object voucherCreate() {
		PageData pd = this.getPageData();
		try {
			pd = purchaseInvoiceService.voucherCreate(pd);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return pd;
	}
	
	/**
	 * 撤销凭证
	 * @return
	 */
	@RequestMapping(value="/voucherRevoke")
	@ResponseBody
	public Object voucherRevoke() {
		PageData pd = this.getPageData();
		try {
			pd = purchaseInvoiceService.voucherRevoke(pd);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return pd;
	}
	
}
