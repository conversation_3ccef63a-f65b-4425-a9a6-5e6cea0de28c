package org.newstanding.service.financialhandle;

import java.util.ArrayList;
import java.util.List;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("checkvouchersService")
public class CheckvouchersService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;

	
	
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CheckvouchersMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CheckvouchersMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findCheckvouchersmxByCheckvouchersid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CheckvouchersMapper.findCheckvouchersmxByCheckvouchersid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CheckvouchersMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	//验证凭证数据   凭证是否平衡、凭证是否满足基本条件（凭证明细至少有两行，行明细金额不可为0等）
	@SuppressWarnings("unchecked")
	public PageData checkVouchers(String[] arrayDATA_IDS,String database) throws Exception {
		PageData resPd = new PageData();
		PageData pd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			pd.put("database", database);
			for (String id : arrayDATA_IDS) {
				pd.put("id", id);
				//查找凭证明细数据
				list=(List<PageData>) dao.findForList("CheckvouchersMapper.findFillVouchersmxDataByFillvouchersid", pd);
				if(list.size()<2) {
					resPd.put("state","error");
					resPd.put("message","凭证号码: "+list.get(0).get("fillvouchers_code")+",明细行数少于2行，无法审核，请检查后重试！");
					return resPd;
				}
				double credit_money = 0 ;
				double debit_money = 0 ;
				for(PageData voucherPd : list) {
					if((voucherPd.get("creditmoney") == null || "".equals(voucherPd.get("creditmoney").toString()) || Double.parseDouble(voucherPd.get("creditmoney").toString()) == 0)
						&& (voucherPd.get("debitmoney") == null || "".equals(voucherPd.get("debitmoney").toString()) || Double.parseDouble(voucherPd.get("debitmoney").toString()) == 0)){
						resPd.put("state","error");
						resPd.put("message","凭证号码: "+list.get(0).get("fillvouchers_code")+",明细中存在借贷方同时为0，请检查后重试！");
						return resPd;
					}
					
					if(voucherPd.get("creditmoney") != null && !"".equals(voucherPd.get("creditmoney").toString())
							&& Double.parseDouble(voucherPd.get("creditmoney").toString()) != 0) {
						credit_money = CalculateUtil.add(credit_money, Double.parseDouble(voucherPd.get("creditmoney").toString()) );
					}else if(voucherPd.get("debitmoney") != null && !"".equals(voucherPd.get("debitmoney").toString())
							&& Double.parseDouble(voucherPd.get("debitmoney").toString()) != 0) {
						debit_money =CalculateUtil.add(debit_money, Double.parseDouble(voucherPd.get("debitmoney").toString()) );
					}
				}
				if(CalculateUtil.round(credit_money, 2) != CalculateUtil.round(debit_money, 2)) {
					resPd.put("state", "error");
					resPd.put("message", "凭证号码: "+list.get(0).get("fillvouchers_code")+"借贷方不平，请检查。");
					return resPd;
				}
			}
			
			resPd.put("state","success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
}
