package org.newstanding.common.entity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TreeJson implements Serializable {
    /**
     * 构建easyui的treegrid 的json 
     */
    private static final long serialVersionUID = 1L;
    //  提供json必要的几个必要的属性
    private String id ; 
    private String pid ; 
    private String text ; 
    private String iconCls ;
    private String state ; 
    private String checked ; 
    private Map<String,String> attributes;
    private List<TreeJson> children = new ArrayList<TreeJson>() ;
    
    
    private String code;
    private String name;
    private String model;
    private String unit;
    private String taxrate;
    private String type_name ; 
	public String getType_name() {
		return type_name;
	}

	public void setType_name(String type_name) {
		this.type_name = type_name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getTaxrate() {
		return taxrate;
	}

	public void setTaxrate(String taxrate) {
		this.taxrate = taxrate;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getPid() {
		return pid;
	}

	public void setPid(String pid) {
		this.pid = pid;
	}

	public String getText() {
		return text;
	}
 
	public void setText(String text) {
		this.text = text;
	}
 
	public String getIconCls() {
		return iconCls;
	}
 
	public void setIconCls(String iconCls) {
		this.iconCls = iconCls;
	}
 
	public String getState() {
		return state;
	}
 
	public void setState(String state) {
		this.state = state;
	}
 
	public String getChecked() {
		return checked;
	}
 
	public void setChecked(String checked) {
		this.checked = checked;
	}
 
	public List<TreeJson> getChildren() {
		return children;
	}
 
	public void setChildren(List<TreeJson> children) {
		this.children = children;
	}
 
    public Map<String, String> getAttributes() {
		return attributes;
	}
 
	public void setAttributes(Map<String, String> attributes) {
		this.attributes = attributes;
	}
 
	public static List<TreeJson> formatTree(List<TreeJson> list) {
        TreeJson root = new TreeJson();
        List<TreeJson> treelist = new ArrayList<TreeJson>();//拼凑好的json格式的数据     
        if (list != null && list.size() > 0) {
        	for(int i= 0; i < list.size(); i++){
                        //如果该节点没有父节点那么它就是根（root）节点
                      if("".equals(list.get(i).getPid())){
		            root = list.get(i) ;
                            //获取该根节点的子节点
                    getChildrenNodes(list,root);
		            treelist.add(root) ;
        		}
        	}
        }      
        return treelist ;
    }
    private static void getChildrenNodes(List<TreeJson> nodes, TreeJson root) {
    	for (TreeJson treeJson : nodes) {
                         //在根节点中下寻找它的子节点
    				if(treeJson.getPid().equals(root.getId())){//如果找到root的子结点
                                //在父节点下添加子节点 
							root.getChildren().add(treeJson);
                                //寻找子节点的子节点
							getChildrenNodes(nodes,treeJson);
			}else {
				treeJson.setState("open");
			}
		}
    }
}
