﻿/**
 * jQuery <PERSON>UI 1.3.5
 * 
 * Copyright (c) 2009-2013 www.jeasyui.com. All rights reserved.
 *
 * Licensed under the GPL or commercial licenses
 * To use it on other terms please contact us: <EMAIL>
 * http://www.gnu.org/licenses/gpl.txt
 * http://www.jeasyui.com/license_commercial.php
 *
 */
(function($){
function _1(_2){
var _3=$.data(_2,"datetimebox");
var _4=_3.options;
$(_2).datebox($.extend({},_4,{onShowPanel:function(){
var _5=$(_2).datetimebox("getValue");
_8(_2,_5,true);
_4.onShowPanel.call(_2);
},formatter:$.fn.datebox.defaults.formatter,parser:$.fn.datebox.defaults.parser}));
$(_2).removeClass("datebox-f").addClass("datetimebox-f");
$(_2).datebox("calendar").calendar({onSelect:function(_6){
_4.onSelect.call(_2,_6);
}});
var _7=$(_2).datebox("panel");
if(!_3.spinner){
var p=$("<div style=\"padding:2px\"><input style=\"width:80px\"></div>").insertAfter(_7.children("div.datebox-calendar-inner"));
_3.spinner=p.children("input");
}
_3.spinner.timespinner({showSeconds:_4.showSeconds,separator:_4.timeSeparator}).unbind(".datetimebox").bind("mousedown.datetimebox",function(e){
e.stopPropagation();
});
_8(_2,_4.value);
};
function _9(_a){
var c=$(_a).datetimebox("calendar");
var t=$(_a).datetimebox("spinner");
var _b=c.calendar("options").current;
return new Date(_b.getFullYear(),_b.getMonth(),_b.getDate(),t.timespinner("getHours"),t.timespinner("getMinutes"),t.timespinner("getSeconds"));
};
function _c(_d,q){
_8(_d,q,true);
};
function _e(_f){
var _10=$.data(_f,"datetimebox").options;
var _11=_9(_f);
_8(_f,_10.formatter.call(_f,_11));
$(_f).combo("hidePanel");
};
function _8(_12,_13,_14){
var _15=$.data(_12,"datetimebox").options;
$(_12).combo("setValue",_13);
if(!_14){
if(_13){
var _16=_15.parser.call(_12,_13);
$(_12).combo("setValue",_15.formatter.call(_12,_16));
$(_12).combo("setText",_15.formatter.call(_12,_16));
}else{
$(_12).combo("setText",_13);
}
}
var _16=_15.parser.call(_12,_13);
$(_12).datetimebox("calendar").calendar("moveTo",_16);
$(_12).datetimebox("spinner").timespinner("setValue",_17(_16));
function _17(_18){
function _19(_1a){
return (_1a<10?"0":"")+_1a;
};
var tt=[_19(_18.getHours()),_19(_18.getMinutes())];
if(_15.showSeconds){
tt.push(_19(_18.getSeconds()));
}
return tt.join($(_12).datetimebox("spinner").timespinner("options").separator);
};
};
$.fn.datetimebox=function(_1b,_1c){
if(typeof _1b=="string"){
var _1d=$.fn.datetimebox.methods[_1b];
if(_1d){
return _1d(this,_1c);
}else{
return this.datebox(_1b,_1c);
}
}
_1b=_1b||{};
return this.each(function(){
var _1e=$.data(this,"datetimebox");
if(_1e){
$.extend(_1e.options,_1b);
}else{
$.data(this,"datetimebox",{options:$.extend({},$.fn.datetimebox.defaults,$.fn.datetimebox.parseOptions(this),_1b)});
}
_1(this);
});
};
$.fn.datetimebox.methods={options:function(jq){
var _1f=jq.datebox("options");
return $.extend($.data(jq[0],"datetimebox").options,{originalValue:_1f.originalValue,disabled:_1f.disabled,readonly:_1f.readonly});
},spinner:function(jq){
return $.data(jq[0],"datetimebox").spinner;
},setValue:function(jq,_20){
return jq.each(function(){
_8(this,_20);
});
},reset:function(jq){
return jq.each(function(){
var _21=$(this).datetimebox("options");
$(this).datetimebox("setValue",_21.originalValue);
});
}};
$.fn.datetimebox.parseOptions=function(_22){
var t=$(_22);
return $.extend({},$.fn.datebox.parseOptions(_22),$.parser.parseOptions(_22,["timeSeparator",{showSeconds:"boolean"}]));
};
$.fn.datetimebox.defaults=$.extend({},$.fn.datebox.defaults,{showSeconds:true,timeSeparator:":",keyHandler:{up:function(e){
},down:function(e){
},left:function(e){
},right:function(e){
},enter:function(e){
_e(this);
},query:function(q,e){
_c(this,q);
}},buttons:[{text:function(_23){
return $(_23).datetimebox("options").currentText;
},handler:function(_24){
$(_24).datetimebox("calendar").calendar({year:new Date().getFullYear(),month:new Date().getMonth()+1,current:new Date()});
_e(_24);
}},{text:function(_25){
return $(_25).datetimebox("options").okText;
},handler:function(_26){
_e(_26);
}},{text:function(_27){
return $(_27).datetimebox("options").closeText;
},handler:function(_28){
$(this).closest("div.combo-panel").panel("close");
}}],formatter:function(_29){
var h=_29.getHours();
var M=_29.getMinutes();
var s=_29.getSeconds();
function _2a(_2b){
return (_2b<10?"0":"")+_2b;
};
var _2c=$(this).datetimebox("spinner").timespinner("options").separator;
var r=$.fn.datebox.defaults.formatter(_29)+" "+_2a(h)+_2c+_2a(M);
if($(this).datetimebox("options").showSeconds){
r+=_2c+_2a(s);
}
return r;
},parser:function(s){
if($.trim(s)==""){
return new Date();
}
var dt=s.split(" ");
var d=$.fn.datebox.defaults.parser(dt[0]);
if(dt.length<2){
return d;
}
var _2d=$(this).datetimebox("spinner").timespinner("options").separator;
var tt=dt[1].split(_2d);
var _2e=parseInt(tt[0],10)||0;
var _2f=parseInt(tt[1],10)||0;
var _30=parseInt(tt[2],10)||0;
return new Date(d.getFullYear(),d.getMonth(),d.getDate(),_2e,_2f,_30);
}});
})(jQuery);

