!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.jsPDF=e()}(this,function(){"use strict";var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},e=(function(){function t(t){this.value=t}function e(e){function n(i,o){try{var s=e[i](o),a=s.value;a instanceof t?Promise.resolve(a.value).then(function(t){n("next",t)},function(t){n("throw",t)}):r(s.done?"return":"normal",s.value)}catch(t){r("throw",t)}}function r(t,e){switch(t){case"return":i.resolve({value:e,done:!0});break;case"throw":i.reject(e);break;default:i.resolve({value:e,done:!1})}(i=i.next)?n(i.key,i.arg):o=null}var i,o;this._invoke=function(t,e){return new Promise(function(r,s){var a={key:t,arg:e,resolve:r,reject:s,next:null};o?o=o.next=a:(i=o=a,n(t,e))})},"function"!=typeof e.return&&(this.return=void 0)}"function"==typeof Symbol&&Symbol.asyncIterator&&(e.prototype[Symbol.asyncIterator]=function(){return this}),e.prototype.next=function(t){return this._invoke("next",t)},e.prototype.throw=function(t){return this._invoke("throw",t)},e.prototype.return=function(t){return this._invoke("return",t)}}(),function(e){function n(t){var n={};this.subscribe=function(t,e,r){if("function"!=typeof e)return!1;n.hasOwnProperty(t)||(n[t]={});var i=Math.random().toString(35);return n[t][i]=[e,!!r],i},this.unsubscribe=function(t){for(var e in n)if(n[e][t])return delete n[e][t],!0;return!1},this.publish=function(r){if(n.hasOwnProperty(r)){var i=Array.prototype.slice.call(arguments,1),o=[];for(var s in n[r])if(Object.prototype.hasOwnProperty.call(n[r],s)){var a=n[r][s];try{a[0].apply(t,i)}catch(t){e.console&&console.error("jsPDF PubSub Error",t.message,t)}a[1]&&o.push(s)}o.length&&o.forEach(this.unsubscribe)}}}function i(a,c,h,l){var u={};"object"===(void 0===a?"undefined":t(a))&&(a=(u=a).orientation,c=u.unit||c,h=u.format||h,l=u.compress||u.compressPdf||l),c=c||"mm",h=h||"a4",a=(""+(a||"P")).toLowerCase();(""+h).toLowerCase();var p,f,d,g,m,w,y,v,b,x=!!l&&"function"==typeof Uint8Array,S=u.textColor||"0 g",k=u.drawColor||"0 G",C=u.fontSize||16,I=u.charSpace||0,A=u.R2L||!1,T=u.lineHeight||1.15,_=u.lineWidth||.200025,q=2,O=!1,P=[],F={},E={},B=0,R=[],D=[],j=[],M=[],U=[],N=0,L=0,z=0,H={title:"",subject:"",author:"",keywords:"",creator:""},W={},V=new n(W),G=u.hotfixes||[],X=function(t){return t.toFixed(2)},Y=function(t){return t.toFixed(3)},J=function(t){return("0"+parseInt(t,10)).slice(-2)},Q=function(t){O?R[g].push(t):(z+=t.length+1,M.push(t))},K=function(){return q++,P[q]=z,Q(q+" 0 obj"),q},Z=function(t){Q("stream"),Q(t),Q("endstream")},$=function(){var t,n,r,o,s,a,c,h,l,u=[];for(c=e.adler32cs||i.adler32cs,x&&void 0===c&&(x=!1),t=1;t<=B;t++){if(u.push(K()),h=(m=j[t].width)*f,l=(w=j[t].height)*f,Q("<</Type /Page"),Q("/Parent 1 0 R"),Q("/Resources 2 0 R"),Q("/MediaBox [0 0 "+X(h)+" "+X(l)+"]"),V.publish("putPage",{pageNumber:t,page:R[t]}),Q("/Contents "+(q+1)+" 0 R"),Q(">>"),Q("endobj"),n=R[t].join("\n"),K(),x){for(r=[],o=n.length;o--;)r[o]=n.charCodeAt(o);a=c.from(n),(s=new Deflater(6)).append(new Uint8Array(r)),n=s.flush(),(r=new Uint8Array(n.length+6)).set(new Uint8Array([120,156])),r.set(n,2),r.set(new Uint8Array([255&a,a>>8&255,a>>16&255,a>>24&255]),n.length+2),n=String.fromCharCode.apply(null,r),Q("<</Length "+n.length+" /Filter [/FlateDecode]>>")}else Q("<</Length "+n.length+">>");Z(n),Q("endobj")}P[1]=z,Q("1 0 obj"),Q("<</Type /Pages");var p="/Kids [";for(o=0;o<B;o++)p+=u[o]+" 0 R ";Q(p+"]"),Q("/Count "+B),Q(">>"),Q("endobj"),V.publish("postPutPages")},tt=function(t){var e=0===Object.keys(t.metadata).length;if(t.id.slice(1)>=14&&!e){var n=t.metadata.embedTTF(t.encoding,K,Q);n?t.objectNumber=n:delete F[t.id]}else t.objectNumber=K(),Q("<</BaseFont/"+t.postScriptName+"/Type/Font"),"string"==typeof t.encoding&&Q("/Encoding/"+t.encoding),Q("/Subtype/Type1>>"),Q("endobj")},et=function(){for(var t in F)F.hasOwnProperty(t)&&tt(F[t])},nt=function(){V.publish("putXobjectDict")},rt=function(){Q("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),Q("/Font <<");for(var t in F)F.hasOwnProperty(t)&&Q("/"+t+" "+F[t].objectNumber+" 0 R");Q(">>"),Q("/XObject <<"),nt(),Q(">>")},it=function(){et(),V.publish("putResources"),P[2]=z,Q("2 0 obj"),Q("<<"),rt(),Q(">>"),Q("endobj"),V.publish("postPutResources")},ot=function(){V.publish("putAdditionalObjects");for(var t=0;t<U.length;t++){var e=U[t];P[e.objId]=z,Q(e.objId+" 0 obj"),Q(e.content),Q("endobj")}q+=U.length,V.publish("postPutAdditionalObjects")},st=function(t,e,n){E.hasOwnProperty(e)||(E[e]={}),E[e][n]=t},at=function(t,e,n,r){var i="F"+(Object.keys(F).length+1).toString(10),o=F[i]={id:i,postScriptName:t,fontName:e,fontStyle:n,encoding:r,metadata:{}};return st(i,e,n),V.publish("addFont",o),i},ct=function(t,e){var n,r,i,o,s,a,c,h,l;if(e=e||{},i=e.sourceEncoding||"Unicode",s=e.outputEncoding,(e.autoencode||s)&&F[p].metadata&&F[p].metadata[i]&&F[p].metadata[i].encoding&&(o=F[p].metadata[i].encoding,!s&&F[p].encoding&&(s=F[p].encoding),!s&&o.codePages&&(s=o.codePages[0]),"string"==typeof s&&(s=o[s]),s)){for(c=!1,a=[],n=0,r=t.length;n<r;n++)(h=s[t.charCodeAt(n)])?a.push(String.fromCharCode(h)):a.push(t[n]),a[n].charCodeAt(0)>>8&&(c=!0);t=a.join("")}for(n=t.length;void 0===c&&0!==n;)t.charCodeAt(n-1)>>8&&(c=!0),n--;if(!c)return t;for(a=e.noBOM?[]:[254,255],n=0,r=t.length;n<r;n++){if(h=t.charCodeAt(n),(l=h>>8)>>8)throw new Error("Character at position "+n+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");a.push(l),a.push(h-(l<<8))}return String.fromCharCode.apply(void 0,a)},ht=function(t,e){return ct(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},lt=function(){Q("/Producer (jsPDF "+i.version+")");for(var t in H)H.hasOwnProperty(t)&&H[t]&&Q("/"+t.substr(0,1).toUpperCase()+t.substr(1)+" ("+ht(H[t])+")");var e=new Date,n=e.getTimezoneOffset(),r=n<0?"+":"-",o=Math.floor(Math.abs(n/60)),s=Math.abs(n%60),a=[r,J(o),"'",J(s),"'"].join("");Q(["/CreationDate (D:",e.getFullYear(),J(e.getMonth()+1),J(e.getDate()),J(e.getHours()),J(e.getMinutes()),J(e.getSeconds()),a,")"].join(""))},ut=function(){switch(Q("/Type /Catalog"),Q("/Pages 1 0 R"),v||(v="fullwidth"),v){case"fullwidth":Q("/OpenAction [3 0 R /FitH null]");break;case"fullheight":Q("/OpenAction [3 0 R /FitV null]");break;case"fullpage":Q("/OpenAction [3 0 R /Fit]");break;case"original":Q("/OpenAction [3 0 R /XYZ null null 1]");break;default:var t=""+v;"%"===t.substr(t.length-1)&&(v=parseInt(v,10)/100),"number"==typeof v&&Q("/OpenAction [3 0 R /XYZ null null "+X(v)+"]")}switch(b||(b="continuous"),b){case"continuous":Q("/PageLayout /OneColumn");break;case"single":Q("/PageLayout /SinglePage");break;case"two":case"twoleft":Q("/PageLayout /TwoColumnLeft");break;case"tworight":Q("/PageLayout /TwoColumnRight")}y&&Q("/PageMode /"+y),V.publish("putCatalog")},pt=function(){Q("/Size "+(q+1)),Q("/Root "+q+" 0 R"),Q("/Info "+(q-1)+" 0 R")},ft=function(t,e){var n="string"==typeof e&&e.toLowerCase();if("string"==typeof t){var r=t.toLowerCase();s.hasOwnProperty(r)&&(t=s[r][0]/f,e=s[r][1]/f)}if(Array.isArray(t)&&(e=t[1],t=t[0]),n){switch(n.substr(0,1)){case"l":e>t&&(n="s");break;case"p":t>e&&(n="s")}"s"===n&&(d=t,t=e,e=d)}O=!0,R[++B]=[],j[B]={width:Number(t)||m,height:Number(e)||w},D[B]={},mt(B)},dt=function(){ft.apply(this,arguments),Q(X(_*f)+" w"),Q(k),0!==N&&Q(N+" J"),0!==L&&Q(L+" j"),V.publish("addPage",{pageNumber:B})},gt=function(t){t>0&&t<=B&&(R.splice(t,1),j.splice(t,1),g>--B&&(g=B),this.setPage(g))},mt=function(t){t>0&&t<=B&&(g=t,m=j[t].width,w=j[t].height)},wt=function(t,e){var n,r;switch(t=void 0!==t?t:F[p].fontName,e=void 0!==e?e:F[p].fontStyle,r=t,void 0!==t&&(t=t.toLowerCase()),t){case"sans-serif":case"verdana":case"arial":case"helvetica":t="helvetica";break;case"fixed":case"monospace":case"terminal":case"courier":t="courier";break;case"serif":case"cursive":case"fantasy":t="times";break;default:t=r}try{n=E[t][e]}catch(t){}return n||void 0===(n=E.times[e])&&(n=E.times.normal),n},yt=function(){O=!1,q=2,z=0,M=[],P=[],U=[],V.publish("buildDocument"),Q("%PDF-"+o),$(),ot(),it(),K(),Q("<<"),lt(),Q(">>"),Q("endobj"),K(),Q("<<"),ut(),Q(">>"),Q("endobj");var t,e=z,n="0000000000";for(Q("xref"),Q("0 "+(q+1)),Q(n+" 65535 f "),t=1;t<=q;t++){var r=P[t];Q("function"==typeof r?(n+P[t]()).slice(-10)+" 00000 n ":(n+P[t]).slice(-10)+" 00000 n ")}return Q("trailer"),Q("<<"),pt(),Q(">>"),Q("startxref"),Q(""+e),Q("%%EOF"),O=!0,M.join("\n")},vt=function(t){var e="S";return"F"===t?e="f":"FD"===t||"DF"===t?e="B":"f"!==t&&"f*"!==t&&"B"!==t&&"B*"!==t||(e=t),e},bt=function(){for(var t=yt(),e=t.length,n=new ArrayBuffer(e),r=new Uint8Array(n);e--;)r[e]=t.charCodeAt(e);return n},xt=function(){return new Blob([bt()],{type:"application/pdf"})},St=function(t){return t.foo=function(){try{return t.apply(this,arguments)}catch(t){var n=t.stack||"";~n.indexOf(" at ")&&(n=n.split(" at ")[1]);var r="Error in function "+n.split("\n")[0].split("<")[0]+": "+t.message;if(!e.console)throw new Error(r);e.console.error(r,t),e.alert&&alert(r)}},t.foo.bar=t,t.foo}(function(t,n){var i="dataur"===(""+t).substr(0,6)?"data:application/pdf;base64,"+btoa(yt()):0;switch(t){case void 0:return yt();case"save":if(navigator.getUserMedia&&(void 0===e.URL||void 0===e.URL.createObjectURL))return W.output("dataurlnewwindow");r(xt(),n),"function"==typeof r.unload&&e.setTimeout&&setTimeout(r.unload,911);break;case"arraybuffer":return bt();case"blob":return xt();case"bloburi":case"bloburl":return e.URL&&e.URL.createObjectURL(xt())||void 0;case"datauristring":case"dataurlstring":return i;case"dataurlnewwindow":var o=e.open(i);if(o||"undefined"==typeof safari)return o;case"datauri":case"dataurl":return e.document.location.href=i;default:throw new Error('Output type "'+t+'" is not supported.')}}),kt=function(t){return!0===Array.isArray(G)&&G.indexOf(t)>-1};switch(c){case"pt":f=1;break;case"mm":f=72/25.4000508;break;case"cm":f=72/2.54000508;break;case"in":f=72;break;case"px":f=!0===kt("px_scaling")?.75:96/72;break;case"pc":case"em":f=12;break;case"ex":f=6;break;default:throw"Invalid unit: "+c}W.internal={pdfEscape:ht,getStyle:vt,getFont:function(){return F[wt.apply(W,arguments)]},getFontSize:function(){return C},getCharSpace:function(){return I},getLineHeight:function(){return C*T},write:function(t){Q(1===arguments.length?t:Array.prototype.join.call(arguments," "))},getCoordinateString:function(t){return X(t*f)},getVerticalCoordinateString:function(t){return X((w-t)*f)},collections:{},newObject:K,newAdditionalObject:function(){var t=2*R.length+1,e={objId:t+=U.length,content:""};return U.push(e),e},newObjectDeferred:function(){return q++,P[q]=function(){return z},q},newObjectDeferredBegin:function(t){P[t]=z},putStream:Z,events:V,scaleFactor:f,pageSize:{get width(){return m},get height(){return w}},output:function(t,e){return St(t,e)},getNumberOfPages:function(){return R.length-1},pages:R,out:Q,f2:X,getPageInfo:function(t){return{objId:2*(t-1)+3,pageNumber:t,pageContext:D[t]}},getCurrentPageInfo:function(){return{objId:2*(g-1)+3,pageNumber:g,pageContext:D[g]}},getPDFVersion:function(){return o},hasHotfix:kt},W.addPage=function(){return dt.apply(this,arguments),this},W.setPage=function(){return mt.apply(this,arguments),this},W.insertPage=function(t){return this.addPage(),this.movePage(g,t),this},W.movePage=function(t,e){var n,r,i,o;if(t>e){for(n=R[t],r=j[t],i=D[t],o=t;o>e;o--)R[o]=R[o-1],j[o]=j[o-1],D[o]=D[o-1];R[e]=n,j[e]=r,D[e]=i,this.setPage(e)}else if(t<e){for(n=R[t],r=j[t],i=D[t],o=t;o<e;o++)R[o]=R[o+1],j[o]=j[o+1],D[o]=D[o+1];R[e]=n,j[e]=r,D[e]=i,this.setPage(e)}return this},W.deletePage=function(){return gt.apply(this,arguments),this},W.setDisplayMode=function(t,e,n){if(v=t,b=e,y=n,-1===[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(n))throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+n+'" is not recognized.');return this},W.text=function(e,n,r,i){function o(t){return t.toFixed(2)}var s,a="",c=arguments[3],h=arguments[4],l=arguments[5];if("number"==typeof e&&(s=r,r=n,n=e,e=s),"string"==typeof e&&(e=e.match(/[\n\r]/)?e.split(/\r\n|\r|\n/g):[e]),"object"===(void 0===c?"undefined":t(c))&&null!==c||("string"==typeof h&&(l=h,h=null),"string"==typeof c&&(l=c,c=null),"number"==typeof c&&(h=c,c=null),i={flags:c,angle:h,align:l}),h){h*=Math.PI/180;var u=Math.cos(h),d=Math.sin(h);[o(u),o(d),o(-1*d),o(u)]}I&&(a+=I+" Tc\n");var g=-1,m=i.renderingMode||i.stroke,y=this.internal.getCurrentPageInfo().pageContext.usedRenderingMode||-1;switch(m){case 0:case!1:case"fill":g=0;break;case 1:case!0:case"stroke":g=1;break;case 2:case"fillThenStroke":g=2;break;case 3:case"invisible":g=3;break;case 4:case"fillAndAddForClipping":g=4;break;case 5:case"strokeAndAddPathForClipping":g=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":g=6;break;case 7:case"addToPathForClipping":g=7}if(-1!==g?a+=g+" Tr\n":-1!==y&&(a+="0 Tr\n"),"[object Array]"!==Object.prototype.toString.call(e))throw new Error('Type of text must be string or Array. "'+e+'" is not recognized.');for(var v=e.concat(),b=[],x=v.length,k=F[p],_="MacRomanEncoding"===k.encoding,q=p.slice(1)>13;x--;)b.push(_?k.metadata.encode(k.metadata.subset,v.shift(),A):function(t){return t=t.split("\t").join(Array(i.TabLen||9).join(" ")),ht(t,c)}(v.shift()));l=l||i.align||"left";var O,P,E,B,R=C*T,D=q?{font:k.metadata,fontSize:C,charSpace:I}:{},j=e.map(function(t){return this.getStringUnitWidth(t,D)*C/f},this),M=Math.max.apply(Math,j),U=_?"<":"(",N=_?">":")";if(l){if("left"===l)P=n;else if("center"===l)O=n-M/2,n-=j[0]/2;else{if("right"!==l)throw new Error('Unrecognized alignment option, use "left", "center", "right".');O=n-M,n-=j[0]}for(e=b[0],B=1,x=b.length;B<x;B++)E=M-j[B],"center"===l&&(E/=2),e+=N+"Tj\n"+(O-P+E)+" -"+R+" Td "+U+b[B],P=O+E}return e=U+b.join(N+" Tj\nT* "+U)+N,Q("BT\n/"+p+" "+C+" Tf\n"+C*T+" TL\n"+S+"\n"+a+o(n*f)+" "+o((w-r)*f)+" Td\n"+e+" Tj\nET"),this},W.lstext=function(t,e,n,r){console.warn("jsPDF.lstext is deprecated");for(var i=0,o=t.length;i<o;i++,e+=r)this.text(t[i],e,n);return this},W.line=function(t,e,n,r){return this.lines([[n-t,r-e]],t,e)},W.clip=function(){Q("W"),Q("S")},W.clip_fixed=function(t){Q("evenodd"===t?"W*":"W"),Q("n")},W.lines=function(t,e,n,r,i,o){var s,a,c,h,l,u,p,g,m,y,v;for("number"==typeof t&&(d=n,n=e,e=t,t=d),r=r||[1,1],Q(Y(e*f)+" "+Y((w-n)*f)+" m "),s=r[0],a=r[1],h=t.length,y=e,v=n,c=0;c<h;c++)2===(l=t[c]).length?(y=l[0]*s+y,v=l[1]*a+v,Q(Y(y*f)+" "+Y((w-v)*f)+" l")):(u=l[0]*s+y,p=l[1]*a+v,g=l[2]*s+y,m=l[3]*a+v,y=l[4]*s+y,v=l[5]*a+v,Q(Y(u*f)+" "+Y((w-p)*f)+" "+Y(g*f)+" "+Y((w-m)*f)+" "+Y(y*f)+" "+Y((w-v)*f)+" c"));return o&&Q(" h"),null!==i&&Q(vt(i)),this},W.rect=function(t,e,n,r,i){vt(i);return Q([X(t*f),X((w-e)*f),X(n*f),X(-r*f),"re"].join(" ")),null!==i&&Q(vt(i)),this},W.triangle=function(t,e,n,r,i,o,s){return this.lines([[n-t,r-e],[i-n,o-r],[t-i,e-o]],t,e,[1,1],s,!0),this},W.roundedRect=function(t,e,n,r,i,o,s){var a=4/3*(Math.SQRT2-1);return this.lines([[n-2*i,0],[i*a,0,i,o-o*a,i,o],[0,r-2*o],[0,o*a,-i*a,o,-i,o],[2*i-n,0],[-i*a,0,-i,-o*a,-i,-o],[0,2*o-r],[0,-o*a,i*a,-o,i,-o]],t+i,e,[1,1],s),this},W.ellipse=function(t,e,n,r,i){var o=4/3*(Math.SQRT2-1)*n,s=4/3*(Math.SQRT2-1)*r;return Q([X((t+n)*f),X((w-e)*f),"m",X((t+n)*f),X((w-(e-s))*f),X((t+o)*f),X((w-(e-r))*f),X(t*f),X((w-(e-r))*f),"c"].join(" ")),Q([X((t-o)*f),X((w-(e-r))*f),X((t-n)*f),X((w-(e-s))*f),X((t-n)*f),X((w-e)*f),"c"].join(" ")),Q([X((t-n)*f),X((w-(e+s))*f),X((t-o)*f),X((w-(e+r))*f),X(t*f),X((w-(e+r))*f),"c"].join(" ")),Q([X((t+o)*f),X((w-(e+r))*f),X((t+n)*f),X((w-(e+s))*f),X((t+n)*f),X((w-e)*f),"c"].join(" ")),null!==i&&Q(vt(i)),this},W.circle=function(t,e,n,r){return this.ellipse(t,e,n,n,r)},W.setProperties=function(t){for(var e in H)H.hasOwnProperty(e)&&t[e]&&(H[e]=t[e]);return this},W.setFontSize=function(t){return C=t,this},W.setFont=function(t,e){return p=wt(t,e),this},W.setFontStyle=W.setFontType=function(t){return p=wt(void 0,t),this},W.getFontList=function(){var t,e,n,r={};for(t in E)if(E.hasOwnProperty(t)){r[t]=n=[];for(e in E[t])E[t].hasOwnProperty(e)&&n.push(e)}return r},W.addFont=function(t,e,n,r){at(t,e,n,r)},W.setLineWidth=function(t){return Q((t*f).toFixed(2)+" w"),this},W.setDrawColor=function(t,e,n,r){var i;return i=void 0===e||void 0===r&&t===e===n?"string"==typeof t?t+" G":X(t/255)+" G":void 0===r?"string"==typeof t?[t,e,n,"RG"].join(" "):[X(t/255),X(e/255),X(n/255),"RG"].join(" "):"string"==typeof t?[t,e,n,r,"K"].join(" "):[X(t),X(e),X(n),X(r),"K"].join(" "),Q(i),this},W.setFillColor=function(e,n,r,i){var o;return void 0===n||void 0===i&&e===n===r?o="string"==typeof e?e+" g":X(e/255)+" g":void 0===i||"object"===(void 0===i?"undefined":t(i))?(o="string"==typeof e?[e,n,r,"rg"].join(" "):[X(e/255),X(n/255),X(r/255),"rg"].join(" "),i&&0===i.a&&(o=["255","255","255","rg"].join(" "))):o="string"==typeof e?[e,n,r,i,"k"].join(" "):[X(e),X(n),X(r),X(i),"k"].join(" "),Q(o),this},W.setTextColor=function(t,e,n){if("string"==typeof t&&/^#[0-9A-Fa-f]{6}$/.test(t)){var r=parseInt(t.substr(1),16);t=r>>16&255,e=r>>8&255,n=255&r}return S=0===t&&0===e&&0===n||void 0===e?Y(t/255)+" g":[Y(t/255),Y(e/255),Y(n/255),"rg"].join(" "),this},W.setCharSpace=function(t){return I=t,this},W.setR2L=function(t){return A=t,this},W.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},W.setLineCap=function(t){var e=this.CapJoinStyles[t];if(void 0===e)throw new Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return N=e,Q(e+" J"),this},W.setLineJoin=function(t){var e=this.CapJoinStyles[t];if(void 0===e)throw new Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return L=e,Q(e+" j"),this},W.output=St,W.save=function(t){W.output("save",t)};for(var Ct in i.API)i.API.hasOwnProperty(Ct)&&("events"===Ct&&i.API.events.length?function(t,e){var n,r,i;for(i=e.length-1;-1!==i;i--)n=e[i][0],r=e[i][1],t.subscribe.apply(t,[n].concat("function"==typeof r?[r]:r))}(V,i.API.events):W[Ct]=i.API[Ct]);return function(){for(var t=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats",void 0,"StandardEncoding"]],e=0,n=t.length;e<n;e++){var r=at(t[e][0],t[e][1],t[e][2],t[e][3]),i=t[e][0].split("-");st(r,i[0],i[1]||"")}V.publish("addFonts",{fonts:F,dictionary:E})}(),p="F1",dt(h,a),V.publish("initialized"),W}var o="1.3",s={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};return i.API={events:[]},i.version="1.x-master","function"==typeof define&&define.amd?define("jsPDF",function(){return i}):"undefined"!=typeof module&&module.exports?module.exports=i:e.jsPDF=i,i}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||window));/**
 * jsPDF AcroForm Plugin
 * Copyright (c) 2016 Alexander Weidt, https://github.com/BiggA94
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
(window.AcroForm=function(t){var n=window.AcroForm;n.scale=function(t){return t*(r.internal.scaleFactor/1)},n.antiScale=function(t){return 1/r.internal.scaleFactor*t};var r={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null};e.API.acroformPlugin=r;var i=function(){for(var t in this.acroformPlugin.acroFormDictionaryRoot.Fields){var e=this.acroformPlugin.acroFormDictionaryRoot.Fields[t];e.hasAnnotation&&s.call(this,e)}},o=function(){if(this.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");this.acroformPlugin.acroFormDictionaryRoot=new n.AcroFormDictionary,this.acroformPlugin.internal=this.internal,this.acroformPlugin.acroFormDictionaryRoot._eventID=this.internal.events.subscribe("postPutResources",h),this.internal.events.subscribe("buildDocument",i),this.internal.events.subscribe("putCatalog",c),this.internal.events.subscribe("postPutPages",l)},s=function(t){var n={type:"reference",object:t};e.API.annotationPlugin.annotations[this.internal.getPageInfo(t.page).pageNumber].push(n)},a=function(t){this.acroformPlugin.printedOut&&(this.acroformPlugin.printedOut=!1,this.acroformPlugin.acroFormDictionaryRoot=null),this.acroformPlugin.acroFormDictionaryRoot||o.call(this),this.acroformPlugin.acroFormDictionaryRoot.Fields.push(t)},c=function(){void 0!==this.acroformPlugin.acroFormDictionaryRoot?this.internal.write("/AcroForm "+this.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R"):console.log("Root missing...")},h=function(){this.internal.events.unsubscribe(this.acroformPlugin.acroFormDictionaryRoot._eventID),delete this.acroformPlugin.acroFormDictionaryRoot._eventID,this.acroformPlugin.printedOut=!0},l=function(t){var e=!t;t||(this.internal.newObjectDeferredBegin(this.acroformPlugin.acroFormDictionaryRoot.objId),this.internal.out(this.acroformPlugin.acroFormDictionaryRoot.getString()));var t=t||this.acroformPlugin.acroFormDictionaryRoot.Kids;for(var r in t){var i=t[r],o=i.Rect;i.Rect&&(i.Rect=n.internal.calculateCoordinates.call(this,i.Rect)),this.internal.newObjectDeferredBegin(i.objId);var s="";if(s+=i.objId+" 0 obj\n",s+="<<\n"+i.getContent(),i.Rect=o,i.hasAppearanceStream&&!i.appearanceStreamContent){var a=n.internal.calculateAppearanceStream.call(this,i);s+="/AP << /N "+a+" >>\n",this.acroformPlugin.xForms.push(a)}if(i.appearanceStreamContent){s+="/AP << ";for(var c in i.appearanceStreamContent){var h=i.appearanceStreamContent[c];if(s+="/"+c+" ",s+="<< ",Object.keys(h).length>=1||Array.isArray(h))for(var r in h)"function"==typeof(l=h[r])&&(l=l.call(this,i)),s+="/"+r+" "+l+" ",this.acroformPlugin.xForms.indexOf(l)>=0||this.acroformPlugin.xForms.push(l);else{var l=h;"function"==typeof l&&(l=l.call(this,i)),s+="/"+r+" "+l+" \n",this.acroformPlugin.xForms.indexOf(l)>=0||this.acroformPlugin.xForms.push(l)}s+=" >>\n"}s+=">>\n"}s+=">>\nendobj\n",this.internal.out(s)}e&&u.call(this,this.acroformPlugin.xForms)},u=function(t){for(var e in t){var n=e,r=t[e];this.internal.newObjectDeferredBegin(r&&r.objId);var i="";i+=r?r.getString():"",this.internal.out(i),delete t[n]}};t.addField=function(t){return t instanceof n.TextField?f.call(this,t):t instanceof n.ChoiceField?d.call(this,t):t instanceof n.Button?p.call(this,t):t instanceof n.ChildClass?a.call(this,t):t&&a.call(this,t),t.page=this.acroformPlugin.internal.getCurrentPageInfo().pageNumber,this};var p=function(t){(t=t||new n.Field).FT="/Btn";var e=t.Ff||0;t.pushbutton&&(e=n.internal.setBitPosition(e,17),delete t.pushbutton),t.radio&&(e=n.internal.setBitPosition(e,16),delete t.radio),t.noToggleToOff&&(e=n.internal.setBitPosition(e,15)),t.Ff=e,a.call(this,t)},f=function(t){(t=t||new n.Field).FT="/Tx";var e=t.Ff||0;t.multiline&&(e|=4096),t.password&&(e|=8192),t.fileSelect&&(e|=1<<20),t.doNotSpellCheck&&(e|=1<<22),t.doNotScroll&&(e|=1<<23),t.Ff=t.Ff||e,a.call(this,t)},d=function(t){var e=t||new n.Field;e.FT="/Ch";var r=e.Ff||0;e.combo&&(r=n.internal.setBitPosition(r,18),delete e.combo),e.edit&&(r=n.internal.setBitPosition(r,19),delete e.edit),e.sort&&(r=n.internal.setBitPosition(r,20),delete e.sort),e.multiSelect&&this.internal.getPDFVersion()>=1.4&&(r=n.internal.setBitPosition(r,22),delete e.multiSelect),e.doNotSpellCheck&&this.internal.getPDFVersion()>=1.4&&(r=n.internal.setBitPosition(r,23),delete e.doNotSpellCheck),e.Ff=r,a.call(this,e)}})(e.API);var n=window.AcroForm;n.internal={},n.createFormXObject=function(t){var e=new n.FormXObject,r=n.Appearance.internal.getHeight(t)||0,i=n.Appearance.internal.getWidth(t)||0;return e.BBox=[0,0,i,r],e},n.Appearance={CheckBox:{createAppearanceStream:function(){return{N:{On:n.Appearance.CheckBox.YesNormal},D:{On:n.Appearance.CheckBox.YesPushDown,Off:n.Appearance.CheckBox.OffPushDown}}},createMK:function(){return"<< /CA (3)>>"},YesPushDown:function(t){var e=n.createFormXObject(t),r="";t.Q=1;var i=n.internal.calculateX(t,"3","ZapfDingbats",50);return r+="0.749023 g\n             0 0 "+n.Appearance.internal.getWidth(t)+" "+n.Appearance.internal.getHeight(t)+" re\n             f\n             BMC\n             q\n             0 0 1 rg\n             /F13 "+i.fontSize+" Tf 0 g\n             BT\n",r+=i.text,r+="ET\n             Q\n             EMC\n",e.stream=r,e},YesNormal:function(t){var e=n.createFormXObject(t),r="";t.Q=1;var i=n.internal.calculateX(t,"3","ZapfDingbats",.9*n.Appearance.internal.getHeight(t));return r+="1 g\n0 0 "+n.Appearance.internal.getWidth(t)+" "+n.Appearance.internal.getHeight(t)+" re\nf\nq\n0 0 1 rg\n0 0 "+(n.Appearance.internal.getWidth(t)-1)+" "+(n.Appearance.internal.getHeight(t)-1)+" re\nW\nn\n0 g\nBT\n/F13 "+i.fontSize+" Tf 0 g\n",r+=i.text,r+="ET\n             Q\n",e.stream=r,e},OffPushDown:function(t){var e=n.createFormXObject(t),r="";return r+="0.749023 g\n            0 0 "+n.Appearance.internal.getWidth(t)+" "+n.Appearance.internal.getHeight(t)+" re\n            f\n",e.stream=r,e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:n.Appearance.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=n.Appearance.RadioButton.Circle.YesNormal,e.D[t]=n.Appearance.RadioButton.Circle.YesPushDown,e},createMK:function(){return"<< /CA (l)>>"},YesNormal:function(t){var e=n.createFormXObject(t),r="",i=n.Appearance.internal.getWidth(t)<=n.Appearance.internal.getHeight(t)?n.Appearance.internal.getWidth(t)/4:n.Appearance.internal.getHeight(t)/4;i*=.9;var o=n.Appearance.internal.Bezier_C;return r+="q\n1 0 0 1 "+n.Appearance.internal.getWidth(t)/2+" "+n.Appearance.internal.getHeight(t)/2+" cm\n"+i+" 0 m\n"+i+" "+i*o+" "+i*o+" "+i+" 0 "+i+" c\n-"+i*o+" "+i+" -"+i+" "+i*o+" -"+i+" 0 c\n-"+i+" -"+i*o+" -"+i*o+" -"+i+" 0 -"+i+" c\n"+i*o+" -"+i+" "+i+" -"+i*o+" "+i+" 0 c\nf\nQ\n",e.stream=r,e},YesPushDown:function(t){var e=n.createFormXObject(t),r="",i=n.Appearance.internal.getWidth(t)<=n.Appearance.internal.getHeight(t)?n.Appearance.internal.getWidth(t)/4:n.Appearance.internal.getHeight(t)/4,o=2*(i*=.9),s=o*n.Appearance.internal.Bezier_C,a=i*n.Appearance.internal.Bezier_C;return r+="0.749023 g\n            q\n           1 0 0 1 "+n.Appearance.internal.getWidth(t)/2+" "+n.Appearance.internal.getHeight(t)/2+" cm\n"+o+" 0 m\n"+o+" "+s+" "+s+" "+o+" 0 "+o+" c\n-"+s+" "+o+" -"+o+" "+s+" -"+o+" 0 c\n-"+o+" -"+s+" -"+s+" -"+o+" 0 -"+o+" c\n"+s+" -"+o+" "+o+" -"+s+" "+o+" 0 c\n            f\n            Q\n            0 g\n            q\n            1 0 0 1 "+n.Appearance.internal.getWidth(t)/2+" "+n.Appearance.internal.getHeight(t)/2+" cm\n"+i+" 0 m\n"+i+" "+a+" "+a+" "+i+" 0 "+i+" c\n-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c\n-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c\n"+a+" -"+i+" "+i+" -"+a+" "+i+" 0 c\n            f\n            Q\n",e.stream=r,e},OffPushDown:function(t){var e=n.createFormXObject(t),r="",i=n.Appearance.internal.getWidth(t)<=n.Appearance.internal.getHeight(t)?n.Appearance.internal.getWidth(t)/4:n.Appearance.internal.getHeight(t)/4,o=2*(i*=.9),s=o*n.Appearance.internal.Bezier_C;return r+="0.749023 g\n            q\n 1 0 0 1 "+n.Appearance.internal.getWidth(t)/2+" "+n.Appearance.internal.getHeight(t)/2+" cm\n"+o+" 0 m\n"+o+" "+s+" "+s+" "+o+" 0 "+o+" c\n-"+s+" "+o+" -"+o+" "+s+" -"+o+" 0 c\n-"+o+" -"+s+" -"+s+" -"+o+" 0 -"+o+" c\n"+s+" -"+o+" "+o+" -"+s+" "+o+" 0 c\n            f\n            Q\n",e.stream=r,e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:n.Appearance.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=n.Appearance.RadioButton.Cross.YesNormal,e.D[t]=n.Appearance.RadioButton.Cross.YesPushDown,e},createMK:function(){return"<< /CA (8)>>"},YesNormal:function(t){var e=n.createFormXObject(t),r="",i=n.Appearance.internal.calculateCross(t);return r+="q\n            1 1 "+(n.Appearance.internal.getWidth(t)-2)+" "+(n.Appearance.internal.getHeight(t)-2)+" re\n            W\n            n\n            "+i.x1.x+" "+i.x1.y+" m\n            "+i.x2.x+" "+i.x2.y+" l\n            "+i.x4.x+" "+i.x4.y+" m\n            "+i.x3.x+" "+i.x3.y+" l\n            s\n            Q\n",e.stream=r,e},YesPushDown:function(t){var e=n.createFormXObject(t),r=n.Appearance.internal.calculateCross(t),i="";return i+="0.749023 g\n            0 0 "+n.Appearance.internal.getWidth(t)+" "+n.Appearance.internal.getHeight(t)+" re\n            f\n            q\n            1 1 "+(n.Appearance.internal.getWidth(t)-2)+" "+(n.Appearance.internal.getHeight(t)-2)+" re\n            W\n            n\n            "+r.x1.x+" "+r.x1.y+" m\n            "+r.x2.x+" "+r.x2.y+" l\n            "+r.x4.x+" "+r.x4.y+" m\n            "+r.x3.x+" "+r.x3.y+" l\n            s\n            Q\n",e.stream=i,e},OffPushDown:function(t){var e=n.createFormXObject(t),r="";return r+="0.749023 g\n            0 0 "+n.Appearance.internal.getWidth(t)+" "+n.Appearance.internal.getHeight(t)+" re\n            f\n",e.stream=r,e}}},createDefaultAppearanceStream:function(t){var e="";return e+="/Helv 0 Tf 0 g"}},n.Appearance.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=n.Appearance.internal.getWidth(t),r=n.Appearance.internal.getHeight(t),i=function(t,e){return t>e?e:t}(e,r);return{x1:{x:(e-i)/2,y:(r-i)/2+i},x2:{x:(e-i)/2+i,y:(r-i)/2},x3:{x:(e-i)/2,y:(r-i)/2},x4:{x:(e-i)/2+i,y:(r-i)/2+i}}}},n.Appearance.internal.getWidth=function(t){return t.Rect[2]},n.Appearance.internal.getHeight=function(t){return t.Rect[3]},n.internal.inherit=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},n.internal.arrayToPdfArray=function(t){if(Array.isArray(t)){var e=" [";for(var n in t)e+=t[n].toString(),e+=n<t.length-1?" ":"";return e+="]"}},n.internal.toPdfString=function(t){return 0!==(t=t||"").indexOf("(")&&(t="("+t),")"!=t.substring(t.length-1)&&(t+="("),t},n.PDFObject=function(){var t;Object.defineProperty(this,"objId",{get:function(){return t||(this.internal?t=this.internal.newObjectDeferred():e.API.acroformPlugin.internal&&(t=e.API.acroformPlugin.internal.newObjectDeferred())),t||console.log("Couldn't create Object ID"),t},configurable:!1})},n.PDFObject.prototype.toString=function(){return this.objId+" 0 R"},n.PDFObject.prototype.getString=function(){var t=this.objId+" 0 obj\n<<";return t+=this.getContent()+">>\n",this.stream&&(t+="stream\n",t+=this.stream,t+="endstream\n"),t+="endobj\n"},n.PDFObject.prototype.getContent=function(){var t="";return t+=function(t){var e="",r=Object.keys(t).filter(function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"_"!=t.substring(0,1)});for(var i in r){var o=r[i],s=t[o];s&&(Array.isArray(s)?e+="/"+o+" "+n.internal.arrayToPdfArray(s)+"\n":s instanceof n.PDFObject?e+="/"+o+" "+s.objId+" 0 R\n":e+="/"+o+" "+s+"\n")}return e}(this)},n.FormXObject=function(){n.PDFObject.call(this),this.Type="/XObject",this.Subtype="/Form",this.FormType=1,this.BBox,this.Matrix,this.Resources="2 0 R",this.PieceInfo;var t;Object.defineProperty(this,"Length",{enumerable:!0,get:function(){return void 0!==t?t.length:0}}),Object.defineProperty(this,"stream",{enumerable:!1,set:function(e){t=e},get:function(){return t||null}})},n.internal.inherit(n.FormXObject,n.PDFObject),n.AcroFormDictionary=function(){n.PDFObject.call(this);var t=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return t.length>0?t:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!0,configurable:!0,get:function(){return t}}),this.DA},n.internal.inherit(n.AcroFormDictionary,n.PDFObject),n.Field=function(){n.PDFObject.call(this);var t;Object.defineProperty(this,"Rect",{enumerable:!0,configurable:!1,get:function(){if(t)return t},set:function(e){t=e}});var e="";Object.defineProperty(this,"FT",{enumerable:!0,set:function(t){e=t},get:function(){return e}});var r;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,set:function(t){r=t},get:function(){if(!r||r.length<1){if(this instanceof n.ChildClass)return;return"(FieldObject"+n.Field.FieldNum+++")"}return"("==r.substring(0,1)&&r.substring(r.length-1)?r:"("+r+")"}});var i;Object.defineProperty(this,"DA",{enumerable:!0,get:function(){if(i)return"("+i+")"},set:function(t){i=t}});var o;Object.defineProperty(this,"DV",{enumerable:!0,configurable:!0,get:function(){if(o)return o},set:function(t){o=t}}),Object.defineProperty(this,"Type",{enumerable:!0,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,get:function(){return this.hasAnnotation?"/Widget":null}}),this.BG,Object.defineProperty(this,"hasAnnotation",{enumerable:!1,get:function(){return!!(this.Rect||this.BC||this.BG)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!1,configurable:!0,writable:!0}),Object.defineProperty(this,"page",{enumerable:!1,configurable:!0,writable:!0})},n.Field.FieldNum=0,n.internal.inherit(n.Field,n.PDFObject),n.ChoiceField=function(){n.Field.call(this),this.FT="/Ch",this.Opt=[],this.V="()",this.TI=0,this.combo=!1,Object.defineProperty(this,"edit",{enumerable:!0,set:function(t){1==t?(this._edit=!0,this.combo=!0):this._edit=!1},get:function(){return!!this._edit&&this._edit},configurable:!1}),this.hasAppearanceStream=!0,Object.defineProperty(this,"V",{get:function(){n.internal.toPdfString()}})},n.internal.inherit(n.ChoiceField,n.Field),window.ChoiceField=n.ChoiceField,n.ListBox=function(){n.ChoiceField.call(this)},n.internal.inherit(n.ListBox,n.ChoiceField),window.ListBox=n.ListBox,n.ComboBox=function(){n.ListBox.call(this),this.combo=!0},n.internal.inherit(n.ComboBox,n.ListBox),window.ComboBox=n.ComboBox,n.EditBox=function(){n.ComboBox.call(this),this.edit=!0},n.internal.inherit(n.EditBox,n.ComboBox),window.EditBox=n.EditBox,n.Button=function(){n.Field.call(this),this.FT="/Btn"},n.internal.inherit(n.Button,n.Field),window.Button=n.Button,n.PushButton=function(){n.Button.call(this),this.pushbutton=!0},n.internal.inherit(n.PushButton,n.Button),window.PushButton=n.PushButton,n.RadioButton=function(){n.Button.call(this),this.radio=!0;var t=[];Object.defineProperty(this,"Kids",{enumerable:!0,get:function(){if(t.length>0)return t}}),Object.defineProperty(this,"__Kids",{get:function(){return t}});var e;Object.defineProperty(this,"noToggleToOff",{enumerable:!1,get:function(){return e},set:function(t){e=t}})},n.internal.inherit(n.RadioButton,n.Button),window.RadioButton=n.RadioButton,n.ChildClass=function(t,e){n.Field.call(this),this.Parent=t,this._AppearanceType=n.Appearance.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(e),this.F=n.internal.setBitPosition(this.F,3,1),this.MK=this._AppearanceType.createMK(),this.AS="/Off",this._Name=e},n.internal.inherit(n.ChildClass,n.Field),n.RadioButton.prototype.setAppearance=function(t){if("createAppearanceStream"in t&&"createMK"in t)for(var e in this.__Kids){var n=this.__Kids[e];n.appearanceStreamContent=t.createAppearanceStream(n._Name),n.MK=t.createMK()}else console.log("Couldn't assign Appearance to RadioButton. Appearance was Invalid!")},n.RadioButton.prototype.createOption=function(t){var r=this,i=new n.ChildClass(r,t);return this.__Kids.push(i),e.API.addField(i),i},n.CheckBox=function(){Button.call(this),this.appearanceStreamContent=n.Appearance.CheckBox.createAppearanceStream(),this.MK=n.Appearance.CheckBox.createMK(),this.AS="/On",this.V="/On"},n.internal.inherit(n.CheckBox,n.Button),window.CheckBox=n.CheckBox,n.TextField=function(){n.Field.call(this),this.DA=n.Appearance.createDefaultAppearanceStream(),this.F=4;var t;Object.defineProperty(this,"V",{get:function(){return t?"("+t+")":t},enumerable:!0,set:function(e){t=e}});var e;Object.defineProperty(this,"DV",{get:function(){return e?"("+e+")":e},enumerable:!0,set:function(t){e=t}});var r=!1;Object.defineProperty(this,"multiline",{enumerable:!1,get:function(){return r},set:function(t){r=t}});var i=!1;Object.defineProperty(this,"MaxLen",{enumerable:!0,get:function(){return i},set:function(t){i=t}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!1,get:function(){return this.V||this.DV}})},n.internal.inherit(n.TextField,n.Field),window.TextField=n.TextField,n.PasswordField=function(){TextField.call(this),Object.defineProperty(this,"password",{value:!0,enumerable:!1,configurable:!1,writable:!1})},n.internal.inherit(n.PasswordField,n.TextField),window.PasswordField=n.PasswordField,n.internal.calculateFontSpace=function(t,e,r){var r=r||"helvetica",i=n.internal.calculateFontSpace.canvas||(n.internal.calculateFontSpace.canvas=document.createElement("canvas"));(a=i.getContext("2d")).save();var o=e+" "+r;a.font=o;var s=a.measureText(t);a.fontcolor="black";var a=i.getContext("2d");return s.height=1.5*a.measureText("3").width,a.restore(),s},n.internal.calculateX=function(t,e,r,i){var i=i||12,r=r||"helvetica",o={text:"",fontSize:""},s=(e=")"==(e="("==e.substr(0,1)?e.substr(1):e).substr(e.length-1)?e.substr(0,e.length-1):e).split(" "),a=i,c=n.Appearance.internal.getHeight(t)||0;c=c<0?-c:c;var h=n.Appearance.internal.getWidth(t)||0;h=h<0?-h:h;a++;t:for(;;){var e="";a--;var l=n.internal.calculateFontSpace("3",a+"px",r).height,u=t.multiline?c-a:(c-l)/2,p=-2,f=u+=2,d=0,g=0,m=0;if(0==a){a=12,e="(...) Tj\n",e+="% Width of Text: "+n.internal.calculateFontSpace(e,"1px").width+", FieldWidth:"+h+"\n";break}m=n.internal.calculateFontSpace(s[0]+" ",a+"px",r).width;var w="",y=0;for(var v in s){w=" "==(w+=s[v]+" ").substr(w.length-1)?w.substr(0,w.length-1):w;var b=parseInt(v);m=n.internal.calculateFontSpace(w+" ",a+"px",r).width;var x=function(t,e,i){if(t+1<s.length){var o=e+" "+s[t+1];return n.internal.calculateFontSpace(o,i+"px",r).width<=h-4}return!1}(b,w,a),S=v>=s.length-1;if(!x||S){if(x||S){if(S)g=b;else if(t.multiline&&(l+2)*(y+2)+2>c)continue t}else{if(!t.multiline)continue t;if((l+2)*(y+2)+2>c)continue t;g=b}for(var k="",C=d;C<=g;C++)k+=s[C]+" ";switch(k=" "==k.substr(k.length-1)?k.substr(0,k.length-1):k,m=n.internal.calculateFontSpace(k,a+"px",r).width,t.Q){case 2:p=h-m-2;break;case 1:p=(h-m)/2;break;case 0:default:p=2}e+=p+" "+f+" Td\n",e+="("+k+") Tj\n",e+=-p+" 0 Td\n",f=-(a+2),m=0,d=g+1,y++,w=""}else w+=" "}break}return o.text=e,o.fontSize=a,o},n.internal.calculateAppearanceStream=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e="",r=t.V||t.DV,i=n.internal.calculateX(t,r);e+="/Tx BMC\nq\n/F1 "+i.fontSize+" Tf\n1 0 0 1 0 0 Tm\n",e+="BT\n",e+=i.text,e+="ET\n",e+="Q\nEMC\n";var o=new n.createFormXObject(t);return o.stream=e,o}},n.internal.calculateCoordinates=function(t,e,r,i){var o={};if(this.internal){var s=function(t){return t*this.internal.scaleFactor};Array.isArray(t)?(t[0]=n.scale(t[0]),t[1]=n.scale(t[1]),t[2]=n.scale(t[2]),t[3]=n.scale(t[3]),o.lowerLeft_X=t[0]||0,o.lowerLeft_Y=s.call(this,this.internal.pageSize.height)-t[3]-t[1]||0,o.upperRight_X=t[0]+t[2]||0,o.upperRight_Y=s.call(this,this.internal.pageSize.height)-t[1]||0):(t=n.scale(t),e=n.scale(e),r=n.scale(r),i=n.scale(i),o.lowerLeft_X=t||0,o.lowerLeft_Y=this.internal.pageSize.height-e||0,o.upperRight_X=t+r||0,o.upperRight_Y=this.internal.pageSize.height-e+i||0)}else Array.isArray(t)?(o.lowerLeft_X=t[0]||0,o.lowerLeft_Y=t[1]||0,o.upperRight_X=t[0]+t[2]||0,o.upperRight_Y=t[1]+t[3]||0):(o.lowerLeft_X=t||0,o.lowerLeft_Y=e||0,o.upperRight_X=t+r||0,o.upperRight_Y=e+i||0);return[o.lowerLeft_X,o.lowerLeft_Y,o.upperRight_X,o.upperRight_Y]},n.internal.calculateColor=function(t,e,n){var r=new Array(3);return r.r=0|t,r.g=0|e,r.b=0|n,r},n.internal.getBitPosition=function(t,e){var n=1;return n<<=e-1,(t=t||0)|n},n.internal.setBitPosition=function(t,e,n){t=t||0;var r=1;if(r<<=e-1,1==(n=n||1))t=t|r;else var t=t&~r;return t},e.API.addHTML=function(t,e,n,r,i){if("undefined"==typeof html2canvas&&"undefined"==typeof rasterizeHTML)throw new Error("You need either https://github.com/niklasvh/html2canvas or https://github.com/cburgmer/rasterizeHTML.js");"number"!=typeof e&&(r=e,i=n),"function"==typeof r&&(i=r,r=null);var o=this.internal,s=o.scaleFactor,a=o.pageSize.width,c=o.pageSize.height;if(r=r||{},r.onrendered=function(t){e=parseInt(e)||0,n=parseInt(n)||0;var o=r.dim||{},h=o.h||0,l=o.w||Math.min(a,t.width/s)-e,u="JPEG";if(r.format&&(u=r.format),t.height>c&&r.pagesplit){var p=function(){for(var r=0;;){var o=document.createElement("canvas");o.width=Math.min(a*s,t.width),o.height=Math.min(c*s,t.height-r),o.getContext("2d").drawImage(t,0,r,t.width,o.height,0,0,o.width,o.height);var h=[o,e,r?0:n,o.width/s,o.height/s,u,null,"SLOW"];if(this.addImage.apply(this,h),(r+=o.height)>=t.height)break;this.addPage()}i(l,r,null,h)}.bind(this);if("CANVAS"===t.nodeName){var f=new Image;f.onload=p,f.src=t.toDataURL("image/png"),t=f}else p()}else{var d=Math.random().toString(35),g=[t,e,n,l,h,u,d,"SLOW"];this.addImage.apply(this,g),i(l,h,d,g)}}.bind(this),"undefined"!=typeof html2canvas&&!r.rstz)return html2canvas(t,r);if("undefined"!=typeof rasterizeHTML){var h="drawDocument";return"string"==typeof t&&(h=/^http/.test(t)?"drawURL":"drawHTML"),r.width=r.width||a*s,rasterizeHTML[h](t,void 0,r).then(function(t){r.onrendered(t.image)},function(t){i(null,t)})}return null},/** @preserve
 * jsPDF addImage plugin
 * Copyright (c) 2012 Jason Siefken, https://github.com/siefkenj/
 *               2013 Chris Dowling, https://github.com/gingerchris
 *               2013 Trinh Ho, https://github.com/ineedfat
 *               2013 Edwin Alejandro Perez, https://github.com/eaparango
 *               2013 Norah Smith, https://github.com/burnburnrocket
 *               2014 Diego Casorran, https://github.com/diegocr
 *               2014 James Robb, https://github.com/jamesbrobb
 *
 * 
 */
function(e){var n=["jpeg","jpg","png"],r=function t(e){var n=this.internal.newObject(),r=this.internal.write,i=this.internal.putStream;if(e.n=n,r("<</Type /XObject"),r("/Subtype /Image"),r("/Width "+e.w),r("/Height "+e.h),e.cs===this.color_spaces.INDEXED?r("/ColorSpace [/Indexed /DeviceRGB "+(e.pal.length/3-1)+" "+("smask"in e?n+2:n+1)+" 0 R]"):(r("/ColorSpace /"+e.cs),e.cs===this.color_spaces.DEVICE_CMYK&&r("/Decode [1 0 1 0 1 0 1 0]")),r("/BitsPerComponent "+e.bpc),"f"in e&&r("/Filter /"+e.f),"dp"in e&&r("/DecodeParms <<"+e.dp+">>"),"trns"in e&&e.trns.constructor==Array){for(var o="",s=0,a=e.trns.length;s<a;s++)o+=e.trns[s]+" "+e.trns[s]+" ";r("/Mask ["+o+"]")}if("smask"in e&&r("/SMask "+(n+1)+" 0 R"),r("/Length "+e.data.length+">>"),i(e.data),r("endobj"),"smask"in e){var c="/Predictor "+e.p+" /Colors 1 /BitsPerComponent "+e.bpc+" /Columns "+e.w,h={w:e.w,h:e.h,cs:"DeviceGray",bpc:e.bpc,dp:c,data:e.smask};"f"in e&&(h.f=e.f),t.call(this,h)}e.cs===this.color_spaces.INDEXED&&(this.internal.newObject(),r("<< /Length "+e.pal.length+">>"),i(this.arrayBufferToBinaryString(new Uint8Array(e.pal))),r("endobj"))},i=function(){var t=this.internal.collections.addImage_images;for(var e in t)r.call(this,t[e])},o=function(){var t,e=this.internal.collections.addImage_images,n=this.internal.write;for(var r in e)n("/I"+(t=e[r]).i,t.n,"0","R")},s=function(t){return t&&"string"==typeof t&&(t=t.toUpperCase()),t in e.image_compression?t:e.image_compression.NONE},a=function(){var t=this.internal.collections.addImage_images;return t||(this.internal.collections.addImage_images=t={},this.internal.events.subscribe("putResources",i),this.internal.events.subscribe("putXobjectDict",o)),t},c=function(t){var e=0;return t&&(e=Object.keys?Object.keys(t).length:function(t){var e=0;for(var n in t)t.hasOwnProperty(n)&&e++;return e}(t)),e},h=function(t){return void 0===t||null===t},l=function(t){return"string"==typeof t&&e.sHashCode(t)},u=function(t){return-1===n.indexOf(t)},p=function(t){return"function"!=typeof e["process"+t.toUpperCase()]},f=function(e){return"object"===(void 0===e?"undefined":t(e))&&1===e.nodeType},d=function(e,n,r){if("IMG"===e.nodeName&&e.hasAttribute("src")){var i=""+e.getAttribute("src");if(!r&&0===i.indexOf("data:image/"))return i;!n&&/\.png(?:[?#].*)?$/i.test(i)&&(n="png")}if("CANVAS"===e.nodeName)var o=e;else{(o=document.createElement("canvas")).width=e.clientWidth||e.width,o.height=e.clientHeight||e.height;var s=o.getContext("2d");if(!s)throw"addImage requires canvas to be supported by browser.";if(r){var a,c,h,l,u,p,f,d,g=Math.PI/180;"object"===(void 0===r?"undefined":t(r))&&(a=r.x,c=r.y,h=r.bg,r=r.angle),d=r*g,l=Math.abs(Math.cos(d)),u=Math.abs(Math.sin(d)),p=o.width,f=o.height,o.width=f*u+p*l,o.height=f*l+p*u,isNaN(a)&&(a=o.width/2),isNaN(c)&&(c=o.height/2),s.clearRect(0,0,o.width,o.height),s.fillStyle=h||"white",s.fillRect(0,0,o.width,o.height),s.save(),s.translate(a,c),s.rotate(d),s.drawImage(e,-p/2,-f/2),s.rotate(-d),s.translate(-a,-c),s.restore()}else s.drawImage(e,0,0,o.width,o.height)}return o.toDataURL("png"==(""+n).toLowerCase()?"image/png":"image/jpeg")},g=function(t,e){var n;if(e)for(var r in e)if(t===e[r].alias){n=e[r];break}return n},m=function(t,e,n){return t||e||(t=-96,e=-96),t<0&&(t=-1*n.w*72/t/this.internal.scaleFactor),e<0&&(e=-1*n.h*72/e/this.internal.scaleFactor),0===t&&(t=e*n.w/n.h),0===e&&(e=t*n.h/n.w),[t,e]},w=function(t,e,n,r,i,o,s){var a=m.call(this,n,r,i),c=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString;n=a[0],r=a[1],s[o]=i,this.internal.write("q",c(n),"0 0",c(r),c(t),h(e+r),"cm /I"+i.i,"Do Q")};e.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"},e.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"},e.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},e.sHashCode=function(t){return Array.prototype.reduce&&t.split("").reduce(function(t,e){return(t=(t<<5)-t+e.charCodeAt(0))&t},0)},e.isString=function(t){return"string"==typeof t},e.extractInfoFromBase64DataURI=function(t){return/^data:([\w]+?\/([\w]+?));base64,(.+?)$/g.exec(t)},e.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array},e.isArrayBuffer=function(t){return!!this.supportsArrayBuffer()&&t instanceof ArrayBuffer},e.isArrayBufferView=function(t){return!!this.supportsArrayBuffer()&&("undefined"!=typeof Uint32Array&&(t instanceof Int8Array||t instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array))},e.binaryStringToUint8Array=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n},e.arrayBufferToBinaryString=function(t){if("function"==typeof window.atob)return atob(this.arrayBufferToBase64(t));for(var e=this.isArrayBuffer(t)?t:new Uint8Array(t),n="",r=Math.round(e.byteLength/20480),i=0;i<r;i++)n+=String.fromCharCode.apply(null,e.slice(20480*i,20480*i+20480));return n},e.arrayBufferToBase64=function(t){for(var e,n,r,i,o="",s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=new Uint8Array(t),c=a.byteLength,h=c%3,l=c-h,u=0;u<l;u+=3)e=(258048&(i=a[u]<<16|a[u+1]<<8|a[u+2]))>>12,n=(4032&i)>>6,r=63&i,o+=s[(16515072&i)>>18]+s[e]+s[n]+s[r];return 1==h?(e=(3&(i=a[l]))<<4,o+=s[(252&i)>>2]+s[e]+"=="):2==h&&(e=(1008&(i=a[l]<<8|a[l+1]))>>4,n=(15&i)<<2,o+=s[(64512&i)>>10]+s[e]+s[n]+"="),o},e.createImageInfo=function(t,e,n,r,i,o,s,a,c,h,l,u,p){var f={alias:a,w:e,h:n,cs:r,bpc:i,i:s,data:t};return o&&(f.f=o),c&&(f.dp=c),h&&(f.trns=h),l&&(f.pal=l),u&&(f.smask=u),p&&(f.p=p),f},e.addImage=function(e,r,i,o,m,y,v,b,x){if("string"!=typeof r){var S=y;y=m,m=o,o=i,i=r,r=S}if("object"===(void 0===e?"undefined":t(e))&&!f(e)&&"imageData"in e){var k=e;e=k.imageData,r=k.format||r,i=k.x||i||0,o=k.y||o||0,m=k.w||m,y=k.h||y,v=k.alias||v,b=k.compression||b,x=k.rotation||k.angle||x}if(isNaN(i)||isNaN(o))throw console.error("jsPDF.addImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addImage");var C,I=a.call(this);if(!(C=g(e,I))){var A;if(f(e)&&(e=d(e,r,x)),h(v)&&(v=l(e)),!(C=g(v,I))){if(this.isString(e)){var T=this.extractInfoFromBase64DataURI(e);T?(r=T[2],e=atob(T[3])):137===e.charCodeAt(0)&&80===e.charCodeAt(1)&&78===e.charCodeAt(2)&&71===e.charCodeAt(3)&&(r="png")}if(r=(r||"JPEG").toLowerCase(),u(r))throw new Error("addImage currently only supports formats "+n+", not '"+r+"'");if(p(r))throw new Error("please ensure that the plugin for '"+r+"' support is added");if(this.supportsArrayBuffer()&&(e instanceof Uint8Array||(A=e,e=this.binaryStringToUint8Array(e))),!(C=this["process"+r.toUpperCase()](e,c(I),v,s(b),A)))throw new Error("An unkwown error occurred whilst processing the image")}}return w.call(this,i,o,m,y,C,C.i,I),this};var y=function(t){var e,n,r;if(255===!t.charCodeAt(0)||216===!t.charCodeAt(1)||255===!t.charCodeAt(2)||224===!t.charCodeAt(3)||!t.charCodeAt(6)==="J".charCodeAt(0)||!t.charCodeAt(7)==="F".charCodeAt(0)||!t.charCodeAt(8)==="I".charCodeAt(0)||!t.charCodeAt(9)==="F".charCodeAt(0)||0===!t.charCodeAt(10))throw new Error("getJpegSize requires a binary string jpeg file");for(var i=256*t.charCodeAt(4)+t.charCodeAt(5),o=4,s=t.length;o<s;){if(o+=i,255!==t.charCodeAt(o))throw new Error("getJpegSize could not find the size of the image");if(192===t.charCodeAt(o+1)||193===t.charCodeAt(o+1)||194===t.charCodeAt(o+1)||195===t.charCodeAt(o+1)||196===t.charCodeAt(o+1)||197===t.charCodeAt(o+1)||198===t.charCodeAt(o+1)||199===t.charCodeAt(o+1))return n=256*t.charCodeAt(o+5)+t.charCodeAt(o+6),e=256*t.charCodeAt(o+7)+t.charCodeAt(o+8),r=t.charCodeAt(o+9),[e,n,r];o+=2,i=256*t.charCodeAt(o)+t.charCodeAt(o+1)}},v=function(t){if(65496!==(t[0]<<8|t[1]))throw new Error("Supplied data is not a JPEG");for(var e,n,r,i,o=t.length,s=(t[4]<<8)+t[5],a=4;a<o;){if(a+=s,e=b(t,a),s=(e[2]<<8)+e[3],(192===e[1]||194===e[1])&&255===e[0]&&s>7)return e=b(t,a+5),n=(e[2]<<8)+e[3],r=(e[0]<<8)+e[1],i=e[4],{width:n,height:r,numcomponents:i};a+=2}throw new Error("getJpegSizeFromBytes could not find the size of the image")},b=function(t,e){return t.subarray(e,e+5)};e.processJPEG=function(t,e,n,r,i){var o,s=this.color_spaces.DEVICE_RGB,a=this.decode.DCT_DECODE;return this.isString(t)?(o=y(t),this.createImageInfo(t,o[0],o[1],1==o[3]?this.color_spaces.DEVICE_GRAY:s,8,a,e,n)):(this.isArrayBuffer(t)&&(t=new Uint8Array(t)),this.isArrayBufferView(t)?(o=v(t),t=i||this.arrayBufferToBinaryString(t),this.createImageInfo(t,o.width,o.height,1==o.numcomponents?this.color_spaces.DEVICE_GRAY:s,8,a,e,n)):null)},e.processJPG=function(){return this.processJPEG.apply(this,arguments)}}(e.API),/**
 * jsPDF Annotations PlugIn
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var n={annotations:[],f2:function(t){return t.toFixed(2)},notEmpty:function(t){if(void 0!==t&&""!=t)return!0}};e.API.annotationPlugin=n,e.API.events.push(["addPage",function(t){this.annotationPlugin.annotations[t.pageNumber]=[]}]),t.events.push(["putPage",function(t){for(var e=this.annotationPlugin.annotations[t.pageNumber],r=!1,i=0;i<e.length&&!r;i++)switch((h=e[i]).type){case"link":if(n.notEmpty(h.options.url)||n.notEmpty(h.options.pageNumber)){r=!0;break}case"reference":case"text":case"freetext":r=!0}if(0!=r){this.internal.write("/Annots [");for(var o=this.annotationPlugin.f2,s=this.internal.scaleFactor,a=this.internal.pageSize.height,c=this.internal.getPageInfo(t.pageNumber),i=0;i<e.length;i++){var h=e[i];switch(h.type){case"reference":this.internal.write(" "+h.object.objId+" 0 R ");break;case"text":var l=this.internal.newAdditionalObject(),u=this.internal.newAdditionalObject(),p=h.title||"Note";w="<</Type /Annot /Subtype /Text "+(d="/Rect ["+o(h.bounds.x*s)+" "+o(a-(h.bounds.y+h.bounds.h)*s)+" "+o((h.bounds.x+h.bounds.w)*s)+" "+o((a-h.bounds.y)*s)+"] ")+"/Contents ("+h.contents+")",w+=" /Popup "+u.objId+" 0 R",w+=" /P "+c.objId+" 0 R",w+=" /T ("+p+") >>",l.content=w;var f=l.objId+" 0 R";w="<</Type /Annot /Subtype /Popup "+(d="/Rect ["+o((h.bounds.x+30)*s)+" "+o(a-(h.bounds.y+h.bounds.h)*s)+" "+o((h.bounds.x+h.bounds.w+30)*s)+" "+o((a-h.bounds.y)*s)+"] ")+" /Parent "+f,h.open&&(w+=" /Open true"),w+=" >>",u.content=w,this.internal.write(l.objId,"0 R",u.objId,"0 R");break;case"freetext":var d="/Rect ["+o(h.bounds.x*s)+" "+o((a-h.bounds.y)*s)+" "+o(h.bounds.x+h.bounds.w*s)+" "+o(a-(h.bounds.y+h.bounds.h)*s)+"] ",g=h.color||"#000000";w="<</Type /Annot /Subtype /FreeText "+d+"/Contents ("+h.contents+")",w+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+g+")",w+=" /Border [0 0 0]",w+=" >>",this.internal.write(w);break;case"link":if(h.options.name){var m=this.annotations._nameMap[h.options.name];h.options.pageNumber=m.page,h.options.top=m.y}else h.options.top||(h.options.top=0);var d="/Rect ["+o(h.x*s)+" "+o((a-h.y)*s)+" "+o((h.x+h.w)*s)+" "+o((a-(h.y+h.h))*s)+"] ",w="";if(h.options.url)w="<</Type /Annot /Subtype /Link "+d+"/Border [0 0 0] /A <</S /URI /URI ("+h.options.url+") >>";else if(h.options.pageNumber)switch(w="<</Type /Annot /Subtype /Link "+d+"/Border [0 0 0] /Dest ["+(t=this.internal.getPageInfo(h.options.pageNumber)).objId+" 0 R",h.options.magFactor=h.options.magFactor||"XYZ",h.options.magFactor){case"Fit":w+=" /Fit]";break;case"FitH":w+=" /FitH "+h.options.top+"]";break;case"FitV":h.options.left=h.options.left||0,w+=" /FitV "+h.options.left+"]";break;case"XYZ":default:var y=o((a-h.options.top)*s);h.options.left=h.options.left||0,void 0===h.options.zoom&&(h.options.zoom=0),w+=" /XYZ "+h.options.left+" "+y+" "+h.options.zoom+"]"}""!=w&&(w+=" >>",this.internal.write(w))}}this.internal.write("]")}}]),t.createAnnotation=function(t){switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":this.annotationPlugin.annotations[this.internal.getCurrentPageInfo().pageNumber].push(t)}},t.link=function(t,e,n,r,i){this.annotationPlugin.annotations[this.internal.getCurrentPageInfo().pageNumber].push({x:t,y:e,w:n,h:r,options:i,type:"link"})},t.textWithLink=function(t,e,n,r){var i=this.getTextWidth(t),o=this.internal.getLineHeight()/this.internal.scaleFactor;return this.text(t,e,n),n+=.2*o,this.link(e,n-o,i,o,r),i},t.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor},t.getLineHeight=function(){return this.internal.getLineHeight()}}(e.API),e.API.autoPrint=function(){var t;return this.internal.events.subscribe("postPutResources",function(){t=this.internal.newObject(),this.internal.write("<< /S/Named /Type/Action /N/Print >>","endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.write("/OpenAction "+t+" 0 R")}),this},/**
 * jsPDF Canvas PlugIn
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.events.push(["initialized",function(){this.canvas.pdf=this}]),t.canvas={getContext:function(t){return this.pdf.context2d._canvas=this,this.pdf.context2d},style:{}},Object.defineProperty(t.canvas,"width",{get:function(){return this._width},set:function(t){this._width=t,this.getContext("2d").pageWrapX=t+1}}),Object.defineProperty(t.canvas,"height",{get:function(){return this._height},set:function(t){this._height=t,this.getContext("2d").pageWrapY=t+1}})}(e.API),/** ====================================================================
 * jsPDF Cell plugin
 * Copyright (c) 2013 Youssef Beddad, <EMAIL>
 *               2013 Eduardo Menezes de Morais, <EMAIL>
 *               2013 Lee Driscoll, https://github.com/lsdriscoll
 *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria
 *               2014 James Hall, <EMAIL>
 *               2014 Diego Casorran, https://github.com/diegocr
 *
 * 
 * ====================================================================
 */
function(t){var e,n,r,i,o={x:void 0,y:void 0,w:void 0,h:void 0,ln:void 0},s=1,a=function(t,e,n,r,i){o={x:t,y:e,w:n,h:r,ln:i}},c=function(){return o},h={left:0,top:0,bottom:0};t.setHeaderFunction=function(t){i=t},t.getTextDimensions=function(t){e=this.internal.getFont().fontName,n=this.table_font_size||this.internal.getFontSize(),r=this.internal.getFont().fontStyle;var i,o;(o=document.createElement("font")).id="jsPDFCell";try{o.style.fontStyle=r}catch(t){o.style.fontWeight=r}o.style.fontName=e,o.style.fontSize=n+"pt";try{o.textContent=t}catch(e){o.innerText=t}return document.body.appendChild(o),i={w:(o.offsetWidth+1)*(19.049976/25.4),h:(o.offsetHeight+1)*(19.049976/25.4)},document.body.removeChild(o),i},t.cellAddPage=function(){var t=this.margins||h;this.addPage(),a(t.left,t.top,void 0,void 0),s+=1},t.cellInitialize=function(){o={x:void 0,y:void 0,w:void 0,h:void 0,ln:void 0},s=1},t.cell=function(t,e,n,r,i,o,s){var l=c(),u=!1;if(void 0!==l.ln)if(l.ln===o)t=l.x+l.w,e=l.y;else{var p=this.margins||h;l.y+l.h+r+13>=this.internal.pageSize.height-p.bottom&&(this.cellAddPage(),u=!0,this.printHeaders&&this.tableHeaderRow&&this.printHeaderRow(o,!0)),e=c().y+c().h,u&&(e=23)}if(void 0!==i[0])if(this.printingHeaderRow?this.rect(t,e,n,r,"FD"):this.rect(t,e,n,r),"right"===s){i instanceof Array||(i=[i]);for(var f=0;f<i.length;f++){var d=i[f],g=this.getStringUnitWidth(d)*this.internal.getFontSize();this.text(d,t+n-g-3,e+this.internal.getLineHeight()*(f+1))}}else this.text(i,t+3,e+this.internal.getLineHeight());return a(t,e,n,r,o),this},t.arrayMax=function(t,e){var n,r,i,o=t[0];for(n=0,r=t.length;n<r;n+=1)i=t[n],e?-1===e(o,i)&&(o=i):i>o&&(o=i);return o},t.table=function(e,n,r,i,a){if(!r)throw"No data for PDF table";var c,l,u,p,f,d,g,m,w,y,v=[],b=[],x={},S={},k=[],C=[],I=!1,A=!0,T=12,_=h;if(_.width=this.internal.pageSize.width,a&&(!0===a.autoSize&&(I=!0),!1===a.printHeaders&&(A=!1),a.fontSize&&(T=a.fontSize),a.css&&void 0!==a.css["font-size"]&&(T=16*a.css["font-size"]),a.margins&&(_=a.margins)),this.lnMod=0,o={x:void 0,y:void 0,w:void 0,h:void 0,ln:void 0},s=1,this.printHeaders=A,this.margins=_,this.setFontSize(T),this.table_font_size=T,void 0===i||null===i)v=Object.keys(r[0]);else if(i[0]&&"string"!=typeof i[0]){for(l=0,u=i.length;l<u;l+=1)c=i[l],v.push(c.name),b.push(c.prompt),S[c.name]=c.width*(19.049976/25.4)}else v=i;if(I)for(y=function(t){return t[c]},l=0,u=v.length;l<u;l+=1){for(x[c=v[l]]=r.map(y),k.push(this.getTextDimensions(b[l]||c).w),g=0,p=(d=x[c]).length;g<p;g+=1)f=d[g],k.push(this.getTextDimensions(f).w);S[c]=t.arrayMax(k),k=[]}if(A){q=this.calculateLineHeight(v,S,b.length?b:v);for(l=0,u=v.length;l<u;l+=1)c=v[l],C.push([e,n,S[c],q,String(b.length?b[l]:c)]);this.setTableHeaderRow(C),this.printHeaderRow(1,!1)}for(l=0,u=r.length;l<u;l+=1){var q;for(m=r[l],q=this.calculateLineHeight(v,S,m),g=0,w=v.length;g<w;g+=1)c=v[g],this.cell(e,n,S[c],q,m[c],l+2,c.align)}return this.lastCellPos=o,this.table_x=e,this.table_y=n,this},t.calculateLineHeight=function(t,e,n){for(var r,i=0,o=0;o<t.length;o++){n[r=t[o]]=this.splitTextToSize(String(n[r]),e[r]-3);var s=this.internal.getLineHeight()*n[r].length+3;s>i&&(i=s)}return i},t.setTableHeaderRow=function(t){this.tableHeaderRow=t},t.printHeaderRow=function(t,e){if(!this.tableHeaderRow)throw"Property tableHeaderRow does not exist.";var n,r,o,c;if(this.printingHeaderRow=!0,void 0!==i){var h=i(this,s);a(h[0],h[1],h[2],h[3],-1)}this.setFontStyle("bold");var l=[];for(o=0,c=this.tableHeaderRow.length;o<c;o+=1)this.setFillColor(200,200,200),n=this.tableHeaderRow[o],e&&(this.margins.top=13,n[1]=this.margins&&this.margins.top||0,l.push(n)),r=[].concat(n),this.cell.apply(this,r.concat(t));l.length>0&&this.setTableHeaderRow(l),this.setFontStyle("normal"),this.printingHeaderRow=!1}}(e.API),/**
 * jsPDF Context2D PlugIn Copyright (c) 2014 Steven Spungin (TwelveTone LLC) <EMAIL>
 *
 * Licensed under the MIT License. http://opensource.org/licenses/mit-license
 */
function(t){function e(){this._isStrokeTransparent=!1,this._strokeOpacity=1,this.strokeStyle="#000000",this.fillStyle="#000000",this._isFillTransparent=!1,this._fillOpacity=1,this.font="12pt times",this.textBaseline="alphabetic",this.textAlign="start",this.lineWidth=1,this.lineJoin="miter",this.lineCap="butt",this._transform=[1,0,0,1,0,0],this.globalCompositeOperation="normal",this.globalAlpha=1,this._clip_path=[],this.ignoreClearRect=!1,this.copy=function(t){this._isStrokeTransparent=t._isStrokeTransparent,this._strokeOpacity=t._strokeOpacity,this.strokeStyle=t.strokeStyle,this._isFillTransparent=t._isFillTransparent,this._fillOpacity=t._fillOpacity,this.fillStyle=t.fillStyle,this.font=t.font,this.lineWidth=t.lineWidth,this.lineJoin=t.lineJoin,this.lineCap=t.lineCap,this.textBaseline=t.textBaseline,this.textAlign=t.textAlign,this._fontSize=t._fontSize,this._transform=t._transform.slice(0),this.globalCompositeOperation=t.globalCompositeOperation,this.globalAlpha=t.globalAlpha,this._clip_path=t._clip_path.slice(0),this.ignoreClearRect=t.ignoreClearRect}}t.events.push(["initialized",function(){this.context2d.pdf=this,this.context2d.internal.pdf=this,this.context2d.ctx=new e,this.context2d.ctxStack=[],this.context2d.path=[]}]),t.context2d={pageWrapXEnabled:!1,pageWrapYEnabled:!1,pageWrapX:9999999,pageWrapY:9999999,ctx:new e,f2:function(t){return t.toFixed(2)},fillRect:function(t,e,n,r){if(!this._isFillTransparent()){t=this._wrapX(t),e=this._wrapY(e);var i=this._matrix_map_rect(this.ctx._transform,{x:t,y:e,w:n,h:r});this.pdf.rect(i.x,i.y,i.w,i.h,"f")}},strokeRect:function(t,e,n,r){if(!this._isStrokeTransparent()){t=this._wrapX(t),e=this._wrapY(e);var i=this._matrix_map_rect(this.ctx._transform,{x:t,y:e,w:n,h:r});this.pdf.rect(i.x,i.y,i.w,i.h,"s")}},clearRect:function(t,e,n,r){if(!this.ctx.ignoreClearRect){t=this._wrapX(t),e=this._wrapY(e);var i=this._matrix_map_rect(this.ctx._transform,{x:t,y:e,w:n,h:r});this.save(),this.setFillStyle("#ffffff"),this.pdf.rect(i.x,i.y,i.w,i.h,"f"),this.restore()}},save:function(){this.ctx._fontSize=this.pdf.internal.getFontSize();var t=new e;t.copy(this.ctx),this.ctxStack.push(this.ctx),this.ctx=t},restore:function(){this.ctx=this.ctxStack.pop(),this.setFillStyle(this.ctx.fillStyle),this.setStrokeStyle(this.ctx.strokeStyle),this.setFont(this.ctx.font),this.pdf.setFontSize(this.ctx._fontSize),this.setLineCap(this.ctx.lineCap),this.setLineWidth(this.ctx.lineWidth),this.setLineJoin(this.ctx.lineJoin)},rect:function(t,e,n,r){this.moveTo(t,e),this.lineTo(t+n,e),this.lineTo(t+n,e+r),this.lineTo(t,e+r),this.lineTo(t,e),this.closePath()},beginPath:function(){this.path=[]},closePath:function(){this.path.push({type:"close"})},_getRGBA:function(t){var e,n,r,i;if(!t)return{r:0,g:0,b:0,a:0,style:t};if(this.internal.rxTransparent.test(t))e=0,n=0,r=0,i=0;else{var o=this.internal.rxRgb.exec(t);null!=o?(e=parseInt(o[1]),n=parseInt(o[2]),r=parseInt(o[3]),i=1):null!=(o=this.internal.rxRgba.exec(t))?(e=parseInt(o[1]),n=parseInt(o[2]),r=parseInt(o[3]),i=parseFloat(o[4])):(i=1,"#"!=t.charAt(0)&&((t=CssColors.colorNameToHex(t))||(t="#000000")),4===t.length?(e=t.substring(1,2),e+=e,n=t.substring(2,3),n+=n,r=t.substring(3,4),r+=r):(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7)),e=parseInt(e,16),n=parseInt(n,16),r=parseInt(r,16))}return{r:e,g:n,b:r,a:i,style:t}},setFillStyle:function(t){var e=this._getRGBA(t);this.ctx.fillStyle=t,this.ctx._isFillTransparent=0===e.a,this.ctx._fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})},setStrokeStyle:function(t){var e=this._getRGBA(t);this.ctx.strokeStyle=e.style,this.ctx._isStrokeTransparent=0===e.a,this.ctx._strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))},fillText:function(t,e,n,r){if(!this._isFillTransparent()){e=this._wrapX(e),n=this._wrapY(n);var i=this._matrix_map_point(this.ctx._transform,[e,n]);e=i[0],n=i[1];var o=57.2958*this._matrix_rotation(this.ctx._transform);if(this.ctx._clip_path.length>0){var s;(s=window.outIntercept?"group"===window.outIntercept.type?window.outIntercept.stream:window.outIntercept:this.internal.getCurrentPage()).push("q");var a=this.path;this.path=this.ctx._clip_path,this.ctx._clip_path=[],this._fill(null,!0),this.ctx._clip_path=this.path,this.path=a}var c=1;try{c=this._matrix_decompose(this._getTransform()).scale[0]}catch(t){console.warn(t)}if(c<.01)this.pdf.text(t,e,this._getBaseline(n),null,o);else{var h=this.pdf.internal.getFontSize();this.pdf.setFontSize(h*c),this.pdf.text(t,e,this._getBaseline(n),null,o),this.pdf.setFontSize(h)}this.ctx._clip_path.length>0&&s.push("Q")}},strokeText:function(t,e,n,r){if(!this._isStrokeTransparent()){e=this._wrapX(e),n=this._wrapY(n);var i=this._matrix_map_point(this.ctx._transform,[e,n]);e=i[0],n=i[1];var o=57.2958*this._matrix_rotation(this.ctx._transform);if(this.ctx._clip_path.length>0){var s;(s=window.outIntercept?"group"===window.outIntercept.type?window.outIntercept.stream:window.outIntercept:this.internal.getCurrentPage()).push("q");var a=this.path;this.path=this.ctx._clip_path,this.ctx._clip_path=[],this._fill(null,!0),this.ctx._clip_path=this.path,this.path=a}var c=1;try{c=this._matrix_decompose(this._getTransform()).scale[0]}catch(t){console.warn(t)}if(1===c)this.pdf.text(t,e,this._getBaseline(n),{stroke:!0},o);else{var h=this.pdf.internal.getFontSize();this.pdf.setFontSize(h*c),this.pdf.text(t,e,this._getBaseline(n),{stroke:!0},o),this.pdf.setFontSize(h)}this.ctx._clip_path.length>0&&s.push("Q")}},setFont:function(t){this.ctx.font=t;var e=/\s*(\w+)\s+(\w+)\s+(\w+)\s+([\d\.]+)(px|pt|em)\s+(.*)?/;if(null!=(l=e.exec(t))){var n=l[1],r=l[3],i=l[4],o=l[5],s=l[6];i="px"===o?Math.floor(parseFloat(i)):"em"===o?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(i)),this.pdf.setFontSize(i),"bold"===r||"700"===r?this.pdf.setFontStyle("bold"):"italic"===n?this.pdf.setFontStyle("italic"):this.pdf.setFontStyle("normal");var a,c=(p=s).toLowerCase().split(/\s*,\s*/);a=-1!=c.indexOf("arial")?"Arial":-1!=c.indexOf("verdana")?"Verdana":-1!=c.indexOf("helvetica")?"Helvetica":-1!=c.indexOf("sans-serif")?"sans-serif":-1!=c.indexOf("fixed")?"Fixed":-1!=c.indexOf("monospace")?"Monospace":-1!=c.indexOf("terminal")?"Terminal":-1!=c.indexOf("courier")?"Courier":-1!=c.indexOf("times")?"Times":-1!=c.indexOf("cursive")?"Cursive":-1!=c.indexOf("fantasy")?"Fantasy":(c.indexOf("serif"),"Serif");var h;h="bold"===r?"bold":"normal",this.pdf.setFont(a,h)}else{var l=(e=/\s*(\d+)(pt|px|em)\s+([\w "]+)\s*([\w "]+)?/).exec(t);if(null!=l){var u=l[1],p=l[3];(h=l[4])||(h="normal"),u="em"===o?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(u)),this.pdf.setFontSize(u),this.pdf.setFont(p,h)}}},setTextBaseline:function(t){this.ctx.textBaseline=t},getTextBaseline:function(){return this.ctx.textBaseline},setTextAlign:function(t){this.ctx.textAlign=t},getTextAlign:function(){return this.ctx.textAlign},setLineWidth:function(t){this.ctx.lineWidth=t,this.pdf.setLineWidth(t)},setLineCap:function(t){this.ctx.lineCap=t,this.pdf.setLineCap(t)},setLineJoin:function(t){this.ctx.lineJoin=t,this.pdf.setLineJoin(t)},moveTo:function(t,e){t=this._wrapX(t),e=this._wrapY(e);var n=this._matrix_map_point(this.ctx._transform,[t,e]),r={type:"mt",x:t=n[0],y:e=n[1]};this.path.push(r)},_wrapX:function(t){return this.pageWrapXEnabled?t%this.pageWrapX:t},_wrapY:function(t){return this.pageWrapYEnabled?(this._gotoPage(this._page(t)),(t-this.lastBreak)%this.pageWrapY):t},transform:function(t,e,n,r,i,o){this.ctx._transform=[t,e,n,r,i,o]},setTransform:function(t,e,n,r,i,o){this.ctx._transform=[t,e,n,r,i,o]},_getTransform:function(){return this.ctx._transform},lastBreak:0,pageBreaks:[],_page:function(t){if(this.pageWrapYEnabled){this.lastBreak=0;for(var e=0,n=0,r=0;r<this.pageBreaks.length;r++)if(t>=this.pageBreaks[r]){e++,0===this.lastBreak&&n++;var i=this.pageBreaks[r]-this.lastBreak;this.lastBreak=this.pageBreaks[r],n+=o=Math.floor(i/this.pageWrapY)}if(0===this.lastBreak){var o=Math.floor(t/this.pageWrapY)+1;n+=o}return n+e}return this.pdf.internal.getCurrentPageInfo().pageNumber},_gotoPage:function(t){},lineTo:function(t,e){t=this._wrapX(t),e=this._wrapY(e);var n=this._matrix_map_point(this.ctx._transform,[t,e]),r={type:"lt",x:t=n[0],y:e=n[1]};this.path.push(r)},bezierCurveTo:function(t,e,n,r,i,o){t=this._wrapX(t),e=this._wrapY(e),n=this._wrapX(n),r=this._wrapY(r),i=this._wrapX(i),o=this._wrapY(o);var s;i=(s=this._matrix_map_point(this.ctx._transform,[i,o]))[0],o=s[1];var a={type:"bct",x1:t=(s=this._matrix_map_point(this.ctx._transform,[t,e]))[0],y1:e=s[1],x2:n=(s=this._matrix_map_point(this.ctx._transform,[n,r]))[0],y2:r=s[1],x:i,y:o};this.path.push(a)},quadraticCurveTo:function(t,e,n,r){t=this._wrapX(t),e=this._wrapY(e),n=this._wrapX(n),r=this._wrapY(r);var i;n=(i=this._matrix_map_point(this.ctx._transform,[n,r]))[0],r=i[1];var o={type:"qct",x1:t=(i=this._matrix_map_point(this.ctx._transform,[t,e]))[0],y1:e=i[1],x:n,y:r};this.path.push(o)},arc:function(t,e,n,r,i,o){if(t=this._wrapX(t),e=this._wrapY(e),!this._matrix_is_identity(this.ctx._transform)){var s=this._matrix_map_point(this.ctx._transform,[t,e]);t=s[0],e=s[1];var a=this._matrix_map_point(this.ctx._transform,[0,0]),c=this._matrix_map_point(this.ctx._transform,[0,n]);n=Math.sqrt(Math.pow(c[0]-a[0],2)+Math.pow(c[1]-a[1],2))}var h={type:"arc",x:t,y:e,radius:n,startAngle:r,endAngle:i,anticlockwise:o};this.path.push(h)},drawImage:function(t,e,n,r,i,o,s,a,c){void 0!==o&&(e=o,n=s,r=a,i=c),e=this._wrapX(e),n=this._wrapY(n);var h,l=this._matrix_map_rect(this.ctx._transform,{x:e,y:n,w:r,h:i}),u=(this._matrix_map_rect(this.ctx._transform,{x:o,y:s,w:a,h:c}),/data:image\/(\w+).*/i.exec(t));h=null!=u?u[1]:"png",this.pdf.addImage(t,h,l.x,l.y,l.w,l.h)},_matrix_multiply:function(t,e){var n=e[0],r=e[1],i=e[2],o=e[3],s=e[4],a=e[5],c=n*t[0]+r*t[2],h=i*t[0]+o*t[2],l=s*t[0]+a*t[2]+t[4];return r=n*t[1]+r*t[3],o=i*t[1]+o*t[3],a=s*t[1]+a*t[3]+t[5],n=c,i=h,s=l,[n,r,i,o,s,a]},_matrix_rotation:function(t){return Math.atan2(t[2],t[0])},_matrix_decompose:function(t){var e=t[0],n=t[1],r=t[2],i=t[3],o=Math.sqrt(e*e+n*n),s=(e/=o)*r+(n/=o)*i;r-=e*s,i-=n*s;var a=Math.sqrt(r*r+i*i);return r/=a,i/=a,s/=a,e*i<n*r&&(e=-e,n=-n,s=-s,o=-o),{scale:[o,0,0,a,0,0],translate:[1,0,0,1,t[4],t[5]],rotate:[e,n,-n,e,0,0],skew:[1,0,s,1,0,0]}},_matrix_map_point:function(t,e){var n=t[0],r=t[1],i=t[2],o=t[3],s=t[4],a=t[5],c=e[0],h=e[1];return[c*n+h*i+s,c*r+h*o+a]},_matrix_map_point_obj:function(t,e){var n=this._matrix_map_point(t,[e.x,e.y]);return{x:n[0],y:n[1]}},_matrix_map_rect:function(t,e){var n=this._matrix_map_point(t,[e.x,e.y]),r=this._matrix_map_point(t,[e.x+e.w,e.y+e.h]);return{x:n[0],y:n[1],w:r[0]-n[0],h:r[1]-n[1]}},_matrix_is_identity:function(t){return 1==t[0]&&(0==t[1]&&(0==t[2]&&(1==t[3]&&(0==t[4]&&0==t[5]))))},rotate:function(t){var e=[Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0];this.ctx._transform=this._matrix_multiply(this.ctx._transform,e)},scale:function(t,e){var n=[t,0,0,e,0,0];this.ctx._transform=this._matrix_multiply(this.ctx._transform,n)},translate:function(t,e){var n=[1,0,0,1,t,e];this.ctx._transform=this._matrix_multiply(this.ctx._transform,n)},stroke:function(){if(this.ctx._clip_path.length>0){var t;(t=window.outIntercept?"group"===window.outIntercept.type?window.outIntercept.stream:window.outIntercept:this.internal.getCurrentPage()).push("q");var e=this.path;this.path=this.ctx._clip_path,this.ctx._clip_path=[],this._stroke(!0),this.ctx._clip_path=this.path,this.path=e,this._stroke(!1),t.push("Q")}else this._stroke(!1)},_stroke:function(t){if(t||!this._isStrokeTransparent()){for(var e=[],n=this.path,r=0;r<n.length;r++){var i=n[r];switch(i.type){case"mt":e.push({start:i,deltas:[],abs:[]});break;case"lt":u=[i.x-n[r-1].x,i.y-n[r-1].y];e[e.length-1].deltas.push(u),e[e.length-1].abs.push(i);break;case"bct":u=[i.x1-n[r-1].x,i.y1-n[r-1].y,i.x2-n[r-1].x,i.y2-n[r-1].y,i.x-n[r-1].x,i.y-n[r-1].y];e[e.length-1].deltas.push(u);break;case"qct":var o=n[r-1].x+2/3*(i.x1-n[r-1].x),s=n[r-1].y+2/3*(i.y1-n[r-1].y),a=i.x+2/3*(i.x1-i.x),c=i.y+2/3*(i.y1-i.y),h=i.x,l=i.y,u=[o-n[r-1].x,s-n[r-1].y,a-n[r-1].x,c-n[r-1].y,h-n[r-1].x,l-n[r-1].y];e[e.length-1].deltas.push(u);break;case"arc":0==e.length&&e.push({start:{x:0,y:0},deltas:[],abs:[]}),e[e.length-1].arc=!0,e[e.length-1].abs.push(i)}}for(r=0;r<e.length;r++){var p;if(p=r==e.length-1?"s":null,e[r].arc)for(var f=e[r].abs,d=0;d<f.length;d++){var g=f[d],m=360*g.startAngle/(2*Math.PI),w=360*g.endAngle/(2*Math.PI),y=g.x,v=g.y;this.internal.arc2(this,y,v,g.radius,m,w,g.anticlockwise,p,t)}else{var y=e[r].start.x,v=e[r].start.y;t?(this.pdf.lines(e[r].deltas,y,v,null,null),this.pdf.clip_fixed()):this.pdf.lines(e[r].deltas,y,v,null,p)}}}},_isFillTransparent:function(){return this.ctx._isFillTransparent||0==this.globalAlpha},_isStrokeTransparent:function(){return this.ctx._isStrokeTransparent||0==this.globalAlpha},fill:function(t){if(this.ctx._clip_path.length>0){var e;(e=window.outIntercept?"group"===window.outIntercept.type?window.outIntercept.stream:window.outIntercept:this.internal.getCurrentPage()).push("q");var n=this.path;this.path=this.ctx._clip_path,this.ctx._clip_path=[],this._fill(t,!0),this.ctx._clip_path=this.path,this.path=n,this._fill(t,!1),e.push("Q")}else this._fill(t,!1)},_fill:function(t,e){if(!this._isFillTransparent()){var r,i="function"==typeof this.pdf.internal.newObject2;r=window.outIntercept?"group"===window.outIntercept.type?window.outIntercept.stream:window.outIntercept:this.internal.getCurrentPage();var o=[],s=window.outIntercept;if(i)switch(this.ctx.globalCompositeOperation){case"normal":case"source-over":break;case"destination-in":case"destination-out":var a=this.pdf.internal.newStreamObject(),c=this.pdf.internal.newObject2();c.push("<</Type /ExtGState"),c.push("/SMask <</S /Alpha /G "+a.objId+" 0 R>>"),c.push(">>");f="MASK"+c.objId;this.pdf.internal.addGraphicsState(f,c.objId);var h="/"+f+" gs";r.splice(0,0,"q"),r.splice(1,0,h),r.push("Q"),window.outIntercept=a;break;default:var l="/"+this.pdf.internal.blendModeMap[this.ctx.globalCompositeOperation.toUpperCase()];l&&this.pdf.internal.out(l+" gs")}var u=this.ctx.globalAlpha;if(this.ctx._fillOpacity<1&&(u=this.ctx._fillOpacity),i){var p=this.pdf.internal.newObject2();p.push("<</Type /ExtGState"),p.push("/CA "+u),p.push("/ca "+u),p.push(">>");var f="GS_O_"+p.objId;this.pdf.internal.addGraphicsState(f,p.objId),this.pdf.internal.out("/"+f+" gs")}for(var d=this.path,g=0;g<d.length;g++){var m=d[g];switch(m.type){case"mt":o.push({start:m,deltas:[],abs:[]});break;case"lt":k=[m.x-d[g-1].x,m.y-d[g-1].y];o[o.length-1].deltas.push(k),o[o.length-1].abs.push(m);break;case"bct":k=[m.x1-d[g-1].x,m.y1-d[g-1].y,m.x2-d[g-1].x,m.y2-d[g-1].y,m.x-d[g-1].x,m.y-d[g-1].y];o[o.length-1].deltas.push(k);break;case"qct":var w=d[g-1].x+2/3*(m.x1-d[g-1].x),y=d[g-1].y+2/3*(m.y1-d[g-1].y),v=m.x+2/3*(m.x1-m.x),b=m.y+2/3*(m.y1-m.y),x=m.x,S=m.y,k=[w-d[g-1].x,y-d[g-1].y,v-d[g-1].x,b-d[g-1].y,x-d[g-1].x,S-d[g-1].y];o[o.length-1].deltas.push(k);break;case"arc":0===o.length&&o.push({deltas:[],abs:[]}),o[o.length-1].arc=!0,o[o.length-1].abs.push(m);break;case"close":o.push({close:!0})}}for(g=0;g<o.length;g++){var C;if(g==o.length-1?(C="f","evenodd"===t&&(C+="*")):C=null,o[g].close)this.pdf.internal.out("h"),this.pdf.internal.out("f");else if(o[g].arc){o[g].start&&this.internal.move2(this,o[g].start.x,o[g].start.y);for(var I=o[g].abs,A=0;A<I.length;A++){var T=I[A];if(void 0!==T.startAngle){var _=360*T.startAngle/(2*Math.PI),q=360*T.endAngle/(2*Math.PI),O=T.x,P=T.y;if(0===A&&this.internal.move2(this,O,P),this.internal.arc2(this,O,P,T.radius,_,q,T.anticlockwise,null,e),A===I.length-1&&o[g].start){var O=o[g].start.x,P=o[g].start.y;this.internal.line2(n,O,P)}}else this.internal.line2(n,T.x,T.y)}}else{var O=o[g].start.x,P=o[g].start.y;e?(this.pdf.lines(o[g].deltas,O,P,null,null),this.pdf.clip_fixed()):this.pdf.lines(o[g].deltas,O,P,null,C)}}window.outIntercept=s}},pushMask:function(){if("function"==typeof this.pdf.internal.newObject2){var t=this.pdf.internal.newStreamObject(),e=this.pdf.internal.newObject2();e.push("<</Type /ExtGState"),e.push("/SMask <</S /Alpha /G "+t.objId+" 0 R>>"),e.push(">>");var n="MASK"+e.objId;this.pdf.internal.addGraphicsState(n,e.objId);var r="/"+n+" gs";this.pdf.internal.out(r)}else console.log("jsPDF v2 not enabled")},clip:function(){if(this.ctx._clip_path.length>0)for(var t=0;t<this.path.length;t++)this.ctx._clip_path.push(this.path[t]);else this.ctx._clip_path=this.path;this.path=[]},measureText:function(t){var e=this.pdf;return{getWidth:function(){var n=e.internal.getFontSize(),r=e.getStringUnitWidth(t)*n/e.internal.scaleFactor;return r*=1.3333},get width(){return this.getWidth(t)}}},_getBaseline:function(t){var e=parseInt(this.pdf.internal.getFontSize()),n=.25*e;switch(this.ctx.textBaseline){case"bottom":return t-n;case"top":return t+e;case"hanging":return t+e-n;case"middle":return t+e/2-n;case"ideographic":return t;case"alphabetic":default:return t}}};var n=t.context2d;Object.defineProperty(n,"fillStyle",{set:function(t){this.setFillStyle(t)},get:function(){return this.ctx.fillStyle}}),Object.defineProperty(n,"strokeStyle",{set:function(t){this.setStrokeStyle(t)},get:function(){return this.ctx.strokeStyle}}),Object.defineProperty(n,"lineWidth",{set:function(t){this.setLineWidth(t)},get:function(){return this.ctx.lineWidth}}),Object.defineProperty(n,"lineCap",{set:function(t){this.setLineCap(t)},get:function(){return this.ctx.lineCap}}),Object.defineProperty(n,"lineJoin",{set:function(t){this.setLineJoin(t)},get:function(){return this.ctx.lineJoin}}),Object.defineProperty(n,"miterLimit",{set:function(t){this.ctx.miterLimit=t},get:function(){return this.ctx.miterLimit}}),Object.defineProperty(n,"textBaseline",{set:function(t){this.setTextBaseline(t)},get:function(){return this.getTextBaseline()}}),Object.defineProperty(n,"textAlign",{set:function(t){this.setTextAlign(t)},get:function(){return this.getTextAlign()}}),Object.defineProperty(n,"font",{set:function(t){this.setFont(t)},get:function(){return this.ctx.font}}),Object.defineProperty(n,"globalCompositeOperation",{set:function(t){this.ctx.globalCompositeOperation=t},get:function(){return this.ctx.globalCompositeOperation}}),Object.defineProperty(n,"globalAlpha",{set:function(t){this.ctx.globalAlpha=t},get:function(){return this.ctx.globalAlpha}}),Object.defineProperty(n,"ignoreClearRect",{set:function(t){this.ctx.ignoreClearRect=t},get:function(){return this.ctx.ignoreClearRect}}),n.internal={},n.internal.rxRgb=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/,n.internal.rxRgba=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d\.]+)\s*\)/,n.internal.rxTransparent=/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/,n.internal.arc=function(t,e,n,r,i,o,s,a){for(var c=this.pdf.internal.scaleFactor,h=this.pdf.internal.pageSize.height,l=this.pdf.internal.f2,u=i*(Math.PI/180),p=o*(Math.PI/180),f=this.createArc(r,u,p,s),d=0;d<f.length;d++){var g=f[d];0===d?this.pdf.internal.out([l((g.x1+e)*c),l((h-(g.y1+n))*c),"m",l((g.x2+e)*c),l((h-(g.y2+n))*c),l((g.x3+e)*c),l((h-(g.y3+n))*c),l((g.x4+e)*c),l((h-(g.y4+n))*c),"c"].join(" ")):this.pdf.internal.out([l((g.x2+e)*c),l((h-(g.y2+n))*c),l((g.x3+e)*c),l((h-(g.y3+n))*c),l((g.x4+e)*c),l((h-(g.y4+n))*c),"c"].join(" ")),t._lastPoint={x:e,y:n}}null!==a&&this.pdf.internal.out(this.pdf.internal.getStyle(a))},n.internal.arc2=function(t,e,n,r,i,o,s,a,c){var h=e,l=n;c?(this.arc(t,h,l,r,i,o,s,null),this.pdf.clip_fixed()):this.arc(t,h,l,r,i,o,s,a)},n.internal.move2=function(t,e,n){var r=this.pdf.internal.scaleFactor,i=this.pdf.internal.pageSize.height,o=this.pdf.internal.f2;this.pdf.internal.out([o(e*r),o((i-n)*r),"m"].join(" ")),t._lastPoint={x:e,y:n}},n.internal.line2=function(t,e,n){var r=this.pdf.internal.scaleFactor,i=this.pdf.internal.pageSize.height,o=this.pdf.internal.f2,s={x:e,y:n};this.pdf.internal.out([o(s.x*r),o((i-s.y)*r),"l"].join(" ")),t._lastPoint=s},n.internal.createArc=function(t,e,n,r){var i=2*Math.PI,o=Math.PI/2,s=e;for((s<i||s>i)&&(s%=i),s<0&&(s=i+s);e>n;)e-=i;var a=Math.abs(n-e);a<i&&r&&(a=i-a);for(var c=[],h=r?-1:1,l=s;a>1e-5;){var u=l+h*Math.min(a,o);c.push(this.createSmallArc(t,l,u)),a-=Math.abs(u-l),l=u}return c},n.internal.getCurrentPage=function(){return this.pdf.internal.pages[this.pdf.internal.getCurrentPageInfo().pageNumber]},n.internal.createSmallArc=function(t,e,n){var r=(n-e)/2,i=t*Math.cos(r),o=t*Math.sin(r),s=i,a=-o,c=s*s+a*a,h=c+s*i+a*o,l=4/3*(Math.sqrt(2*c*h)-h)/(s*o-a*i),u=s-l*a,p=a+l*s,f=u,d=-p,g=r+e,m=Math.cos(g),w=Math.sin(g);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:u*m-p*w,y2:u*w+p*m,x3:f*m-d*w,y3:f*w+d*m,x4:t*Math.cos(n),y4:t*Math.sin(n)}}}(e.API),/** @preserve
 * jsPDF fromHTML plugin. BETA stage. API subject to change. Needs browser
 * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com
 *               2014 Juan Pablo Gaviria, https://github.com/juanpgaviria
 *               2014 Diego Casorran, https://github.com/diegocr
 *               2014 Daniel Husar, https://github.com/danielhusar
 *               2014 Wolfgang Gassler, https://github.com/woolfg
 *               2014 Steven Spungin, https://github.com/flamenco
 *
 * 
 * ====================================================================
 */
function(e){var n,r,i,o,s,a,c,h,l,u,p,f,d,g,m,w,y,v,b,x;n=function(){function t(){}return function(e){return t.prototype=e,new t}}(),u=function(t){var e,n,r,i,o,s,a;for(n=0,r=t.length,e=void 0,i=!1,s=!1;!i&&n!==r;)(e=t[n]=t[n].trimLeft())&&(i=!0),n++;for(n=r-1;r&&!s&&-1!==n;)(e=t[n]=t[n].trimRight())&&(s=!0),n--;for(o=/\s+$/g,a=!0,n=0;n!==r;)"\u2028"!=t[n]&&(e=t[n].replace(/\s+/g," "),a&&(e=e.trimLeft()),e&&(a=o.test(e)),t[n]=e),n++;return t},f=function(t){var e,n,r;for(e=void 0,n=(r=t.split(",")).shift();!e&&n;)e=i[n.trim().toLowerCase()],n=r.shift();return e},d=function(t){(t="auto"===t?"0px":t).indexOf("em")>-1&&!isNaN(Number(t.replace("em","")))&&(t=18.719*Number(t.replace("em",""))+"px"),t.indexOf("pt")>-1&&!isNaN(Number(t.replace("pt","")))&&(t=1.333*Number(t.replace("pt",""))+"px");var e;return void 0,16,(e=g[t])?e:void 0!==(e={"xx-small":9,"x-small":11,small:13,medium:16,large:19,"x-large":23,"xx-large":28,auto:0}[{css_line_height_string:t}])?g[t]=e/16:(e=parseFloat(t))?g[t]=e/16:3===(e=t.match(/([\d\.]+)(px)/)).length?g[t]=parseFloat(e[1])/16:g[t]=1},l=function(t){var e,n,r;return r=function(e){var n;return n=function(t){return document.defaultView&&document.defaultView.getComputedStyle?document.defaultView.getComputedStyle(t,null):t.currentStyle?t.currentStyle:t.style}(t),function(t){return t=t.replace(/-\D/g,function(t){return t.charAt(1).toUpperCase()}),n[t]}}(),e={},n=void 0,e["font-family"]=f(r("font-family"))||"times",e["font-style"]=o[r("font-style")]||"normal",e["text-align"]=s[r("text-align")]||"left","bold"===(n=a[r("font-weight")]||"normal")&&("normal"===e["font-style"]?e["font-style"]=n:e["font-style"]=n+e["font-style"]),e["font-size"]=d(r("font-size"))||1,e["line-height"]=d(r("line-height"))||1,e.display="inline"===r("display")?"inline":"block",n="block"===e.display,e["margin-top"]=n&&d(r("margin-top"))||0,e["margin-bottom"]=n&&d(r("margin-bottom"))||0,e["padding-top"]=n&&d(r("padding-top"))||0,e["padding-bottom"]=n&&d(r("padding-bottom"))||0,e["margin-left"]=n&&d(r("margin-left"))||0,e["margin-right"]=n&&d(r("margin-right"))||0,e["padding-left"]=n&&d(r("padding-left"))||0,e["padding-right"]=n&&d(r("padding-right"))||0,e["page-break-before"]=r("page-break-before")||"auto",e.float=c[r("cssFloat")]||"none",e.clear=h[r("clear")]||"none",e.color=r("color"),e},m=function(t,e,n){var r,i,o,s,a;if(o=!1,i=void 0,s=void 0,r=n["#"+t.id])if("function"==typeof r)o=r(t,e);else for(i=0,s=r.length;!o&&i!==s;)o=r[i](t,e),i++;if(r=n[t.nodeName],!o&&r)if("function"==typeof r)o=r(t,e);else for(i=0,s=r.length;!o&&i!==s;)o=r[i](t,e),i++;for(a=t.className?t.className.split(" "):[],i=0;i<a.length;i++)if(r=n["."+a[i]],!o&&r)if("function"==typeof r)o=r(t,e);else for(i=0,s=r.length;!o&&i!==s;)o=r[i](t,e),i++;return o},x=function(t,e){var n,r,i,o,s,a,c,h,l;for(n=[],r=[],i=0,l=t.rows[0].cells.length,c=t.clientWidth;i<l;)h=t.rows[0].cells[i],r[i]={name:h.textContent.toLowerCase().replace(/\s+/g,""),prompt:h.textContent.replace(/\r?\n/g,""),width:h.clientWidth/c*e.pdf.internal.pageSize.width},i++;for(i=1;i<t.rows.length;){for(a=t.rows[i],s={},o=0;o<a.cells.length;)s[r[o].name]=a.cells[o].textContent.replace(/\r?\n/g,""),o++;n.push(s),i++}return{rows:n,headers:r}};var S={SCRIPT:1,STYLE:1,NOSCRIPT:1,OBJECT:1,EMBED:1,SELECT:1},k=1;r=function(e,i,o){var s,a,c,h,u,p,f,d;for(a=e.childNodes,s=void 0,(u="block"===(c=l(e)).display)&&(i.setBlockBoundary(),i.setBlockStyle(c)),h=0,p=a.length;h<p;){if("object"===(void 0===(s=a[h])?"undefined":t(s))){if(i.executeWatchFunctions(s),1===s.nodeType&&"HEADER"===s.nodeName){var g=s,y=i.pdf.margins_doc.top;i.pdf.internal.events.subscribe("addPage",function(t){i.y=y,r(g,i,o),i.pdf.margins_doc.top=i.y+10,i.y+=10},!1)}if(8===s.nodeType&&"#comment"===s.nodeName)~s.textContent.indexOf("ADD_PAGE")&&(i.pdf.addPage(),i.y=i.pdf.margins_doc.top);else if(1!==s.nodeType||S[s.nodeName])if(3===s.nodeType){var v=s.nodeValue;if(s.nodeValue&&"LI"===s.parentNode.nodeName)if("OL"===s.parentNode.parentNode.nodeName)v=k+++". "+v;else{var b=c["font-size"],C=(3-.75*b)*i.pdf.internal.scaleFactor,I=.75*b*i.pdf.internal.scaleFactor,A=1.74*b/i.pdf.internal.scaleFactor;d=function(t,e){this.pdf.circle(t+C,e+I,A,"FD")}}16&s.ownerDocument.body.compareDocumentPosition(s)&&i.addText(v,c)}else"string"==typeof s&&i.addText(s,c);else{var T;if("IMG"===s.nodeName){var _=s.getAttribute("src");T=w[i.pdf.sHashCode(_)||_]}if(T){i.pdf.internal.pageSize.height-i.pdf.margins_doc.bottom<i.y+s.height&&i.y>i.pdf.margins_doc.top&&(i.pdf.addPage(),i.y=i.pdf.margins_doc.top,i.executeWatchFunctions(s));var q=l(s),O=i.x,P=12/i.pdf.internal.scaleFactor,F=(q["margin-left"]+q["padding-left"])*P,E=(q["margin-right"]+q["padding-right"])*P,B=(q["margin-top"]+q["padding-top"])*P,R=(q["margin-bottom"]+q["padding-bottom"])*P;void 0!==q.float&&"right"===q.float?O+=i.settings.width-s.width-E:O+=F,i.pdf.addImage(T,O,i.y+B,s.width,s.height),T=void 0,"right"===q.float||"left"===q.float?(i.watchFunctions.push(function(t,e,n,r){return i.y>=e?(i.x+=t,i.settings.width+=n,!0):!!(r&&1===r.nodeType&&!S[r.nodeName]&&i.x+r.width>i.pdf.margins_doc.left+i.pdf.margins_doc.width)&&(i.x+=t,i.y=e,i.settings.width+=n,!0)}.bind(this,"left"===q.float?-s.width-F-E:0,i.y+s.height+B+R,s.width)),i.watchFunctions.push(function(t,e,n){return!(i.y<t&&e===i.pdf.internal.getNumberOfPages())||1===n.nodeType&&"both"===l(n).clear&&(i.y=t,!0)}.bind(this,i.y+s.height,i.pdf.internal.getNumberOfPages())),i.settings.width-=s.width+F+E,"left"===q.float&&(i.x+=s.width+F+E)):i.y+=s.height+B+R}else if("TABLE"===s.nodeName)f=x(s,i),i.y+=10,i.pdf.table(i.x,i.y,f.rows,f.headers,{autoSize:!1,printHeaders:o.printHeaders,margins:i.pdf.margins_doc,css:l(s)}),i.y=i.pdf.lastCellPos.y+i.pdf.lastCellPos.h+20;else if("OL"===s.nodeName||"UL"===s.nodeName)k=1,m(s,i,o)||r(s,i,o),i.y+=10;else if("LI"===s.nodeName){var D=i.x;i.x+=20/i.pdf.internal.scaleFactor,i.y+=3,m(s,i,o)||r(s,i,o),i.x=D}else"BR"===s.nodeName?(i.y+=c["font-size"]*i.pdf.internal.scaleFactor,i.addText("\u2028",n(c))):m(s,i,o)||r(s,i,o)}}h++}if(o.outY=i.y,u)return i.setBlockBoundary(d)},w={},y=function(t,e,n,r){function i(){e.pdf.internal.events.publish("imagesLoaded"),r(o)}for(var o,s=t.getElementsByTagName("img"),a=s.length,c=0;a--;)!function(t,n,r){if(t){var s=new Image;o=++c,s.crossOrigin="",s.onerror=s.onload=function(){if(s.complete&&(0===s.src.indexOf("data:image/")&&(s.width=n||s.width||0,s.height=r||s.height||0),s.width+s.height)){var o=e.pdf.sHashCode(t)||t;w[o]=w[o]||s}--c||i()},s.src=t}}(s[a].getAttribute("src"),s[a].width,s[a].height);return c||i()},v=function(t,e,n){var i=t.getElementsByTagName("footer");if(i.length>0){i=i[0];var o=e.pdf.internal.write,s=e.y;e.pdf.internal.write=function(){},r(i,e,n);var a=Math.ceil(e.y-s)+5;e.y=s,e.pdf.internal.write=o,e.pdf.margins_doc.bottom+=a;for(var c=function(t){var o=void 0!==t?t.pageNumber:1,s=e.y;e.y=e.pdf.internal.pageSize.height-e.pdf.margins_doc.bottom,e.pdf.margins_doc.bottom-=a;for(var c=i.getElementsByTagName("span"),h=0;h<c.length;++h)(" "+c[h].className+" ").replace(/[\n\t]/g," ").indexOf(" pageCounter ")>-1&&(c[h].innerHTML=o),(" "+c[h].className+" ").replace(/[\n\t]/g," ").indexOf(" totalPages ")>-1&&(c[h].innerHTML="###jsPDFVarTotalPages###");r(i,e,n),e.pdf.margins_doc.bottom+=a,e.y=s},h=i.getElementsByTagName("span"),l=0;l<h.length;++l)(" "+h[l].className+" ").replace(/[\n\t]/g," ").indexOf(" totalPages ")>-1&&e.pdf.internal.events.subscribe("htmlRenderingFinished",e.pdf.putTotalPages.bind(e.pdf,"###jsPDFVarTotalPages###"),!0);e.pdf.internal.events.subscribe("addPage",c,!1),c(),S.FOOTER=1}},b=function(t,e,n,i,o,s){if(!e)return!1;"string"==typeof e||e.parentNode||(e=""+e.innerHTML),"string"==typeof e&&(e=function(t){var e,n,r,i;return r="jsPDFhtmlText"+Date.now().toString()+(1e3*Math.random()).toFixed(0),i="position: absolute !important;clip: rect(1px 1px 1px 1px); /* IE6, IE7 */clip: rect(1px, 1px, 1px, 1px);padding:0 !important;border:0 !important;height: 1px !important;width: 1px !important; top:auto;left:-100px;overflow: hidden;",n=document.createElement("div"),n.style.cssText=i,n.innerHTML='<iframe style="height:1px;width:1px" name="'+r+'" />',document.body.appendChild(n),(e=window.frames[r]).document.open(),e.document.writeln(t),e.document.close(),e.document.body}(e.replace(/<\/?script[^>]*?>/gi,"")));var a,c=new p(t,n,i,o);return y.call(this,e,c,o.elementHandlers,function(t){v(e,c,o.elementHandlers),r(e,c,o.elementHandlers),c.pdf.internal.events.publish("htmlRenderingFinished"),a=c.dispose(),"function"==typeof s?s(a):t&&console.error("jsPDF Warning: rendering issues? provide a callback to fromHTML!")}),a||{x:c.x,y:c.y}},(p=function(t,e,n,r){return this.pdf=t,this.x=e,this.y=n,this.settings=r,this.watchFunctions=[],this.init(),this}).prototype.init=function(){return this.paragraph={text:[],style:[]},this.pdf.internal.write("q")},p.prototype.dispose=function(){return this.pdf.internal.write("Q"),{x:this.x,y:this.y,ready:!0}},p.prototype.executeWatchFunctions=function(t){var e=!1,n=[];if(this.watchFunctions.length>0){for(var r=0;r<this.watchFunctions.length;++r)!0===this.watchFunctions[r](t)?e=!0:n.push(this.watchFunctions[r]);this.watchFunctions=n}return e},p.prototype.splitFragmentsIntoLines=function(t,e){var r,i,o,s,a,c,h,l,u,p,f,d,g,m;for(12,p=this.pdf.internal.scaleFactor,s={},i=void 0,u=void 0,o=void 0,a=void 0,m=void 0,l=void 0,h=void 0,c=void 0,d=[f=[]],r=0,g=this.settings.width;t.length;)if(a=t.shift(),m=e.shift(),a)if(i=m["font-family"],u=m["font-style"],(o=s[i+u])||(o=this.pdf.internal.getFont(i,u).metadata.Unicode,s[i+u]=o),l={widths:o.widths,kerning:o.kerning,fontSize:12*m["font-size"],textIndent:r},h=this.pdf.getStringUnitWidth(a,l)*l.fontSize/p,"\u2028"==a)f=[],d.push(f);else if(r+h>g){for(c=this.pdf.splitTextToSize(a,g,l),f.push([c.shift(),m]);c.length;)f=[[c.shift(),m]],d.push(f);r=this.pdf.getStringUnitWidth(f[0][0],l)*l.fontSize/p}else f.push([a,m]),r+=h;if(void 0!==m["text-align"]&&("center"===m["text-align"]||"right"===m["text-align"]||"justify"===m["text-align"]))for(var w=0;w<d.length;++w){var y=this.pdf.getStringUnitWidth(d[w][0][0],l)*l.fontSize/p;w>0&&(d[w][0][1]=n(d[w][0][1]));var v=g-y;if("right"===m["text-align"])d[w][0][1]["margin-left"]=v;else if("center"===m["text-align"])d[w][0][1]["margin-left"]=v/2;else if("justify"===m["text-align"]){var b=d[w][0][0].split(" ").length-1;d[w][0][1]["word-spacing"]=v/b,w===d.length-1&&(d[w][0][1]["word-spacing"]=0)}}return d},p.prototype.RenderTextFragment=function(t,e){var n,r;r=0,this.pdf.internal.pageSize.height-this.pdf.margins_doc.bottom<this.y+this.pdf.internal.getFontSize()&&(this.pdf.internal.write("ET","Q"),this.pdf.addPage(),this.y=this.pdf.margins_doc.top,this.pdf.internal.write("q","BT 0 g",this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),e.color,"Td"),r=Math.max(r,e["line-height"],e["font-size"]),this.pdf.internal.write(0,(-12*r).toFixed(2),"Td")),n=this.pdf.internal.getFont(e["font-family"],e["font-style"]);var i=this.getPdfColor(e.color);i!==this.lastTextColor&&(this.pdf.internal.write(i),this.lastTextColor=i),void 0!==e["word-spacing"]&&e["word-spacing"]>0&&this.pdf.internal.write(e["word-spacing"].toFixed(2),"Tw"),this.pdf.internal.write("/"+n.id,(12*e["font-size"]).toFixed(2),"Tf","("+this.pdf.internal.pdfEscape(t)+") Tj"),void 0!==e["word-spacing"]&&this.pdf.internal.write(0,"Tw")},p.prototype.getPdfColor=function(t){var e,n,r,i=/rgb\s*\(\s*(\d+),\s*(\d+),\s*(\d+\s*)\)/.exec(t);if(null!=i?(e=parseInt(i[1]),n=parseInt(i[2]),r=parseInt(i[3])):("#"!=t.charAt(0)&&((t=CssColors.colorNameToHex(t))||(t="#000000")),e=t.substring(1,3),e=parseInt(e,16),n=t.substring(3,5),n=parseInt(n,16),r=t.substring(5,7),r=parseInt(r,16)),"string"==typeof e&&/^#[0-9A-Fa-f]{6}$/.test(e)){var o=parseInt(e.substr(1),16);e=o>>16&255,n=o>>8&255,r=255&o}var s=this.f3;return 0===e&&0===n&&0===r||void 0===n?s(e/255)+" g":[s(e/255),s(n/255),s(r/255),"rg"].join(" ")},p.prototype.f3=function(t){return t.toFixed(3)},p.prototype.renderParagraph=function(t){var e,n,r,i,o,s,a,c,h,l,p,f,d;if(r=u(this.paragraph.text),f=this.paragraph.style,e=this.paragraph.blockstyle,this.paragraph={text:[],style:[],blockstyle:{},priorblockstyle:e},r.join("").trim()){a=this.splitFragmentsIntoLines(r,f),s=void 0,c=void 0,12,n=12/this.pdf.internal.scaleFactor,this.priorMarginBottom=this.priorMarginBottom||0,p=(Math.max((e["margin-top"]||0)-this.priorMarginBottom,0)+(e["padding-top"]||0))*n,l=((e["margin-bottom"]||0)+(e["padding-bottom"]||0))*n,this.priorMarginBottom=e["margin-bottom"]||0,"always"===e["page-break-before"]&&(this.pdf.addPage(),this.y=0,p=((e["margin-top"]||0)+(e["padding-top"]||0))*n),h=this.pdf.internal.write,i=void 0,o=void 0,this.y+=p,h("q","BT 0 g",this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td");for(var g=0;a.length;){for(c=0,i=0,o=(s=a.shift()).length;i!==o;)s[i][0].trim()&&(c=Math.max(c,s[i][1]["line-height"],s[i][1]["font-size"]),d=7*s[i][1]["font-size"]),i++;var m=0,w=0;for(void 0!==s[0][1]["margin-left"]&&s[0][1]["margin-left"]>0&&(m=(w=this.pdf.internal.getCoordinateString(s[0][1]["margin-left"]))-g,g=w),h(m+Math.max(e["margin-left"]||0,0)*n,(-12*c).toFixed(2),"Td"),i=0,o=s.length;i!==o;)s[i][0]&&this.RenderTextFragment(s[i][0],s[i][1]),i++;if(this.y+=c*n,this.executeWatchFunctions(s[0][1])&&a.length>0){var y=[],v=[];a.forEach(function(t){for(var e=0,n=t.length;e!==n;)t[e][0]&&(y.push(t[e][0]+" "),v.push(t[e][1])),++e}),a=this.splitFragmentsIntoLines(u(y),v),h("ET","Q"),h("q","BT 0 g",this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td")}}return t&&"function"==typeof t&&t.call(this,this.x-9,this.y-d/2),h("ET","Q"),this.y+=l}},p.prototype.setBlockBoundary=function(t){return this.renderParagraph(t)},p.prototype.setBlockStyle=function(t){return this.paragraph.blockstyle=t},p.prototype.addText=function(t,e){return this.paragraph.text.push(t),this.paragraph.style.push(e)},i={helvetica:"helvetica","sans-serif":"helvetica","times new roman":"times",serif:"times",times:"times",monospace:"courier",courier:"courier"},a={100:"normal",200:"normal",300:"normal",400:"normal",500:"bold",600:"bold",700:"bold",800:"bold",900:"bold",normal:"normal",bold:"bold",bolder:"bold",lighter:"normal"},o={normal:"normal",italic:"italic",oblique:"italic"},s={left:"left",right:"right",center:"center",justify:"justify"},c={none:"none",right:"right",left:"left"},h={none:"none",both:"both"},g={normal:1},e.fromHTML=function(t,e,n,r,i,o){return this.margins_doc=o||{top:0,bottom:0},r||(r={}),r.elementHandlers||(r.elementHandlers={}),b(this,t,isNaN(e)?4:e,isNaN(n)?4:n,r,i)}}(e.API),/** ==================================================================== 
 * jsPDF JavaScript plugin
 * Copyright (c) 2013 Youssef Beddad, <EMAIL>
 * 
 * 
 * ====================================================================
 */
function(t){var n,r,i;e.API.addJS=function(t){return i=t,this.internal.events.subscribe("postPutResources",function(t){n=this.internal.newObject(),this.internal.write("<< /Names [(EmbeddedJS) "+(n+1)+" 0 R] >>","endobj"),r=this.internal.newObject(),this.internal.write("<< /S /JavaScript /JS (",i,") >>","endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==n&&void 0!==r&&this.internal.write("/Names <</JavaScript "+n+" 0 R>>")}),this}}(),/**
 * jsPDF Outline PlugIn
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.events.push(["postPutResources",function(){var t=this,e=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var n=t.outline.render().split(/\r\n/),r=0;r<n.length;r++){var i=n[r],o=e.exec(i);if(null!=o){var s=o[1];t.internal.newObjectDeferredBegin(s)}t.internal.write(i)}if(this.outline.createNamedDestinations){for(var a=this.internal.pages.length,c=[],r=0;r<a;r++){var h=t.internal.newObject();c.push(h);var l=t.internal.getPageInfo(r+1);t.internal.write("<< /D["+l.objId+" 0 R /XYZ null null null]>> endobj")}var u=t.internal.newObject();t.internal.write("<< /Names [ ");for(r=0;r<c.length;r++)t.internal.write("(page_"+(r+1)+")"+c[r]+" 0 R");t.internal.write(" ] >>","endobj");t.internal.newObject();t.internal.write("<< /Dests "+u+" 0 R"),t.internal.write(">>","endobj")}}]),t.events.push(["putCatalog",function(){var t=this;t.outline.root.children.length>0&&(t.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&t.internal.write("/Names "+namesOid+" 0 R"))}]),t.events.push(["initialized",function(){var t=this;t.outline={createNamedDestinations:!1,root:{children:[]}},t.outline.add=function(t,e,n){var r={title:e,options:n,children:[]};return null==t&&(t=this.root),t.children.push(r),r},t.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=t,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},t.outline.genIds_r=function(e){e.id=t.internal.newObjectDeferred();for(var n=0;n<e.children.length;n++)this.genIds_r(e.children[n])},t.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),t.children.length>0&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},t.outline.renderItems=function(e){for(i=0;i<e.children.length;i++){o=e.children[i];this.objStart(o),this.line("/Title "+this.makeString(o.title)),this.line("/Parent "+this.makeRef(e)),i>0&&this.line("/Prev "+this.makeRef(e.children[i-1])),i<e.children.length-1&&this.line("/Next "+this.makeRef(e.children[i+1])),o.children.length>0&&(this.line("/First "+this.makeRef(o.children[0])),this.line("/Last "+this.makeRef(o.children[o.children.length-1])));var n=this.count=this.count_r({count:0},o);if(n>0&&this.line("/Count "+n),o.options&&o.options.pageNumber){var r=t.internal.getPageInfo(o.options.pageNumber);this.line("/Dest ["+r.objId+" 0 R /XYZ 0 "+this.ctx.pdf.internal.pageSize.height+" 0]")}this.objEnd()}for(var i=0;i<e.children.length;i++){var o=e.children[i];this.renderItems(o)}},t.outline.line=function(t){this.ctx.val+=t+"\r\n"},t.outline.makeRef=function(t){return t.id+" 0 R"},t.outline.makeString=function(e){return"("+t.internal.pdfEscape(e)+")"},t.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},t.outline.objEnd=function(t){this.ctx.val+=">> \r\nendobj\r\n"},t.outline.count_r=function(t,e){for(var n=0;n<e.children.length;n++)t.count++,this.count_r(t,e.children[n]);return t.count}}])}(e.API),/**@preserve
 *  ====================================================================
 * jsPDF PNG PlugIn
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * 
 * ====================================================================
 */
function(t){var e=function(){return"function"!=typeof PNG||"function"!=typeof o},n=function(e){return e!==t.image_compression.NONE&&r()},r=function(){var t="function"==typeof Deflater;if(!t)throw new Error("requires deflate.js for compression");return t},i=function(e,n,r,i){var o=5,h=u;switch(i){case t.image_compression.FAST:o=3,h=l;break;case t.image_compression.MEDIUM:o=6,h=p;break;case t.image_compression.SLOW:o=9,h=f}e=c(e,n,r,h);var d=new Uint8Array(s(o)),g=a(e),m=new Deflater(o),w=m.append(e),y=m.flush(),v=d.length+w.length+y.length,b=new Uint8Array(v+4);return b.set(d),b.set(w,d.length),b.set(y,d.length+w.length),b[v++]=g>>>24&255,b[v++]=g>>>16&255,b[v++]=g>>>8&255,b[v++]=255&g,t.arrayBufferToBinaryString(b)},s=function(t,e){var n=Math.LOG2E*Math.log(32768)-8<<4|8,r=n<<8;return r|=Math.min(3,(e-1&255)>>1)<<6,r|=0,r+=31-r%31,[n,255&r]},a=function(t,e){for(var n,r=1,i=0,o=t.length,s=0;o>0;){o-=n=o>e?e:o;do{i+=r+=t[s++]}while(--n);r%=65521,i%=65521}return(i<<16|r)>>>0},c=function(t,e,n,r){for(var i,o,s,a=t.length/e,c=new Uint8Array(t.length+a),h=g(),l=0;l<a;l++){if(s=l*e,i=t.subarray(s,s+e),r)c.set(r(i,n,o),s+l);else{for(var u=0,p=h.length,f=[];u<p;u++)f[u]=h[u](i,n,o);var d=m(f.concat());c.set(f[d],s+l)}o=i}return c},h=function(t,e,n){var r=Array.apply([],t);return r.unshift(0),r},l=function(t,e,n){var r,i=[],o=0,s=t.length;for(i[0]=1;o<s;o++)r=t[o-e]||0,i[o+1]=t[o]-r+256&255;return i},u=function(t,e,n){var r,i=[],o=0,s=t.length;for(i[0]=2;o<s;o++)r=n&&n[o]||0,i[o+1]=t[o]-r+256&255;return i},p=function(t,e,n){var r,i,o=[],s=0,a=t.length;for(o[0]=3;s<a;s++)r=t[s-e]||0,i=n&&n[s]||0,o[s+1]=t[s]+256-(r+i>>>1)&255;return o},f=function(t,e,n){var r,i,o,s,a=[],c=0,h=t.length;for(a[0]=4;c<h;c++)r=t[c-e]||0,i=n&&n[c]||0,o=n&&n[c-e]||0,s=d(r,i,o),a[c+1]=t[c]-s+256&255;return a},d=function(t,e,n){var r=t+e-n,i=Math.abs(r-t),o=Math.abs(r-e),s=Math.abs(r-n);return i<=o&&i<=s?t:o<=s?e:n},g=function(){return[h,l,u,p,f]},m=function(t){for(var e,n,r,i=0,o=t.length;i<o;)((e=w(t[i].slice(1)))<n||!n)&&(n=e,r=i),i++;return r},w=function(t){for(var e=0,n=t.length,r=0;e<n;)r+=Math.abs(t[e++]);return r},y=function(e){var n;switch(e){case t.image_compression.FAST:n=11;break;case t.image_compression.MEDIUM:n=13;break;case t.image_compression.SLOW:n=14;break;default:n=12}return n};t.processPNG=function(t,r,o,s,a){var c,h,l,u,p,f,d=this.color_spaces.DEVICE_RGB,g=this.decode.FLATE_DECODE,m=8;if(this.isArrayBuffer(t)&&(t=new Uint8Array(t)),this.isArrayBufferView(t)){if(e())throw new Error("PNG support requires png.js and zlib.js");if(c=new PNG(t),t=c.imgData,m=c.bits,d=c.colorSpace,u=c.colors,-1!==[4,6].indexOf(c.colorType)){if(8===c.bits)for(var w,v=(O=32==c.pixelBitlength?new Uint32Array(c.decodePixels().buffer):16==c.pixelBitlength?new Uint16Array(c.decodePixels().buffer):new Uint8Array(c.decodePixels().buffer)).length,b=new Uint8Array(v*c.colors),x=new Uint8Array(v),S=c.pixelBitlength-c.bits,k=0,C=0;k<v;k++){for(I=O[k],w=0;w<S;)b[C++]=I>>>w&255,w+=c.bits;x[k]=I>>>w&255}if(16===c.bits){for(var I,v=(O=new Uint32Array(c.decodePixels().buffer)).length,b=new Uint8Array(v*(32/c.pixelBitlength)*c.colors),x=new Uint8Array(v*(32/c.pixelBitlength)),A=c.colors>1,k=0,C=0,T=0;k<v;)I=O[k++],b[C++]=I>>>0&255,A&&(b[C++]=I>>>16&255,I=O[k++],b[C++]=I>>>0&255),x[T++]=I>>>16&255;m=8}n(s)?(t=i(b,c.width*c.colors,c.colors,s),f=i(x,c.width,1,s)):(t=b,f=x,g=null)}if(3===c.colorType&&(d=this.color_spaces.INDEXED,p=c.palette,c.transparency.indexed)){for(var _=c.transparency.indexed,q=0,k=0,v=_.length;k<v;++k)q+=_[k];if((q/=255)===v-1&&-1!==_.indexOf(0))l=[_.indexOf(0)];else if(q!==v){for(var O=c.decodePixels(),x=new Uint8Array(O.length),k=0,v=O.length;k<v;k++)x[k]=_[O[k]];f=i(x,c.width,1)}}var P=y(s);return h=g===this.decode.FLATE_DECODE?"/Predictor "+P+" /Colors "+u+" /BitsPerComponent "+m+" /Columns "+c.width:"/Colors "+u+" /BitsPerComponent "+m+" /Columns "+c.width,(this.isArrayBuffer(t)||this.isArrayBufferView(t))&&(t=this.arrayBufferToBinaryString(t)),(f&&this.isArrayBuffer(f)||this.isArrayBufferView(f))&&(f=this.arrayBufferToBinaryString(f)),this.createImageInfo(t,c.width,c.height,d,m,g,r,o,h,l,p,f,P)}throw new Error("Unsupported PNG image data, try using JPEG instead.")}}(e.API),e.API.autoPrint=function(){var t;return this.internal.events.subscribe("postPutResources",function(){t=this.internal.newObject(),this.internal.write("<< /S/Named /Type/Action /N/Print >>","endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.write("/OpenAction "+t+" 0 R")}),this},/** @preserve
 * jsPDF split_text_to_size plugin - MIT license.
 * Copyright (c) 2012 Willow Systems Corporation, willow-systems.com
 *               2014 Diego Casorran, https://github.com/diegocr
 */
function(t){var e=t.getCharWidthsArray=function(t,e){e||(e={});var n,r=0!==Object.keys(this.internal.getFont().metadata).length,i=t.length,o=[];if(this.internal.getFont().id.slice(1)>=14&&r){var s=this.internal.getFontSize(),a=this.internal.getCharSpace();for(n=0;n<i;n++)o.push(this.internal.getFont().metadata.widthOfString(t[n],s,a)/s)}else{var c=e.widths?e.widths:this.internal.getFont().metadata.Unicode.widths,h=c.fof?c.fof:1,l=e.kerning?e.kerning:this.internal.getFont().metadata.Unicode.kerning,u=l.fof?l.fof:1,p=0,f=0,d=c[0]||h;for(n=0;n<i;n++)p=t.charCodeAt(n),o.push((c[p]||d)/h+(l[p]&&l[p][f]||0)/u),f=p}return o},n=function(t){for(var e=t.length,n=0;e;)n+=t[--e];return n},r=t.getStringUnitWidth=function(t,r){return n(e.call(this,t,r))},i=function(t,e,n,r){for(var i=[],o=0,s=t.length,a=0;o!==s&&a+e[o]<n;)a+=e[o],o++;i.push(t.slice(0,o));var c=o;for(a=0;o!==s;)a+e[o]>r&&(i.push(t.slice(c,o)),a=0,c=o),a+=e[o],o++;return c!==o&&i.push(t.slice(c,o)),i},o=function(t,o,s){s||(s={});var a,c,h,l,u,p,f,d=[],g=[d],m=s.textIndent||0,w=0,y=0,v=t.split(" "),b=e.call(this," ",s)[0];if(p=-1===s.lineIndent?v[0].length+2:s.lineIndent||0){var x=Array(p).join(" "),S=[];v.map(function(t){(t=t.split(/\s*\n/)).length>1?S=S.concat(t.map(function(t,e){return(e&&t.length?"\n":"")+t})):S.push(t[0])}),v=S,p=r(x,s)}for(h=0,l=v.length;h<l;h++){var k=0;if(a=v[h],p&&"\n"===a[0]&&(a=a.substr(1),k=1),c=e.call(this,a,s),y=n(c),m+w+y>o||k){if(y>o){for(u=i(a,c,o-(m+w),o),d.push(u.shift()),d=[u.pop()];u.length;)g.push([u.shift()]);y=n(c.slice(a.length-d[0].length))}else d=[a];g.push(d),m=y+p,w=b}else d.push(a),m+=w+y,w=b}return f=p?function(t,e){return(e?x:"")+t.join(" ")}:function(t){return t.join(" ")},g.map(f)};t.splitTextToSize=function(t,e,n){n||(n={});var r,i=n.fontSize||this.internal.getFontSize(),s=function(t){var e={0:1},n={};if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var r=this.internal.getFont(t.fontName,t.fontStyle);return r.metadata.Unicode?{widths:r.metadata.Unicode.widths||e,kerning:r.metadata.Unicode.kerning||n}:{widths:e,kerning:n}}.call(this,n);r=Array.isArray(t)?t:t.split(/\r?\n/);var a=1*this.internal.scaleFactor*e/i;s.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/i:0,s.lineIndent=n.lineIndent;var c,h,l=[];for(c=0,h=r.length;c<h;c++)l=l.concat(o.call(this,r[c],a,s));return l}}(e.API),/** @preserve 
jsPDF standard_fonts_metrics plugin
Copyright (c) 2012 Willow Systems Corporation, willow-systems.com
MIT license.
*/
function(t){var e=function(t){for(var e={},n=0;n<"klmnopqrstuvwxyz".length;n++)e["klmnopqrstuvwxyz"[n]]="0123456789abcdef"[n];var r,i,o,s,a={},c=1,h=a,l=[],u="",p="",f=t.length-1;for(n=1;n!=f;)s=t[n],n+=1,"'"==s?r?(o=r.join(""),r=void 0):r=[]:r?r.push(s):"{"==s?(l.push([h,o]),h={},o=void 0):"}"==s?((i=l.pop())[0][i[1]]=h,o=void 0,h=i[0]):"-"==s?c=-1:void 0===o?e.hasOwnProperty(s)?(u+=e[s],o=parseInt(u,16)*c,c=1,u=""):u+=s:e.hasOwnProperty(s)?(p+=e[s],h[o]=parseInt(p,16)*c,c=1,o=void 0,p=""):p+=s;return a},n={codePages:["WinAnsiEncoding"],WinAnsiEncoding:e("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},r={Unicode:{Courier:n,"Courier-Bold":n,"Courier-BoldOblique":n,"Courier-Oblique":n,Helvetica:n,"Helvetica-Bold":n,"Helvetica-BoldOblique":n,"Helvetica-Oblique":n,"Times-Roman":n,"Times-Bold":n,"Times-BoldItalic":n,"Times-Italic":n}},i={Unicode:{"Courier-Oblique":e("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":e("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":e("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:e("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":e("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":e("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Helvetica:e("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":e("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),"Courier-Bold":e("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":e("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":e("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":e("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(t){var e,n,o;(e=i.Unicode[t.postScriptName])&&((n=t.metadata.Unicode?t.metadata.Unicode:t.metadata.Unicode={}).widths=e.widths,n.kerning=e.kerning),(o=r.Unicode[t.postScriptName])&&((n=t.metadata.Unicode?t.metadata.Unicode:t.metadata.Unicode={}).encoding=o,o.codePages&&o.codePages.length&&(t.encoding=o.codePages[0]))}])}(e.API),e.API.addSVG=function(t,e,n,r,i){function o(t,e){var n=e.createElement("style");n.type="text/css",n.styleSheet?n.styleSheet.cssText=t:n.appendChild(e.createTextNode(t)),e.getElementsByTagName("head")[0].appendChild(n)}if(void 0===e||void 0===n)throw new Error("addSVG needs values for 'x' and 'y'");var s=function(t,e){var n=(e.contentWindow||e.contentDocument).document;return n.write(t),n.close(),n.getElementsByTagName("svg")[0]}(t,function(t){var e=t.createElement("iframe");return o(".jsPDF_sillysvg_iframe {display:none;position:absolute;}",t),e.name="childframe",e.setAttribute("width",0),e.setAttribute("height",0),e.setAttribute("frameborder","0"),e.setAttribute("scrolling","no"),e.setAttribute("seamless","seamless"),e.setAttribute("class","jsPDF_sillysvg_iframe"),t.body.appendChild(e),e}(document)),a=[1,1],c=parseFloat(s.getAttribute("width")),h=parseFloat(s.getAttribute("height"));c&&h&&(r&&i?a=[r/c,i/h]:r?a=[r/c,r/c]:i&&(a=[i/h,i/h]));var l,u,p,f,d=s.childNodes;for(l=0,u=d.length;l<u;l++)(p=d[l]).tagName&&"PATH"===p.tagName.toUpperCase()&&((f=function(t){for(var e=parseFloat(t[1]),n=parseFloat(t[2]),r=[],i=3,o=t.length;i<o;)"c"===t[i]?(r.push([parseFloat(t[i+1]),parseFloat(t[i+2]),parseFloat(t[i+3]),parseFloat(t[i+4]),parseFloat(t[i+5]),parseFloat(t[i+6])]),i+=7):"l"===t[i]?(r.push([parseFloat(t[i+1]),parseFloat(t[i+2])]),i+=3):i+=1;return[e,n,r]}(p.getAttribute("d").split(" ")))[0]=f[0]*a[0]+e,f[1]=f[1]*a[1]+n,this.lines.call(this,f[2],f[0],f[1],a));return this},e.API.putTotalPages=function(t){for(var e=new RegExp(t,"g"),n=1;n<=this.internal.getNumberOfPages();n++)for(var r=0;r<this.internal.pages[n].length;r++)this.internal.pages[n][r]=this.internal.pages[n][r].replace(e,this.internal.getNumberOfPages());return this},e.API.viewerPreferences=function(e,n){function r(t,e){var n,r=!1;for(n=0;n<t.length;n+=1)t[n]===e&&(r=!0);return r}e=e||{},n=n||!1;var i,o,s,a={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},c=Object.keys(a),h=[],l=0,u=0,p=0,f=!0;if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(a)),this.internal.viewerpreferences.isSubscribed=!1),i=this.internal.viewerpreferences.configuration,"reset"===e||!0===n){var d=c.length;for(p=0;p<d;p+=1)i[c[p]].value=i[c[p]].defaultValue,i[c[p]].explicitSet=!1}if("object"===(void 0===e?"undefined":t(e)))for(o in e)if(s=e[o],r(c,o)&&void 0!==s){if("boolean"===i[o].type&&"boolean"==typeof s)i[o].value=s;else if("name"===i[o].type&&r(i[o].valueSet,s))i[o].value=s;else if("integer"===i[o].type&&Number.isInteger(s))i[o].value=s;else if("array"===i[o].type){for(l=0;l<s.length;l+=1)if(f=!0,1===s[l].length&&"number"==typeof s[l][0])h.push(String(s[l]));else if(s[l].length>1){for(u=0;u<s[l].length;u+=1)"number"!=typeof s[l][u]&&(f=!1);!0===f&&h.push(String(s[l].join("-")))}i[o].value=String(h)}else i[o].value=i[o].defaultValue;i[o].explicitSet=!0}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var t,e=[];for(t in i)!0===i[t].explicitSet&&("name"===i[t].type?e.push("/"+t+" /"+i[t].value):e.push("/"+t+" "+i[t].value));0!==e.length&&this.internal.write("/ViewerPreferences<<\n"+e.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=i,this},/** ==================================================================== 
 * jsPDF XMP metadata plugin
 * Copyright (c) 2016 Jussi Utunen, <EMAIL>
 * 
 * 
 * ====================================================================
 */
function(t){var n="",r="",i="";e.API.addMetadata=function(t,e){return r=e||"http://jspdf.default.namespaceuri/",n=t,this.internal.events.subscribe("postPutResources",function(){if(n){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+r+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),o=unescape(encodeURIComponent(t)),s=unescape(encodeURIComponent(n)),a=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),c=unescape(encodeURIComponent("</x:xmpmeta>")),h=o.length+s.length+a.length+e.length+c.length;i=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+h+" >>"),this.internal.write("stream"),this.internal.write(e+o+s+a+c),this.internal.write("endstream"),this.internal.write("endobj")}else i=""}),this.internal.events.subscribe("putCatalog",function(){i&&this.internal.write("/Metadata "+i+" 0 R")}),this}}(),function(t){var e=function(){function t(t,e,r){var i;if(this.rawData=t,i=this.contents=new n(t),this.contents.pos=4,"ttcf"===i.readString(4)){if(!e)throw new Error("Must specify a font name for TTC files.");throw new Error("Font "+e+" not found in TTC file.")}i.pos=0,this.parse(),this.subset=new S(this),this.registerTTF()}t.open=function(n,r,i,o){var s;return s=e(i),new t(s,r,o)},t.prototype.parse=function(){return this.directory=new r(this.contents),this.head=new a(this),this.name=new d(this),this.cmap=new c(this),this.hhea=new l(this),this.maxp=new m(this),this.hmtx=new w(this),this.post=new p(this),this.os2=new u(this),this.loca=new x(this),this.glyf=new y(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax],this},t.prototype.registerTTF=function(){var t,e,n,r,i;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=function(){var e,n,r,i;for(i=[],e=0,n=(r=this.bbox).length;e<n;e++)t=r[e],i.push(Math.round(t*this.scaleFactor));return i}.call(this),this.stemV=0,this.post.exists?(n=255&(r=this.post.italic_angle),!0&(e=r>>16)&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+n)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(i=this.familyClass)||2===i||3===i||4===i||5===i||7===i,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(t){var e;return(void 0!==(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},t.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},t.prototype.widthOfString=function(t,e,n){var r,i,o,s,a,c;for(s=0,i=a=0,c=(t=""+t).length;0<=c?a<c:a>c;i=0<=c?++a:--a)r=t.charCodeAt(i),s+=this.widthOfGlyph(this.characterToGlyph(r))+n*(1e3/e)||0;return o=e/1e3,s*o},t.prototype.lineHeight=function(t,e){var n;return void 0===e&&(e=!1),n=e?this.lineGap:0,(this.ascender+n-this.decender)/1e3*t},t.prototype.encode=function(t,e,n){return t.use(e),e=n?f(t.encodeText(e)):t.encodeText(e),e=function(){for(var t=[],n=0,r=e.length;0<=r?n<r:n>r;0<=r?n++:n--)t.push(e.charCodeAt(n).toString(16));return t}().join("")},t.prototype.embedTTF=function(t,e,n){function r(t){var i;return"Font"===t.Type?(g&&(t.ToUnicode=r(t.ToUnicode)+" 0 R"),t.FontDescriptor=r(t.FontDescriptor)+" 0 R",i=e(),n(I.convert(t))):"FontDescriptor"===t.Type?(t.FontFile2=r(t.FontFile2)+" 0 R",i=e(),n(I.convert(t))):(i=e(),n("<</Length1 "+t.length+">>"),n("stream"),n(Array.isArray(t)||t.constructor===Uint8Array?o(t):t),n("endstream")),n("endobj"),i}var i,a,c,l,u,p,f,d,g="MacRomanEncoding"===t;return l=this.subset.encode(),f={},f=g?l:this.rawData,u={Type:"FontDescriptor",FontName:this.subset.postscriptName,FontFile2:f,FontBBox:this.bbox,Flags:this.flags,StemV:this.stemV,ItalicAngle:this.italicAngle,Ascent:this.ascender,Descent:this.decender,CapHeight:this.capHeight,XHeight:this.xHeight},(33==(p=+Object.keys(this.subset.cmap)[0])||!g)&&(i=function(){var t,e;t=this.subset.cmap,e=[];for(c in t)Object.prototype.hasOwnProperty.call(t,c)&&(d=t[c],e.push(Math.round(this.widthOfGlyph(d))));return e}.call(this),a=h(this.subset.subset),r(g?{Type:"Font",BaseFont:this.subset.postscriptName,Subtype:"TrueType",FontDescriptor:u,FirstChar:p,LastChar:p+i.length-1,Widths:i,Encoding:t,ToUnicode:a}:{Type:"Font",BaseFont:this.subset.postscriptName,Subtype:"TrueType",FontDescriptor:u,FirstChar:0,LastChar:255,Widths:s(this),Encoding:t}))};var e=function(t){function e(t){c[l++]=t}var n,r,o,s,a,c;if(t.length%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var h=t.length;a="="===t.charAt(h-2)?2:"="===t.charAt(h-1)?1:0,c=new Uint8Array(3*t.length/4-a),o=a>0?t.length-4:t.length;var l=0;for(n=0,r=0;n<o;n+=4,r+=3)e((16711680&(s=i(t.charAt(n))<<18|i(t.charAt(n+1))<<12|i(t.charAt(n+2))<<6|i(t.charAt(n+3))))>>16),e((65280&s)>>8),e(255&s);return 2===a?e(255&(s=i(t.charAt(n))<<2|i(t.charAt(n+1))>>4)):1===a&&(e((s=i(t.charAt(n))<<10|i(t.charAt(n+1))<<4|i(t.charAt(n+2))>>2)>>8&255),e(255&s)),c},i=function(t){var e="+".charCodeAt(0),n="/".charCodeAt(0),r="0".charCodeAt(0),i="a".charCodeAt(0),o="A".charCodeAt(0),s="-".charCodeAt(0),a="_".charCodeAt(0),c=t.charCodeAt(0);return c===e||c===s?62:c===n||c===a?63:c<r?-1:c<r+10?c-r+26+26:c<o+26?c-o:c<i+26?c-i+26:void 0},o=function(t){for(var e=[],n=0,r=t.length;n<r;n++)e.push(String.fromCharCode(t[n]));return e.join("")},s=function(t){for(var e=[],n=0;n<256;n++)e[n]=0;var r=1e3/t.head.unitsPerEm,i=t.cmap.unicode.codeMap,o={402:131,8211:150,8212:151,8216:145,8217:146,8218:130,8220:147,8221:148,8222:132,8224:134,8225:135,8226:149,8230:133,8364:128,8240:137,8249:139,8250:155,710:136,8482:153,338:140,339:156,732:152,352:138,353:154,376:159,381:142,382:158};return Object.keys(i).map(function(n){var s=o[n],a=Math.round(t.hmtx.metrics[i[n]].advance*r);s?e[s]=a:n<256&&(e[n]=a)}),e},h=function(t){var e,n,r,i,o,s,a;for(o="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<00><ff>\nendcodespacerange",r=[],s=0,a=(n=Object.keys(t).sort(function(t,e){return t-e})).length;s<a;s++)e=n[s],r.length>=100&&(o+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar",r=[]),i=("0000"+t[e].toString(16)).slice(-4),e=(+e).toString(16),r.push("<"+e+"><"+i+">");return r.length&&(o+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar\n"),o+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"},f=function(t){return t.split("").reverse().join("")};return t}(),n=function(){function t(t){this.data=void 0!==t?t:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(t){return this.data[this.pos++]=t,this},t.prototype.readUInt32=function(){var t,e,n,r;return t=16777216*this.readByte(),e=this.readByte()<<16,n=this.readByte()<<8,r=this.readByte(),t+e+n+r},t.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=2147483648?t-4294967296:t},t.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},t.prototype.readUInt16=function(){var t,e;return t=this.readByte()<<8,e=this.readByte(),t|e},t.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},t.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},t.prototype.readString=function(t){var e,n,r;for(n=[],e=r=0;0<=t?r<t:r>t;e=0<=t?++r:--r)n[e]=String.fromCharCode(this.readByte());return n.join("")},t.prototype.writeString=function(t){var e,n,r,i;for(i=[],e=n=0,r=t.length;0<=r?n<r:n>r;e=0<=r?++n:--n)i.push(this.writeByte(t.charCodeAt(e)));return i},t.prototype.stringAt=function(t,e){return this.pos=t,this.readString(e)},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(t){return this.writeInt16(t)},t.prototype.readLongLong=function(){var t,e,n,r,i,o,s,a;return t=this.readByte(),e=this.readByte(),n=this.readByte(),r=this.readByte(),i=this.readByte(),o=this.readByte(),s=this.readByte(),a=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^n)+4294967296*(255^r)+16777216*(255^i)+65536*(255^o)+256*(255^s)+(255^a)+1):72057594037927940*t+281474976710656*e+1099511627776*n+4294967296*r+16777216*i+65536*o+256*s+a},t.prototype.writeLongLong=function(t){var e,n;return e=Math.floor(t/4294967296),n=4294967295&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(t){return this.writeInt32(t)},t.prototype.slice=function(t,e){return this.data.slice(t,e)},t.prototype.read=function(t){var e,n;for(e=[],n=0;0<=t?n<t:n>t;0<=t?++n:--n)e.push(this.readByte());return e},t.prototype.write=function(t){var e,n,r,i;for(i=[],n=0,r=t.length;n<r;n++)e=t[n],i.push(this.writeByte(e));return i},t}(),r=function(){function t(t){var e,n,r;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},n=0,r=this.tableCount;0<=r?n<r:n>r;0<=r?++n:--n)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}var e;return t.prototype.encode=function(t){var r,i,o,s,a,c,h,l,u,p,f,d,g,m;d=Object.keys(t).length,c=Math.log(2),u=16*Math.floor(Math.log(d)/c),s=Math.floor(u/c),l=16*d-u,(i=new n).writeInt(this.scalarType),i.writeShort(d),i.writeShort(u),i.writeShort(s),i.writeShort(l),o=16*d,h=i.pos+o,a=null,g=[];for(m in t)if(Object.prototype.hasOwnProperty.call(t,m))for(f=t[m],i.writeString(m),i.writeInt(e(f)),i.writeInt(h),i.writeInt(f.length),g=g.concat(f),"head"===m&&(a=h),h+=f.length;h%4;)g.push(0),h++;return i.write(g),p=e(i.data),r=2981146554-p,i.pos=a+8,i.writeUInt32(r),i.data},e=function(t){var e,r,o,s;for(t=i.call(t);t.length%4;)t.push(0);for(r=new n(t),e=0,o=0,s=t.length;o<s;o+=4)e+=r.readUInt32();return 4294967295&e},t}(),i=[].slice,o=function(t,e){function n(){this.constructor=t}for(var r in e)({}).hasOwnProperty.call(e,r)&&(t[r]=e[r]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},s=function(){function t(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}(),a=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="head",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort(),this},e.prototype.encode=function(t){var e;return(e=new n).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t.type),e.writeShort(this.glyphDataFormat),e.data},e}(),c=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="cmap",e.prototype.parse=function(t){var e,n,r;for(t.pos=this.offset,this.version=t.readUInt16(),n=t.readUInt16(),this.tables=[],r=0;0<=n?r<n:r>n;0<=n?++r:--r)e=new h(t,this.offset),this.tables.push(e),e.isUnicode&&void 0===this.unicode&&(this.unicode=e);return!0},e.encode=function(t,e){var r,i;return void 0===e&&(e="macroman"),r=h.encode(t,e),(i=new n).writeUInt16(0),i.writeUInt16(1),r.table=i.data.concat(r.subtable),r},e}(),h=function(){function t(t,e){var n,r,i,o,s,a,c,h,l,u,p,f,d,g,m,w,y,v;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),l=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(a=m=0;m<256;a=++m)this.codeMap[a]=t.readByte();break;case 4:for(p=t.readUInt16(),u=p/2,t.pos+=6,i=function(){var e,n;for(n=[],a=e=0;0<=u?e<u:e>u;a=0<=u?++e:--e)n.push(t.readUInt16());return n}(),t.pos+=2,d=function(){var e,n;for(n=[],a=e=0;0<=u?e<u:e>u;a=0<=u?++e:--e)n.push(t.readUInt16());return n}(),c=function(){var e,n;for(n=[],a=e=0;0<=u?e<u:e>u;a=0<=u?++e:--e)n.push(t.readUInt16());return n}(),h=function(){var e,n;for(n=[],a=e=0;0<=u?e<u:e>u;a=0<=u?++e:--e)n.push(t.readUInt16());return n}(),r=(this.length-t.pos+this.offset)/2,s=function(){var e,n;for(n=[],a=e=0;0<=r?e<r:e>r;a=0<=r?++e:--e)n.push(t.readUInt16());return n}(),a=w=0,v=i.length;w<v;a=++w)for(g=i[a],n=y=f=d[a];f<=g?y<=g:y>=g;n=f<=g?++y:--y)0===h[a]?o=n+c[a]:0!==(o=s[h[a]/2+(n-f)-(u-a)]||0)&&(o+=c[a]),this.codeMap[n]=65535&o}t.pos=l}return t.encode=function(t,e){var r,i,o,s,a,c,h,l,u,p,f,d,g,m,w,y,v,b,x,S,k,C,I,A,T,_,q,O,P,F,E,B,R,D,j,M,U,N,L,z,H,W,V,G,X,Y;switch(O=new n,s=Object.keys(t).sort(function(t,e){return t-e}),e){case"macroman":for(g=0,m=function(){var t,e;for(e=[],d=t=0;t<256;d=++t)e.push(0);return e}(),y={0:0},o={},P=0,R=s.length;P<R;P++)void 0===y[V=t[i=s[P]]]&&(y[V]=++g),o[i]={old:t[i],new:y[t[i]]},m[i]=y[t[i]];return O.writeUInt16(1),O.writeUInt16(0),O.writeUInt32(12),O.writeUInt16(0),O.writeUInt16(262),O.writeUInt16(0),O.write(m),{charMap:o,subtable:O.data,maxGlyphID:g+1};case"unicode":for(_=[],u=[],v=0,y={},r={},F=0,D=s.length;F<D;F++)void 0===y[x=t[i=s[F]]]&&(y[x]=++v),r[i]={old:x,new:y[x]},a=y[x]-i,void 0!==w&&a===h||(w&&u.push(w),_.push(i),h=a),w=i;for(w&&u.push(w),u.push(65535),_.push(65535),A=2*(I=_.length),C=2*Math.pow(Math.log(I)/Math.LN2,2),p=Math.log(C/2)/Math.LN2,k=2*I-C,c=[],S=[],f=[],d=E=0,j=_.length;E<j;d=++E){if(T=_[d],l=u[d],65535===T){c.push(0),S.push(0);break}if(q=r[T].new,T-q>=32768)for(c.push(0),S.push(2*(f.length+I-d)),i=B=T;T<=l?B<=l:B>=l;i=T<=l?++B:--B)f.push(r[i].new);else c.push(q-T),S.push(0)}for(O.writeUInt16(3),O.writeUInt16(1),O.writeUInt32(12),O.writeUInt16(4),O.writeUInt16(16+8*I+2*f.length),O.writeUInt16(0),O.writeUInt16(A),O.writeUInt16(C),O.writeUInt16(p),O.writeUInt16(k),H=0,M=u.length;H<M;H++)i=u[H],O.writeUInt16(i);for(O.writeUInt16(0),W=0,U=_.length;W<U;W++)i=_[W],O.writeUInt16(i);for(G=0,N=c.length;G<N;G++)a=c[G],O.writeUInt16(a);for(X=0,L=S.length;X<L;X++)b=S[X],O.writeUInt16(b);for(Y=0,z=f.length;Y<z;Y++)g=f[Y],O.writeUInt16(g);return{charMap:r,subtable:O.data,maxGlyphID:v+1}}},t}(),l=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="hhea",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16(),this},e.prototype.encode=function(t){var e,r;for((e=new n).writeInt(this.version),e.writeShort(this.ascender),e.writeShort(this.decender),e.writeShort(this.lineGap),e.writeShort(this.advanceWidthMax),e.writeShort(this.minLeftSideBearing),e.writeShort(this.minRightSideBearing),e.writeShort(this.xMaxExtent),e.writeShort(this.caretSlopeRise),e.writeShort(this.caretSlopeRun),e.writeShort(this.caretOffset),r=0,8;r<8;++r)e.writeByte(0);return e.writeShort(this.metricDataFormat),e.writeUInt16(t.length),e.data},e}(),u=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="OS/2",e.prototype.parse=function(t){var e;if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=function(){var n,r;for(r=[],e=n=0;n<10;e=++n)r.push(t.readByte());return r}(),this.charRange=function(){var n,r;for(r=[],e=n=0;n<4;e=++n)r.push(t.readInt());return r}(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=function(){var n,r;for(r=[],e=n=0;n<2;e=++n)r.push(t.readInt());return r}(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort(),this},e.prototype.encode=function(){return this.raw()},e}(),p=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}var r;return o(e,s),e.prototype.tag="post",e.prototype.parse=function(t){var e,n,r,i,o;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:break;case 131072:for(r=t.readUInt16(),this.glyphNameIndex=[],e=i=0;0<=r?i<r:i>r;e=0<=r?++i:--i)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],o=[];t.pos<this.offset+this.length;)n=t.readByte(),o.push(this.names.push(t.readString(n)));return o;case 151552:return r=t.readUInt16(),this.offsets=t.read(r),this.offsets;case 196608:break;case 262144:return this.map=function(){var n,r,i;for(i=[],e=n=0,r=this.file.maxp.numGlyphs;0<=r?n<r:n>r;e=0<=r?++n:--n)i.push(t.readUInt32());return i}.call(this),this.map}},e.prototype.glyphFor=function(t){var e;switch(this.format){case 65536:return r[t]||".notdef";case 131072:return(e=this.glyphNameIndex[t])<=257?r[e]:this.names[e-258]||".notdef";case 151552:return r[t+this.offsets[t]]||".notdef";case 196608:return".notdef";case 262144:return this.map[t]||65535}},e.prototype.encode=function(t){var e,i,o,s,a,c,h,l,u,p,f,d,g,m,w;if(!this.exists)return null;if(c=this.raw(),196608===this.format)return c;for((u=new n(c.slice(0,32))).writeUInt32(131072),u.pos=32,o=[],l=[],p=0,g=t.length;p<g;p++)e=t[p],a=this.glyphFor(e),-1!==(s=r.indexOf(a))?o.push(s):(o.push(257+l.length),l.push(a));for(u.writeUInt16(Object.keys(t).length),f=0,m=o.length;f<m;f++)i=o[f],u.writeUInt16(i);for(d=0,w=l.length;d<w;d++)h=l[d],u.writeByte(h.length),u.writeString(h);return u.data},r=".notdef .null nonmarkingreturn space exclam quotedbl numbersign dollar percent\nampersand quotesingle parenleft parenright asterisk plus comma hyphen period slash\nzero one two three four five six seven eight nine colon semicolon less equal greater\nquestion at A B C D E F G H I J K L M N O P Q R S T U V W X Y Z\nbracketleft backslash bracketright asciicircum underscore grave\na b c d e f g h i j k l m n o p q r s t u v w x y z\nbraceleft bar braceright asciitilde Adieresis Aring Ccedilla Eacute Ntilde Odieresis\nUdieresis aacute agrave acircumflex adieresis atilde aring ccedilla eacute egrave\necircumflex edieresis iacute igrave icircumflex idieresis ntilde oacute ograve\nocircumflex odieresis otilde uacute ugrave ucircumflex udieresis dagger degree cent\nsterling section bullet paragraph germandbls registered copyright trademark acute\ndieresis notequal AE Oslash infinity plusminus lessequal greaterequal yen mu\npartialdiff summation product pi integral ordfeminine ordmasculine Omega ae oslash\nquestiondown exclamdown logicalnot radical florin approxequal Delta guillemotleft\nguillemotright ellipsis nonbreakingspace Agrave Atilde Otilde OE oe endash emdash\nquotedblleft quotedblright quoteleft quoteright divide lozenge ydieresis Ydieresis\nfraction currency guilsinglleft guilsinglright fi fl daggerdbl periodcentered\nquotesinglbase quotedblbase perthousand Acircumflex Ecircumflex Aacute Edieresis\nEgrave Iacute Icircumflex Idieresis Igrave Oacute Ocircumflex apple Ograve Uacute\nUcircumflex Ugrave dotlessi circumflex tilde macron breve dotaccent ring cedilla\nhungarumlaut ogonek caron Lslash lslash Scaron scaron Zcaron zcaron brokenbar Eth\neth Yacute yacute Thorn thorn minus multiply onesuperior twosuperior threesuperior\nonehalf onequarter threequarters franc Gbreve gbreve Idotaccent Scedilla scedilla\nCacute cacute Ccaron ccaron dcroat".split(/\s+/g),e}(),f=function(){return function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID}}(),d=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}var r;return o(e,s),e.prototype.tag="name",e.prototype.parse=function(t){var e,n,r,i,o,s,a,c,h,l,u,p;for(t.pos=this.offset,t.readShort(),e=t.readShort(),s=t.readShort(),n=[],i=h=0;0<=e?h<e:h>e;i=0<=e?++h:--h)n.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+s+t.readShort()});for(a={},i=l=0,u=n.length;l<u;i=++l)r=n[i],t.pos=r.offset,c=t.readString(r.length),o=new f(c,r),void 0===a[p=r.nameID]&&(a[p]=[]),a[r.nameID].push(o);return this.strings=a,this.copyright=a[0],this.fontFamily=a[1],this.fontSubfamily=a[2],this.uniqueSubfamily=a[3],this.fontName=a[4],this.version=a[5],this.postscriptName=a[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,""),this.trademark=a[7],this.manufacturer=a[8],this.designer=a[9],this.description=a[10],this.vendorUrl=a[11],this.designerUrl=a[12],this.license=a[13],this.licenseUrl=a[14],this.preferredFamily=a[15],this.preferredSubfamily=a[17],this.compatibleFull=a[18],this.sampleText=a[19],this},r="AAAAAA",e.prototype.encode=function(){var t,e,i,o,s,a,c,h,l,u,p,d,m;h={},m=this.strings;for(t in m)Object.prototype.hasOwnProperty.call(m,t)&&(u=m[t],h[t]=u);o=new f(r+"+"+this.postscriptName,{platformID:1,encodingID:0,languageID:0}),h[6]=[o],r=g(r),s=0;for(t in h)Object.prototype.hasOwnProperty.call(h,t)&&null!==(e=h[t])&&(s+=e.length);l=new n,a=new n,l.writeShort(0),l.writeShort(s),l.writeShort(6+12*s);for(i in h)if(Object.prototype.hasOwnProperty.call(h,i)&&null!==(e=h[i]))for(p=0,d=e.length;p<d;p++)c=e[p],l.writeShort(c.platformID),l.writeShort(c.encodingID),l.writeShort(c.languageID),l.writeShort(i),l.writeShort(c.length),l.writeShort(a.pos),a.writeString(c.raw);return{postscriptName:o.raw,table:l.data.concat(a.data)}},e}(),g=function(t){var e,n,r,i,o,s,a,c,h;for(a=(e="abcdefghijklmnopqrstuvwxyz").length,h=t,r=t.length;r>=0;){if(s=t.charAt(--r),isNaN(s)){if(-1===(i=e.indexOf(s.toLowerCase())))c=s,n=!0;else if(c=e.charAt((i+1)%a),(o=s===s.toUpperCase())&&(c=c.toUpperCase()),(n=i+1>=a)&&0===r){h=(o?"A":"a")+c+h.slice(1);break}}else if(c=+s+1,(n=c>9)&&(c=0),n&&0===r){h="1"+c+h.slice(1);break}if(h=h.slice(0,r)+c+h.slice(r+1),!n)break}return h},m=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="maxp",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16(),this},e.prototype.encode=function(t){var e;return(e=new n).writeInt(this.version),e.writeUInt16(t.length),e.writeUInt16(this.maxPoints),e.writeUInt16(this.maxContours),e.writeUInt16(this.maxCompositePoints),e.writeUInt16(this.maxComponentContours),e.writeUInt16(this.maxZones),e.writeUInt16(this.maxTwilightPoints),e.writeUInt16(this.maxStorage),e.writeUInt16(this.maxFunctionDefs),e.writeUInt16(this.maxInstructionDefs),e.writeUInt16(this.maxStackElements),e.writeUInt16(this.maxSizeOfInstructions),e.writeUInt16(this.maxComponentElements),e.writeUInt16(this.maxComponentDepth),e.data},e}(),w=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="hmtx",e.prototype.parse=function(t){var e,n,r,i,o,s,a,c;for(t.pos=this.offset,this.metrics=[],e=o=0,a=this.file.hhea.numberOfMetrics;0<=a?o<a:o>a;e=0<=a?++o:--o)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(r=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var n,i;for(i=[],e=n=0;0<=r?n<r:n>r;e=0<=r?++n:--n)i.push(t.readInt16());return i}(),this.widths=function(){var t,e,n,r;for(r=[],t=0,e=(n=this.metrics).length;t<e;t++)i=n[t],r.push(i.advance);return r}.call(this),n=this.widths[this.widths.length-1],c=[],e=s=0;0<=r?s<r:s>r;e=0<=r?++s:--s)c.push(this.widths.push(n));return c},e.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},e.prototype.encode=function(t){var e,r,i,o,s;for(i=new n,o=0,s=t.length;o<s;o++)e=t[o],r=this.forGlyph(e),i.writeUInt16(r.advance),i.writeUInt16(r.lsb);return i.data},e}(),y=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="glyf",e.prototype.parse=function(t){return this.cache={},this.cache},e.prototype.glyphFor=function(t){var e,r,i,o,s,a,c,h,l,u;return t in this.cache?this.cache[t]:(o=this.file.loca,e=this.file.contents,r=o.indexOf(t),0===(i=o.lengthOf(t))?(this.cache[t]=null,this.cache[t]):(e.pos=this.offset+r,a=new n(e.read(i)),s=a.readShort(),h=a.readShort(),u=a.readShort(),c=a.readShort(),l=a.readShort(),this.cache[t]=-1===s?new b(a,h,u,c,l):new v(a,s,h,u,c,l),this.cache[t]))},e.prototype.encode=function(t,e,n){var r,i,o,s,a;for(o=[],i=[],s=0,a=e.length;s<a;s++)r=t[e[s]],i.push(o.length),r&&(o=o.concat(r.encode(n)));return i.push(o.length),{table:o,offsets:i}},e}(),v=function(){function t(t,e,n,r,i,o){this.raw=t,this.numberOfContours=e,this.xMin=n,this.yMin=r,this.xMax=i,this.yMax=o,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),b=function(){function t(t,n,i,c,h){var l,u;for(this.raw=t,this.xMin=n,this.yMin=i,this.xMax=c,this.yMax=h,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],l=this.raw;;){if(u=l.readShort(),this.glyphOffsets.push(l.pos),this.glyphIDs.push(l.readShort()),!(u&r))break;l.pos+=u&e?4:2,u&a?l.pos+=8:u&o?l.pos+=4:u&s&&(l.pos+=2)}}var e,r,o,s,a;return e=1,s=8,r=32,o=64,a=128,t.prototype.encode=function(t){var e,r,o,s,a,c;for(o=new n(i.call(this.raw.data)),e=s=0,a=(c=this.glyphIDs).length;s<a;e=++s)r=c[e],o.pos=this.glyphOffsets[e],o.writeShort(t[r]);return o.data},t}(),x=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return o(e,s),e.prototype.tag="loca",e.prototype.parse=function(t){var e;return t.pos=this.offset,0===this.file.head.indexToLocFormat?(this.offsets=function(){var n,r,i;for(i=[],e=n=0,r=this.length;n<r;e=n+=2)i.push(2*t.readUInt16());return i}.call(this),this):(this.offsets=function(){var n,r,i;for(i=[],e=n=0,r=this.length;n<r;e=n+=4)i.push(t.readUInt32());return i}.call(this),this)},e.prototype.indexOf=function(t){return this.offsets[t]},e.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},e.prototype.encode=function(t){var e,r,i,o,s,a,c,h,l;for(r=new n,i=0,a=t.length;i<a;i++)if(t[i]>65535){for(o=0,c=(l=this.offsets).length;o<c;o++)e=l[o],r.writeUInt32(e);return{format:1,table:r.data}}for(s=0,h=t.length;s<h;s++)e=t[s],r.writeUInt16(e/2);return{format:0,table:r.data}},e}(),S=function(){function t(t){this.font=t,this.subset={},this.unicodes={},this.unicodeCmap={},this.next=33}return t.prototype.use=function(t){var e,n,r;if("string"!=typeof t){if(!this.unicodes[t])return this.subset[this.next]=t,this.unicodes[t]=this.next++,this}else for(e=n=0,r=t.length;0<=r?n<r:n>r;e=0<=r?++n:--n)this.use(t.charCodeAt(e))},t.prototype.encodeText=function(t){var e,n,r,i,o;for(r="",n=i=0,o=t.length;0<=o?i<o:i>o;n=0<=o?++i:--i)e=this.unicodes[t.charCodeAt(n)],r+=String.fromCharCode(e);return r},t.prototype.generateCmap=function(){var t,e,n,r,i;r=this.font.cmap.tables[0].codeMap,t={},i=this.subset;for(e in i)Object.prototype.hasOwnProperty.call(i,e)&&(n=i[e],t[e]=r[n]);return t},t.prototype.glyphIDs=function(){var t,e,n,r,i;n=this.font.cmap.tables[0].codeMap,t=[0],i=this.subset;for(e in i)Object.prototype.hasOwnProperty.call(i,e)&&null!==(r=n[i[e]])&&k.call(t,r)<0&&t.push(r);return t.sort()},t.prototype.glyphsFor=function(t){var e,n,r,i,o,s,a;for(r={},o=0,s=t.length;o<s;o++)r[i=t[o]]=this.font.glyf.glyphFor(i);e=[];for(i in r)Object.prototype.hasOwnProperty.call(r,i)&&(null!==(n=r[i])?n.compound:void 0)&&e.push.apply(e,n.glyphIDs);if(e.length>0){a=this.glyphsFor(e);for(i in a)Object.prototype.hasOwnProperty.call(a,i)&&(n=a[i],r[i]=n)}return r},t.prototype.encode=function(){var t,e,n,r,i,o,s,a,h,l,u,p,f,d,g,m,w;t=c.encode(this.generateCmap(),"unicode"),r=this.glyphsFor(this.glyphIDs()),p={0:0},m=t.charMap;for(e in m)Object.prototype.hasOwnProperty.call(m,e)&&(p[(o=m[e]).old]=o.new);u=t.maxGlyphID;for(f in r)f in p||(p[f]=u++);h=C(p),l=Object.keys(h).sort(function(t,e){return t-e}),d=function(){var t,e,n;for(n=[],t=0,e=l.length;t<e;t++)i=l[t],n.push(h[i]);return n}(),n=this.font.glyf.encode(r,d,p),s=this.font.loca.encode(n.offsets),a=this.font.name.encode(),this.postscriptName=a.postscriptName,this.cmap={},w=t.charMap;for(e in w)Object.prototype.hasOwnProperty.call(w,e)&&(o=w[e],this.cmap[e]=o.old);return g={cmap:t.table,glyf:n.table,loca:s.table,hmtx:this.font.hmtx.encode(d),hhea:this.font.hhea.encode(d),maxp:this.font.maxp.encode(d),post:this.font.post.encode(d),name:a.table,head:this.font.head.encode(s)},this.font.os2.exists&&(g["OS/2"]=this.font.os2.raw()),this.font.directory.encode(g)},t}(),k=[].indexOf||function(t){for(var e=0,n=this.length;e<n;e++)if(e in this&&this[e]===t)return e;return-1},C=function(t){var e,n;n={};for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[t[e]]=e);return n},I=function(){function t(){}var e;return e=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},t.convert=function(n){var r,i,o,s;if(Array.isArray(n))return"["+function(){var e,i,o;for(o=[],e=0,i=n.length;e<i;e++)r=n[e],o.push(t.convert(r));return o}().join(" ")+"]";if("string"==typeof n)return-1===n.indexOf(" 0 R")?"/"+n:n;if(void 0!==n?n.isString:void 0)return"("+n+")";if(n instanceof Date)return"(D:"+e(n.getUTCFullYear(),4)+e(n.getUTCMonth(),2)+e(n.getUTCDate(),2)+e(n.getUTCHours(),2)+e(n.getUTCMinutes(),2)+e(n.getUTCSeconds(),2)+"Z)";if("[object Object]"==={}.toString.call(n)){o=["<<"];for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&(s=n[i],o.push("/"+i+" "+t.convert(s)));return o.push(">>"),o.join("\n")}return""+n},t}();t.events.push(["addFont",function(n){t.existsFileInVFS(n.postScriptName)?(n.metadata=e.open(n.postScriptName,n.fontName,t.getFileFromVFS(n.postScriptName),n.encoding),n.encoding=n.metadata.hmtx.widths.length<500&&n.metadata.capHeight<800?"WinAnsiEncoding":"MacRomanEncoding"):n.id.slice(1)>=14&&console.error("Font does not exist in FileInVFS, import fonts or remove declaration doc.addFont('"+n.postScriptName+"').")}])}(e.API),function(t){var e={};t.existsFileInVFS=function(t){return e.hasOwnProperty(t)},t.addFileToVFS=function(t,n){return e[t]=n,this},t.getFileFromVFS=function(t){return e.hasOwnProperty(t)?e[t]:null}}(e.API),function(t){if(t.URL=t.URL||t.webkitURL,t.Blob&&t.URL)try{return void new Blob}catch(t){}var e=t.BlobBuilder||t.WebKitBlobBuilder||t.MozBlobBuilder||function(t){var e=function(t){return Object.prototype.toString.call(t).match(/^\[object\s(.*)\]$/)[1]},n=function(){this.data=[]},r=function(t,e,n){this.data=t,this.size=t.length,this.type=e,this.encoding=n},i=n.prototype,o=r.prototype,s=t.FileReaderSync,a=function(t){this.code=this[this.name=t]},c="NOT_FOUND_ERR SECURITY_ERR ABORT_ERR NOT_READABLE_ERR ENCODING_ERR NO_MODIFICATION_ALLOWED_ERR INVALID_STATE_ERR SYNTAX_ERR".split(" "),h=c.length,l=t.URL||t.webkitURL||t,u=l.createObjectURL,p=l.revokeObjectURL,f=l,d=t.btoa,g=t.atob,m=t.ArrayBuffer,w=t.Uint8Array,y=/^[\w-]+:\/*\[?[\w\.:-]+\]?(?::[0-9]+)?/;for(r.fake=o.fake=!0;h--;)a.prototype[c[h]]=h+1;return l.createObjectURL||(f=t.URL=function(t){var e,n=document.createElementNS("http://www.w3.org/1999/xhtml","a");return n.href=t,"origin"in n||("data:"===n.protocol.toLowerCase()?n.origin=null:(e=t.match(y),n.origin=e&&e[1])),n}),f.createObjectURL=function(t){var e,n=t.type;return null===n&&(n="application/octet-stream"),t instanceof r?(e="data:"+n,"base64"===t.encoding?e+";base64,"+t.data:"URI"===t.encoding?e+","+decodeURIComponent(t.data):d?e+";base64,"+d(t.data):e+","+encodeURIComponent(t.data)):u?u.call(l,t):void 0},f.revokeObjectURL=function(t){"data:"!==t.substring(0,5)&&p&&p.call(l,t)},i.append=function(t){var n=this.data;if(w&&(t instanceof m||t instanceof w)){for(var i="",o=new w(t),c=0,h=o.length;c<h;c++)i+=String.fromCharCode(o[c]);n.push(i)}else if("Blob"===e(t)||"File"===e(t)){if(!s)throw new a("NOT_READABLE_ERR");var l=new s;n.push(l.readAsBinaryString(t))}else t instanceof r?"base64"===t.encoding&&g?n.push(g(t.data)):"URI"===t.encoding?n.push(decodeURIComponent(t.data)):"raw"===t.encoding&&n.push(t.data):("string"!=typeof t&&(t+=""),n.push(unescape(encodeURIComponent(t))))},i.getBlob=function(t){return arguments.length||(t=null),new r(this.data.join(""),t,"raw")},i.toString=function(){return"[object BlobBuilder]"},o.slice=function(t,e,n){var i=arguments.length;return i<3&&(n=null),new r(this.data.slice(t,i>1?e:this.data.length),n,this.encoding)},o.toString=function(){return"[object Blob]"},o.close=function(){this.size=0,delete this.data},n}(t);t.Blob=function(t,n){var r=n?n.type||"":"",i=new e;if(t)for(var o=0,s=t.length;o<s;o++)Uint8Array&&t[o]instanceof Uint8Array?i.append(t[o].buffer):i.append(t[o]);var a=i.getBlob(r);return!a.slice&&a.webkitSlice&&(a.slice=a.webkitSlice),a};var n=Object.getPrototypeOf||function(t){return t.__proto__};t.Blob.prototype=n(new t.Blob)}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||window.content||window);var r=r||function(t){if(!(void 0===t||"undefined"!=typeof navigator&&/MSIE [1-9]\./.test(navigator.userAgent))){var e=function(){return t.URL||t.webkitURL||t},n=t.document.createElementNS("http://www.w3.org/1999/xhtml","a"),r="download"in n,i=function(t){var e=new MouseEvent("click");t.dispatchEvent(e)},o=/constructor/i.test(t.HTMLElement)||t.safari,s=/CriOS\/[\d]+/.test(navigator.userAgent),a=function(e){(t.setImmediate||t.setTimeout)(function(){throw e},0)},c=function(t){setTimeout(function(){"string"==typeof t?e().revokeObjectURL(t):t.remove()},4e4)},h=function(t,e,n){for(var r=(e=[].concat(e)).length;r--;){var i=t["on"+e[r]];if("function"==typeof i)try{i.call(t,n||t)}catch(t){a(t)}}},l=function(t){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t},u=function(a,u,p){p||(a=l(a));var f,d=this,g="application/octet-stream"===a.type,m=function(){h(d,"writestart progress write writeend".split(" "))};if(d.readyState=d.INIT,r)return f=e().createObjectURL(a),void setTimeout(function(){n.href=f,n.download=u,i(n),m(),c(f),d.readyState=d.DONE});!function(){if((s||g&&o)&&t.FileReader){var n=new FileReader;return n.onloadend=function(){var e=s?n.result:n.result.replace(/^data:[^;]*;/,"data:attachment/file;");t.open(e,"_blank")||(t.location.href=e),e=void 0,d.readyState=d.DONE,m()},n.readAsDataURL(a),void(d.readyState=d.INIT)}f||(f=e().createObjectURL(a)),g?t.location.href=f:t.open(f,"_blank")||(t.location.href=f),d.readyState=d.DONE,m(),c(f)}()},p=u.prototype;return"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(t,e,n){return e=e||t.name||"download",n||(t=l(t)),navigator.msSaveOrOpenBlob(t,e)}:(p.abort=function(){},p.readyState=p.INIT=0,p.WRITING=1,p.DONE=2,p.error=p.onwritestart=p.onprogress=p.onwrite=p.onabort=p.onerror=p.onwriteend=null,function(t,e,n){return new u(t,e||t.name||"download",n)})}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||window.content);"undefined"!=typeof module&&module.exports?module.exports.saveAs=r:"undefined"!=typeof define&&null!==define&&null!==define.amd&&define("FileSaver.js",function(){return r}),function(t,e){"object"==typeof module?module.exports=e():"function"==typeof define?define(e):t.adler32cs=e()}(window,function(){var t="function"==typeof ArrayBuffer&&"function"==typeof Uint8Array,e=null,n=function(){if(!t)return function(){return!1};try{var n=require("buffer");"function"==typeof n.Buffer&&(e=n.Buffer)}catch(t){}return function(t){return t instanceof ArrayBuffer||null!==e&&t instanceof e}}(),r=null!==e?function(t){return new e(t,"utf8").toString("binary")}:function(t){return unescape(encodeURIComponent(t))},i=function(t,e){for(var n=65535&t,r=t>>>16,i=0,o=e.length;i<o;i++)r=(r+(n=(n+(255&e.charCodeAt(i)))%65521))%65521;return(r<<16|n)>>>0},o=function(t,e){for(var n=65535&t,r=t>>>16,i=0,o=e.length;i<o;i++)r=(r+(n=(n+e[i])%65521))%65521;return(r<<16|n)>>>0},s={},a=s.Adler32=function(){var e=function(t){if(!(this instanceof e))throw new TypeError("Constructor cannot called be as a function.");if(!isFinite(t=null==t?1:+t))throw new Error("First arguments needs to be a finite number.");this.checksum=t>>>0},s=e.prototype={};return s.constructor=e,e.from=function(t){return t.prototype=s,t}(function(t){if(!(this instanceof e))throw new TypeError("Constructor cannot called be as a function.");if(null==t)throw new Error("First argument needs to be a string.");this.checksum=i(1,t.toString())}),e.fromUtf8=function(t){return t.prototype=s,t}(function(t){if(!(this instanceof e))throw new TypeError("Constructor cannot called be as a function.");if(null==t)throw new Error("First argument needs to be a string.");var n=r(t.toString());this.checksum=i(1,n)}),t&&(e.fromBuffer=function(t){return t.prototype=s,t}(function(t){if(!(this instanceof e))throw new TypeError("Constructor cannot called be as a function.");if(!n(t))throw new Error("First argument needs to be ArrayBuffer.");var r=new Uint8Array(t);return this.checksum=o(1,r)})),s.update=function(t){if(null==t)throw new Error("First argument needs to be a string.");return t=t.toString(),this.checksum=i(this.checksum,t)},s.updateUtf8=function(t){if(null==t)throw new Error("First argument needs to be a string.");var e=r(t.toString());return this.checksum=i(this.checksum,e)},t&&(s.updateBuffer=function(t){if(!n(t))throw new Error("First argument needs to be ArrayBuffer.");var e=new Uint8Array(t);return this.checksum=o(this.checksum,e)}),s.clone=function(){return new a(this.checksum)},e}();return s.from=function(t){if(null==t)throw new Error("First argument needs to be a string.");return i(1,t.toString())},s.fromUtf8=function(t){if(null==t)throw new Error("First argument needs to be a string.");var e=r(t.toString());return i(1,e)},t&&(s.fromBuffer=function(t){if(!n(t))throw new Error("First argument need to be ArrayBuffer.");var e=new Uint8Array(t);return o(1,e)}),s}),function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;"undefined"!=typeof window?e=window:"undefined"!=typeof global?e=global:"undefined"!=typeof self&&(e=self),e.html2canvas=t()}}(function(){return function t(e,n,r){function i(s,a){if(!n[s]){if(!e[s]){var c="function"==typeof require&&require;if(!a&&c)return c(s,!0);if(o)return o(s,!0);var h=new Error("Cannot find module '"+s+"'");throw h.code="MODULE_NOT_FOUND",h}var l=n[s]={exports:{}};e[s][0].call(l.exports,function(t){var n=e[s][1][t];return i(n||t)},l,l.exports,t,e,n,r)}return n[s].exports}for(var o="function"==typeof require&&require,s=0;s<r.length;s++)i(r[s]);return i}({1:[function(t,e,n){(function(t){!function(r){function i(t){throw RangeError(P[t])}function o(t,e){for(var n=t.length;n--;)t[n]=e(t[n]);return t}function s(t,e){return o(t.split(O),e).join(".")}function a(t){for(var e,n,r=[],i=0,o=t.length;i<o;)(e=t.charCodeAt(i++))>=55296&&e<=56319&&i<o?56320==(64512&(n=t.charCodeAt(i++)))?r.push(((1023&e)<<10)+(1023&n)+65536):(r.push(e),i--):r.push(e);return r}function c(t){return o(t,function(t){var e="";return t>65535&&(e+=B((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+=B(t)}).join("")}function h(t){return t-48<10?t-22:t-65<26?t-65:t-97<26?t-97:b}function l(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function u(t,e,n){var r=0;for(t=n?E(t/C):t>>1,t+=E(t/e);t>F*S>>1;r+=b)t=E(t/F);return E(r+(F+1)*t/(t+k))}function p(t){var e,n,r,o,s,a,l,p,f,d,g=[],m=t.length,w=0,y=A,k=I;for((n=t.lastIndexOf(T))<0&&(n=0),r=0;r<n;++r)t.charCodeAt(r)>=128&&i("not-basic"),g.push(t.charCodeAt(r));for(o=n>0?n+1:0;o<m;){for(s=w,a=1,l=b;o>=m&&i("invalid-input"),((p=h(t.charCodeAt(o++)))>=b||p>E((v-w)/a))&&i("overflow"),w+=p*a,f=l<=k?x:l>=k+S?S:l-k,!(p<f);l+=b)a>E(v/(d=b-f))&&i("overflow"),a*=d;k=u(w-s,e=g.length+1,0==s),E(w/e)>v-y&&i("overflow"),y+=E(w/e),w%=e,g.splice(w++,0,y)}return c(g)}function f(t){var e,n,r,o,s,c,h,p,f,d,g,m,w,y,k,C=[];for(m=(t=a(t)).length,e=A,n=0,s=I,c=0;c<m;++c)(g=t[c])<128&&C.push(B(g));for(r=o=C.length,o&&C.push(T);r<m;){for(h=v,c=0;c<m;++c)(g=t[c])>=e&&g<h&&(h=g);for(h-e>E((v-n)/(w=r+1))&&i("overflow"),n+=(h-e)*w,e=h,c=0;c<m;++c)if((g=t[c])<e&&++n>v&&i("overflow"),g==e){for(p=n,f=b;d=f<=s?x:f>=s+S?S:f-s,!(p<d);f+=b)k=p-d,y=b-d,C.push(B(l(d+k%y,0))),p=E(k/y);C.push(B(l(p,0))),s=u(n,w,r==o),n=0,++r}++n,++e}return C.join("")}var d="object"==typeof n&&n,g="object"==typeof e&&e&&e.exports==d&&e,m="object"==typeof t&&t;m.global!==m&&m.window!==m||(r=m);var w,y,v=2147483647,b=36,x=1,S=26,k=38,C=700,I=72,A=128,T="-",_=/^xn--/,q=/[^ -~]/,O=/\x2E|\u3002|\uFF0E|\uFF61/g,P={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},F=b-x,E=Math.floor,B=String.fromCharCode;if(w={version:"1.2.4",ucs2:{decode:a,encode:c},decode:p,encode:f,toASCII:function(t){return s(t,function(t){return q.test(t)?"xn--"+f(t):t})},toUnicode:function(t){return s(t,function(t){return _.test(t)?p(t.slice(4).toLowerCase()):t})}},d&&!d.nodeType)if(g)g.exports=w;else for(y in w)w.hasOwnProperty(y)&&(d[y]=w[y]);else r.punycode=w}(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e,n){function r(t,e,n){!t.defaultView||e===t.defaultView.pageXOffset&&n===t.defaultView.pageYOffset||t.defaultView.scrollTo(e,n)}function i(t,e){try{e&&(e.width=t.width,e.height=t.height,e.getContext("2d").putImageData(t.getContext("2d").getImageData(0,0,t.width,t.height),0,0))}catch(e){a("Unable to copy canvas content from",t,e)}}function o(t,e){for(var n=3===t.nodeType?document.createTextNode(t.nodeValue):t.cloneNode(!1),r=t.firstChild;r;)!0!==e&&1===r.nodeType&&"SCRIPT"===r.nodeName||n.appendChild(o(r,e)),r=r.nextSibling;return 1===t.nodeType&&(n._scrollTop=t.scrollTop,n._scrollLeft=t.scrollLeft,"CANVAS"===t.nodeName?i(t,n):"TEXTAREA"!==t.nodeName&&"SELECT"!==t.nodeName||(n.value=t.value)),n}function s(t){if(1===t.nodeType){t.scrollTop=t._scrollTop,t.scrollLeft=t._scrollLeft;for(var e=t.firstChild;e;)s(e),e=e.nextSibling}}var a=t("./log");e.exports=function(t,e,n,i,a,c,h){var l=o(t.documentElement,a.javascriptEnabled),u=e.createElement("iframe");return u.className="html2canvas-container",u.style.visibility="hidden",u.style.position="fixed",u.style.left="-10000px",u.style.top="0px",u.style.border="0",u.width=n,u.height=i,u.scrolling="no",e.body.appendChild(u),new Promise(function(e){var n=u.contentWindow.document;u.contentWindow.onload=u.onload=function(){var t=setInterval(function(){n.body.childNodes.length>0&&(s(n.documentElement),clearInterval(t),"view"===a.type&&(u.contentWindow.scrollTo(c,h),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||u.contentWindow.scrollY===h&&u.contentWindow.scrollX===c||(n.documentElement.style.top=-h+"px",n.documentElement.style.left=-c+"px",n.documentElement.style.position="absolute")),e(u))},50)},n.open(),n.write("<!DOCTYPE html><html></html>"),r(t,c,h),n.replaceChild(n.adoptNode(l),n.documentElement),n.close()})}},{"./log":13}],3:[function(t,e,n){function r(t){this.r=0,this.g=0,this.b=0,this.a=null;this.fromArray(t)||this.namedColor(t)||this.rgb(t)||this.rgba(t)||this.hex6(t)||this.hex3(t)}r.prototype.darken=function(t){var e=1-t;return new r([Math.round(this.r*e),Math.round(this.g*e),Math.round(this.b*e),this.a])},r.prototype.isTransparent=function(){return 0===this.a},r.prototype.isBlack=function(){return 0===this.r&&0===this.g&&0===this.b},r.prototype.fromArray=function(t){return Array.isArray(t)&&(this.r=Math.min(t[0],255),this.g=Math.min(t[1],255),this.b=Math.min(t[2],255),t.length>3&&(this.a=t[3])),Array.isArray(t)};var i=/^#([a-f0-9]{3})$/i;r.prototype.hex3=function(t){var e=null;return null!==(e=t.match(i))&&(this.r=parseInt(e[1][0]+e[1][0],16),this.g=parseInt(e[1][1]+e[1][1],16),this.b=parseInt(e[1][2]+e[1][2],16)),null!==e};var o=/^#([a-f0-9]{6})$/i;r.prototype.hex6=function(t){var e=null;return null!==(e=t.match(o))&&(this.r=parseInt(e[1].substring(0,2),16),this.g=parseInt(e[1].substring(2,4),16),this.b=parseInt(e[1].substring(4,6),16)),null!==e};var s=/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/;r.prototype.rgb=function(t){var e=null;return null!==(e=t.match(s))&&(this.r=Number(e[1]),this.g=Number(e[2]),this.b=Number(e[3])),null!==e};var a=/^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?\.?\d+)\s*\)$/;r.prototype.rgba=function(t){var e=null;return null!==(e=t.match(a))&&(this.r=Number(e[1]),this.g=Number(e[2]),this.b=Number(e[3]),this.a=Number(e[4])),null!==e},r.prototype.toString=function(){return null!==this.a&&1!==this.a?"rgba("+[this.r,this.g,this.b,this.a].join(",")+")":"rgb("+[this.r,this.g,this.b].join(",")+")"},r.prototype.namedColor=function(t){t=t.toLowerCase();var e=c[t];if(e)this.r=e[0],this.g=e[1],this.b=e[2];else if("transparent"===t)return this.r=this.g=this.b=this.a=0,!0;return!!e},r.prototype.isColor=!0;var c={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};e.exports=r},{}],4:[function(t,e,n){function r(t,e){var n=S++;if((e=e||{}).logging&&(m.options.logging=!0,m.options.start=Date.now()),e.async=void 0===e.async||e.async,e.allowTaint=void 0!==e.allowTaint&&e.allowTaint,e.removeContainer=void 0===e.removeContainer||e.removeContainer,e.javascriptEnabled=void 0!==e.javascriptEnabled&&e.javascriptEnabled,e.imageTimeout=void 0===e.imageTimeout?1e4:e.imageTimeout,e.renderer="function"==typeof e.renderer?e.renderer:p,e.strict=!!e.strict,"string"==typeof t){if("string"!=typeof e.proxy)return Promise.reject("Proxy must be used when rendering url");var r=null!=e.width?e.width:window.innerWidth,s=null!=e.height?e.height:window.innerHeight;return v(l(t),e.proxy,document,r,s,e).then(function(t){return o(t.contentWindow.document.documentElement,t,e,r,s)})}var a=(void 0===t?[document.documentElement]:t.length?t:[t])[0];return a.setAttribute(x+n,n),i(a.ownerDocument,e,a.ownerDocument.defaultView.innerWidth,a.ownerDocument.defaultView.innerHeight,n).then(function(t){return"function"==typeof e.onrendered&&(m("options.onrendered is deprecated, html2canvas returns a Promise containing the canvas"),e.onrendered(t)),t})}function i(t,e,n,r,i){return y(t,t,n,r,e,t.defaultView.pageXOffset,t.defaultView.pageYOffset).then(function(s){m("Document cloned");var a=x+i,c="["+a+"='"+i+"']";t.querySelector(c).removeAttribute(a);var h=s.contentWindow,l=h.document.querySelector(c);return("function"==typeof e.onclone?Promise.resolve(e.onclone(h.document)):Promise.resolve(!0)).then(function(){return o(l,s,e,n,r)})})}function o(t,e,n,r,i){var o=e.contentWindow,l=new u(o.document),p=new f(n,l),g=b(t),w="view"===n.type?r:c(o.document),y="view"===n.type?i:h(o.document),v=new n.renderer(w,y,p,n,document);return new d(t,v,l,p,n).ready.then(function(){m("Finished rendering");var r;return r="view"===n.type?a(v.canvas,{width:v.canvas.width,height:v.canvas.height,top:0,left:0,x:0,y:0}):t===o.document.body||t===o.document.documentElement||null!=n.canvas?v.canvas:a(v.canvas,{width:null!=n.width?n.width:g.width,height:null!=n.height?n.height:g.height,top:g.top,left:g.left,x:0,y:0}),s(e,n),r})}function s(t,e){e.removeContainer&&(t.parentNode.removeChild(t),m("Cleaned up container"))}function a(t,e){var n=document.createElement("canvas"),r=Math.min(t.width-1,Math.max(0,e.left)),i=Math.min(t.width,Math.max(1,e.left+e.width)),o=Math.min(t.height-1,Math.max(0,e.top)),s=Math.min(t.height,Math.max(1,e.top+e.height));n.width=e.width,n.height=e.height;var a=i-r,c=s-o;return m("Cropping canvas at:","left:",e.left,"top:",e.top,"width:",a,"height:",c),m("Resulting crop with width",e.width,"and height",e.height,"with x",r,"and y",o),n.getContext("2d").drawImage(t,r,o,a,c,e.x,e.y,a,c),n}function c(t){return Math.max(Math.max(t.body.scrollWidth,t.documentElement.scrollWidth),Math.max(t.body.offsetWidth,t.documentElement.offsetWidth),Math.max(t.body.clientWidth,t.documentElement.clientWidth))}function h(t){return Math.max(Math.max(t.body.scrollHeight,t.documentElement.scrollHeight),Math.max(t.body.offsetHeight,t.documentElement.offsetHeight),Math.max(t.body.clientHeight,t.documentElement.clientHeight))}function l(t){var e=document.createElement("a");return e.href=t,e.href=e.href,e}var u=t("./support"),p=t("./renderers/canvas"),f=t("./imageloader"),d=t("./nodeparser"),g=t("./nodecontainer"),m=t("./log"),w=t("./utils"),y=t("./clone"),v=t("./proxy").loadUrlDocument,b=w.getBounds,x="data-html2canvas-node",S=0;r.CanvasRenderer=p,r.NodeContainer=g,r.log=m,r.utils=w;var k="undefined"==typeof document||"function"!=typeof Object.create||"function"!=typeof document.createElement("canvas").getContext?function(){return Promise.reject("No canvas support")}:r;e.exports=k},{"./clone":2,"./imageloader":11,"./log":13,"./nodecontainer":14,"./nodeparser":15,"./proxy":16,"./renderers/canvas":20,"./support":22,"./utils":26}],5:[function(t,e,n){function r(t){if(this.src=t,i("DummyImageContainer for",t),!this.promise||!this.image){i("Initiating DummyImageContainer"),r.prototype.image=new Image;var e=this.image;r.prototype.promise=new Promise(function(t,n){e.onload=t,e.onerror=n,e.src=o(),!0===e.complete&&t(e)})}}var i=t("./log"),o=t("./utils").smallImage;e.exports=r},{"./log":13,"./utils":26}],6:[function(t,e,n){var r=t("./utils").smallImage;e.exports=function(t,e){var n,i,o=document.createElement("div"),s=document.createElement("img"),a=document.createElement("span");o.style.visibility="hidden",o.style.fontFamily=t,o.style.fontSize=e,o.style.margin=0,o.style.padding=0,document.body.appendChild(o),s.src=r(),s.width=1,s.height=1,s.style.margin=0,s.style.padding=0,s.style.verticalAlign="baseline",a.style.fontFamily=t,a.style.fontSize=e,a.style.margin=0,a.style.padding=0,a.appendChild(document.createTextNode("Hidden Text")),o.appendChild(a),o.appendChild(s),n=s.offsetTop-a.offsetTop+1,o.removeChild(a),o.appendChild(document.createTextNode("Hidden Text")),o.style.lineHeight="normal",s.style.verticalAlign="super",i=s.offsetTop-o.offsetTop+1,document.body.removeChild(o),this.baseline=n,this.lineWidth=1,this.middle=i}},{"./utils":26}],7:[function(t,e,n){function r(){this.data={}}var i=t("./font");r.prototype.getMetrics=function(t,e){return void 0===this.data[t+"-"+e]&&(this.data[t+"-"+e]=new i(t,e)),this.data[t+"-"+e]},e.exports=r},{"./font":6}],8:[function(t,e,n){function r(e,n,r){this.image=null,this.src=e;var o=this,s=i(e);this.promise=(n?new Promise(function(t){"about:blank"===e.contentWindow.document.URL||null==e.contentWindow.document.documentElement?e.contentWindow.onload=e.onload=function(){t(e)}:t(e)}):this.proxyLoad(r.proxy,s,r)).then(function(e){return t("./core")(e.contentWindow.document.documentElement,{type:"view",width:e.width,height:e.height,proxy:r.proxy,javascriptEnabled:r.javascriptEnabled,removeContainer:r.removeContainer,allowTaint:r.allowTaint,imageTimeout:r.imageTimeout/2})}).then(function(t){return o.image=t})}var i=t("./utils").getBounds,o=t("./proxy").loadUrlDocument;r.prototype.proxyLoad=function(t,e,n){var r=this.src;return o(r.src,t,r.ownerDocument,e.width,e.height,n)},e.exports=r},{"./core":4,"./proxy":16,"./utils":26}],9:[function(t,e,n){function r(t){this.src=t.value,this.colorStops=[],this.type=null,this.x0=.5,this.y0=.5,this.x1=.5,this.y1=.5,this.promise=Promise.resolve(!0)}r.TYPES={LINEAR:1,RADIAL:2},r.REGEXP_COLORSTOP=/^\s*(rgba?\(\s*\d{1,3},\s*\d{1,3},\s*\d{1,3}(?:,\s*[0-9\.]+)?\s*\)|[a-z]{3,20}|#[a-f0-9]{3,6})(?:\s+(\d{1,3}(?:\.\d+)?)(%|px)?)?(?:\s|$)/i,e.exports=r},{}],10:[function(t,e,n){e.exports=function(t,e){this.src=t,this.image=new Image;var n=this;this.tainted=null,this.promise=new Promise(function(r,i){n.image.onload=r,n.image.onerror=i,e&&(n.image.crossOrigin="anonymous"),n.image.src=t,!0===n.image.complete&&r(n.image)})}},{}],11:[function(t,e,n){function r(t,e){this.link=null,this.options=t,this.support=e,this.origin=this.getOrigin(window.location.href)}var i=t("./log"),o=t("./imagecontainer"),s=t("./dummyimagecontainer"),a=t("./proxyimagecontainer"),c=t("./framecontainer"),h=t("./svgcontainer"),l=t("./svgnodecontainer"),u=t("./lineargradientcontainer"),p=t("./webkitgradientcontainer"),f=t("./utils").bind;r.prototype.findImages=function(t){var e=[];return t.reduce(function(t,e){switch(e.node.nodeName){case"IMG":return t.concat([{args:[e.node.src],method:"url"}]);case"svg":case"IFRAME":return t.concat([{args:[e.node],method:e.node.nodeName}])}return t},[]).forEach(this.addImage(e,this.loadImage),this),e},r.prototype.findBackgroundImage=function(t,e){return e.parseBackgroundImages().filter(this.hasImageBackground).forEach(this.addImage(t,this.loadImage),this),t},r.prototype.addImage=function(t,e){return function(n){n.args.forEach(function(r){this.imageExists(t,r)||(t.splice(0,0,e.call(this,n)),i("Added image #"+t.length,"string"==typeof r?r.substring(0,100):r))},this)}},r.prototype.hasImageBackground=function(t){return"none"!==t.method},r.prototype.loadImage=function(t){if("url"===t.method){var e=t.args[0];return!this.isSVG(e)||this.support.svg||this.options.allowTaint?e.match(/data:image\/.*;base64,/i)?new o(e.replace(/url\(['"]{0,}|['"]{0,}\)$/gi,""),!1):this.isSameOrigin(e)||!0===this.options.allowTaint||this.isSVG(e)?new o(e,!1):this.support.cors&&!this.options.allowTaint&&this.options.useCORS?new o(e,!0):this.options.proxy?new a(e,this.options.proxy):new s(e):new h(e)}return"linear-gradient"===t.method?new u(t):"gradient"===t.method?new p(t):"svg"===t.method?new l(t.args[0],this.support.svg):"IFRAME"===t.method?new c(t.args[0],this.isSameOrigin(t.args[0].src),this.options):new s(t)},r.prototype.isSVG=function(t){return"svg"===t.substring(t.length-3).toLowerCase()||h.prototype.isInline(t)},r.prototype.imageExists=function(t,e){return t.some(function(t){return t.src===e})},r.prototype.isSameOrigin=function(t){return this.getOrigin(t)===this.origin},r.prototype.getOrigin=function(t){var e=this.link||(this.link=document.createElement("a"));return e.href=t,e.href=e.href,e.protocol+e.hostname+e.port},r.prototype.getPromise=function(t){return this.timeout(t,this.options.imageTimeout).catch(function(){return new s(t.src).promise.then(function(e){t.image=e})})},r.prototype.get=function(t){var e=null;return this.images.some(function(n){return(e=n).src===t})?e:null},r.prototype.fetch=function(t){return this.images=t.reduce(f(this.findBackgroundImage,this),this.findImages(t)),this.images.forEach(function(t,e){t.promise.then(function(){i("Succesfully loaded image #"+(e+1),t)},function(n){i("Failed loading image #"+(e+1),t,n)})}),this.ready=Promise.all(this.images.map(this.getPromise,this)),i("Finished searching images"),this},r.prototype.timeout=function(t,e){var n,r=Promise.race([t.promise,new Promise(function(r,o){n=setTimeout(function(){i("Timed out loading image",t),o(t)},e)})]).then(function(t){return clearTimeout(n),t});return r.catch(function(){clearTimeout(n)}),r},e.exports=r},{"./dummyimagecontainer":5,"./framecontainer":8,"./imagecontainer":10,"./lineargradientcontainer":12,"./log":13,"./proxyimagecontainer":17,"./svgcontainer":23,"./svgnodecontainer":24,"./utils":26,"./webkitgradientcontainer":27}],12:[function(t,e,n){function r(t){i.apply(this,arguments),this.type=i.TYPES.LINEAR;var e=r.REGEXP_DIRECTION.test(t.args[0])||!i.REGEXP_COLORSTOP.test(t.args[0]);e?t.args[0].split(/\s+/).reverse().forEach(function(t,e){switch(t){case"left":this.x0=0,this.x1=1;break;case"top":this.y0=0,this.y1=1;break;case"right":this.x0=1,this.x1=0;break;case"bottom":this.y0=1,this.y1=0;break;case"to":var n=this.y0,r=this.x0;this.y0=this.y1,this.x0=this.x1,this.x1=r,this.y1=n;break;case"center":break;default:var i=.01*parseFloat(t,10);if(isNaN(i))break;0===e?(this.y0=i,this.y1=1-this.y0):(this.x0=i,this.x1=1-this.x0)}},this):(this.y0=0,this.y1=1),this.colorStops=t.args.slice(e?1:0).map(function(t){var e=t.match(i.REGEXP_COLORSTOP),n=+e[2],r=0===n?"%":e[3];return{color:new o(e[1]),stop:"%"===r?n/100:null}}),null===this.colorStops[0].stop&&(this.colorStops[0].stop=0),null===this.colorStops[this.colorStops.length-1].stop&&(this.colorStops[this.colorStops.length-1].stop=1),this.colorStops.forEach(function(t,e){null===t.stop&&this.colorStops.slice(e).some(function(n,r){return null!==n.stop&&(t.stop=(n.stop-this.colorStops[e-1].stop)/(r+1)+this.colorStops[e-1].stop,!0)},this)},this)}var i=t("./gradientcontainer"),o=t("./color");r.prototype=Object.create(i.prototype),r.REGEXP_DIRECTION=/^\s*(?:to|left|right|top|bottom|center|\d{1,3}(?:\.\d+)?%?)(?:\s|$)/i,e.exports=r},{"./color":3,"./gradientcontainer":9}],13:[function(t,e,n){var r=function(){r.options.logging&&window.console&&window.console.log&&Function.prototype.bind.call(window.console.log,window.console).apply(window.console,[Date.now()-r.options.start+"ms","html2canvas:"].concat([].slice.call(arguments,0)))};r.options={logging:!1},e.exports=r},{}],14:[function(t,e,n){function r(t,e){this.node=t,this.parent=e,this.stack=null,this.bounds=null,this.borders=null,this.clip=[],this.backgroundClip=[],this.offsetBounds=null,this.visible=null,this.computedStyles=null,this.colors={},this.styles={},this.backgroundImages=null,this.transformData=null,this.transformMatrix=null,this.isPseudoElement=!1,this.opacity=null}function i(t){var e=t.options[t.selectedIndex||0];return e?e.text||"":""}function o(t){if(t&&"matrix"===t[1])return t[2].split(",").map(function(t){return parseFloat(t.trim())});if(t&&"matrix3d"===t[1]){var e=t[2].split(",").map(function(t){return parseFloat(t.trim())});return[e[0],e[1],e[4],e[5],e[12],e[13]]}}function s(t){return-1!==t.toString().indexOf("%")}function a(t){return t.replace("px","")}function c(t){return parseFloat(t)}var h=t("./color"),l=t("./utils"),u=l.getBounds,p=l.parseBackgrounds,f=l.offsetBounds;r.prototype.cloneTo=function(t){t.visible=this.visible,t.borders=this.borders,t.bounds=this.bounds,t.clip=this.clip,t.backgroundClip=this.backgroundClip,t.computedStyles=this.computedStyles,t.styles=this.styles,t.backgroundImages=this.backgroundImages,t.opacity=this.opacity},r.prototype.getOpacity=function(){return null===this.opacity?this.opacity=this.cssFloat("opacity"):this.opacity},r.prototype.assignStack=function(t){this.stack=t,t.children.push(this)},r.prototype.isElementVisible=function(){return this.node.nodeType===Node.TEXT_NODE?this.parent.visible:"none"!==this.css("display")&&"hidden"!==this.css("visibility")&&!this.node.hasAttribute("data-html2canvas-ignore")&&("INPUT"!==this.node.nodeName||"hidden"!==this.node.getAttribute("type"))},r.prototype.css=function(t){return this.computedStyles||(this.computedStyles=this.isPseudoElement?this.parent.computedStyle(this.before?":before":":after"):this.computedStyle(null)),this.styles[t]||(this.styles[t]=this.computedStyles[t])},r.prototype.prefixedCss=function(t){var e=["webkit","moz","ms","o"],n=this.css(t);return void 0===n&&e.some(function(e){return void 0!==(n=this.css(e+t.substr(0,1).toUpperCase()+t.substr(1)))},this),void 0===n?null:n},r.prototype.computedStyle=function(t){return this.node.ownerDocument.defaultView.getComputedStyle(this.node,t)},r.prototype.cssInt=function(t){var e=parseInt(this.css(t),10);return isNaN(e)?0:e},r.prototype.color=function(t){return this.colors[t]||(this.colors[t]=new h(this.css(t)))},r.prototype.cssFloat=function(t){var e=parseFloat(this.css(t));return isNaN(e)?0:e},r.prototype.fontWeight=function(){var t=this.css("fontWeight");switch(parseInt(t,10)){case 401:t="bold";break;case 400:t="normal"}return t},r.prototype.parseClip=function(){var t=this.css("clip").match(this.CLIP);return t?{top:parseInt(t[1],10),right:parseInt(t[2],10),bottom:parseInt(t[3],10),left:parseInt(t[4],10)}:null},r.prototype.parseBackgroundImages=function(){return this.backgroundImages||(this.backgroundImages=p(this.css("backgroundImage")))},r.prototype.cssList=function(t,e){var n=(this.css(t)||"").split(",");return n=n[e||0]||n[0]||"auto",1===(n=n.trim().split(" ")).length&&(n=[n[0],s(n[0])?"auto":n[0]]),n},r.prototype.parseBackgroundSize=function(t,e,n){var r,i,o=this.cssList("backgroundSize",n);if(s(o[0]))r=t.width*parseFloat(o[0])/100;else{if(/contain|cover/.test(o[0])){var a=t.width/t.height,c=e.width/e.height;return a<c^"contain"===o[0]?{width:t.height*c,height:t.height}:{width:t.width,height:t.width/c}}r=parseInt(o[0],10)}return i="auto"===o[0]&&"auto"===o[1]?e.height:"auto"===o[1]?r/e.width*e.height:s(o[1])?t.height*parseFloat(o[1])/100:parseInt(o[1],10),"auto"===o[0]&&(r=i/e.height*e.width),{width:r,height:i}},r.prototype.parseBackgroundPosition=function(t,e,n,r){var i,o,a=this.cssList("backgroundPosition",n);return i=s(a[0])?(t.width-(r||e).width)*(parseFloat(a[0])/100):parseInt(a[0],10),o="auto"===a[1]?i/e.width*e.height:s(a[1])?(t.height-(r||e).height)*parseFloat(a[1])/100:parseInt(a[1],10),"auto"===a[0]&&(i=o/e.height*e.width),{left:i,top:o}},r.prototype.parseBackgroundRepeat=function(t){return this.cssList("backgroundRepeat",t)[0]},r.prototype.parseTextShadows=function(){var t=this.css("textShadow"),e=[];if(t&&"none"!==t)for(var n=t.match(this.TEXT_SHADOW_PROPERTY),r=0;n&&r<n.length;r++){var i=n[r].match(this.TEXT_SHADOW_VALUES);e.push({color:new h(i[0]),offsetX:i[1]?parseFloat(i[1].replace("px","")):0,offsetY:i[2]?parseFloat(i[2].replace("px","")):0,blur:i[3]?i[3].replace("px",""):0})}return e},r.prototype.parseTransform=function(){if(!this.transformData)if(this.hasTransform()){var t=this.parseBounds(),e=this.prefixedCss("transformOrigin").split(" ").map(a).map(c);e[0]+=t.left,e[1]+=t.top,this.transformData={origin:e,matrix:this.parseTransformMatrix()}}else this.transformData={origin:[0,0],matrix:[1,0,0,1,0,0]};return this.transformData},r.prototype.parseTransformMatrix=function(){if(!this.transformMatrix){var t=this.prefixedCss("transform"),e=t?o(t.match(this.MATRIX_PROPERTY)):null;this.transformMatrix=e||[1,0,0,1,0,0]}return this.transformMatrix},r.prototype.parseBounds=function(){return this.bounds||(this.bounds=this.hasTransform()?f(this.node):u(this.node))},r.prototype.hasTransform=function(){return"1,0,0,1,0,0"!==this.parseTransformMatrix().join(",")||this.parent&&this.parent.hasTransform()},r.prototype.getValue=function(){var t=this.node.value||"";return"SELECT"===this.node.tagName?t=i(this.node):"password"===this.node.type&&(t=Array(t.length+1).join("•")),0===t.length?this.node.placeholder||"":t},r.prototype.MATRIX_PROPERTY=/(matrix|matrix3d)\((.+)\)/,r.prototype.TEXT_SHADOW_PROPERTY=/((rgba|rgb)\([^\)]+\)(\s-?\d+px){0,})/g,r.prototype.TEXT_SHADOW_VALUES=/(-?\d+px)|(#.+)|(rgb\(.+\))|(rgba\(.+\))/g,r.prototype.CLIP=/^rect\((\d+)px,? (\d+)px,? (\d+)px,? (\d+)px\)$/,e.exports=r},{"./color":3,"./utils":26}],15:[function(t,e,n){function r(t,e,n,r,i){N("Starting NodeParser"),this.renderer=e,this.options=i,this.range=null,this.support=n,this.renderQueue=[],this.stack=new X(!0,1,t.ownerDocument,null);var o=new z(t,null);if(i.background&&e.rectangle(0,0,e.width,e.height,new G(i.background)),t===t.ownerDocument.documentElement){var s=new z(o.color("backgroundColor").isTransparent()?t.ownerDocument.body:t.ownerDocument.documentElement,null);e.rectangle(0,0,e.width,e.height,s.color("backgroundColor"))}o.visibile=o.isElementVisible(),this.createPseudoHideStyles(t.ownerDocument),this.disableAnimations(t.ownerDocument),this.nodes=R([o].concat(this.getChildren(o)).filter(function(t){return t.visible=t.isElementVisible()}).map(this.getPseudoElements,this)),this.fontMetrics=new V,N("Fetched nodes, total:",this.nodes.length),N("Calculate overflow clips"),this.calculateOverflowClips(),N("Start fetching images"),this.images=r.fetch(this.nodes.filter(T)),this.ready=this.images.ready.then(J(function(){return N("Images loaded, starting parsing"),N("Creating stacking contexts"),this.createStackingContexts(),N("Sorting stacking contexts"),this.sortStackingContexts(this.stack),this.parse(this.stack),N("Render queue created with "+this.renderQueue.length+" items"),new Promise(J(function(t){i.async?"function"==typeof i.async?i.async.call(this,this.renderQueue,t):this.renderQueue.length>0?(this.renderIndex=0,this.asyncRenderer(this.renderQueue,t)):t():(this.renderQueue.forEach(this.paint,this),t())},this))},this))}function i(t){return t.parent&&t.parent.clip.length}function o(t){return t.replace(/(\-[a-z])/g,function(t){return t.toUpperCase().replace("-","")})}function s(){}function a(t,e,n,r){return t.map(function(i,o){if(i.width>0){var s=e.left,a=e.top,c=e.width,h=e.height-t[2].width;switch(o){case 0:h=t[0].width,i.args=u({c1:[s,a],c2:[s+c,a],c3:[s+c-t[1].width,a+h],c4:[s+t[3].width,a+h]},r[0],r[1],n.topLeftOuter,n.topLeftInner,n.topRightOuter,n.topRightInner);break;case 1:s=e.left+e.width-t[1].width,c=t[1].width,i.args=u({c1:[s+c,a],c2:[s+c,a+h+t[2].width],c3:[s,a+h],c4:[s,a+t[0].width]},r[1],r[2],n.topRightOuter,n.topRightInner,n.bottomRightOuter,n.bottomRightInner);break;case 2:a=a+e.height-t[2].width,h=t[2].width,i.args=u({c1:[s+c,a+h],c2:[s,a+h],c3:[s+t[3].width,a],c4:[s+c-t[3].width,a]},r[2],r[3],n.bottomRightOuter,n.bottomRightInner,n.bottomLeftOuter,n.bottomLeftInner);break;case 3:c=t[3].width,i.args=u({c1:[s,a+h+t[2].width],c2:[s,a],c3:[s+c,a+t[0].width],c4:[s+c,a+h]},r[3],r[0],n.bottomLeftOuter,n.bottomLeftInner,n.topLeftOuter,n.topLeftInner)}}return i})}function c(t,e,n,r){var i=(Math.sqrt(2)-1)/3*4,o=n*i,s=r*i,a=t+n,c=e+r;return{topLeft:l({x:t,y:c},{x:t,y:c-s},{x:a-o,y:e},{x:a,y:e}),topRight:l({x:t,y:e},{x:t+o,y:e},{x:a,y:c-s},{x:a,y:c}),bottomRight:l({x:a,y:e},{x:a,y:e+s},{x:t+o,y:c},{x:t,y:c}),bottomLeft:l({x:a,y:c},{x:a-o,y:c},{x:t,y:e+s},{x:t,y:e})}}function h(t,e,n){var r=t.left,i=t.top,o=t.width,s=t.height,a=e[0][0]<o/2?e[0][0]:o/2,h=e[0][1]<s/2?e[0][1]:s/2,l=e[1][0]<o/2?e[1][0]:o/2,u=e[1][1]<s/2?e[1][1]:s/2,p=e[2][0]<o/2?e[2][0]:o/2,f=e[2][1]<s/2?e[2][1]:s/2,d=e[3][0]<o/2?e[3][0]:o/2,g=e[3][1]<s/2?e[3][1]:s/2,m=o-l,w=s-f,y=o-p,v=s-g;return{topLeftOuter:c(r,i,a,h).topLeft.subdivide(.5),topLeftInner:c(r+n[3].width,i+n[0].width,Math.max(0,a-n[3].width),Math.max(0,h-n[0].width)).topLeft.subdivide(.5),topRightOuter:c(r+m,i,l,u).topRight.subdivide(.5),topRightInner:c(r+Math.min(m,o+n[3].width),i+n[0].width,m>o+n[3].width?0:l-n[3].width,u-n[0].width).topRight.subdivide(.5),bottomRightOuter:c(r+y,i+w,p,f).bottomRight.subdivide(.5),bottomRightInner:c(r+Math.min(y,o-n[3].width),i+Math.min(w,s+n[0].width),Math.max(0,p-n[1].width),f-n[2].width).bottomRight.subdivide(.5),bottomLeftOuter:c(r,i+v,d,g).bottomLeft.subdivide(.5),bottomLeftInner:c(r+n[3].width,i+v,Math.max(0,d-n[3].width),g-n[2].width).bottomLeft.subdivide(.5)}}function l(t,e,n,r){var i=function(t,e,n){return{x:t.x+(e.x-t.x)*n,y:t.y+(e.y-t.y)*n}};return{start:t,startControl:e,endControl:n,end:r,subdivide:function(o){var s=i(t,e,o),a=i(e,n,o),c=i(n,r,o),h=i(s,a,o),u=i(a,c,o),p=i(h,u,o);return[l(t,s,h,p),l(p,u,c,r)]},curveTo:function(t){t.push(["bezierCurve",e.x,e.y,n.x,n.y,r.x,r.y])},curveToReversed:function(r){r.push(["bezierCurve",n.x,n.y,e.x,e.y,t.x,t.y])}}}function u(t,e,n,r,i,o,s){var a=[];return e[0]>0||e[1]>0?(a.push(["line",r[1].start.x,r[1].start.y]),r[1].curveTo(a)):a.push(["line",t.c1[0],t.c1[1]]),n[0]>0||n[1]>0?(a.push(["line",o[0].start.x,o[0].start.y]),o[0].curveTo(a),a.push(["line",s[0].end.x,s[0].end.y]),s[0].curveToReversed(a)):(a.push(["line",t.c2[0],t.c2[1]]),a.push(["line",t.c3[0],t.c3[1]])),e[0]>0||e[1]>0?(a.push(["line",i[1].end.x,i[1].end.y]),i[1].curveToReversed(a)):a.push(["line",t.c4[0],t.c4[1]]),a}function p(t,e,n,r,i,o,s){e[0]>0||e[1]>0?(t.push(["line",r[0].start.x,r[0].start.y]),r[0].curveTo(t),r[1].curveTo(t)):t.push(["line",o,s]),(n[0]>0||n[1]>0)&&t.push(["line",i[0].start.x,i[0].start.y])}function f(t){return t.cssInt("zIndex")<0}function d(t){return t.cssInt("zIndex")>0}function g(t){return 0===t.cssInt("zIndex")}function m(t){return-1!==["inline","inline-block","inline-table"].indexOf(t.css("display"))}function w(t){return t instanceof X}function y(t){return t.node.data.trim().length>0}function v(t){return/^(normal|none|0px)$/.test(t.parent.css("letterSpacing"))}function b(t){return["TopLeft","TopRight","BottomRight","BottomLeft"].map(function(e){var n=t.css("border"+e+"Radius").split(" ");return n.length<=1&&(n[1]=n[0]),n.map(F)})}function x(t){return t.nodeType===Node.TEXT_NODE||t.nodeType===Node.ELEMENT_NODE}function S(t){var e=t.css("position");return"auto"!==(-1!==["absolute","relative","fixed"].indexOf(e)?t.css("zIndex"):"auto")}function k(t){return"static"!==t.css("position")}function C(t){return"none"!==t.css("float")}function I(t){return-1!==["inline-block","inline-table"].indexOf(t.css("display"))}function A(t){var e=this;return function(){return!t.apply(e,arguments)}}function T(t){return t.node.nodeType===Node.ELEMENT_NODE}function _(t){return!0===t.isPseudoElement}function q(t){return t.node.nodeType===Node.TEXT_NODE}function O(t){return function(e,n){return e.cssInt("zIndex")+t.indexOf(e)/t.length-(n.cssInt("zIndex")+t.indexOf(n)/t.length)}}function P(t){return t.getOpacity()<1}function F(t){return parseInt(t,10)}function E(t){return t.width}function B(t){return t.node.nodeType!==Node.ELEMENT_NODE||-1===["SCRIPT","HEAD","TITLE","OBJECT","BR","OPTION"].indexOf(t.node.nodeName)}function R(t){return[].concat.apply([],t)}function D(t){var e=t.substr(0,1);return e===t.substr(t.length-1)&&e.match(/'|"/)?t.substr(1,t.length-2):t}function j(t){for(var e,n=[],r=0,i=!1;t.length;)M(t[r])===i?((e=t.splice(0,r)).length&&n.push(L.ucs2.encode(e)),i=!i,r=0):r++,r>=t.length&&(e=t.splice(0,r)).length&&n.push(L.ucs2.encode(e));return n}function M(t){return-1!==[32,13,10,9,45].indexOf(t)}function U(t){return/[^\u0000-\u00ff]/.test(t)}var N=t("./log"),L=t("punycode"),z=t("./nodecontainer"),H=t("./textcontainer"),W=t("./pseudoelementcontainer"),V=t("./fontmetrics"),G=t("./color"),X=t("./stackingcontext"),Y=t("./utils"),J=Y.bind,Q=Y.getBounds,K=Y.parseBackgrounds,Z=Y.offsetBounds;r.prototype.calculateOverflowClips=function(){this.nodes.forEach(function(t){if(T(t)){_(t)&&t.appendToDOM(),t.borders=this.parseBorders(t);var e="hidden"===t.css("overflow")?[t.borders.clip]:[],n=t.parseClip();n&&-1!==["absolute","fixed"].indexOf(t.css("position"))&&e.push([["rect",t.bounds.left+n.left,t.bounds.top+n.top,n.right-n.left,n.bottom-n.top]]),t.clip=i(t)?t.parent.clip.concat(e):e,t.backgroundClip="hidden"!==t.css("overflow")?t.clip.concat([t.borders.clip]):t.clip,_(t)&&t.cleanDOM()}else q(t)&&(t.clip=i(t)?t.parent.clip:[]);_(t)||(t.bounds=null)},this)},r.prototype.asyncRenderer=function(t,e,n){n=n||Date.now(),this.paint(t[this.renderIndex++]),t.length===this.renderIndex?e():n+20>Date.now()?this.asyncRenderer(t,e,n):setTimeout(J(function(){this.asyncRenderer(t,e)},this),0)},r.prototype.createPseudoHideStyles=function(t){this.createStyles(t,"."+W.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+':before { content: "" !important; display: none !important; }.'+W.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER+':after { content: "" !important; display: none !important; }')},r.prototype.disableAnimations=function(t){this.createStyles(t,"* { -webkit-animation: none !important; -moz-animation: none !important; -o-animation: none !important; animation: none !important; -webkit-transition: none !important; -moz-transition: none !important; -o-transition: none !important; transition: none !important;}")},r.prototype.createStyles=function(t,e){var n=t.createElement("style");n.innerHTML=e,t.body.appendChild(n)},r.prototype.getPseudoElements=function(t){var e=[[t]];if(t.node.nodeType===Node.ELEMENT_NODE){var n=this.getPseudoElement(t,":before"),r=this.getPseudoElement(t,":after");n&&e.push(n),r&&e.push(r)}return R(e)},r.prototype.getPseudoElement=function(t,e){var n=t.computedStyle(e);if(!n||!n.content||"none"===n.content||"-moz-alt-content"===n.content||"none"===n.display)return null;for(var r=D(n.content),i="url"===r.substr(0,3),s=document.createElement(i?"img":"html2canvaspseudoelement"),a=new W(s,t,e),c=n.length-1;c>=0;c--){var h=o(n.item(c));s.style[h]=n[h]}if(s.className=W.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+" "+W.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER,i)return s.src=K(r)[0].args[0],[a];var l=document.createTextNode(r);return s.appendChild(l),[a,new H(l,a)]},r.prototype.getChildren=function(t){return R([].filter.call(t.node.childNodes,x).map(function(e){var n=[e.nodeType===Node.TEXT_NODE?new H(e,t):new z(e,t)].filter(B);return e.nodeType===Node.ELEMENT_NODE&&n.length&&"TEXTAREA"!==e.tagName?n[0].isElementVisible()?n.concat(this.getChildren(n[0])):[]:n},this))},r.prototype.newStackingContext=function(t,e){var n=new X(e,t.getOpacity(),t.node,t.parent);t.cloneTo(n),(e?n.getParentStack(this):n.parent.stack).contexts.push(n),t.stack=n},r.prototype.createStackingContexts=function(){this.nodes.forEach(function(t){T(t)&&(this.isRootElement(t)||P(t)||S(t)||this.isBodyWithTransparentRoot(t)||t.hasTransform())?this.newStackingContext(t,!0):T(t)&&(k(t)&&g(t)||I(t)||C(t))?this.newStackingContext(t,!1):t.assignStack(t.parent.stack)},this)},r.prototype.isBodyWithTransparentRoot=function(t){return"BODY"===t.node.nodeName&&t.parent.color("backgroundColor").isTransparent()},r.prototype.isRootElement=function(t){return null===t.parent},r.prototype.sortStackingContexts=function(t){t.contexts.sort(O(t.contexts.slice(0))),t.contexts.forEach(this.sortStackingContexts,this)},r.prototype.parseTextBounds=function(t){return function(e,n,r){if("none"!==t.parent.css("textDecoration").substr(0,4)||0!==e.trim().length){if(this.support.rangeBounds&&!t.parent.hasTransform()){var i=r.slice(0,n).join("").length;return this.getRangeBounds(t.node,i,e.length)}if(t.node&&"string"==typeof t.node.data){var o=t.node.splitText(e.length),s=this.getWrapperBounds(t.node,t.parent.hasTransform());return t.node=o,s}}else this.support.rangeBounds&&!t.parent.hasTransform()||(t.node=t.node.splitText(e.length));return{}}},r.prototype.getWrapperBounds=function(t,e){var n=t.ownerDocument.createElement("html2canvaswrapper"),r=t.parentNode,i=t.cloneNode(!0);n.appendChild(t.cloneNode(!0)),r.replaceChild(n,t);var o=e?Z(n):Q(n);return r.replaceChild(i,n),o},r.prototype.getRangeBounds=function(t,e,n){var r=this.range||(this.range=t.ownerDocument.createRange());return r.setStart(t,e),r.setEnd(t,e+n),r.getBoundingClientRect()},r.prototype.parse=function(t){var e=t.contexts.filter(f),n=t.children.filter(T),r=n.filter(A(C)),i=r.filter(A(k)).filter(A(m)),o=n.filter(A(k)).filter(C),a=r.filter(A(k)).filter(m),c=t.contexts.concat(r.filter(k)).filter(g),h=t.children.filter(q).filter(y),l=t.contexts.filter(d);e.concat(i).concat(o).concat(a).concat(c).concat(h).concat(l).forEach(function(t){this.renderQueue.push(t),w(t)&&(this.parse(t),this.renderQueue.push(new s))},this)},r.prototype.paint=function(t){try{t instanceof s?this.renderer.ctx.restore():q(t)?(_(t.parent)&&t.parent.appendToDOM(),this.paintText(t),_(t.parent)&&t.parent.cleanDOM()):this.paintNode(t)}catch(t){if(N(t),this.options.strict)throw t}},r.prototype.paintNode=function(t){w(t)&&(this.renderer.setOpacity(t.opacity),this.renderer.ctx.save(),t.hasTransform()&&this.renderer.setTransform(t.parseTransform())),"INPUT"===t.node.nodeName&&"checkbox"===t.node.type?this.paintCheckbox(t):"INPUT"===t.node.nodeName&&"radio"===t.node.type?this.paintRadio(t):this.paintElement(t)},r.prototype.paintElement=function(t){var e=t.parseBounds();this.renderer.clip(t.backgroundClip,function(){this.renderer.renderBackground(t,e,t.borders.borders.map(E))},this),this.renderer.clip(t.clip,function(){this.renderer.renderBorders(t.borders.borders)},this),this.renderer.clip(t.backgroundClip,function(){switch(t.node.nodeName){case"svg":case"IFRAME":var n=this.images.get(t.node);n?this.renderer.renderImage(t,e,t.borders,n):N("Error loading <"+t.node.nodeName+">",t.node);break;case"IMG":var r=this.images.get(t.node.src);r?this.renderer.renderImage(t,e,t.borders,r):N("Error loading <img>",t.node.src);break;case"CANVAS":this.renderer.renderImage(t,e,t.borders,{image:t.node});break;case"SELECT":case"INPUT":case"TEXTAREA":this.paintFormValue(t)}},this)},r.prototype.paintCheckbox=function(t){var e=t.parseBounds(),n=Math.min(e.width,e.height),r={width:n-1,height:n-1,top:e.top,left:e.left},i=[3,3],o=[i,i,i,i],s=[1,1,1,1].map(function(t){return{color:new G("#A5A5A5"),width:t}}),c=h(r,o,s);this.renderer.clip(t.backgroundClip,function(){this.renderer.rectangle(r.left+1,r.top+1,r.width-2,r.height-2,new G("#DEDEDE")),this.renderer.renderBorders(a(s,r,c,o)),t.node.checked&&(this.renderer.font(new G("#424242"),"normal","normal","bold",n-3+"px","arial"),this.renderer.text("✔",r.left+n/6,r.top+n-1))},this)},r.prototype.paintRadio=function(t){var e=t.parseBounds(),n=Math.min(e.width,e.height)-2;this.renderer.clip(t.backgroundClip,function(){this.renderer.circleStroke(e.left+1,e.top+1,n,new G("#DEDEDE"),1,new G("#A5A5A5")),t.node.checked&&this.renderer.circle(Math.ceil(e.left+n/4)+1,Math.ceil(e.top+n/4)+1,Math.floor(n/2),new G("#424242"))},this)},r.prototype.paintFormValue=function(t){var e=t.getValue();if(e.length>0){var n=t.node.ownerDocument,r=n.createElement("html2canvaswrapper");["lineHeight","textAlign","fontFamily","fontWeight","fontSize","color","paddingLeft","paddingTop","paddingRight","paddingBottom","width","height","borderLeftStyle","borderTopStyle","borderLeftWidth","borderTopWidth","boxSizing","whiteSpace","wordWrap"].forEach(function(e){try{r.style[e]=t.css(e)}catch(t){N("html2canvas: Parse: Exception caught in renderFormValue: "+t.message)}});var i=t.parseBounds();r.style.position="fixed",r.style.left=i.left+"px",r.style.top=i.top+"px",r.textContent=e,n.body.appendChild(r),this.paintText(new H(r.firstChild,t)),n.body.removeChild(r)}},r.prototype.paintText=function(t){t.applyTextTransform();var e=L.ucs2.decode(t.node.data),n=this.options.letterRendering&&!v(t)||U(t.node.data)?e.map(function(t){return L.ucs2.encode([t])}):j(e),r=t.parent.fontWeight(),i=t.parent.css("fontSize"),o=t.parent.css("fontFamily"),s=t.parent.parseTextShadows();this.renderer.font(t.parent.color("color"),t.parent.css("fontStyle"),t.parent.css("fontVariant"),r,i,o),s.length?this.renderer.fontShadow(s[0].color,s[0].offsetX,s[0].offsetY,s[0].blur):this.renderer.clearShadow(),this.renderer.clip(t.parent.clip,function(){n.map(this.parseTextBounds(t),this).forEach(function(e,r){e&&(this.renderer.text(n[r],e.left,e.bottom),this.renderTextDecoration(t.parent,e,this.fontMetrics.getMetrics(o,i)))},this)},this)},r.prototype.renderTextDecoration=function(t,e,n){switch(t.css("textDecoration").split(" ")[0]){case"underline":this.renderer.rectangle(e.left,Math.round(e.top+n.baseline+n.lineWidth),e.width,1,t.color("color"));break;case"overline":this.renderer.rectangle(e.left,Math.round(e.top),e.width,1,t.color("color"));break;case"line-through":this.renderer.rectangle(e.left,Math.ceil(e.top+n.middle+n.lineWidth),e.width,1,t.color("color"))}};var $={inset:[["darken",.6],["darken",.1],["darken",.1],["darken",.6]]};r.prototype.parseBorders=function(t){var e=t.parseBounds(),n=b(t),r=["Top","Right","Bottom","Left"].map(function(e,n){var r=t.css("border"+e+"Style"),i=t.color("border"+e+"Color");"inset"===r&&i.isBlack()&&(i=new G([255,255,255,i.a]));var o=$[r]?$[r][n]:null;return{width:t.cssInt("border"+e+"Width"),color:o?i[o[0]](o[1]):i,args:null}}),i=h(e,n,r);return{clip:this.parseBackgroundClip(t,i,r,n,e),borders:a(r,e,i,n)}},r.prototype.parseBackgroundClip=function(t,e,n,r,i){var o=[];switch(t.css("backgroundClip")){case"content-box":case"padding-box":p(o,r[0],r[1],e.topLeftInner,e.topRightInner,i.left+n[3].width,i.top+n[0].width),p(o,r[1],r[2],e.topRightInner,e.bottomRightInner,i.left+i.width-n[1].width,i.top+n[0].width),p(o,r[2],r[3],e.bottomRightInner,e.bottomLeftInner,i.left+i.width-n[1].width,i.top+i.height-n[2].width),p(o,r[3],r[0],e.bottomLeftInner,e.topLeftInner,i.left+n[3].width,i.top+i.height-n[2].width);break;default:p(o,r[0],r[1],e.topLeftOuter,e.topRightOuter,i.left,i.top),p(o,r[1],r[2],e.topRightOuter,e.bottomRightOuter,i.left+i.width,i.top),p(o,r[2],r[3],e.bottomRightOuter,e.bottomLeftOuter,i.left+i.width,i.top+i.height),p(o,r[3],r[0],e.bottomLeftOuter,e.topLeftOuter,i.left,i.top+i.height)}return o},e.exports=r},{"./color":3,"./fontmetrics":7,"./log":13,"./nodecontainer":14,"./pseudoelementcontainer":18,"./stackingcontext":21,"./textcontainer":25,"./utils":26,punycode:1}],16:[function(t,e,n){function r(t,e,n){var r="withCredentials"in new XMLHttpRequest;if(!e)return Promise.reject("No proxy configured");var a=o(r),h=s(e,t,a);return r?c(h):i(n,h,a).then(function(t){return p(t.content)})}function i(t,e,n){return new Promise(function(r,i){var o=t.createElement("script"),s=function(){delete window.html2canvas.proxy[n],t.body.removeChild(o)};window.html2canvas.proxy[n]=function(t){s(),r(t)},o.src=e,o.onerror=function(t){s(),i(t)},t.body.appendChild(o)})}function o(t){return t?"":"html2canvas_"+Date.now()+"_"+ ++f+"_"+Math.round(1e5*Math.random())}function s(t,e,n){return t+"?url="+encodeURIComponent(e)+(n.length?"&callback=html2canvas.proxy."+n:"")}function a(t){return function(e){var n,r=new DOMParser;try{n=r.parseFromString(e,"text/html")}catch(t){l("DOMParser not supported, falling back to createHTMLDocument"),n=document.implementation.createHTMLDocument("");try{n.open(),n.write(e),n.close()}catch(t){l("createHTMLDocument write not supported, falling back to document.body.innerHTML"),n.body.innerHTML=e}}var i=n.querySelector("base");if(!i||!i.href.host){var o=n.createElement("base");o.href=t,n.head.insertBefore(o,n.head.firstChild)}return n}}var c=t("./xhr"),h=t("./utils"),l=t("./log"),u=t("./clone"),p=h.decode64,f=0;n.Proxy=r,n.ProxyURL=function(t,e,n){var r="crossOrigin"in new Image,a=o(r),c=s(e,t,a);return r?Promise.resolve(c):i(n,c,a).then(function(t){return"data:"+t.type+";base64,"+t.content})},n.loadUrlDocument=function(t,e,n,i,o,s){return new r(t,e,window.document).then(a(t)).then(function(t){return u(t,n,i,o,s,0,0)})}},{"./clone":2,"./log":13,"./utils":26,"./xhr":28}],17:[function(t,e,n){var r=t("./proxy").ProxyURL;e.exports=function(t,e){var n=document.createElement("a");n.href=t,t=n.href,this.src=t,this.image=new Image;var i=this;this.promise=new Promise(function(n,o){i.image.crossOrigin="Anonymous",i.image.onload=n,i.image.onerror=o,new r(t,e,document).then(function(t){i.image.src=t}).catch(o)})}},{"./proxy":16}],18:[function(t,e,n){function r(t,e,n){i.call(this,t,e),this.isPseudoElement=!0,this.before=":before"===n}var i=t("./nodecontainer");r.prototype.cloneTo=function(t){r.prototype.cloneTo.call(this,t),t.isPseudoElement=!0,t.before=this.before},(r.prototype=Object.create(i.prototype)).appendToDOM=function(){this.before?this.parent.node.insertBefore(this.node,this.parent.node.firstChild):this.parent.node.appendChild(this.node),this.parent.node.className+=" "+this.getHideClass()},r.prototype.cleanDOM=function(){this.node.parentNode.removeChild(this.node),this.parent.node.className=this.parent.node.className.replace(this.getHideClass(),"")},r.prototype.getHideClass=function(){return this["PSEUDO_HIDE_ELEMENT_CLASS_"+(this.before?"BEFORE":"AFTER")]},r.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE="___html2canvas___pseudoelement_before",r.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER="___html2canvas___pseudoelement_after",e.exports=r},{"./nodecontainer":14}],19:[function(t,e,n){function r(t,e,n,r,i){this.width=t,this.height=e,this.images=n,this.options=r,this.document=i}var i=t("./log");r.prototype.renderImage=function(t,e,n,r){var i=t.cssInt("paddingLeft"),o=t.cssInt("paddingTop"),s=t.cssInt("paddingRight"),a=t.cssInt("paddingBottom"),c=n.borders,h=e.width-(c[1].width+c[3].width+i+s),l=e.height-(c[0].width+c[2].width+o+a);this.drawImage(r,0,0,r.image.width||h,r.image.height||l,e.left+i+c[3].width,e.top+o+c[0].width,h,l)},r.prototype.renderBackground=function(t,e,n){e.height>0&&e.width>0&&(this.renderBackgroundColor(t,e),this.renderBackgroundImage(t,e,n))},r.prototype.renderBackgroundColor=function(t,e){var n=t.color("backgroundColor");n.isTransparent()||this.rectangle(e.left,e.top,e.width,e.height,n)},r.prototype.renderBorders=function(t){t.forEach(this.renderBorder,this)},r.prototype.renderBorder=function(t){t.color.isTransparent()||null===t.args||this.drawShape(t.args,t.color)},r.prototype.renderBackgroundImage=function(t,e,n){t.parseBackgroundImages().reverse().forEach(function(r,o,s){switch(r.method){case"url":var a=this.images.get(r.args[0]);a?this.renderBackgroundRepeating(t,e,a,s.length-(o+1),n):i("Error loading background-image",r.args[0]);break;case"linear-gradient":case"gradient":var c=this.images.get(r.value);c?this.renderBackgroundGradient(c,e,n):i("Error loading background-image",r.args[0]);break;case"none":break;default:i("Unknown background-image type",r.args[0])}},this)},r.prototype.renderBackgroundRepeating=function(t,e,n,r,i){var o=t.parseBackgroundSize(e,n.image,r),s=t.parseBackgroundPosition(e,n.image,r,o);switch(t.parseBackgroundRepeat(r)){case"repeat-x":case"repeat no-repeat":this.backgroundRepeatShape(n,s,o,e,e.left+i[3],e.top+s.top+i[0],99999,o.height,i);break;case"repeat-y":case"no-repeat repeat":this.backgroundRepeatShape(n,s,o,e,e.left+s.left+i[3],e.top+i[0],o.width,99999,i);break;case"no-repeat":this.backgroundRepeatShape(n,s,o,e,e.left+s.left+i[3],e.top+s.top+i[0],o.width,o.height,i);break;default:this.renderBackgroundRepeat(n,s,o,{top:e.top,left:e.left},i[3],i[0])}},e.exports=r},{"./log":13}],20:[function(t,e,n){function r(t,e){o.apply(this,arguments),this.canvas=this.options.canvas||this.document.createElement("canvas"),this.options.canvas||(this.canvas.width=t,this.canvas.height=e),this.ctx=this.canvas.getContext("2d"),this.taintCtx=this.document.createElement("canvas").getContext("2d"),this.ctx.textBaseline="bottom",this.variables={},a("Initialized CanvasRenderer with size",t,"x",e)}function i(t){return t.length>0}var o=t("../renderer"),s=t("../lineargradientcontainer"),a=t("../log");(r.prototype=Object.create(o.prototype)).setFillStyle=function(t){return this.ctx.fillStyle="object"==typeof t&&t.isColor?t.toString():t,this.ctx},r.prototype.rectangle=function(t,e,n,r,i){this.setFillStyle(i).fillRect(t,e,n,r)},r.prototype.circle=function(t,e,n,r){this.setFillStyle(r),this.ctx.beginPath(),this.ctx.arc(t+n/2,e+n/2,n/2,0,2*Math.PI,!0),this.ctx.closePath(),this.ctx.fill()},r.prototype.circleStroke=function(t,e,n,r,i,o){this.circle(t,e,n,r),this.ctx.strokeStyle=o.toString(),this.ctx.stroke()},r.prototype.drawShape=function(t,e){this.shape(t),this.setFillStyle(e).fill()},r.prototype.taints=function(t){if(null===t.tainted){this.taintCtx.drawImage(t.image,0,0);try{this.taintCtx.getImageData(0,0,1,1),t.tainted=!1}catch(e){this.taintCtx=document.createElement("canvas").getContext("2d"),t.tainted=!0}}return t.tainted},r.prototype.drawImage=function(t,e,n,r,i,o,s,a,c){this.taints(t)&&!this.options.allowTaint||this.ctx.drawImage(t.image,e,n,r,i,o,s,a,c)},r.prototype.clip=function(t,e,n){this.ctx.save(),t.filter(i).forEach(function(t){this.shape(t).clip()},this),e.call(n),this.ctx.restore()},r.prototype.shape=function(t){return this.ctx.beginPath(),t.forEach(function(t,e){"rect"===t[0]?this.ctx.rect.apply(this.ctx,t.slice(1)):this.ctx[0===e?"moveTo":t[0]+"To"].apply(this.ctx,t.slice(1))},this),this.ctx.closePath(),this.ctx},r.prototype.font=function(t,e,n,r,i,o){this.setFillStyle(t).font=[e,n,r,i,o].join(" ").split(",")[0]},r.prototype.fontShadow=function(t,e,n,r){this.setVariable("shadowColor",t.toString()).setVariable("shadowOffsetY",e).setVariable("shadowOffsetX",n).setVariable("shadowBlur",r)},r.prototype.clearShadow=function(){this.setVariable("shadowColor","rgba(0,0,0,0)")},r.prototype.setOpacity=function(t){this.ctx.globalAlpha=t},r.prototype.setTransform=function(t){this.ctx.translate(t.origin[0],t.origin[1]),this.ctx.transform.apply(this.ctx,t.matrix),this.ctx.translate(-t.origin[0],-t.origin[1])},r.prototype.setVariable=function(t,e){return this.variables[t]!==e&&(this.variables[t]=this.ctx[t]=e),this},r.prototype.text=function(t,e,n){this.ctx.fillText(t,e,n)},r.prototype.backgroundRepeatShape=function(t,e,n,r,i,o,s,a,c){var h=[["line",Math.round(i),Math.round(o)],["line",Math.round(i+s),Math.round(o)],["line",Math.round(i+s),Math.round(a+o)],["line",Math.round(i),Math.round(a+o)]];this.clip([h],function(){this.renderBackgroundRepeat(t,e,n,r,c[3],c[0])},this)},r.prototype.renderBackgroundRepeat=function(t,e,n,r,i,o){var s=Math.round(r.left+e.left+i),a=Math.round(r.top+e.top+o);this.setFillStyle(this.ctx.createPattern(this.resizeImage(t,n),"repeat")),this.ctx.translate(s,a),this.ctx.fill(),this.ctx.translate(-s,-a)},r.prototype.renderBackgroundGradient=function(t,e){if(t instanceof s){var n=this.ctx.createLinearGradient(e.left+e.width*t.x0,e.top+e.height*t.y0,e.left+e.width*t.x1,e.top+e.height*t.y1);t.colorStops.forEach(function(t){n.addColorStop(t.stop,t.color.toString())}),this.rectangle(e.left,e.top,e.width,e.height,n)}},r.prototype.resizeImage=function(t,e){var n=t.image;if(n.width===e.width&&n.height===e.height)return n;var r=document.createElement("canvas");return r.width=e.width,r.height=e.height,r.getContext("2d").drawImage(n,0,0,n.width,n.height,0,0,e.width,e.height),r},e.exports=r},{"../lineargradientcontainer":12,"../log":13,"../renderer":19}],21:[function(t,e,n){function r(t,e,n,r){i.call(this,n,r),this.ownStacking=t,this.contexts=[],this.children=[],this.opacity=(this.parent?this.parent.stack.opacity:1)*e}var i=t("./nodecontainer");(r.prototype=Object.create(i.prototype)).getParentStack=function(t){var e=this.parent?this.parent.stack:null;return e?e.ownStacking?e:e.getParentStack(t):t.stack},e.exports=r},{"./nodecontainer":14}],22:[function(t,e,n){function r(t){this.rangeBounds=this.testRangeBounds(t),this.cors=this.testCORS(),this.svg=this.testSVG()}r.prototype.testRangeBounds=function(t){var e,n,r=!1;return t.createRange&&(e=t.createRange()).getBoundingClientRect&&((n=t.createElement("boundtest")).style.height="123px",n.style.display="block",t.body.appendChild(n),e.selectNode(n),123===e.getBoundingClientRect().height&&(r=!0),t.body.removeChild(n)),r},r.prototype.testCORS=function(){return void 0!==(new Image).crossOrigin},r.prototype.testSVG=function(){var t=new Image,e=document.createElement("canvas"),n=e.getContext("2d");t.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{n.drawImage(t,0,0),e.toDataURL()}catch(t){return!1}return!0},e.exports=r},{}],23:[function(t,e,n){function r(t){this.src=t,this.image=null;var e=this;this.promise=this.hasFabric().then(function(){return e.isInline(t)?Promise.resolve(e.inlineFormatting(t)):i(t)}).then(function(t){return new Promise(function(n){window.html2canvas.svg.fabric.loadSVGFromString(t,e.createCanvas.call(e,n))})})}var i=t("./xhr"),o=t("./utils").decode64;r.prototype.hasFabric=function(){return window.html2canvas.svg&&window.html2canvas.svg.fabric?Promise.resolve():Promise.reject(new Error("html2canvas.svg.js is not loaded, cannot render svg"))},r.prototype.inlineFormatting=function(t){return/^data:image\/svg\+xml;base64,/.test(t)?this.decode64(this.removeContentType(t)):this.removeContentType(t)},r.prototype.removeContentType=function(t){return t.replace(/^data:image\/svg\+xml(;base64)?,/,"")},r.prototype.isInline=function(t){return/^data:image\/svg\+xml/i.test(t)},r.prototype.createCanvas=function(t){var e=this;return function(n,r){var i=new window.html2canvas.svg.fabric.StaticCanvas("c");e.image=i.lowerCanvasEl,i.setWidth(r.width).setHeight(r.height).add(window.html2canvas.svg.fabric.util.groupSVGElements(n,r)).renderAll(),t(i.lowerCanvasEl)}},r.prototype.decode64=function(t){return"function"==typeof window.atob?window.atob(t):o(t)},e.exports=r},{"./utils":26,"./xhr":28}],24:[function(t,e,n){function r(t,e){this.src=t,this.image=null;var n=this;this.promise=e?new Promise(function(e,r){n.image=new Image,n.image.onload=e,n.image.onerror=r,n.image.src="data:image/svg+xml,"+(new XMLSerializer).serializeToString(t),!0===n.image.complete&&e(n.image)}):this.hasFabric().then(function(){return new Promise(function(e){window.html2canvas.svg.fabric.parseSVGDocument(t,n.createCanvas.call(n,e))})})}var i=t("./svgcontainer");r.prototype=Object.create(i.prototype),e.exports=r},{"./svgcontainer":23}],25:[function(t,e,n){function r(t,e){o.call(this,t,e)}function i(t,e,n){if(t.length>0)return e+n.toUpperCase()}var o=t("./nodecontainer");(r.prototype=Object.create(o.prototype)).applyTextTransform=function(){this.node.data=this.transform(this.parent.css("textTransform"))},r.prototype.transform=function(t){var e=this.node.data;switch(t){case"lowercase":return e.toLowerCase();case"capitalize":return e.replace(/(^|\s|:|-|\(|\))([a-z])/g,i);case"uppercase":return e.toUpperCase();default:return e}},e.exports=r},{"./nodecontainer":14}],26:[function(t,e,n){n.smallImage=function(){return"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"},n.bind=function(t,e){return function(){return t.apply(e,arguments)}},/*
 * base64-arraybuffer
 * https://github.com/niklasvh/base64-arraybuffer
 *
 * Copyright (c) 2012 Niklas von Hertzen
 * Licensed under the MIT license.
 */
n.decode64=function(t){var e,n,r,i,o,s,a,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",h=t.length,l="";for(e=0;e<h;e+=4)o=c.indexOf(t[e])<<2|(n=c.indexOf(t[e+1]))>>4,s=(15&n)<<4|(r=c.indexOf(t[e+2]))>>2,a=(3&r)<<6|(i=c.indexOf(t[e+3])),l+=64===r?String.fromCharCode(o):64===i||-1===i?String.fromCharCode(o,s):String.fromCharCode(o,s,a);return l},n.getBounds=function(t){if(t.getBoundingClientRect){var e=t.getBoundingClientRect(),n=null==t.offsetWidth?e.width:t.offsetWidth;return{top:e.top,bottom:e.bottom||e.top+e.height,right:e.left+n,left:e.left,width:n,height:null==t.offsetHeight?e.height:t.offsetHeight}}return{}},n.offsetBounds=function(t){var e=t.offsetParent?n.offsetBounds(t.offsetParent):{top:0,left:0};return{top:t.offsetTop+e.top,bottom:t.offsetTop+t.offsetHeight+e.top,right:t.offsetLeft+e.left+t.offsetWidth,left:t.offsetLeft+e.left,width:t.offsetWidth,height:t.offsetHeight}},n.parseBackgrounds=function(t){var e,n,r,i,o,s,a,c=[],h=0,l=0,u=function(){e&&('"'===n.substr(0,1)&&(n=n.substr(1,n.length-2)),n&&a.push(n),"-"===e.substr(0,1)&&(i=e.indexOf("-",1)+1)>0&&(r=e.substr(0,i),e=e.substr(i)),c.push({prefix:r,method:e.toLowerCase(),value:o,args:a,image:null})),a=[],e=r=n=o=""};return a=[],e=r=n=o="",t.split("").forEach(function(t){if(!(0===h&&" \r\n\t".indexOf(t)>-1)){switch(t){case'"':s?s===t&&(s=null):s=t;break;case"(":if(s)break;if(0===h)return h=1,void(o+=t);l++;break;case")":if(s)break;if(1===h){if(0===l)return h=0,o+=t,void u();l--}break;case",":if(s)break;if(0===h)return void u();if(1===h&&0===l&&!e.match(/^url$/i))return a.push(n),n="",void(o+=t)}o+=t,0===h?e+=t:n+=t}}),u(),c}},{}],27:[function(t,e,n){function r(t){i.apply(this,arguments),this.type="linear"===t.args[0]?i.TYPES.LINEAR:i.TYPES.RADIAL}var i=t("./gradientcontainer");r.prototype=Object.create(i.prototype),e.exports=r},{"./gradientcontainer":9}],28:[function(t,e,n){e.exports=function(t){return new Promise(function(e,n){var r=new XMLHttpRequest;r.open("GET",t),r.onload=function(){200===r.status?e(r.responseText):n(new Error(r.statusText))},r.onerror=function(){n(new Error("Network Error"))},r.send()})}},{}]},{},[4])(4)}),/*
# PNG.js
# Copyright (c) 2011 Devon Govett
# MIT LICENSE
# 
# 
*/
function(t){var e;e=function(){function e(t){var e,n,r,i,o,s,a,c,h,l,u,p,f,d,g;for(this.data=t,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},s=null;;){switch(e=this.readUInt32(),l=function(){var t,e;for(e=[],a=t=0;t<4;a=++t)e.push(String.fromCharCode(this.data[this.pos++]));return e}.call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(e);break;case"fcTL":s&&this.animation.frames.push(s),this.pos+=4,s={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},o=this.readUInt16(),i=this.readUInt16()||100,s.delay=1e3*o/i,s.disposeOp=this.data[this.pos++],s.blendOp=this.data[this.pos++],s.data=[];break;case"IDAT":case"fdAT":for("fdAT"===l&&(this.pos+=4,e-=4),t=(null!=s?s.data:void 0)||this.imgData,a=f=0;0<=e?f<e:f>e;a=0<=e?++f:--f)t.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(r=this.palette.length/3,this.transparency.indexed=this.read(e),this.transparency.indexed.length>r)throw new Error("More transparent colors than palette size");if((u=r-this.transparency.indexed.length)>0)for(a=d=0;0<=u?d<u:d>u;a=0<=u?++d:--d)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":c=(p=this.read(e)).indexOf(0),h=String.fromCharCode.apply(String,p.slice(0,c)),this.text[h]=String.fromCharCode.apply(String,p.slice(c+1));break;case"IEND":return s&&this.animation.frames.push(s),this.colors=function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}.call(this),this.hasAlphaChannel=4===(g=this.colorType)||6===g,n=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*n,this.colorSpace=function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}.call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}var n,r,i;e.load=function(t,n,r){var i;return"function"==typeof n&&(r=n),(i=new XMLHttpRequest).open("GET",t,!0),i.responseType="arraybuffer",i.onload=function(){var t,o;return t=new Uint8Array(i.response||i.mozResponseArrayBuffer),o=new e(t),"function"==typeof(null!=n?n.getContext:void 0)&&o.render(n),"function"==typeof r?r(o):void 0},i.send(null)},e.prototype.read=function(t){var e,n;for(n=[],e=0;0<=t?e<t:e>t;0<=t?++e:--e)n.push(this.data[this.pos++]);return n},e.prototype.readUInt32=function(){var t,e,n,r;return t=this.data[this.pos++]<<24,e=this.data[this.pos++]<<16,n=this.data[this.pos++]<<8,r=this.data[this.pos++],t|e|n|r},e.prototype.readUInt16=function(){var t,e;return t=this.data[this.pos++]<<8,e=this.data[this.pos++],t|e},e.prototype.decodePixels=function(t){var e,n,r,i,s,a,c,h,l,u,p,f,d,g,m,w,y,v,b,x,S,k,C;if(null==t&&(t=this.imgData),0===t.length)return new Uint8Array(0);for(t=(t=new o(t)).getBytes(),w=(f=this.pixelBitlength/8)*this.width,d=new Uint8Array(w*this.height),a=t.length,m=0,g=0,n=0;g<a;){switch(t[g++]){case 0:for(i=b=0;b<w;i=b+=1)d[n++]=t[g++];break;case 1:for(i=x=0;x<w;i=x+=1)e=t[g++],s=i<f?0:d[n-f],d[n++]=(e+s)%256;break;case 2:for(i=S=0;S<w;i=S+=1)e=t[g++],r=(i-i%f)/f,y=m&&d[(m-1)*w+r*f+i%f],d[n++]=(y+e)%256;break;case 3:for(i=k=0;k<w;i=k+=1)e=t[g++],r=(i-i%f)/f,s=i<f?0:d[n-f],y=m&&d[(m-1)*w+r*f+i%f],d[n++]=(e+Math.floor((s+y)/2))%256;break;case 4:for(i=C=0;C<w;i=C+=1)e=t[g++],r=(i-i%f)/f,s=i<f?0:d[n-f],0===m?y=v=0:(y=d[(m-1)*w+r*f+i%f],v=r&&d[(m-1)*w+(r-1)*f+i%f]),c=s+y-v,h=Math.abs(c-s),u=Math.abs(c-y),p=Math.abs(c-v),l=h<=u&&h<=p?s:u<=p?y:v,d[n++]=(e+l)%256;break;default:throw new Error("Invalid filter algorithm: "+t[g-1])}m++}return d},e.prototype.decodePalette=function(){var t,e,n,r,i,o,s,a,c;for(n=this.palette,o=this.transparency.indexed||[],i=new Uint8Array((o.length||0)+n.length),r=0,t=0,e=s=0,a=n.length;s<a;e=s+=3)i[r++]=n[e],i[r++]=n[e+1],i[r++]=n[e+2],i[r++]=null!=(c=o[t++])?c:255;return i},e.prototype.copyToImageData=function(t,e){var n,r,i,o,s,a,c,h,l,u,p;if(r=this.colors,l=null,n=this.hasAlphaChannel,this.palette.length&&(l=null!=(p=this._decodedPalette)?p:this._decodedPalette=this.decodePalette(),r=4,n=!0),i=t.data||t,h=i.length,s=l||e,o=a=0,1===r)for(;o<h;)c=l?4*e[o/4]:a,u=s[c++],i[o++]=u,i[o++]=u,i[o++]=u,i[o++]=n?s[c++]:255,a=c;else for(;o<h;)c=l?4*e[o/4]:a,i[o++]=s[c++],i[o++]=s[c++],i[o++]=s[c++],i[o++]=n?s[c++]:255,a=c},e.prototype.decode=function(){var t;return t=new Uint8Array(this.width*this.height*4),this.copyToImageData(t,this.decodePixels()),t};try{r=t.document.createElement("canvas"),i=r.getContext("2d")}catch(t){return-1}return n=function(t){var e;return i.width=t.width,i.height=t.height,i.clearRect(0,0,t.width,t.height),i.putImageData(t,0,0),e=new Image,e.src=r.toDataURL(),e},e.prototype.decodeFrames=function(t){var e,r,i,o,s,a,c,h;if(this.animation){for(h=[],r=s=0,a=(c=this.animation.frames).length;s<a;r=++s)e=c[r],i=t.createImageData(e.width,e.height),o=this.decodePixels(new Uint8Array(e.data)),this.copyToImageData(i,o),e.imageData=i,h.push(e.image=n(i));return h}},e.prototype.renderFrame=function(t,e){var n,r,i;return r=this.animation.frames,n=r[e],i=r[e-1],0===e&&t.clearRect(0,0,this.width,this.height),1===(null!=i?i.disposeOp:void 0)?t.clearRect(i.xOffset,i.yOffset,i.width,i.height):2===(null!=i?i.disposeOp:void 0)&&t.putImageData(i.imageData,i.xOffset,i.yOffset),0===n.blendOp&&t.clearRect(n.xOffset,n.yOffset,n.width,n.height),t.drawImage(n.image,n.xOffset,n.yOffset)},e.prototype.animate=function(t){var e,n,r,i,o,s,a=this;return n=0,s=this.animation,i=s.numFrames,r=s.frames,o=s.numPlays,(e=function(){var s,c;if(s=n++%i,c=r[s],a.renderFrame(t,s),i>1&&n/i<o)return a.animation._timeout=setTimeout(e,c.delay)})()},e.prototype.stopAnimation=function(){var t;return clearTimeout(null!=(t=this.animation)?t._timeout:void 0)},e.prototype.render=function(t){var e,n;return t._png&&t._png.stopAnimation(),t._png=this,t.width=this.width,t.height=this.height,e=t.getContext("2d"),this.animation?(this.decodeFrames(e),this.animate(e)):(n=e.createImageData(this.width,this.height),this.copyToImageData(n,this.decodePixels()),e.putImageData(n,0,0))},e}(),t.PNG=e}("undefined"!=typeof window&&window||window);/*
 * Extracted from pdf.js
 * https://github.com/andreasgal/pdf.js
 *
 * Copyright (c) 2011 Mozilla Foundation
 *
 * Contributors: Andreas Gal <<EMAIL>>
 *               Chris G Jones <<EMAIL>>
 *               Shaon Barman <<EMAIL>>
 *               Vivien Nicolas <<EMAIL>>
 *               Justin D'Arcangelo <<EMAIL>>
 *               Yury Delendik
 *
 * 
 */
var i=function(){function t(){this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=null}return t.prototype={ensureBuffer:function(t){var e=this.buffer,n=e?e.byteLength:0;if(t<n)return e;for(var r=512;r<t;)r<<=1;for(var i=new Uint8Array(r),o=0;o<n;++o)i[o]=e[o];return this.buffer=i},getByte:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return this.buffer[this.pos++]},getBytes:function(t){var e=this.pos;if(t){this.ensureBuffer(e+t);for(r=e+t;!this.eof&&this.bufferLength<r;)this.readBlock();var n=this.bufferLength;r>n&&(r=n)}else{for(;!this.eof;)this.readBlock();var r=this.bufferLength}return this.pos=r,this.buffer.subarray(e,r)},lookChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos])},getChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos++])},makeSubStream:function(t,e,n){for(var r=t+e;this.bufferLength<=r&&!this.eof;)this.readBlock();return new Stream(this.buffer,t,e,n)},skip:function(t){t||(t=1),this.pos+=t},reset:function(){this.pos=0}},t}(),o=function(){function t(t){throw new Error(t)}function e(e){var n=0,r=e[n++],o=e[n++];-1!=r&&-1!=o||t("Invalid header in flate stream"),8!=(15&r)&&t("Unknown compression method in flate stream"),((r<<8)+o)%31!=0&&t("Bad FCHECK in flate stream"),32&o&&t("FDICT bit set in flate stream"),this.bytes=e,this.bytesPos=n,this.codeSize=0,this.codeBuf=0,i.call(this)}if("undefined"!=typeof Uint32Array){var n=new Uint32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),r=new Uint32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),o=new Uint32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),s=[new Uint32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],a=[new Uint32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5];return e.prototype=Object.create(i.prototype),e.prototype.getBits=function(e){for(var n,r=this.codeSize,i=this.codeBuf,o=this.bytes,s=this.bytesPos;r<e;)void 0===(n=o[s++])&&t("Bad encoding in flate stream"),i|=n<<r,r+=8;return n=i&(1<<e)-1,this.codeBuf=i>>e,this.codeSize=r-=e,this.bytesPos=s,n},e.prototype.getCode=function(e){for(var n=e[0],r=e[1],i=this.codeSize,o=this.codeBuf,s=this.bytes,a=this.bytesPos;i<r;){var c;void 0===(c=s[a++])&&t("Bad encoding in flate stream"),o|=c<<i,i+=8}var h=n[o&(1<<r)-1],l=h>>16,u=65535&h;return(0==i||i<l||0==l)&&t("Bad encoding in flate stream"),this.codeBuf=o>>l,this.codeSize=i-l,this.bytesPos=a,u},e.prototype.generateHuffmanTable=function(t){for(var e=t.length,n=0,r=0;r<e;++r)t[r]>n&&(n=t[r]);for(var i=1<<n,o=new Uint32Array(i),s=1,a=0,c=2;s<=n;++s,a<<=1,c<<=1)for(var h=0;h<e;++h)if(t[h]==s){for(var l=0,u=a,r=0;r<s;++r)l=l<<1|1&u,u>>=1;for(r=l;r<i;r+=c)o[r]=s<<16|h;++a}return[o,n]},e.prototype.readBlock=function(){function e(t,e,n,r,i){for(var o=t.getBits(n)+r;o-- >0;)e[d++]=i}var i=this.getBits(3);if(1&i&&(this.eof=!0),0!=(i>>=1)){var c,h;if(1==i)c=s,h=a;else if(2==i){for(var l=this.getBits(5)+257,u=this.getBits(5)+1,p=this.getBits(4)+4,f=Array(n.length),d=0;d<p;)f[n[d++]]=this.getBits(3);for(var g=this.generateHuffmanTable(f),m=0,d=0,w=l+u,y=new Array(w);d<w;){var v=this.getCode(g);16==v?e(this,y,2,3,m):17==v?e(this,y,3,3,m=0):18==v?e(this,y,7,11,m=0):y[d++]=m=v}c=this.generateHuffmanTable(y.slice(0,l)),h=this.generateHuffmanTable(y.slice(l,w))}else t("Unknown block type in flate stream");for(var b=(F=this.buffer)?F.length:0,x=this.bufferLength;;){var S=this.getCode(c);if(S<256)x+1>=b&&(b=(F=this.ensureBuffer(x+1)).length),F[x++]=S;else{if(256==S)return void(this.bufferLength=x);var k=(S=r[S-=257])>>16;k>0&&(k=this.getBits(k));m=(65535&S)+k;S=this.getCode(h),(k=(S=o[S])>>16)>0&&(k=this.getBits(k));var C=(65535&S)+k;x+m>=b&&(b=(F=this.ensureBuffer(x+m)).length);for(var I=0;I<m;++I,++x)F[x]=F[x-C]}}}else{var A,T=this.bytes,_=this.bytesPos;void 0===(A=T[_++])&&t("Bad block header in flate stream");var q=A;void 0===(A=T[_++])&&t("Bad block header in flate stream"),q|=A<<8,void 0===(A=T[_++])&&t("Bad block header in flate stream");var O=A;void 0===(A=T[_++])&&t("Bad block header in flate stream"),(O|=A<<8)!=(65535&~q)&&t("Bad uncompressed block length in flate stream"),this.codeBuf=0,this.codeSize=0;var P=this.bufferLength,F=this.ensureBuffer(P+q),E=P+q;this.bufferLength=E;for(var B=P;B<E;++B){if(void 0===(A=T[_++])){this.eof=!0;break}F[B]=A}this.bytesPos=_}},e}}();return function(t){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";void 0===t.btoa&&(t.btoa=function(t){var n,r,i,o,s,a=0,c=0,h="",l=[];if(!t)return t;do{n=(s=t.charCodeAt(a++)<<16|t.charCodeAt(a++)<<8|t.charCodeAt(a++))>>18&63,r=s>>12&63,i=s>>6&63,o=63&s,l[c++]=e.charAt(n)+e.charAt(r)+e.charAt(i)+e.charAt(o)}while(a<t.length);h=l.join("");var u=t.length%3;return(u?h.slice(0,u-3):h)+"===".slice(u||3)}),void 0===t.atob&&(t.atob=function(t){var n,r,i,o,s,a,c=0,h=0,l=[];if(!t)return t;t+="";do{n=(a=e.indexOf(t.charAt(c++))<<18|e.indexOf(t.charAt(c++))<<12|(o=e.indexOf(t.charAt(c++)))<<6|(s=e.indexOf(t.charAt(c++))))>>16&255,r=a>>8&255,i=255&a,l[h++]=64==o?String.fromCharCode(n):64==s?String.fromCharCode(n,r):String.fromCharCode(n,r,i)}while(c<t.length);return l.join("")}),Array.prototype.map||(Array.prototype.map=function(t){if(void 0===this||null===this||"function"!=typeof t)throw new TypeError;for(var e=Object(this),n=e.length>>>0,r=new Array(n),i=arguments.length>1?arguments[1]:void 0,o=0;o<n;o++)o in e&&(r[o]=t.call(i,e[o],o,e));return r}),Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),Array.prototype.forEach||(Array.prototype.forEach=function(t,e){if(void 0===this||null===this||"function"!=typeof t)throw new TypeError;for(var n=Object(this),r=n.length>>>0,i=0;i<r;i++)i in n&&t.call(e,n[i],i,n)}),Object.keys||(Object.keys=function(){var t=Object.prototype.hasOwnProperty,e=!{toString:null}.propertyIsEnumerable("toString"),n=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],r=n.length;return function(i){if("object"!=typeof i&&("function"!=typeof i||null===i))throw new TypeError;var o,s,a=[];for(o in i)t.call(i,o)&&a.push(o);if(e)for(s=0;s<r;s++)t.call(i,n[s])&&a.push(n[s]);return a}}()),String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")}),String.prototype.trimLeft||(String.prototype.trimLeft=function(){return this.replace(/^\s+/g,"")}),String.prototype.trimRight||(String.prototype.trimRight=function(){return this.replace(/\s+$/g,"")})}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||window),e});