<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="AccountInfoMapper">

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_accountinfo
		set 
			compname = #{compname},
			taxnum = #{taxnum},
			accbank = #{accbank},
			bankaccount = #{bankaccount},
			compaddress = #{compaddress},
			telno = #{telno},
			accountcode = #{accountcode},
			accountname = #{accountname},
			accountingsys = #{accountingsys},
			comptype = #{comptype},
			accountingcode = #{accountingcode},
			startusedate = #{startusedate},
			periodofaccount = #{periodofaccount},
			<choose>
				<when test="autopickno != null and autopickno != ''">autopickno = 1,</when>
				<otherwise>autopickno = 0,</otherwise>
			</choose>
			<choose>
				<when test="sameperson != null and sameperson != ''">sameperson = 1,</when>
				<otherwise>sameperson = 0,</otherwise>
			</choose>
			incometaxrate = #{incometaxrate},
			calmethod = #{calmethod},
			plsubject_id = #{plsubject_id}
		where 
			id = 1
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.id,
			a.compname,
			a.taxnum,
			a.accbank,
			a.bankaccount,
			a.compaddress,
			a.telno,
			a.accountcode,
			a.accountname,
			a.accountingsys,
			a.comptype,
			a.accountingcode,
			a.startusedate,
			a.periodofaccount,
			a.autopickno,
			a.sameperson,
			a.incometaxrate,
			a.plsubject_id,
			c.aliasname as plsubject_name,
			a.initbalanceisacc,
			a.calmethod,
			a.version
		from 
			${ database }.c_accountinfo as a
			left join ${ database }.c_accsubjects as c on a.plsubject_id=c.id			
		where 
			a.id = 1
	</select>
	
	<select id="findSubList" parameterType="pd" resultType="pd">
		select a.* from ${ database }.c_accsubjects a
		where 1=1
			<if test="beginid !=null and beginid !='' ">
			and a.code &gt;= #{beginid}
			</if> 
			<if test="finishid !=null and finishid !='' ">
			and a.code &lt;= #{finishid}
			</if> 
			
			and not exists(select 1 from ${ database }.c_accsubjects s where a.id = s.parentid )
			<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
					and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join ${ database }.t_fillvouchers as b on t.fillvouchersid = b.id
									where t.subjectid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) 
									and  str_to_date( b.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( b.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m') )
							or exists(	select 1 from ${ database }.i_kmye i 
										where i.subjectid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)
										and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m')  )
							or (select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
								from ${ database }.i_kmye kmye 
								where kmye.subjectid = a.id 
								and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
						)
					</when>
					<otherwise>
						and 
						((select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
						from ${ database }.i_kmye kmye 
						where kmye.subjectid = a.id 
						and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
						
						or exists(	select 1 from ${ database }.i_kmye i
									where i.subjectid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) 
									and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
									and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m') ) )
					</otherwise>
				</choose>
			</if>
		order by a.code asc
	</select>
	
	
		<select id="findFirstLevelSubList" parameterType="pd" resultType="pd">
		select a.* from ${ database }.c_accsubjects a
		where 1=1
			<if test="beginid !=null and beginid !='' ">
			and a.code &gt;= #{beginid}
			</if> 
			<if test="finishid !=null and finishid !='' ">
			and a.code &lt;= #{finishid}
			</if> 
			and ifnull(a.parentid,'') =''
			<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t 
										left join ${ database }.t_fillvouchers as a on t.fillvouchersid = a.id
										left join ${ database }.c_accsubjects as b on t.subjectid = b.id 
									where left(b.code,4) = a.code and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) 
									and  str_to_date( a.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( a.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m'))
							or exists(	select 1 from ${ database }.i_kmye i left join ${ database }.c_accsubjects as b on i.subjectid = b.id 
										where left(b.code,4) = a.code and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)
										and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m')  )
										
							or (select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
								from ${ database }.i_kmye kmye 
								where kmye.subjectid = a.id 
								and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
						)
					</when>
					<otherwise>
						and (
						(select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
						from ${ database }.i_kmye kmye left join ${ database }.c_accsubjects as b on kmye.subjectid = b.id 
						where left(b.code,4) = a.code
						and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
						
						or exists(	select 1 from ${ database }.i_kmye i left join ${ database }.c_accsubjects as b on i.subjectid = b.id 
									where left(b.code,4) = a.code and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) 
									and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
									and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m') )
									)
					</otherwise>
				</choose>
			</if>
		order by a.code
	</select>
	
	<select id="findAuxList" parameterType="pd" resultType="pd">
	<if test="auxtype==1 or auxtype=='1' or auxtype=='客户' ">
		select a.* from ${ database }.c_customer a
		where 
			1=1
			<if test="beginid !=null and beginid !='' ">
			and a.id &gt;= #{beginid}
			</if> 
			<if test="finishid !=null and finishid !='' ">
			and a.id &lt;= #{finishid}
			</if> 
		<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join  ${ database }.c_accsubjects as acc on t.subjectid = acc.id 
									left join ${ database }.t_fillvouchers fill on t.fillvouchersid = fill.id 
									where t.assistaccountid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) and acc.auxbus = 1 
									and fill.accountperiod &gt;= #{month_begin} and fill.accountperiod &lt;= #{month_finish} and t.subjectid = #{subjectid})
							or exists(	select 1 from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) and (i.assisttype = '客户' or i.assisttype =1) 
										and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish}  and i.subjectid = #{subjectid})
										
							or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '客户' or i.assisttype =1) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
						)
					</when>
					<otherwise>
						and (exists(	select 1 from ${ database }.i_gzdxye i
									where i.assistid = a.id and (i.assisttype = '客户' or i.assisttype =1) and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)
									and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid}
									)
									
									or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
										from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (i.assisttype = '客户' or i.assisttype =1) and i.accountperiod &lt; #{month_begin}
										and i.subjectid = #{subjectid})!=0
									)
					</otherwise>
				</choose>
			</if>
		order by a.id asc 
	</if>
	<if test="auxtype==2 or auxtype=='2'  or auxtype=='供应商' ">
		select a.* from ${ database }.c_supplier a
		where 1=1
			<if test="beginid !=null and beginid !='' ">
			and a.id &gt;= #{beginid}
			</if> 
			<if test="finishid !=null and finishid !='' ">
			and a.id &lt;= #{finishid}
			</if> 
				<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join  ${ database }.c_accsubjects as acc on t.subjectid = acc.id 
									left join ${ database }.t_fillvouchers fill on t.fillvouchersid = fill.id 
									where t.assistaccountid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) and acc.auxbus = 2 
									and fill.accountperiod &gt;= #{month_begin} and fill.accountperiod &lt;= #{month_finish} and t.subjectid = #{subjectid})
							or exists(	select 1 from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)  and (i.assisttype = '供应商' or i.assisttype =2) 
										and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid})
										
							or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '供应商' or i.assisttype =2) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
						)
					</when>
					<otherwise>
						and  (exists(	select 1 from ${ database }.i_gzdxye i
									where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) and (i.assisttype = '供应商' or i.assisttype =2) 
									and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid})
								
								or 
								(select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '供应商' or i.assisttype =2) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0	
									)
					</otherwise>
				</choose>
			</if>
		order by a.id  asc 
	</if>
	</select>
	
		<select id="findGoodsList" parameterType="pd" resultType="pd">
		select a.* from ${ database }.c_goods a
		where 
			a.code &gt;= #{beginid}
			and a.code &lt;= #{finishid}
		order by a.code asc
	</select>
	
	
	
	<select id="findSaleoutList" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.make_date,
			a.make_date as createdate,
			a.customerid,
			b.name customer_name,
			a.total_money,
			a.tax_money,
			a.taxtotal,
			a.has_invoice,
			0 as now_invoice,
			ifnull(a.taxtotal,0) - ifnull(a.has_invoice,0) as un_invoice,
			a.this_invoice,
			a.pre_invoice,
			ifnull(a.invoicecode,'') invoicecode,
			a.voucher_id,
			v.code as vouchercode,
			ifnull(c.voucher_id,'') invoice_vouchernumber,
			ifnull(a.createby,'') createby,
			a.createtime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
			
		from 
			${ database }.t_saleout as a 
			left join ${ database }.c_customer as b on a.customerid=b.id
			left join ${ database }.t_fillvouchers as v on a.voucher_id = v.id
			left join ${ database }.t_saleinvoice as c on a.invoicecode = c.invoice_number
		where 
			left(a.make_date,7) = #{accountperiod }
		order by a.code asc
	</select>
	
	
	
	<select id="findPurchaseList" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.make_date,
			a.make_date as createdate,
			a.supplierid,
			b.name supplier_name,
			a.total_money,
			a.total_tax_money tax_money,
			a.total_taxtotal taxtotal,
		
			ifnull(a.invoicecode,'') invoicecode,
			a.voucher_id,
			v.code as vouchercode,
			ifnull(c.voucher_id,'') invoice_vouchernumber,
			ifnull(a.createby,'') createby,
			a.createtime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
			
		from 
			${ database }.t_purchaselist as a 
			left join ${ database }.c_supplier as b on a.supplierid=b.id
			left join ${ database }.t_fillvouchers as v on a.voucher_id = v.id
			left join ${ database }.t_purchaseinvoice as c on a.invoicecode = c.invoice_number
		where 
			left(a.make_date,7) = #{accountperiod }
		order by a.code asc
	</select>
	
	<select id="findPurchaseInvoiceList" parameterType="pd" resultType="pd">
		select
			a.accountperiod,
			a.invoice_number,
			a.invoice_date,
			a.tmp_supplier,
			c2.id tmp_supplier_id,
			a.money,
			a.tax,
			a.advalorem,
			a.supplier_id,
			ifnull(c1.name,'') supplier_name,
			a.purchase_type_id,
			ifnull(s.item,'') purchase_type,
			a.voucher_id,
			t.code voucher_number,
			a.has_instock,
			a.this_instock,
			a.pre_instock,
			ifnull(a.money,0)-ifnull(a.has_instock,0) as un_instock,
			ifnull(a.instockcode,'') instockcode,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.t_purchaseinvoice a
			left join ${ pd.database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ pd.database }.c_supplier c1 on a.supplier_id = c1.id
			left join ${ pd.database }.c_supplier c2 on a.tmp_supplier = c2.name
			left join ${ pd.database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where 
			left(a.make_date,7) = #{accountperiod }
		order by a.code asc
	</select>
</mapper>