<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<script type="text/javascript" src="static/plugin/jquery-easyui-1.5.3/datagrid-cellediting.js"></script>
</head>
<body>
	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div class="calsurtax_div">
			<div class="calsurtax_divtow">
				<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px #cedae4 solid;">
					<input type="hidden" id="id" name="id">
					<input type="hidden" id="accountperiod" name="accountperiod" value='${accountperiod}'>
					<div class="bgdivtit" style="height: 45px;margin: -10px 0 0 0;border: 0;">
					    <ul class="calvataxmx_topul">
					        <li style="height: 28px;">
			    		       <p>会计期间：</p> 	
			    		       <input type='text' id="year" class="readonlinput" value="${year }" readonly="readonly" />
				    		   <input type="text" name="month" class="readonlinput" id="month" value="${month }" readonly="readonly" />
				    		   <span style="color: #647077;margin:0 0 0 8px">期</span>
				    		   <a class="easyui-linkbutton count_btn" style="margin:0 0 0 14px" href="javascript:void(0)" onclick="caculation()">计算</a>
			    		    </li>
			    		</ul>
			    	</div>	
		    		<div style="width: 980px;height: 280px; border: 1px #cedae4 solid;border-left: 0; border-right: 0;background: #fff;">
		    			<div id="gridlist" style="width:980px;height:280px"></div>
		    		</div>
				</form>
			</div>
		</div>
	
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="ButtonFun.cancelFun('costaccounting')">关闭</a>
		</div>
		</div>
		
	</div>

<script type="text/javascript">

	$(function(){
		$('#gridlist').datagrid({    
		    url:getUrl('costaccounting/findcalucationcost.do?accountperiod='+$('#accountperiod').val()), 
		    fitColumns: false,
		    rownumbers: true,
		    showFooter:true,
		    columns:[[    
		        {field:'goodstype_name',title:'商品类别',align: 'left', halign: 'center',width:120},  
		        {field:'goods_name',title:'商品名称',align: 'left', halign: 'center',width:180}, 
		        {field:'qcsl',title:'期初数量',align: 'right', halign: 'center',width:80}, 
		        {field:'qcje',title:'期初金额',align: 'right', halign: 'center',width:80},   
		        {field:'bcrk',title:'本期入库',align: 'right', halign: 'center',width:80},
		        {field:'rkje',title:'入库金额',align: 'right', halign: 'center',width:80},
		        {field:'bcck',title:'本期出库',align: 'right', halign: 'center',width:80},
		        {field:'ckje',title:'出库金额',align: 'right', halign: 'center',width:80},
		        {field:'qmsl',title:'期末数量',align: 'right', halign: 'center',width:80},
		        {field:'qmje',title:'期末金额',align: 'right', halign: 'center',width:80},
		    ]]    
		});  
	})
	function caculation(){
		/**
			1、会计期间为当前会计期间，不可选择
			2、点击计算成本后按“成本计算方法”计算成本
			3、明细中的数据：
			     按商品汇总显示
			     期初：略
			     本期入库：数量，合计
			     入库金额：金额，合计
			     本期出库：出库数量，合计
			     出库金额：要有个进销存的表格，刚开始的时候是没有的，点击计算成本后，按规则计算数据再填写进去
			                     全月加权平均的计算：（期初金额+入库金额）/（期初数量+本期入库数量）*本期出库数量
			     期末数量：=期初数量+本期入库-本期出库
			     期末金额：=期初金额+入库金额-出库金额
		**/
		$.ajax({
		    url:getUrl('costaccounting/calucationcost.do'),
		    type:'post',
		    async:false,
		    data: {'accountperiod':$('#accountperiod').val()},
		    dataType:'json',
		    success:function(data){
	    			if(data.state == 'success'){
	    				$('#gridlist').datagrid({    
	    				    data:data, 
	    				    fitColumns: false,
	    				    rownumbers: true,
	    				    showFooter:true,
	    				    columns:[[    
	    				        {field:'goodstype_name',title:'商品类别',align: 'left', halign: 'center',width:120},  
	    				        {field:'goods_name',title:'商品名称',align: 'left', halign: 'center',width:180}, 
	    				        {field:'qcsl',title:'期初数量',align: 'right', halign: 'center',width:80}, 
	    				        {field:'qcje',title:'期初金额',align: 'right', halign: 'center',width:80},   
	    				        {field:'bcrk',title:'本期入库',align: 'right', halign: 'center',width:80},
	    				        {field:'rkje',title:'入库金额',align: 'right', halign: 'center',width:80},
	    				        {field:'bcck',title:'本期出库',align: 'right', halign: 'center',width:80},
	    				        {field:'ckje',title:'出库金额',align: 'right', halign: 'center',width:80},
	    				        {field:'qmsl',title:'期末数量',align: 'right', halign: 'center',width:80},
	    				        {field:'qmje',title:'期末金额',align: 'right', halign: 'center',width:80},
	    				    ]]    
	    				});  
		    			promptbox('success','操作成功！');
					}else if(data.state == 'error'){
						$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
					}
		    },
		    error : function(data){
		    	$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败，请联系管理员','error');
		    }
		    
	    });
	}


</script>

</body>
</html>