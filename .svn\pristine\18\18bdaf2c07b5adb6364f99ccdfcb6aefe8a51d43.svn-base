$.extend($.fn.datagrid.methods, {
	editCell: function(jq,param){
		return jq.each(function(){
			var opts = $(this).datagrid('options');
			var fields = $(this).datagrid('getColumnFields',true).concat($(this).datagrid('getColumnFields'));
			for(var i=0; i<fields.length; i++){
				var col = $(this).datagrid('getColumnOption', fields[i]);
				col.editor1 = col.editor;
				if (fields[i] != param.field){
					col.editor = null;
				}
			}
			$(this).datagrid('beginEdit', param.index);
			for(var i=0; i<fields.length; i++){
				var col = $(this).datagrid('getColumnOption', fields[i]);
				col.editor = col.editor1;
			}
		});
	}
});

$.extend($.fn.datagrid.defaults.editors, {
	textarea: {  
		init: function(container, options){  
			var input = $('<textarea class="datagrid-editable-input" rows='+options.rows+'></textarea>').appendTo(container);  
			return input;  
		},  
		getValue: function(target){  
			return $(target).val();  
		},  
		setValue: function(target, value){  
			$(target).val(value);  
		},  
		resize: function(target, width){  
			var input = $(target);  
			if ($.boxModel == true){  
				input.width(width - (input.outerWidth() - input.width()));  
			} else {  
				input.width(width);  
			}
		}  
	}  
});
var currField = '';
function focusEditor(target, field,index) {
	   debugger
	   currField = field;
     var opts = $(target).datagrid('options');
     var t;
     currField = field;
     var editor = $(target).datagrid('getEditor', {
         index : index,
         field : field
     });
     if (editor) {
         t = editor.target;
     } else {
         var editors = $(target).datagrid('getEditors',index);
         if (editors.length) {
             t = editors[0].target;
             currField = editors[0].field;
         }
     }
     if (t) {
         if ($(t).hasClass('textbox-f')) {
             $(t).textbox('textbox').focus();
         } else {
             $(t).focus();
         }
     }
 }

/**
 * 	数据表格
 * 	<AUTHOR>
 */
var Grid = {
		/**
		 * 	数据列表Grid，基础列表结构
		 * 	@param obj	{
		 * 					position		弹出框在页面Dom的定位
		 * 					url				请求
		 * 					columns			数据列
		 * 					pageSize		列表显示条数
		 * 					fitColumns		是否自适应
		 * 	}
		 */
		"list_grid" : function(obj){
			
			var url = getUrl(obj.url);
			if(checkEm(obj.singleSelect)){
				obj["singleSelect"]=true;
			}
			$(obj.position).datagrid({
				url: url,
			    columns: obj.columns,
			    fitColumns: false,
			    rownumbers:  checkEm(obj.rownumbers) && true,
			    showFooter: obj.showFooter ||  false,
			    singleSelect: obj.singleSelect,				//是否单行选择
			    scrollbarSize: 5,			//滚动条宽度5px
			    nowrap: false,				//禁止文字自动换行
			    loadMsg: '正在加载...',		//当数据没有加载出来时显示的文字
			    pagination: checkEm(obj.pagination) && true,			//是否有分页
			    pagePosition: 'bottom',		//分页符在底部,可选参数为top，bottom，both
			    pageSize: obj.pageSize || 50,
			    pageList: [50,100,200,500,'全部'],
			    pageNumber: 1,				//默认当前的页码
			    queryParams: {},			//往后台传递参数，json格式
			    onDblClickRow: function(index, row){
					obj.listDbClickFun(row);
				}
			});
		},
		
		/**
		 * 	报表用 数据列表
		 */
		"list_report" : function(obj){
			
			var url = getUrl(obj.url);
			
			$(obj.position).datagrid({
				url: url,
			    columns: obj.columns,
			    fitColumns: checkEm(obj.fitColumns) && true,
			    rownumbers: true,
			    singleSelect: true,			//是否单行选择
			    scrollbarSize: 5,			//滚动条宽度5px
			    nowrap: false,				//禁止文字自动换行
			    loadMsg: '正在加载...',		//当数据没有加载出来时显示的文字
			    pagination: checkEm(obj.pagination) && true,			//是否有分页
			    pagePosition: 'bottom',		//分页符在底部,可选参数为top，bottom，both
			    pageSize: obj.pageSize || 50,
			    pageList: [50,100,200,500,'全部'],
			    pageNumber: 1,				//默认当前的页码
			    showFooter : obj.showFooter || false,
			    queryParams: obj.queryParams || {},			//往后台传递参数，json格式
			    onDblClickRow: function(index, row){
					obj.listDbClickFun(row);
				}
			});
		},
		/**
		 * 	报表用 数据列表
		 */
		"list_report_data" : function(obj){
			$(obj.position).datagrid({
				/*url:obj.url,*/
				data:obj.data,
			    columns: obj.columns,
			    fitColumns: checkEm(obj.fitColumns) && true,
			    rownumbers: true,
			    singleSelect: true,			//是否单行选择
			    scrollbarSize: 5,			//滚动条宽度5px
			    nowrap: false,				//禁止文字自动换行
			    loadMsg: '正在加载...',		//当数据没有加载出来时显示的文字
			    pagination: checkEm(obj.pagination) && true,			//是否有分页
			    pagePosition: 'bottom',		//分页符在底部,可选参数为top，bottom，both
			    pageSize: obj.pageSize || 50,
			    pageList: [50,100,200,500,'全部'],
			    pageNumber: 1,				//默认当前的页码
			    showFooter : obj.showFooter || false,
			    queryParams: obj.queryParams || {},			//往后台传递参数，json格式
			    onDblClickRow: function(index, row){
					/*obj.listDbClickFun(row);*/
				}
			});
		},
		"edit_cell_grid_url" : function(obj){
			
			var url = getUrl(obj.url);
			
			$(obj.position).datagrid({
				url: url,
			    columns: obj.columns,
			    fitColumns: false,
			    rownumbers: true,
			    singleSelect: obj.singleSelect || true,			//是否单行选择
			    scrollbarSize: 5,			//滚动条宽度5px
			    nowrap: false,				//禁止文字自动换行
			    pagination: false,			//是否有分页
			    queryParams: {},
			    editIndex : undefined,
			    cache:false,
			    showFooter : obj.showFooter || false,
			    onEndEdit : obj.onEndEdit || function(){},
			    onAfterEdit:obj.onAfterEdit || function(){},
			    onClickCell: function(index, field){
			    	debugger;
					if ($(obj.position).datagrid('options').editIndex == undefined){
						$(obj.position).datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
						$(obj.position).datagrid('options').editIndex = index;
						var ed = $(obj.position).datagrid('getEditor', {index:index,field:field});  
						if($(ed.target).next().find(".textbox-text").length>0){
							$(ed.target).next().find(".textbox-text").focus();
						}else{
							$(ed.target).focus();
						}
					}else if ($(obj.position).datagrid('validateRow', $(obj.position).datagrid('options').editIndex)){
						$(obj.position).datagrid('endEdit', $(obj.position).datagrid('options').editIndex);
						$(obj.position).datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
						$(obj.position).datagrid('options').editIndex = index;
						var ed = $(obj.position).datagrid('getEditor', {index:index,field:field});
						if($(ed.target).next().find(".textbox-text").length>0){
							$(ed.target).next().find(".textbox-text").focus();
						}else{
							$(ed.target).focus();
						}
					}
				}
			});
		},
		
		"edit_cell_grid_data" : function(obj){
			$(obj.position).datagrid({
				data: obj.data,
			    columns: obj.columns,
			    fitColumns: false,
			    rownumbers: true,
			    singleSelect: true,			//是否单行选择
			    scrollbarSize: 5,			//滚动条宽度5px
			    nowrap: false,				//禁止文字自动换行
			    pagination: false,			//是否有分页
			    editIndex : undefined,
			    showFooter : obj.showFooter || false,
			    onEndEdit : obj.onEndEdit || function(){},
			    onClickCell: function(index, field){
					if ($(obj.position).datagrid('options').editIndex == undefined){
						$(obj.position).datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
						$(obj.position).datagrid('options').editIndex = index;
						var ed = $(obj.position).datagrid('getEditor', {index:index,field:field});  
						$(ed.target).focus();
					}else if ($(obj.position).datagrid('validateRow', $(obj.position).datagrid('options').editIndex)){
						$(obj.position).datagrid('endEdit', $(obj.position).datagrid('options').editIndex);
						$(obj.position).datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
						$(obj.position).datagrid('options').editIndex = index;
						var ed = $(obj.position).datagrid('getEditor', {index:index,field:field});  
						$(ed.target).focus();
					}
				}
			});
		},
		
		"edit_row_grid" : function(obj){
			
			var url = getUrl(obj.url);
			
			$(obj.position).datagrid({
				idField: obj.idField,
				url: url,
				columns: obj.columns,
				pageSize: obj.pageSize || 50,
			    pageList: [50,100,200,500,'全部'],
				fitColumns: false,
			    rownumbers: true,
			    showFooter: obj.showFooter ||  false,
			    pagination: checkEm(obj.pagination) && true,			//是否有分页
			    pagePosition: 'bottom',		//分页符在底部,可选参数为top，bottom，both
			    scrollbarSize: 5,			//滚动条宽度5px
			    nowrap: true,				//flase禁止文字自动换行
			    loadMsg: '正在加载...',		//当数据没有加载出来时显示的文字
			    ctrlSelect: true,			//允许使用Ctrl键+鼠标点击的方式进行多选操作
			    autoRowHeight: false,
				onBeforeEdit:obj.onBeforeEdit || function(index,row){
					row.editing = true;
					$(obj.position).datagrid('refreshRow',index);
				},
				onAfterEdit:function(index,row){
					row.editing = false;
					$(obj.position).datagrid('refreshRow',index);
				},
				onCancelEdit:function(index,row){
					row.editing = false;
					$(obj.position).datagrid('refreshRow',index);
				},
				 /////处理了按Enter切换单元格的问题
		/*	       onBeginEdit : function (index, row) {
		            	debugger
		            	var rowEndCount = 0;
		                currentEdatagrid = $(obj.position);
		                
		                var editors = $(currentEdatagrid).datagrid('getEditors', index);
		           	 	currField  = editors[0].field;
		                if (!$('.datagrid-editable-input')) {
		                    console.log("no element")
		                }
		                $('.datagrid-editable .textbox,.datagrid-editable .datagrid-editable-input,.datagrid-editable .textbox-text').bind('keydown', function (e) {
		                    var code = e.keyCode || e.which;
		                    if (code == 13) {
		                        var editors = $(currentEdatagrid).datagrid('getEditors', index);
		                        var i = 0;
		                        for (i = 0; i < editors.length; i++) {
		                        	if(checkEm(currField)){
		                        		currFiled = editors[0].field;
		                        	}
		                        	 if (editors[i].field == currField) {
		                                if ((i + 1) < editors.length) {
		                                    var temp = editors[i + 1].field;
		                                    focusEditor(obj.position, temp,index);
		                                    break;
		                                }
		                                else if ((i + 1) == editors.length) {
		                                    var temp = editors[i].field;
		                                    focusEditor(obj.position, temp,index);
		                                    rowEndCount++;
		                                    break;
		                                }
		                          
		                        }
		                        }
		                        if (rowEndCount > 1) {
		                            rowEndCount = 0;
		                            
		                        }
		                    }
		                });
		            },*/
				
			});
		},
	"edit_row_treegrid" : function(obj){
			
			var url = getUrl(obj.url);
			
			$(obj.position).treegrid({
				idField: obj.idField,
				treeField:obj.treeField,
				parentId: obj.parentId,
				url: url,
				columns: obj.columns,
				pageSize: obj.pageSize || 50,
			    pageList: [50,100,200,500,'全部'],
				fitColumns: false,
			    rownumbers: true,
			    showFooter: obj.showFooter ||  false,
			    pagination: checkEm(obj.pagination) && true,			//是否有分页
			    pagePosition: 'bottom',		//分页符在底部,可选参数为top，bottom，both
			    scrollbarSize: 5,			//滚动条宽度5px
			    nowrap: true,				//flase禁止文字自动换行
			    loadMsg: '正在加载...',		//当数据没有加载出来时显示的文字
			    ctrlSelect: true,			//允许使用Ctrl键+鼠标点击的方式进行多选操作
			    autoRowHeight: false,
				onBeforeEdit:function(index,row){
					row.editing = true;
					$(obj.position).datagrid('refreshRow',index);
				},
				onAfterEdit:function(index,row){
					row.editing = false;
					$(obj.position).datagrid('refreshRow',index);
				},
				onCancelEdit:function(index,row){
					row.editing = false;
					$(obj.position).datagrid('refreshRow',index);
				}
			});
		}
}
