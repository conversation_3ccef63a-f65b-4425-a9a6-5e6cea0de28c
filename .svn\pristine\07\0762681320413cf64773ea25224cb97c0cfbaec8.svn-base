<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Initialize Value for ComboTree - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Initialize Value for ComboTree</h2>
	<p>Initialize Value when ComboTree is created.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-combotree" value="122" data-options="url:'tree_data1.json',method:'get',label:'Select Node:',labelPosition:'top'" style="width:100%">
		</div>
	</div>
</body>
</html>