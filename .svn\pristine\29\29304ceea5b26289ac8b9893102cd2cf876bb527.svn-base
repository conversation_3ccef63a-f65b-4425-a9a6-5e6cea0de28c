<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="InsuranceitemsMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_insuranceitems(
			name,
			belongsto,
			
			createby,createtime
		) values (
			#{name},
			#{belongsto},
			#{createby},now()
		)
	</insert>
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_insuranceitems where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_insuranceitems
		set 
			name = #{name},
			belongsto = #{belongsto},
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.name,
			case a.belongsto when 0 then '基本社保' when 1 then '公积金' when 2 then '补充养老' when 3 then '补充医疗' else '' end as belongsto,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_insuranceitems as a
		where 
			a.id = #{ id }
	</select>
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.name,
			case a.belongsto when 0 then '基本社保' when 1 then '公积金' when 2 then '补充养老' when 3 then '补充医疗' else '' end as belongsto,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_insuranceitems a
		where 
			a.closestatus=0
			<if test="pd.q != null and pd.q !=''">
				and (a.name like '%${ pd.q }%')
			</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.name,
			case a.belongsto when 0 then '基本社保' when 1 then '公积金' when 2 then '补充养老' when 3 then '补充医疗' else '' end as belongsto,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_insuranceitems a
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
		select 
		a.calnorinsurancemxdata as mxlist
		from
		${ database }.t_calnorinsurance as a
	union 
		select 
		a.caloutsourcemxdata as mxlist
		from
		${ database }.t_caloutsource as a
	union
		select 
		a.calhousefundmxdata as mxlist
		from
		${ database }.t_calhousefund as a
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkName" parameterType="pd" resultType="pd">
		select count(*) num from ${ database }.c_insuranceitems where name = #{name}
	</select>
	
</mapper>