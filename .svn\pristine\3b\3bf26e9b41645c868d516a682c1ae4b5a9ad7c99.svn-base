package org.newstanding.plugin.webservice;

import java.util.List;

import javax.jws.WebParam;
import javax.jws.WebService;

@WebService(name = "execWebService")
public interface ExecWebService {

	/**
	 * Service层 保存，修改，查询 等等
	 * @param serviceName 
	 * @param methodName
	 * @param parameters
	 * @return
	 * @throws Exception
	 */
	public Object execute(@WebParam(name="serviceName")String serviceName,@WebParam(name="methodName")String methodName,@WebParam(name="parameters") String parameters) throws Exception;	
	
	public Object login(String user) throws Exception;
	
	/**
	 * 通过用户名 和 密码登陆
	 * @param username
	 * @param password
	 * @return
	 * @throws Exception
	 */
	public Object loginByUernameAndPassword(@WebParam(name="username")String username, @WebParam(name="password")String password) throws Exception;
	
	public Object logout () throws Exception;
	
	public Object getmenu (String userId) throws Exception;
	
	/**
	 * 通过 用户名 得到 系统菜单
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public Object getmenuByUserId (@WebParam(name="userId")String userId) throws Exception;
	
	public Object sayHello(String message);
	
	public void readResource(String exection_id);
    
}
