<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="HousefundrateMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_housefundrate(
			insuranceitemsid,
			comppart,
			personpart,
			createby,createtime
		) values (
			#{insuranceitemsid},
			#{comppart},
			#{personpart},
			#{createby},now()
		)
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_housefundrate where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_housefundrate
		set 
			insuranceitemsid = #{insuranceitemsid},
			comppart = #{comppart},
			personpart = #{personpart},
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
			a.insuranceitemsid,
			b.name as insuranceitems_name,
			a.comppart,
			a.personpart,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_housefundrate a
			left join ${ pd.database }.c_insuranceitems b on a.insuranceitemsid = b.id
		where 
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.insuranceitemsid,
			b.name as insuranceitems_name,
			a.comppart,
			a.personpart,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_housefundrate a
			left join ${ pd.database }.c_insuranceitems b on a.insuranceitemsid = b.id		
			
		where 
			a.closestatus=0 and b.closestatus=0
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.insuranceitemsid,
			b.name as insuranceitems_name,
			a.comppart,
			a.personpart,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_housefundrate a
			left join ${ pd.database }.c_insuranceitems b on a.insuranceitemsid = b.id		
			
		where 
			a.closestatus=0
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	</select>
</mapper>