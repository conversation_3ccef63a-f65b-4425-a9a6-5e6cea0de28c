<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD SQL Map Config 3.0//EN"  
	"http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<settings> 
		<!-- 全局映射器启用缓存    -->
        <setting name="cacheEnabled" value="true" />
        <setting name="useGeneratedKeys" value="true" /> 
        <setting name="defaultExecutorType" value="REUSE" /> 
    </settings>

	<typeAliases>
		<typeAlias type="org.newstanding.common.entity.PageData" alias="pd"/>
		<!-- 分页 -->
		<typeAlias type="org.newstanding.common.entity.Page" alias="Page"/>
	</typeAliases>
	
	<plugins>
		<plugin interceptor="org.newstanding.plugin.PagePlugin">
			<property name="dialect" value="mysql"/>
			<property name="pageSqlId" value=".*listPage.*"/>
			<property name="findByIdSqlId" value=".*findById"/>
			<property name="editSqlId" value=".*edit"/>
		</plugin>
	</plugins>
	
</configuration>