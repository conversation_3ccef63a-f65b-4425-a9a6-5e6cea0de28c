<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<script type="text/javascript" src="static/plugin/jquery-easyui-1.5.3/datagrid-cellediting.js"></script>

</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div class="receivable_div" style="margin: 88px 0 0 0;height: 256px;width: 526px;">
			<div class="receivable_divtow" style="width: 526px;border-top:0">
				<div id="gridlist" data-options="region:'center'" style="border: 1px solid rgb(206, 218, 228);height:232px;width:526px;margin:15px"></div>
			</div>
		</div>
		 <!-- 顶部功能栏 -->
		<div id="eastToolDiv" data-options="region:'north'" style="height:92px;">
			<div id="toolbar" class="fillvouch_poptop">
			   <div class="bgdivtit" style="height: 90px;border: 0;">
			        <form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px #cedae4 solid;border-top: 0;">
						<input type="hidden" id="id" name="id">
						<input type="hidden" id="carryovermx" name="carryovermx">
					    <ul class="calvataxmx_topul calvataxmx_topult">
			    		    <li style="margin:0">
			    		       <span style="margin: 0 0 0 20px;">会计期间：</span>
			    		       <input id="year" name="year"  value="${year }" class="readonlinput" style="height:24px"  type="text" readonly="readonly"></input>
			    		       <input id="month" name="month" value="${month }"  class="readonlinput" style="height:24px"  type="text" readonly="readonly"></input>
			    		       <span style="margin: 0 0 0 20px;">本年利润科目：</span>
			    		        <input type="hidden" id= "plsubject_id" name="plsubject_id"  value="${plsubject_id }" />
								<input class="easyui-textbox" type="text" style="width: 142px;height:30px; padding:0 24px 0 2px;   margin: 9px 0 9px 14px;" id= "plsubject_name" name="plsubject_name" value="${plsubject_name }"  />
								<div class="kjkm_btn" style=" right: 30px;top: 21px;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;plsubject_id:id,plsubject_name:name')"></div>
			    		    </li>
			    		    <li style="margin:0 0 12px 0">
			    		       <span style="margin: 0 0 0 20px;">凭证摘要：</span>
			    		       <input id="abstracta" name="abstracta"   style="width: 418px;height:24px" type="text" value="${abstracta }" ></input>
			    		    </li>
			    		</ul>
					</form>
				</div>	
			</div>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
			<!-- 底部功能栏 -->
			
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="ButtonFun.cancelFun('carryover')">关闭</a>
			<a class="easyui-linkbutton sure-dialog" href="javascript:void(0)" onclick="submitForm_receivable({code:'carryover',type:2,renderid:'#gridlist'})">结转</a>
		</div>
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:661px;height:300px;padding:10px;"></div>
	</div>
<script type="text/javascript">
	$(function(){
		var gridObj = new Object();
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'carryover/list';
		gridObj["columns"] = [[
								{field:'subjectid',title:'subjectid',hidden:true},
								{field:'debitmoney',title:'debitmoney',width:100,hidden:true},
								{field:'creditmoney',title:'debitmoney',width:100,hidden:true},
								{field:'subjectcode',title:'科目编码',align: 'left', halign: 'center',width:130},
								{field:'subjectname',title:'科目名称',align: 'left', halign: 'center',width:145},
								{field:'sub_direction',title:'科目方向',align: 'left', halign: 'center',width:80},
								{field:'accountmoney',title:'本期发生额',align: 'right', halign: 'center',width:142,
									formatter:function(value,row,index){
										return '<p style="width:100%;height:100%;margin:0 0 0 -17px">'+formatMoney(value)+'</p>';
									}
								},
					         ]];
		gridObj["idField"] = 'id';
		gridObj["pagination"] =false;
		Grid.list_grid(gridObj);
	});
	
	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	
	function afterEditFun(node){
		
	}
	//显示可编辑表格
	function afterAddFun(){
		
	}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){
		var flag=0;
		var data = $('#gridlist').datagrid('getData');
		var rows=data.rows;
		if(rows.length>0){
			//当前会计期间是否有未过账凭证，如果有提示：本月存在未过账凭证，请过账后重新结转损益。
			$.ajax({
				url:getUrl('carryover/checkUnAccountVouchers'),
				data:{accountperiod : $('#year').val() + '-' + $('#month').val()},
				async:false
			})
			.done(function(data){
				if(data.state=='success'){
					flag=1;
					$('#carryovermx').val(JSON.stringify(rows));
				}
				if(data.state =='error'){
					$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
					return;
				}
	          }) 
	          .fail(function(err){
	        	  $.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败，请联系管理员','error');
	          })
		}else{
			$.messager.confirm('提示', '<span class="hintsp_w">提示</span>本月无结转数据，系统将退出结转界面。', function(r){
				if (r){
					ButtonFun.cancelFun('carryover');
				}
			})
			return;
		}
		if(flag==1){
			return true;
		}else{
			return false;
		}
	}

</script>

</body>
</html>