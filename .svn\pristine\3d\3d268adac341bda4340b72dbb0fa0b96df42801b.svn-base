package org.newstanding.controller.financialhandle;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.financialhandle.CheckvouchersService;
import org.newstanding.service.invoicing.PurchaseInvoiceService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
/**
 * deptdoc
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "checkvouchers")
public class CheckvouchersController extends BaseController {
	@Resource(name = "checkvouchersService")
	private CheckvouchersService checkvouchersService;
	@Resource(name = "purchaseInvoiceService")
	private PurchaseInvoiceService purchaseInvoiceService;

	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		String accountperiod="";
		if(pd.get("accountperiod") == null){
			accountperiod = purchaseInvoiceService.getAccountperiod(pd);
			pd.put("accountperiod", accountperiod);
		}else{
			accountperiod = pd.get("accountperiod").toString();
		}
		String[] atcode=getAccountCodeArray();
		pd.put("codeArray", atcode);
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		page.setPd(pd);
		resultPageData =  checkvouchersService.list(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
			resultMap.put("accountperiod", accountperiod);
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return resultMap;
	}

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		mv.setViewName("system/financialhandle/checkvouchers");
		return mv;
	}

	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "c_checkvouchers");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = checkvouchersService.deleteAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "c_checkvouchers");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = checkvouchersService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要关闭的数据,请检查！");
		}
		return pd;
	}
	/**
	 * 批量审核，撤审
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/auditAll")
	public Object auditAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("auditby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "t_fillvouchers");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			//验证  凭证是否平衡、凭证是否满足基本条件（凭证明细至少有两行，行明细金额不可为0等）
			
			PageData checkPd = checkvouchersService.checkVouchers(ArrayDATA_IDS,pd.get("database").toString());
			if(checkPd !=null && "success".equals(checkPd.get("state").toString())) {
				pd = checkvouchersService.auditAll(pd);
			}else {
				pd.put("state", "error");
				pd.put("message", checkPd.get("message"));
			}
			
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要审核的数据,请检查！");
		}
		return pd;
	}

	/**
	 * 搜索 导出 查询 部门列表
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = checkvouchersService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/listCheckvouchersmx")
	public Map<String, Object> listCheckvouchersmx() throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String[] atcode=getAccountCodeArray();
		pd.put("codeArray", atcode);
		resultPageData =  checkvouchersService.findCheckvouchersmxByCheckvouchersid(pd);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			if(varList.size()<5){
				int tem=5-varList.size();
				for(int i=0;i<tem;i++){
					PageData temPd=new PageData();
					temPd.put("rowid",(int)(Math.random()*100000));
					varList.add(temPd);
				}
			}
			//合计行  数据构建
			List<PageData> footerList=new ArrayList<PageData>();
			PageData footerPd=new PageData();
			footerPd.put("abstracta", "合计");
			for(Map<String,String> empPd :varList){
				for (String key : empPd.keySet()) {  
					  if(key.equals("debitmoney") || key.equals("creditmoney")){
						if(empPd.get(key) !=null){
							 if(footerPd.get(key) !=null){
								 String footer=footerPd.get(key).toString();
								 String em=String.valueOf(empPd.get(key));
								 footerPd.put(key, 
										 CalculateUtil.add(footer
												 , em));
							 }else{
								 footerPd.put(key, empPd.get(key));
							 }
						}else{
							footerPd.put(key, 0);
						}
						 
					  }
				  
				} 
			}
			footerList.add(footerPd);
			resultMap.put("footer", footerList);
			resultMap.put("total",varList.size());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return resultMap;
	}
}




