package org.newstanding.service.financialhandle;

import java.util.ArrayList;
import java.util.List;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.financialhandle.buildaccount.GzdxyeService;
import org.newstanding.service.financialhandle.buildaccount.KmyeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("carryoverService")
public class CarryoverService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private FillvouchersService fillvouchersService;
	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			//将json数据转化为list
			List<PageData> addList = new ArrayList<PageData>();
			if (!"[]".equals(pd.getString("carryovermx"))) {
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(pd.getString("carryovermx"));
				for (Object o : jay) {
					PageData surtaxmx = new PageData(JsonUtils.strToMap(o.toString()));
					addList.add(surtaxmx);
				}
			}else{
				resPd.put("state", "error");
				resPd.put("message", "没有获取到结转损益数据，请检查");
				return resPd;
			}
			//构造凭证数据
			PageData mainPd = new PageData();
			List<PageData> mx_list = new ArrayList<PageData>();
			mainPd.put("accountperiod", pd.get("year")+"-"+pd.get("month"));
			mainPd.put("createby", getCurrentUserByCatch(pd).getUsername());
			mainPd .put("source", "carryover");
			mainPd .put("attachcount", "0");
			mainPd.put("database", getDatabase(pd));
			mainPd.put("voucherdate", DateUtil.getMaxDayOfMonth(pd.get("year")+"-"+pd.get("month")));
			PageData codePd=fillvouchersService.findMaxCodeAndEtc(mainPd);
			if(codePd != null && codePd.get("code")!=null){
				mainPd.put("code", codePd.get("code"));
			}
			//循环明细  封装凭证明细数据
			Double debittotal=0d;
			Double credittotal=0d;
			for (PageData vm : addList) {
				PageData mxPd = new PageData();
				mxPd.put("abstracta", pd.get("abstracta"));
				mxPd.put("rowid",(int)(Math.random()*100000));
				mxPd.put("subjectid",vm.get("subjectid"));
				//余额方向的发生额-对方发生额    如果是正数则显示在对方，如果是负数则显示在本方
				Double accountmoney=Double.parseDouble(vm.get("accountmoney").toString());
				if("借".equals(vm.get("sub_direction").toString())){
				/*	if(accountmoney>0){*/
						mxPd.put("creditmoney",accountmoney);
						credittotal =CalculateUtil.add(credittotal, accountmoney);
					/*}else{
						mxPd.put("debitmoney",Math.abs(accountmoney));
						debittotal =CalculateUtil.add(debittotal, Math.abs(accountmoney));
					}*/
					
				}
				if("贷".equals(vm.get("sub_direction").toString())){
				/*	if(accountmoney>0){*/
						mxPd.put("debitmoney",accountmoney);
						debittotal =CalculateUtil.add(debittotal, accountmoney);
					/*}else{
						mxPd.put("creditmoney",Math.abs(accountmoney));
						credittotal =CalculateUtil.add(credittotal, Math.abs(accountmoney));
					}*/
					
				}
				mx_list.add(mxPd);
			}
			//封装  本年利润科目数据   如果借-贷 大于0  则显示在贷方；   如果小于0，则显示在借方，绝对值
			PageData mxPd1 = new PageData();
			mxPd1.put("abstracta",  pd.get("abstracta"));
			mxPd1.put("rowid",(int)(Math.random()*100000));
			mxPd1.put("subjectid",pd.get("plsubject_id"));
		/*	if(CalculateUtil.subtract(debittotal, credittotal)>0){*/
				mxPd1.put("creditmoney",CalculateUtil.subtract(debittotal, credittotal));
			/*}else{
				mxPd1.put("debitmoney",Math.abs(CalculateUtil.subtract(debittotal, credittotal)));
			}*/
			mx_list.add(mxPd1);
			mainPd.put("fillvouchersmx", JsonUtils.PageDataToJSONArray(mx_list).toString());
			//生成凭证
			resPd=fillvouchersService.save(mainPd);
			
			if(resPd .get("state") !=null && "success".equals(resPd .get("state").toString())){
				//生成凭证后验证：包含未过账凭证时，损益科目的余额是否为0，如果不为0时，重新处理
				
				//查询期间内损益类科目的借方金额  贷方金额合计。如果不相等  则提示错误
				PageData zPd = new PageData();
				zPd.put("accountperiod", pd.get("year")+"-"+pd.get("month"));
				zPd.put("database", pd.get("database"));
				PageData mxPd = (PageData) dao.findForObject("CarryoverMapper.findSyFillVouchersmx", zPd);
				if(mxPd !=null && mxPd.get("debitmoney") !=null && mxPd.get("creditmoney") !=null) {
					if(Double.parseDouble(mxPd.get("debitmoney").toString()) != Double.parseDouble(mxPd.get("creditmoney").toString())) {
						resPd.put("state", "error");
						resPd.put("message", "结转损益失败，结转后，损益类科目的借贷方金额不相等，请检查后重试！");
						return resPd;
					}
				}
				resPd.put("message",  "已生成凭证,凭证号码："+mainPd.get("code").toString()+",请及时对生成的凭证进行审核，过账！");
			}else{
				resPd.put("message",  "生成凭证失败，请联系管理员！");
			}
				
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CarryoverMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CarryoverMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findCarryovermxByCarryoverid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CarryoverMapper.findCarryovermxByCarryoverid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CarryoverMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	public List<PageData> checkUnAccountVouchers(PageData pd) throws Exception {
		PageData resPd=new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CarryoverMapper.checkUnAccountVouchers", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return list;
	}
}
