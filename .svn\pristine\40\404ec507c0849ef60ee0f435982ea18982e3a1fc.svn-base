<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Number Range - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Number Range</h2>
	<div class="demo-info">
		<div class="demo-tip icon-tip"></div>
		<div>The value is constrained to a range between 10 and 90.</div>
	</div>
	<div style="margin:10px 0;"></div>
	<input class="easyui-numberbox" data-options="min:10,max:90,precision:2,required:true">
</body>
</html>