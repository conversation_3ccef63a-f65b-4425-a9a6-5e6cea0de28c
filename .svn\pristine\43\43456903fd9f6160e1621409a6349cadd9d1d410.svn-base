<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<script type="text/javascript" src="static/plugin/jquery-easyui-1.5.3/datagrid-cellediting.js"></script>
</head>
<body>
	<div id="panelDiv" class="easyui-layout" data-options="fit:true"style="">
	   	<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background: #fff;">
		
			<!-- 左侧功能栏 -->
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="add()">新增</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:1120,height:441})">查看</a>
			<a class="easyui-linkbutton recover-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')">删除</a>
		</div>
	    <!-- 顶部功能栏 -->

		<div class="parceldivs" style="height: 316px; width: 605px;">
			<div class="parceldiv_tow" style="height: 316px; width: 605px;">
			    <div class="bgdivtit">
					<span class="invo_title" style="line-height: 45px;">会计年度</span>
				    <input type='text' id="year" class="invo_title_year" style="margin: 8px 4px 0 0" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M-%d',dateFmt:'yyyy',onpicked:yearPickedFunc})" />
				</div>
				<div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);height: 277px;"></div>
			</div>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('caloutsource')">关闭</a>
		</div>
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="height:300px;">
			<!-- 单据表单 -->
			<input type="hidden" id="accountperiod" name="accountperiod">
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px #cedae4 solid;">
				<input type="hidden" id="id" name="id">
				<input type="hidden" id="caloutsourcemx" name="caloutsourcemx">
				<input type="hidden" id="caloutsourcemxdata" name="caloutsourcemxdata">
				<input type="hidden" id="itemNames" name="itemNames">
				<input type="hidden" id="totalmoney" name="totalmoney">
				<input type="hidden" id="persontotalmoney" name="persontotalmoney">
				<input type="hidden" id="voucherid" name="voucherid">
				<input type="hidden" id="isaccount" name="isaccount">
				<input type="hidden" id="isaudit" name="isaudit">
			    <span style="display: block; text-align: center;position: absolute;top: 8px; font-size: 16px;color: #fff;left: 523px;">计算外包保险<span id="name" name="name"></span></span>
			    <!--  <div id="caloutsourcemx_gridlist" style="width:520;height:400"></div> -->
			    <ul class="pageForm_topul">
		    		<li class="fristList">
	    		       <span style="margin:0 0 0 9px">保险月份：</span>
	    		       <input  id="insurancemonth" style="width:100px;height:25px"  type="text" name="insurancemonth" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M-%d',dateFmt:'yyyy-MM',onpicked:voucherPickedFunc})"/>
		    		   <a class="easyui-linkbutton count_btn" href="javascript:void(0)" onclick="caculation()">生成社保单</a>
		    		    <a class="easyui-linkbutton dr_btns" href="javascript:void(0)" style="margin: 8px 0 0 5px;" onclick="importEx()"><i></i></a>
	    		    </li>
	    		    <li>
	    		       <span>制单人：</span>
	    		       <input  id="createby" name="createby" class="readonlinput"  style="width:100px;height:25px" type="text" readonly="readonly" />
	    		    </li>
		    		   <li>
	    		       <span>制单日期：</span>
	    		       <input  id="createdate" name="createdate"  type="text" style="width:100px;height:25px" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M-%d',dateFmt:'yyyy-MM-dd',onpicked:createDataPickFunc})" />
	    		    </li>
	    		</ul>
	    		<div style="border: 1px #cedae4 solid;border-left:0;border-right:0;border-bottom:0;">
	    		<div id="caloutsourcemx_gridlist" style="width:1116px;height:305px"></div></div>
	    	
		    </form>
		    <div style="height:38px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;padding:3px">
		    	<div style="float: left;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton xz_btn " onclick="javascript:ButtonFun.addFun({width:1120,height:441})">新增</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton bc_btn " onclick="submit()">保存</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton xg_btn " onclick="editVoucher()">修改</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton sc_btn " onclick="closeOrDelete('删除1')">删除</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton scpzh_btn " onclick="createvoucher()">生成凭证</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton czh_btn " onclick="unaccount()">撤账</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton czh_btn " onclick="ExportExcel()">导出</a>
		    	</div>
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="clearFormWithGrid('#caloutsourcemx_gridlist')">关闭</a>
		    </div>
		</div>
			
				<!-- 导入弹出框 -->
			<div id="import_panel" class="easyui-window" style="width: 400px;height:200px;display:none" title="导入" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
				<form id="importFileForm" method="post" enctype="multipart/form-data" data-options="novalidate:true" style="height: 116px;">
				    <div class="bgdivtit" style="height: 116px;    border: 0;">
						<ul class="tolead_ul">
						    <li>
						    	<p>选择文件：</p>
						        <input class="easyui-filebox" id="file_name" name="file_name" data-options="buttonText:'浏览'" style="width:200px; height: 26px;">
						    </li>
						</ul>
					</div>
				</form>
				<div style="text-align:center;height: 44px;background:#ecf5fa; border-radius: 0 0 5px 5px;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" style="margin: -1px 13px 0 0;" onclick="$('#import_panel').window('close')">取消</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" style="margin: -1px 8px 0 0;" onclick="importExcel()">确定</a>
			    </div>
			</div>
	</div>

<script type="text/javascript">
	$(function(){
		var gridObj = new Object();
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'caloutsource/list';
		gridObj["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'accountperiod',title:'accountperiod',width:100,hidden:true},
								{field:'itemNames',title:'itemNames',width:100,hidden:true},
								{field:'createdate',title:'制单日期',align: 'left', halign: 'center',width:110},
								{field:'insurancemonth',title:'保险月份',align: 'left', halign: 'center',width:100},
								{field:'totalmoney',title:'保险总额',align: 'right', halign: 'center',width:130,formatter:cloformatMoney},
								{field:'voucherid',title:'凭证id',hidden:true},
								{field:'vouchercode',title:'凭证号码',align: 'left', halign: 'center',width:114,
									formatter:function(value,row,index){
										if(!checkEm(row.voucherid)){
											return '<a href="javascript:void(0)" onclick="Dialog.linkVoucher('+ row.voucherid +')">'+ row.vouchercode +'</a>';
										}
										return '';
									}},
								{field:'createby',title:'制单人',align: 'left', halign: 'center',width:120},
					         ]];
		gridObj["idField"] = 'id';
		gridObj["listDbClickFun"] = listDbClickFun;
		gridObj["pagination"]=false;
		Grid.list_grid(gridObj);
		$('#gridlist').datagrid({
			onLoadSuccess: function(data){
				var itemNames=data.itemNames;
				$("#itemNames").val(JSON.stringify(itemNames));
				if(!checkEm(data.accountperiod)){
					$("#accountperiod").val(data.accountperiod);
				}
				if(!checkEm(data.year)){
					$('#year').val(data.year);
				}
			}
		});
	});
	function importEx(){
		if($("#isaccount").val()==1 || $("#isaudit").val()==1){
			return;
		}
		$('#import_panel').window('open');
	}
	//导入excel
	function importExcel(){
		$.messager.progress();	// 显示进度条
		$('#importFileForm').form('submit', {
				url: getUrl('calnorinsurance/importExcel?type=outsource'),
				onSubmit: function(){
					var file_name = $('#file_name').filebox('getText');
					if(file_name == ''){
						$.messager.alert('提示','<span class="hintsp_e">提示</span>请选择要导入的文件！','error');
						$.messager.progress('close');
						return false;
					}
				},
				success: function (data) {
					debugger
					data=JSON.parse(data);
					if(data.state == 'success'){
		  				var footercomptotal=0;
		  				var footerpertotal=0;
		  				var footertotaltotal=0;
		  				for(var i =0; i<data.rows.length; i++){//加载成功计算个人合计  和单位合计  和合计
	                		var row=data.rows[i];
	                		var compparttotal=0;
	                		var personparttotal=0;
	                		var totalparttotal=0;
	                		$.each(row, function(key, val) {
	            				if(key.split('-')[1]=='comppart'){
	            					compparttotal+=parseFloat(row[key]);
	            				}else if(key.split('-')[1]=='personpart'){
	            					personparttotal+=parseFloat(row[key]);
	            				}
	                		});
	                		row.compparttotal=compparttotal.toFixed(2);
	                		row.personparttotal=personparttotal.toFixed(2);
	                		row.totalparttotal=(parseFloat(compparttotal)+parseFloat(personparttotal)).toFixed(2);
	                		//footer合计
	                		footercomptotal=(parseFloat(footercomptotal) + parseFloat(compparttotal)).toFixed(2);
	                		footerpertotal =(parseFloat(footerpertotal) + parseFloat(personparttotal)).toFixed(2);
	                		footertotaltotal =(parseFloat(footertotaltotal) + parseFloat(compparttotal)+parseFloat(personparttotal)).toFixed(2);
	                	}
		  				(data.footer)[0].compparttotal=footercomptotal;
		  				(data.footer)[0].personparttotal=footerpertotal;
		  				(data.footer)[0].totalparttotal=footertotaltotal;
		  				//封装columns
	    				var columns=new Array();
	    				columns.push({field:'id',title:'id',hidden:true});
	    				
	    				columns.push({field:'empdocid',title:'empdocid',hidden:true});
	    				columns.push({field:'empdoc_name',title:'职员名称',align: 'left', halign: 'center',width:70});
	    				columns.push({field:'deptid',title:'deptid',hidden:true});
	    				columns.push({field:'dept_name',title:'部门名称',align: 'left', halign: 'center',width:76});
	    				columns.push({field:'insurancesubid',title:'insurancesubid',hidden:true});
	    				columns.push({field:'basemoney',title:'参保基数',align: 'right', halign: 'center',width:70,formatter:cloformatMoney});
	            
	    				var itemNames=data.itemNames;
	    				$("#itemNames").val(JSON.stringify(itemNames));
	    				var editor=new Object();
	  	    				editor["type"]="numberbox";
	  	    				editor["options"]={precision:2};
	    				for(var i=0 ; i<itemNames.length ; i++){
	    					var column=new Object();
	    					column["field"]=itemNames[i].insuranceitemscode;
	    					column["title"]=itemNames[i].insuranceitemsname;
	    					column["width"]=97;
	    					column["editor"]=editor;
	    					column["align"]='right';
	    					column["halign"]='center';
	    					column["formatter"]=cloformatMoney;
	    					columns.push(column);
	    					//columns.push({field : itemNames[i].code ,title:itemNames[i].name,width:80,type:'numberbox'});
	    				}
	    				columns.push({field:'compparttotal',title:'单位合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
	    				columns.push({field:'personparttotal',title:'个人合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
	    				columns.push({field:'totalparttotal',title:'合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
		               //渲染grid
	    				$('#caloutsourcemx_gridlist').datagrid({  
		                    title:'',  
		                    height:305,  
		                    singleSelect:true,  
		                    data:data,
		                    showFooter:true,
		                    editIndex : undefined,
		                   	columns : [  
		                        columns  
		                    ],
		                    onLoadSuccess : function (data){
		                    },
		                    onEndEdit : function (index,row,changes){//单元格编辑后计算 个人合计  和单位合计  和合计
		                    	var editors = $('#caloutsourcemx_gridlist').datagrid('getEditors', index);     
		                    	if(editors.length>0){
		                    		var footer=(data.footer)[0];
		                    		var field=editors[0].field;
		                    		if(editors[0].field.split('-')[1]=='comppart'){
			                    		var compparttotal=0;
			                    		$.each(row, function(key, val) {
		                    				if(key.split('-')[1]=='comppart'){
		                    					compparttotal+=parseFloat(row[key])
		                    				}
			                    		});
			                    		row.compparttotal=compparttotal.toFixed(2);
			                    		if(checkEm(row.personparttotal)){
			                    			row.personparttotal=0;
			                    		}
			                    		row.totalparttotal=(compparttotal+parseFloat(row.personparttotal)).toFixed(2);
			                    		footer[field] = calTotalByCol("#caloutsourcemx_gridlist",field);
			                    		footer["compparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","compparttotal");
			                    		footer["totalparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","totalparttotal");
			                    		$('#caloutsourcemx_gridlist').datagrid('reloadFooter',data.footer);
			                    	}else if(editors[0].field.split('-')[1]=='personpart'){
			                    		var personparttotal=0;
			                    		$.each(row, function(key, val) {
		                    				if(key.split('-')[1]=='personpart'){
		                    					personparttotal+=parseFloat(row[key])
		                    				}
			                    		});
			                    		row.personparttotal=personparttotal.toFixed(2);
			                    		if(checkEm(row.compparttotal)){
			                    			row.compparttotal=0;
			                    		}
			                    		row.totalparttotal=(personparttotal+parseFloat(row.compparttotal)).toFixed(2);
			                    		footer[field] = calTotalByCol("#caloutsourcemx_gridlist",field);
			                    		footer["personparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","personparttotal");
			                    		footer["totalparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","totalparttotal");
			                    		$('#caloutsourcemx_gridlist').datagrid('reloadFooter',data.footer);
			                    	}
		                    	}
		                    	
		                    },
		                    onClickCell: function(index, field){//编辑单元格
		    					if ($('#caloutsourcemx_gridlist').datagrid('options').editIndex == undefined){
		    						$('#caloutsourcemx_gridlist').datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
		    						$('#caloutsourcemx_gridlist').datagrid('options').editIndex = index;
		    						var ed = $('#caloutsourcemx_gridlist').datagrid('getEditor', {index:index,field:field});  
		    						if($(ed.target).next().find(".textbox-text").length>0){
		    							$(ed.target).next().find(".textbox-text").focus();
		    						}else{
		    							$(ed.target).focus();
		    						}
		    					}else if ($('#caloutsourcemx_gridlist').datagrid('validateRow', $('#caloutsourcemx_gridlist').datagrid('options').editIndex)){
		    						$('#caloutsourcemx_gridlist').datagrid('endEdit', $('#caloutsourcemx_gridlist').datagrid('options').editIndex);
		    						$('#caloutsourcemx_gridlist').datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
		    						$('#caloutsourcemx_gridlist').datagrid('options').editIndex = index;
		    						var ed = $('#caloutsourcemx_gridlist').datagrid('getEditor', {index:index,field:field});
		    						if($(ed.target).next().find(".textbox-text").length>0){
		    							$(ed.target).next().find(".textbox-text").focus();
		    						}else{
		    							$(ed.target).focus();
		    						}
		    					}
		    				}
		    			});  
		    			promptbox('success','操作成功！');
					}else if(data.state == 'error'){
						$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
					}
					$.messager.progress('close');// 如果表单是无效的则隐藏进度条 
					$('#import_panel').window('close');
				}
		});
	}
	//会计期间年  change事件
	function yearPickedFunc(){
		var period=$('#year').val();
		$('#gridlist').datagrid('load',{
			accountperiod: period
		});
	}
	//外包保险月份：默认=上一外包保险月份的次月，可修改，但不可以重复
	function voucherPickedFunc(){
		var obj=new Object();
		var insurancemonth=$('#insurancemonth').val();
		obj["code"]='caloutsource';
		obj["table"]='t_caloutsource';
		obj["id"]= $("#id").val();
		obj["check_param"]='insurancemonth;'+insurancemonth;
		if(!checkRepeat(obj)){
			$.messager.alert("提示","<span class='hintsp_w'>提示</span>当前月份已经存在外包保险单,不能重复计算！",'warning');
			$('#insurancemonth').val('');
			return;
		};
	}
	//制单日期
	function createDataPickFunc(){
		//验证是当前会计期间的 日期TODO
		var accountperiod=$('#accountperiod').val();
		var createdate=$('#createdate').val();
		if(!checkEm(accountperiod)){
			if(createdate.substring(0,7) != accountperiod){
				$.messager.alert("提示","<span class='hintsp_w'>提示</span>制单日期必须在当前会计期间内！",'warning');
				$('#createdate').val('');
				return;
			}
		}
		
	}
	//双击进入编辑
	function listDbClickFun(row){
		var obj=new Object();
		obj["renderid"]='#gridlist';
		obj["type"]=2;
		obj["width"]='1120px';
		obj["height"]='441px';
		ButtonFun.editFun(obj);
	}
	//计算外包保险
	function caculation(){
		$.ajax({
		    url:getUrl('caloutsource/calinsurance.do'),
		    type:'post',
		    async:false,
		    data: {},
		    dataType:'json',
		    success:function(data){
	  			if(data.state == 'success'){
	  				var footercomptotal=0;
	  				var footerpertotal=0;
	  				var footertotaltotal=0;
	  				for(var i =0; i<data.rows.length; i++){//加载成功计算个人合计  和单位合计  和合计
                		var row=data.rows[i];
                		var compparttotal=0;
                		var personparttotal=0;
                		var totalparttotal=0;
                		$.each(row, function(key, val) {
            				if(key.split('-')[1]=='comppart'){
            					compparttotal+=parseFloat(row[key]);
            				}else if(key.split('-')[1]=='personpart'){
            					personparttotal+=parseFloat(row[key]);
            				}
                		});
                		row.compparttotal=compparttotal.toFixed(2);
                		row.personparttotal=personparttotal.toFixed(2);
                		row.totalparttotal=(parseFloat(compparttotal)+parseFloat(personparttotal)).toFixed(2);
                		//footer合计
                		footercomptotal=(parseFloat(footercomptotal) + parseFloat(compparttotal)).toFixed(2);
                		footerpertotal =(parseFloat(footerpertotal) + parseFloat(personparttotal)).toFixed(2);
                		footertotaltotal =(parseFloat(footertotaltotal) + parseFloat(compparttotal)+parseFloat(personparttotal)).toFixed(2);
                	}
	  				(data.footer)[0].compparttotal=footercomptotal;
	  				(data.footer)[0].personparttotal=footerpertotal;
	  				(data.footer)[0].totalparttotal=footertotaltotal;
	  				//封装columns
    				var columns=new Array();
    				columns.push({field:'id',title:'id',hidden:true});
    				
    				columns.push({field:'empdocid',title:'empdocid',hidden:true});
    				columns.push({field:'empdoc_name',title:'职员名称',align: 'left', halign: 'center',width:70});
    				columns.push({field:'deptid',title:'deptid',hidden:true});
    				columns.push({field:'dept_name',title:'部门名称',align: 'left', halign: 'center',width:76});
    				columns.push({field:'insurancesubid',title:'insurancesubid',hidden:true});
    				columns.push({field:'basemoney',title:'参保基数',align: 'right', halign: 'center',width:70,formatter:cloformatMoney});
            
    				var itemNames=data.itemNames;
    				$("#itemNames").val(JSON.stringify(itemNames));
    				var editor=new Object();
  	    				editor["type"]="numberbox";
  	    				editor["options"]={precision:2};
    				for(var i=0 ; i<itemNames.length ; i++){
    					var column=new Object();
    					column["field"]=itemNames[i].insuranceitemscode;
    					column["title"]=itemNames[i].insuranceitemsname;
    					column["width"]=97;
    					column["editor"]=editor;
    					column["align"]='right';
    					column["halign"]='center';
    					column["formatter"]=cloformatMoney;
    					columns.push(column);
    					//columns.push({field : itemNames[i].code ,title:itemNames[i].name,width:80,type:'numberbox'});
    				}
    				columns.push({field:'compparttotal',title:'单位合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
    				columns.push({field:'personparttotal',title:'个人合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
    				columns.push({field:'totalparttotal',title:'合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
	               //渲染grid
    				$('#caloutsourcemx_gridlist').datagrid({  
	                    title:'',  
	                    height:305,  
	                    singleSelect:true,  
	                    data:data,
	                    showFooter:true,
	                    editIndex : undefined,
	                   	columns : [  
	                        columns  
	                    ],
	                    onLoadSuccess : function (data){
	                    },
	                    onEndEdit : function (index,row,changes){//单元格编辑后计算 个人合计  和单位合计  和合计
	                    	var editors = $('#caloutsourcemx_gridlist').datagrid('getEditors', index);     
	                    	if(editors.length>0){
	                    		var footer=(data.footer)[0];
	                    		var field=editors[0].field;
	                    		if(editors[0].field.split('-')[1]=='comppart'){
		                    		var compparttotal=0;
		                    		$.each(row, function(key, val) {
	                    				if(key.split('-')[1]=='comppart'){
	                    					compparttotal+=parseFloat(row[key])
	                    				}
		                    		});
		                    		row.compparttotal=compparttotal.toFixed(2);
		                    		if(checkEm(row.personparttotal)){
		                    			row.personparttotal=0;
		                    		}
		                    		row.totalparttotal=(compparttotal+parseFloat(row.personparttotal)).toFixed(2);
		                    		footer[field] = calTotalByCol("#caloutsourcemx_gridlist",field);
		                    		footer["compparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","compparttotal");
		                    		footer["totalparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","totalparttotal");
		                    		$('#caloutsourcemx_gridlist').datagrid('reloadFooter',data.footer);
		                    	}else if(editors[0].field.split('-')[1]=='personpart'){
		                    		var personparttotal=0;
		                    		$.each(row, function(key, val) {
	                    				if(key.split('-')[1]=='personpart'){
	                    					personparttotal+=parseFloat(row[key])
	                    				}
		                    		});
		                    		row.personparttotal=personparttotal.toFixed(2);
		                    		if(checkEm(row.compparttotal)){
		                    			row.compparttotal=0;
		                    		}
		                    		row.totalparttotal=(personparttotal+parseFloat(row.compparttotal)).toFixed(2);
		                    		footer[field] = calTotalByCol("#caloutsourcemx_gridlist",field);
		                    		footer["personparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","personparttotal");
		                    		footer["totalparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","totalparttotal");
		                    		$('#caloutsourcemx_gridlist').datagrid('reloadFooter',data.footer);
		                    	}
	                    	}
	                    	
	                    },
	                    onClickCell: function(index, field){//编辑单元格
	    					if ($('#caloutsourcemx_gridlist').datagrid('options').editIndex == undefined){
	    						$('#caloutsourcemx_gridlist').datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
	    						$('#caloutsourcemx_gridlist').datagrid('options').editIndex = index;
	    						var ed = $('#caloutsourcemx_gridlist').datagrid('getEditor', {index:index,field:field});  
	    						if($(ed.target).next().find(".textbox-text").length>0){
	    							$(ed.target).next().find(".textbox-text").focus();
	    						}else{
	    							$(ed.target).focus();
	    						}
	    					}else if ($('#caloutsourcemx_gridlist').datagrid('validateRow', $('#caloutsourcemx_gridlist').datagrid('options').editIndex)){
	    						$('#caloutsourcemx_gridlist').datagrid('endEdit', $('#caloutsourcemx_gridlist').datagrid('options').editIndex);
	    						$('#caloutsourcemx_gridlist').datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
	    						$('#caloutsourcemx_gridlist').datagrid('options').editIndex = index;
	    						var ed = $('#caloutsourcemx_gridlist').datagrid('getEditor', {index:index,field:field});
	    						if($(ed.target).next().find(".textbox-text").length>0){
	    							$(ed.target).next().find(".textbox-text").focus();
	    						}else{
	    							$(ed.target).focus();
	    						}
	    					}
	    				}
	    			});  

	    			/* $.messager.show({
	    				title:'操作成功',
	    				msg:data.message,
	    				showType:'show',
	    				timeout:1000,
	    			}); */
	    			promptbox('success','操作成功！');
				}else if(data.state == 'error'){
					$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
				}
		    },
		    error : function(data){
		    	$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败，请联系管理员','error');
		    }
		    
	    });
	}
	//数字类型format  会计格式
	function cloformatMoney(value,row,index){
		return formatMoney(value);
	}
	//编辑通用赋值前触发方法
	function beforeEditFun(node){
	}
	
	//编辑后带入数据方法
	function afterEditFun(node){
		$('#accountperiod').val(node.accountperiod);
		$("#insurancemonth").attr("disabled",true);
		$("#createdate").attr("disabled",true);
		 var id=node.id;
		var itemNames=node.itemNames/* JSON.parse($("#itemNames").val()) */;
		 if (Array.isArray(node.itemNames)) {
			    // Array
			}else{
				itemNames=JSON.parse(node.itemNames);
			}
		var rows=JSON.parse(node.caloutsourcemx);
		//封装columns
		var columns=new Array();
		columns.push({field:'id',title:'id',hidden:true});
		columns.push({field:'empdocid',title:'empdocid',hidden:true});
		columns.push({field:'empdoc_name',title:'职员名称',align: 'left', halign: 'center',width:70});
		columns.push({field:'deptid',title:'deptid',hidden:true});
		columns.push({field:'dept_name',title:'部门名称',align: 'left', halign: 'center',width:76});
		columns.push({field:'insurancesubid',title:'insurancesubid',hidden:true});
		columns.push({field:'basemoney',title:'参保基数',align: 'right', halign: 'center',width:70,formatter:cloformatMoney});

		var editor=new Object();
				editor["type"]="numberbox";
				editor["options"]={precision:2};
		for(var i=0 ; i<itemNames.length ; i++){
			var column=new Object();
			column["field"]=itemNames[i].insuranceitemscode;
			column["title"]=itemNames[i].insuranceitemsname;
			column["width"]=97;
			column["editor"]=editor;
			column["align"]='right';
			column["halign"]='center';
			column["formatter"]=cloformatMoney;
			columns.push(column);
			//columns.push({field : itemNames[i].code ,title:itemNames[i].name,width:80,type:'numberbox'});
		}
		columns.push({field:'compparttotal',title:'单位合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
		columns.push({field:'personparttotal',title:'个人合计',width:80,align: 'right', halign: 'center',formatter:cloformatMoney});
		columns.push({field:'totalparttotal',title:'合计',width:80,align: 'right', halign: 'center',
			formatter:function(value,row,index){
			return '<p style="width:100%;height:100%;margin:0 0 0 -17px">'+formatMoney(value)+'</p>';
		}});
       //渲染grid
		$('#caloutsourcemx_gridlist').datagrid({  
            title:'',  
            height:305,  
            singleSelect:true,  
            data:rows,
            showFooter:true,
            editIndex : undefined,
           	columns : [  
                columns  
            ],
            onLoadSuccess : function (data){
            },
            onEndEdit : function (index,row,changes){//单元格编辑后计算 个人合计  和单位合计  和合计
            	debugger
            	var editors = $('#caloutsourcemx_gridlist').datagrid('getEditors', index);     
            	if(editors.length>0){
            		var footer=(rows.footer)[0];
            		var field=editors[0].field;
            		if(editors[0].field.split('-')[1]=='comppart'){
                		var compparttotal=0;
                		$.each(row, function(key, val) {
            				if(key.split('-')[1]=='comppart'){
            					compparttotal+=parseFloat(row[key])
            				}
                		});
                		row.compparttotal=compparttotal.toFixed(2);
                		if(checkEm(row.personparttotal)){
                			row.personparttotal=0;
                		}
                		row.totalparttotal=(compparttotal+parseFloat(row.personparttotal)).toFixed(2);
                		footer[field] = calTotalByCol("#caloutsourcemx_gridlist",field);
                		footer["personparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","personparttotal");
                		footer["totalparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","totalparttotal");
                		$('#caloutsourcemx_gridlist').datagrid('reloadFooter',rows.footer);
                	}else if(editors[0].field.split('-')[1]=='personpart'){
                		var personparttotal=0;
                		$.each(row, function(key, val) {
            				if(key.split('-')[1]=='personpart'){
            					personparttotal+=parseFloat(row[key])
            				}
                		});
                		row.personparttotal=personparttotal.toFixed(2);
                		if(checkEm(row.compparttotal)){
                			row.compparttotal=0;
                		}
                		row.totalparttotal=(personparttotal+parseFloat(row.compparttotal)).toFixed(2);
                		footer[field] = calTotalByCol("#caloutsourcemx_gridlist",field);
                		footer["compparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","compparttotal");
                		footer["totalparttotal"] = calTotalByCol("#caloutsourcemx_gridlist","totalparttotal");
                		$('#caloutsourcemx_gridlist').datagrid('reloadFooter',rows.footer);
                	}
            	}
            	
            }
		});  

	}
	function editVoucher(){
		if(!checkEm($("#isaudit").val()) && $("#isaudit").val()=='1'){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>当前外包保险单已生成凭证，不可修改,请撤帐后进行修改！','info');
			return;
		}
		$("#insurancemonth").attr("disabled",false);
		$("#createdate").attr("disabled",false);
	/* 	if(!checkEm($("#id").val())){ */
			$("#caloutsourcemx_gridlist").datagrid({
	            onClickCell: function(index, field){//编辑单元格
					if ($('#caloutsourcemx_gridlist').datagrid('options').editIndex == undefined){
						$('#caloutsourcemx_gridlist').datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
						$('#caloutsourcemx_gridlist').datagrid('options').editIndex = index;
						var ed = $('#caloutsourcemx_gridlist').datagrid('getEditor', {index:index,field:field});  
						if($(ed.target).next().find(".textbox-text").length>0){
							$(ed.target).next().find(".textbox-text").focus();
						}else{
							$(ed.target).focus();
						}
					}else if ($('#caloutsourcemx_gridlist').datagrid('validateRow', $('#caloutsourcemx_gridlist').datagrid('options').editIndex)){
						$('#caloutsourcemx_gridlist').datagrid('endEdit', $('#caloutsourcemx_gridlist').datagrid('options').editIndex);
						$('#caloutsourcemx_gridlist').datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
						$('#caloutsourcemx_gridlist').datagrid('options').editIndex = index;
						var ed = $('#caloutsourcemx_gridlist').datagrid('getEditor', {index:index,field:field});
						if($(ed.target).next().find(".textbox-text").length>0){
							$(ed.target).next().find(".textbox-text").focus();
						}else{
							$(ed.target).focus();
						}
					}
				}
			})
		/* } */
	}
	function add(){
		//当前会计期间内是否有计算社保单（制单日期，不是社保月份），如果有则提示：当月已计算完社保，不可重复计算，是否继续。    如果是，则仍进入编辑界面
		var obj=new Object();
		var accountperiod=$('#accountperiod').val();
		obj["code"]='calnorinsurance';
		obj["table"]='t_caloutsource';
		obj["id"]= $("#id").val();
		obj["check_param"]='left(createdate,7);'+accountperiod;
		if(!checkRepeat(obj)){
			$.messager.confirm('提示', '<span class="hintsp_w">提示</span>当月已计算过外包保险，不可重复计算，是否继续？', function(r){
				if (r){
					ButtonFun.addFun({width:1120,height:441});
				}
			})
		}else{
			ButtonFun.addFun({width:1120,height:441});
		}
		
	};
	//显示可编辑表格
	function afterAddFun(){
		$.ajax({
			url:getUrl("caloutsource/findMaxCodeAndEtc"),
			type: 'post',
			async: false,
			data: {},
			dataType: 'json',
			success: function(data){
				if(!checkEm(data)){
					$('#caloutsourcemx_gridlist').datagrid({ columns : []});
					$('#accountperiod').val(data.accountperiod);
					$('#createby').val(data.createby);
					$('#insurancemonth').val(data.insurancemonth);
					$("#createdate").val(data.createdate);
				}
				}
	});
	}
	
	function submit(){
		//保存时判断：制单日期所属期间内是否有计算社保单，如果有则提示：制单日期内已存在计算社保单据，是否继续保存。   可保存
		var obj=new Object();
		var accountperiod=$('#accountperiod').val();
		obj["code"]='calnorinsurance';
		obj["table"]='t_caloutsource';
		obj["id"]= $("#id").val();
		obj["check_param"]='left(createdate,7);'+accountperiod;
		if(!checkRepeat(obj)){
			$.messager.confirm('提示', '<span class="hintsp_w">提示</span>制单日期内已存在计算外包保险单据，是否继续保存？', function(r){
				if (r){
					var data = $('#caloutsourcemx_gridlist').datagrid('getData');
					var rows=data.rows;
					var totalmoney=0;
					var persontotalmoney=0;
					for(var i=0;i<rows.length;i++){
						if(!checkEm(rows[i].totalparttotal)){
							totalmoney += parseFloat(rows[i].totalparttotal);
						}
						if(!checkEm(rows[i].personparttotal)){
							persontotalmoney += parseFloat(rows[i].personparttotal);
						}
					}
					$("#persontotalmoney").val(persontotalmoney.toFixed(2));
					$("#totalmoney").val(totalmoney.toFixed(2));
					$('#caloutsourcemx').val(JSON.stringify(data));
					$('#caloutsourcemxdata').val(JSON.stringify(data.rows));
					$("#insurancemonth").attr("disabled",false);
					$("#createdate").attr("disabled",false);
					submitForm({code:'caloutsource',type:2,renderid:'#gridlist',noClose:true});
				}
			})
		}else{
			var data = $('#caloutsourcemx_gridlist').datagrid('getData');
			var rows=data.rows;
			var totalmoney=0;
			var persontotalmoney=0;
			for(var i=0;i<rows.length;i++){
				if(!checkEm(rows[i].totalparttotal)){
					totalmoney += parseFloat(rows[i].totalparttotal);
				}
				if(!checkEm(rows[i].personparttotal)){
					persontotalmoney += parseFloat(rows[i].personparttotal);
				}
			}
			$("#persontotalmoney").val(persontotalmoney.toFixed(2));
			$("#totalmoney").val(totalmoney.toFixed(2));
			$('#caloutsourcemx').val(JSON.stringify(data));
			$('#caloutsourcemxdata').val(JSON.stringify(data.rows));
			$("#insurancemonth").attr("disabled",false);
			$("#createdate").attr("disabled",false);
			submitForm({code:'caloutsource',type:2,renderid:'#gridlist',noClose:true});
		}
	}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){
		if(!checkEm($("#isaudit").val()) && $("#isaudit").val()=='1' && !checkEm($("#voucherid").val())){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>保存失败，当前外包保险单已生成凭证，若要修改，请撤帐后继续！','info');
			return false;
		}
		return true;
	}
	//通用保存后触发方法
	function afterSaveFun(id){
		$('#id').val(id);
		$("#insurancemonth").attr("disabled",true);
		$("#createdate").attr("disabled",true);
		$("#caloutsourcemx_gridlist").datagrid({
			 onClickCell: function(index, field){
				 
			 }
		})
	}

	//通用撤账后触发方法
	function afterVoucherRevokeFun(){}

	//通用生成凭证后触发方法
	function afterVoucherCreateFun(){
		var id = $('#id').val();
		$('#pageForm').form('load',getUrl('caloutsource/findById?id='+id));
		$("#insurancemonth").attr("disabled",true);
		$("#createdate").attr("disabled",true);
		$("#caloutsourcemx_gridlist").datagrid({
			 onClickCell: function(index, field){
				 
			 }
		})
	}
	//生成凭证方法
	function createvoucher(){
		//判断当前外包保险单是否已经生成凭证 TODO
		if(!checkEm($("#voucherid").val()) && $("#isaudit").val()=='1'){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>当前外包保险单已生成凭证，不可重复生成！','info');
			return;
		}
		if(checkEm($("#id").val())){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>请先保存单据后再进行操作！','info');
			return;
		}
		//判断制单日期是否已经结账 TODO
		
		var obj=new Object();
		var data = $('#caloutsourcemx_gridlist').datagrid('getData');
		var rows=data.rows;
		var totalmoney=0;
		var personparttotal=0;
		for(var i=0;i<rows.length;i++){
			if(!checkEm(rows[i].totalparttotal)){
				totalmoney += parseFloat(rows[i].totalparttotal);
			}
			if(!checkEm(rows[i].personparttotal)){
				personparttotal += parseFloat(rows[i].personparttotal);
			}
		}
		obj["personparttotal"] = personparttotal.toFixed(2);
		obj["totalmoney"] =totalmoney.toFixed(2);
		obj["code"] ='caloutsource';
		obj["id"] =$("#id").val();
		obj["createdate"] = $("#createdate").val();
		obj["accountperiod"] =$("#accountperiod").val();
		obj["caloutsourcemx"] = JSON.stringify(rows);
		$.ajax({
			url:getUrl("caloutsource/createvoucher"),
			type: 'post',
			async: false,
			data: obj,
			dataType: 'json',
			success: function(data){
				//后台返回数据处理逻辑
				if(!checkEm(data)){
					if(data.state == 'success'){
						$.messager.alert('提示', '<span class="hintsp_w">提示</span>'+data.message,'info');
						//clearForm();
						$('#gridlist').datagrid('reload');
						//$.messager.progress('close');
						afterVoucherCreateFun();
					}else if(data.state == 'error'){
						$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败','error');
					}
				}
				$.messager.progress('close');// 如果提交成功则隐藏进度条
			}
			,error:function(data){
				$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败,请联系管理员！','error');
				$.messager.progress('close');
			}
	});
	}
	function unaccount(){
		//判断当前外包保险单是否已生成凭证，如果未生成提示：当前外包保险单未生成凭证，撤账无效
		// 当前外包保险单的制单日期是否为当前会计期间，如果不是则提示：当前外包保险单所属期不是当前会计期间，不可执行撤账
		var id=$("#id").val();
		var isaudit=$("#isaudit").val();
		var voucherid=$("#voucherid").val();
		var accountperiod=$("#accountperiod").val();
		var createdate=$("#createdate ").val();
		if(checkEm(isaudit) || isaudit=='0'){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>当前外包保险单未生成凭证，撤帐无效！','info');
			return;
		}
		if(createdate.substring(0,7) != accountperiod){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>当前外包保险单所属期不是当前会计期间，不可执行撤账！','info');
			return;
		}
		$.ajax({
			url:getUrl("payroll/unaccount"),
			type: 'post',
			async: false,
			data: {"id" : id,"voucherid" : voucherid,"table" : 't_caloutsource'},
			dataType: 'json',
			success: function(data){
				//后台返回数据处理逻辑
				if(!checkEm(data)){
					if(data.state == 'success'){
						$.messager.alert('提示', '<span class="hintsp_w">提示</span>'+data.message,'info');
						//clearForm();
						$('#gridlist').datagrid('reload');
						//$.messager.progress('close');
						$("#voucherid").val('');
						$("#isaudit").val('');
					}else if(data.state == 'error'){
						var msg='';
						if(!checkEm(data.message)){
							msg=data.message;
						}else{
							msg='操作失败，请联系管理员。'
						}
						$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+msg,'error');
					}
				}
				$.messager.progress('close');// 如果提交成功则隐藏进度条
			}
			,error:function(data){
				$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败,请联系管理员！','error');
				$.messager.progress('close');
			}
	});
	}
	//删除方法
	function closeOrDelete(operType){
		
		var voucherid='';
		var nodes;
		if(operType=='删除1'){
			voucherid=$("#voucherid").val();
			nodes=1;
		}else{
			nodes=$('#gridlist').datagrid('getSelected');
			voucherid=nodes.voucherid;
		}
		if(!checkEm(voucherid)){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>当前外包保险单已生成凭证，不可删除！','info');
			return;
		}
		var url='';
		if(operType=='关闭'){
			url='caloutsource/closeAll?closestatus=1';
		}else{
			url='caloutsource/delete';
		}
		if(!checkEm(nodes)){
			if(operType=='删除1'){
				var obj=new Object();
					obj["ids"]=$('#id').val();
					delAndCloseAjax(url,obj,function(data){
						if(data != null && data["state"] == 'success'){
							/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>操作成功！',showType:'slide',timeout:1000,style:{
			    				right : '', bottom : '',
			    				top : document.body.scrollTop + document.documentElement.scrollTop
			    			}}); */
							promptbox('success','操作成功！');
							reloadData("#gridlist",2);
						}else if(data != null && data.state =='error'){
							$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
					}
					});
				ButtonFun.addFun({width:1120,height:445})
			}else{
				ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
			}
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}
	
	//导出
	function ExportExcel(){
		var obj=new Object;
		obj['renderid']='#caloutsourcemx_gridlist';
		obj['title'] = '计算外包保险';
		obj['controllername']='caloutsource';
		obj['cs'] = '';
		obj['pms'] = {accountperiod:$('#accountperiod').val()};
		toExcel(obj);
	}
	
</script>

</body>
</html>