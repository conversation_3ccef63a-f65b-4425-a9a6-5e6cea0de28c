<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background: #fff;">
		
			<!-- 左侧功能栏 -->
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({isCheckInitBalance:false,width:425,height:443})">新增</a>
			<a class="easyui-linkbutton compile-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:425,height:443})">编辑</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.findFun()">查找</a>
			<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')">删除</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('关闭')">禁用</a>
			<a class="easyui-linkbutton recover-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.recoveryFun()">恢复</a>
		</div>
		<div class="parceldivs" style="height: 331px; width: 630px;border:0">
			<div class="parceldiv_tow" style="height: 331px; width: 630px;">
				<div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);height: 331px;"></div>
			</div>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('empdoc')">关闭</a>
		</div>
		
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:600px;height:380px;">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px solid rgb(206, 218, 228);">
				<input type="hidden" id="id" name="id">
				<div class="bgdivtitfixed" style="border: 0;margin: -11px 0 0 0;height: 360px;width: 100%">
					<ul class="empdoc_ul" style="height: 350px;">
					   <li style="margin:25px 0 0 3px;">
					      <span>职员编码：</span>
					      <input class="easyui-textbox" type="text" name="code" data-options="required:true,validType:['repeat[\'empdoc\',\'c_empdoc\',\'code\',\'pageForm\']']" />
					   </li>
					   <li style="margin:25px 0 0 3px;">
					      <span>职员姓名：</span>
					      <input class="easyui-textbox" type="text" name="name" data-options="required:true,validType:['repeat[\'empdoc\',\'c_empdoc\',\'name\',\'pageForm\']']" />
					   </li>
					   <li>
					      <span>性别：</span>
						  <select id="sex" class="easyui-combobox" name="sex" data-options="required:true,editable:false,panelHeight:'auto'"  style="width:105px" title="款项种类">			
							  <option value="0">男</option>
						      <option value="1">女</option>
						  </select>
					   </li>
					   <li>
					      <span>所属部门：</span>
					      <input class="easyui-textbox" type="text" id="dept_name" name="dept_name" data-options="required:true" />
					   </li>
					    <li>
					      <span>保险类型：</span>
						  <select id="insurancetype" class="easyui-combobox" name="insurancetype" data-options="required:true,editable:false,panelHeight:'auto'" style="width:105px">			
							  <option value="0"></option>
						      <option value="1">正常</option>
						      <option value="2">外包</option>
						  </select>
					   </li>
					   <li  style="width: 100%;">
					      <span>工资科目：</span>
					      <input type="hidden" id="salarysubid"  name="salarysubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="salarysub_name"  name="salarysub_name" data-options="required:true" />
					      <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;salarysubid:id,salarysub_name:name')"></div>
					   </li>
					   <li style="width: 100%;">
					      <span>基本社保科目：</span>
					      <input type="hidden" id="insurancesubid"  name="insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="insurancesub_name" name="insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;insurancesubid:id,insurancesub_name:name')"></div>
					   </li>
					   <li style="width: 100%;">
					      <span>公积金科目：</span>
					      <input type="hidden" id="fund_insurancesubid"  name="fund_insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="fund_insurancesub_name" name="fund_insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;fund_insurancesubid:id,fund_insurancesub_name:name')"></div>
					   </li>
					   <li style="width: 100%;">
					      <span>补充养老科目：</span>
					      <input type="hidden" id="oldage_insurancesubid"  name="oldage_insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="oldage_insurancesub_name" name="oldage_insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;oldage_insurancesubid:id,oldage_insurancesub_name:name')"></div>
					   </li>
					    <li style="width: 100%;">
					      <span>补充医疗科目：</span>
					      <input type="hidden" id="medical_insurancesubid"  name="medical_insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="medical_insurancesub_name" name="medical_insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;medical_insurancesubid:id,medical_insurancesub_name:name')"></div>
					   </li>
					  <li style="width: 100%;">
					       <span>专项扣除：</span>
					       <input class="easyui-numberbox" style="width: 287px; padding:0 24px 0 2px;" id="specialdeducate" name="specialdeducate" data-options="required:true,min:0,precision:2" />
					   </li>
					</ul>
				</div>	
		    </form>
		    <div class="windowbottom" style="height:39px">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="clearForm()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" onclick="submitForm({code:'empdoc',type:2,renderid:'#gridlist'})">确定</a>
		    </div>
		</div>
		<!-- 查找弹窗 -->
		<div id="find_panel" class="easyui-window" style="width: 300px;height:184px" title="查找" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="search_pel"><input id="find_input" name="find_input" class="easyui-textbox" style="width:200px" data-options="iconCls:'icon-search'"></div> 
			<div class="windowbottom" style="height:41px">
		    	<a href="javascript:void(0)" class="easyui-linkbutton  cancel-btn qxNew_btn" onclick="clearFindPanel()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" onclick="$('#gridlist').datagrid('load',{q:$('#find_input').val()})">确定</a>
		    </div>
		</div>
	</div>

<script type="text/javascript">
	$(function(){
		var gridObj = new Object();
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'empdoc/list';
		gridObj["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'code',title:'职员编码',align: 'left', halign: 'center',width:100},
								{field:'name',title:'职员名称',align: 'left', halign: 'center',width:100},
								{field:'sex',title:'性别',align: 'left', halign: 'center',width:50},
								{field:'dept_name',title:'所属部门',align: 'left', halign: 'center',width:100},
								{field:'insurancetype',title:'保险类型',align: 'left', halign: 'center',width:100},
								{field:'salarysub_name',title:'工资科目',align: 'left', halign: 'center',width:120},
								{field:'insurancesub_name',title:'基本社保科目',align: 'left', halign: 'center',width:120},
								{field:'fund_insurancesub_name',title:'公积金科目',align: 'left', halign: 'center',width:120},
								{field:'oldage_insurancesub_name',title:'补充养老科目',align: 'left', halign: 'center',width:120},
								{field:'medical_insurancesub_name',title:'补充医疗科目',align: 'left', halign: 'center',width:120},
								{field:'specialdeducate',title:'专项扣除',align: 'left', halign: 'center',width:100}
		                     ]];
		gridObj["idField"] = 'id';
		gridObj["listDbClickFun"] = listDbClickFun;
		Grid.list_grid(gridObj);
	});
	//双击行的触发事件
	function listDbClickFun(row){
		ButtonFun.editFun({type:2,renderid:'#gridlist',width:425,height:443});
	}
	
	//新增按钮点击后通用触发方法
	function afterAddFun(){}
	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	//编辑通用赋值后触发方法
	function afterEditFun(node){
		$("#sex").combobox("setValue",node.sexid);
		$("#insurancetype").combobox("setValue",node.insurancetypeid);
	}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){return true;}
	//通用保存后触发方法
	function afterSaveFun(id){}
	
	//恢复按钮初始化参数
	function initRecovery(){
		var obj= {};
		obj["title"] = "恢复";
		obj["code"] = "empdoc";
		obj["position"] = "#grid_selectrlist";
		obj["columns"] = [[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'code',title:'职员编码',align: 'left', halign: 'center',width:172},
							{field:'name',title:'职员名称',align: 'left', halign: 'center',width:172},
		                    {field:'operation',title:'操作',width:123,formatter:formatOper_renew,no_select:true}
	                     ]];
		return obj;
	}
	
	//渲染恢复按钮
	function formatOper_renew(val,row,index){
		var url = "empdoc/closeAll.do?DATA_IDS="+row["id"]+"&closestatus=0&table_name=c_custom";
	    return '<a href="javascript:void(0)" class="easyui-linkbutton  recover-cssbtn" onclick="ButtonFun.renew(\''+url+'\')">恢复</a>';  
	}
	//刷新视图
	function reshView(){
		$('#grid_selectrlist').datagrid('reload');
		$('#gridlist').datagrid('reload');
	}
	//部门选择框
	var obj = new Object();
	obj["idColName"] = 'name';
	obj["url"] = 'deptdoc/list';
	obj["cgId"] = 'dept_name';
	obj["cgColumns"] = 	[[	{field:'code',title:'部门编码',align: 'left', halign: 'center',width:100},
		             		{field:'name',title:'部门名称',align: 'left', halign: 'center',width:100}
						]];
	Dialog.archives_selector_list(obj);
	
	/* var obj = new Object();
	obj["idColName"] = 'name';
	obj["url"] = 'accsubjects/list';
	obj["cgId"] = 'salarysub_name';
	obj["cgColumns"] = 	[[	{field:'code',title:'科目编码',width:100},
		             		{field:'name',title:'科目名称',width:100}
						]];
	Dialog.archives_selector_list(obj);
	
	var obj = new Object();
	obj["idColName"] = 'name';
	obj["url"] = 'accsubjects/list';
	obj["cgId"] = 'insurancesub_name';
	obj["cgColumns"] = 	[[	{field:'code',title:'科目编码',width:100},
		             		{field:'name',title:'科目名称',width:100}
						]];
	Dialog.archives_selector_list(obj); */
	
	//侧栏关闭按钮方法 operType：str  关闭   删除
	function closeOrDelete(operType){
		var nodes=$('#gridlist').datagrid('getSelected');
		var url='';
		if(operType=='关闭'){
			url='empdoc/closeAll?closestatus=1';
		}else{
			url='empdoc/deleteAll';
		}
		if(!checkEm(nodes)){
			$.messager.confirm(operType+'提示', '<span class="hintsp_w">提示</span>确定是否对该职员执行'+operType+'，'+operType+'后不再被引用？', function(r){
				if (r){
					ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
				}
			})
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}
	
</script>

</body>
</html>