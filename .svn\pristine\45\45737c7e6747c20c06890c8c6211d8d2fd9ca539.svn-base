package org.newstanding.controller.systemset;

	import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

	import javax.annotation.Resource;

	import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.financialhandle.FillvouchersService;
import org.newstanding.service.systemset.PrintSetService;
import org.newstanding.service.systemset.SupplierService;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
	/**
	 * deptdoc
	 * 
	 * <AUTHOR>
	 *
	 */
	@Controller
	@RequestMapping(value = "/printSet")
	public class PrintSetController extends BaseController {
		@Resource(name = "PrintsetService")
		private PrintSetService PrintsetService;
		@Resource(name = "fillvouchersService")
		private FillvouchersService fillvouchersService;
		/**
		 * 新增
		 * 
		 * @return
		 * @throws Exception
		 */
		@RequestMapping(value = "/save")
		@ResponseBody
		public PageData save() throws Exception {
			PageData pd = new PageData();
			pd = this.getPageData();
			pd.put("createby", getCurrentUserByCatch().getUsername());
			return PrintsetService.save(pd);
		}

		/**
		 * 删除
		 * 
		 * @throws Exception
		 */
		@RequestMapping(value = "/delete")
		@ResponseBody
		public Object delete() throws Exception {
			PageData pd = this.getPageData();
			return PrintsetService.delete(pd);
		}

		/**
		 * 修改
		 */
		@RequestMapping(value = "/edit")
		@ResponseBody
		public Object edit() throws Exception {
			PageData pd = this.getPageData();
			return PrintsetService.edit(pd);
		}

		/**
		 * 列表
		 * @throws Exception 
		 */
		@ResponseBody
		@RequestMapping(value = "/list")
		public Map<String, Object> list(Page page) throws Exception {
			PageData pd = this.getPageData(),resultPageData = null;
			setPage(pd,page);
			Map<String, Object> resultMap = new HashMap<String, Object>();
			page.setPd(pd);
			resultPageData = PrintsetService.list(page);
			List<PageData> varList = null;
			if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
				varList = (List<PageData>) resultPageData.get("list");
				resultMap.put("total", page.getTotalResult());
				resultMap.put("rows", varList);
			}else{
				resultMap.putAll(resultPageData);
			}
			
			return resultMap;
		}
		/**
		 * 
		 */
		@RequestMapping(value = "/findByCode")
		@ResponseBody
		public Object findByCode() throws Exception {
			PageData pd = this.getPageData();
			return PrintsetService.findByCode(pd);
		}
		/**
		 * 验证预览页面
		 */
		@ResponseBody
		@RequestMapping(value="/check_yl")
		public Object check_yl(){
			PageData pd = new PageData();
			try {
				pd = this.getPageData();
				List<PageData> list = PrintsetService.findPrintSetByMenucode(pd);
				if(list.size()>0 && list.get(0)!=null ){
					pd.put("size", list.size());
					List<String> strs = new ArrayList<String>();
					for (PageData p : list) {
						strs.add(p.get("name").toString());
					}
					pd.put("list", strs);
				
				}else{
					pd.put("size", "0");
				}
			} catch (Exception e) {
				logger.error(e.toString(), e);
			}
			return pd;
		}
		
		/**
		 * 去edit预览页面
		 * @throws UnsupportedEncodingException 
		 */
		@RequestMapping(value="/goEdit_yl")
		public ModelAndView goEdit_yl() throws UnsupportedEncodingException{
			logBefore(logger, "去修改Xsdd预览页面");
			ModelAndView mv = this.getModelAndView();
			PageData pd = new PageData();
			PageData pageData = new PageData();
			pd = this.getPageData();
			String name=URLDecoder.decode( URLDecoder.decode(pd.get("name").toString(), "utf-8"),"utf-8");
			pd.put("name",name);
			String param = "";
			if(!StringUtils.isEmpty(pd.get("param"))) {
				param=URLDecoder.decode( URLDecoder.decode(pd.get("param").toString(), "utf-8"),"utf-8");
			}
			pd.put("param",param);
			try {
				List<PageData> list = PrintsetService.findPrintSetByMenucode(pd);
				if(pd.get("size") != null){
					Integer size = Integer.parseInt(pd.get("size").toString());
					pageData = list.get(size);
				}else{
					pageData = list.get(0);
				}
				pd.put("html", pageData.get("html"));
				pd.put("tdf", pageData.get("tdf"));
				pd.put("pagegs", pageData.get("pagegs"));
				pd.put("css", pageData.get("css"));
				pd.put("content", pageData.get("content"));
				pd.put("title", pageData.get("title"));
				pd.put("width", pageData.get("width"));
				pd.put("height", pageData.get("height"));
				pd.put("hs", pageData.get("hs"));
				pd.put("name", pageData.get("name"));
				pd.put("menucode", pageData.get("menucode"));
				pd.put("page_top", pageData.get("page_top"));
				pd.put("page_bottom", pageData.get("page_bottom"));
				pd.put("page_left", pageData.get("page_left"));
				pd.put("page_right", pageData.get("page_right"));
				
				pd.put("abstractawidth", pageData.get("abstractawidth"));
				pd.put("subjectnamewidth", pageData.get("subjectnamewidth"));
				pd.put("debitmoneywidth", pageData.get("debitmoneywidth"));
				pd.put("creditmoneywidth", pageData.get("creditmoneywidth"));
				
				//如果在列表中批量打印  需要根据 会计期间  查出  总数据条数，放在页面中  循环打印
				List<PageData> datalist = fillvouchersService.listByPeriod(pd);
				pd.put("dataSize", datalist.size());
				if(pd.get("operpage") !=null && "report".equals(pd.get("operpage"))){
					mv.setViewName("system/systemset/report_yl");
				}else{
					mv.setViewName("system/systemset/edit_yl");
				}
				mv.addObject("pd", pd);
			} catch (Exception e) {
				logger.error(e.toString(), e);
			}
			return mv;
		}
	}
