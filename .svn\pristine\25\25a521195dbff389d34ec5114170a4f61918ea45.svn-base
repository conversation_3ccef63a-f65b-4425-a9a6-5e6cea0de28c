package org.newstanding.controller.systemset;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.systemset.AccSubjectsService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
/**
 * deptdoc
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "/accsubjects")
public class AccSubjectsController extends BaseController {
	@Resource(name = "accSubjectsService")
	private AccSubjectsService accSubjectsService;

	/**
	 * 新增
	 * 
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/save")
	@ResponseBody
	public PageData save() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return accSubjectsService.save(pd);
	}

	/**
	 * 删除
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/delete")
	@ResponseBody
	public Object delete() throws Exception {
		PageData pd = this.getPageData();
		return accSubjectsService.delete(pd);
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit")
	@ResponseBody
	public Object edit() throws Exception {
		PageData pd = this.getPageData();
		return accSubjectsService.edit(pd);
	}
	/**
	 * 通过ID获取数据
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/findById")
	public Object findById() throws Exception {
		PageData pd = this.getPageData();
		PageData tmp = accSubjectsService.findById(pd);
		return tmp;
	}
	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		page.setPd(pd);
		resultPageData = accSubjectsService.list(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return resultMap;
	}
	
	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/treeList")
	public Object treeList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		page.setPd(pd);
		resultPageData = accSubjectsService.treelist(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return varList;
	}

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		PageData pd=this.getPageData();
		String accountingcode = accSubjectsService.getAccountcode(pd);
		mv.addObject("accountingcode",accountingcode);
		mv.setViewName("system/systemset/accsubjects");
		return mv;
	}
	
	/**
	 * 去新增页
	 */
	@RequestMapping(value = "/goAdd")
	public ModelAndView goAdd() {
		ModelAndView mv = this.getModelAndView();
		PageData pd = this.getPageData();
		mv.setViewName("public/accsubjects_edit");
		mv.addObject("pd", pd);
		String accountingcode = accSubjectsService.getAccountcode(pd);
		mv.addObject("accountingcode",accountingcode);
		return mv;
	}
	
	/**
	 * 去修改页面
	 * @throws Exception 
	 */
	@RequestMapping(value="/goEdit")
	public ModelAndView goEdit() throws Exception{
		ModelAndView mv = this.getModelAndView();
		PageData pd = this.getPageData();
		PageData pd2 = new PageData();
		pd2 = accSubjectsService.findById(pd);	//根据ID读取
		mv.setViewName("public/accsubjects_edit");
		pd.putAll(pd2);
		mv.addObject("pd", pd);
		String accountingcode = accSubjectsService.getAccountcode(pd);
		mv.addObject("accountingcode",accountingcode);
		return mv;
	}
	
	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "c_accSubjects");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			List<PageData> list = accSubjectsService.checkDelete(pd);
			if(list != null && list.size() > 0 && list.get(0) != null && Integer.parseInt(list.get(0).get("s_num").toString()) > 0 ){
				pd.put("state", "error");
				pd.put("message", "该档案已经被使用,不可删除！");
			}else{
				pd = accSubjectsService.deleteAll(pd);
			}
			
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	/**
	 * 验证是否被使用
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkDelete")
	public PageData checkDelete() throws Exception {
		PageData pd = this.getPageData();
		List<PageData> list = accSubjectsService.checkDelete(pd);
		if(list != null && list.size() > 0 && list.get(0) != null && Integer.parseInt(list.get(0).get("s_num").toString()) > 0 ){
			pd.put("state", "error");
			pd.put("message", "该档案已经被使用,不可修改层级！");
		}
		return pd;
	}
	/**
	 * 验证重复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkRepeatByParam")
	public boolean checkRepeatByParam() throws Exception {
		PageData pd = this.getPageData();
		if( accSubjectsService.checkRepeatByParam(pd) ){
			return false;
		}
		return true;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "c_accSubjects");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = accSubjectsService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 搜索 导出 查询 部门列表
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = accSubjectsService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	/**
	 * 根据科目code 查询科目
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/findByCode")
	public Object findByCode() throws Exception {
		PageData pd=this.getPageData();
		PageData resPd=accSubjectsService.findByCode(pd);
		List<PageData> list = accSubjectsService.findChildByCode(pd);
		if(list.size()>0){
			resPd.put("hasChild", "是");
		}else{
			resPd.put("hasChild", "否");
		}
		return resPd;
	}
	
	/**
	 * 根据科目code 查询科目
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/findHaveKmye")
	public Object findHaveKmye() throws Exception {
		PageData pd=this.getPageData();
		List<PageData> list = accSubjectsService.findHaveKmye(pd);
		if(list.size()>0){
			pd.put("state", "error");
		}else{
			pd.put("state", "success");
		}
		return pd;
	}
	
	@ResponseBody
	@RequestMapping(value = "/updateAliasName")
	public Object updateAliasName() throws Exception {
		PageData pd=this.getPageData();
		String[] atcode=getAccountCodeArray();
		pd.put("codeArray", atcode);
		PageData resPd=accSubjectsService.updateAliasName(pd);
		return resPd;
	}
}
