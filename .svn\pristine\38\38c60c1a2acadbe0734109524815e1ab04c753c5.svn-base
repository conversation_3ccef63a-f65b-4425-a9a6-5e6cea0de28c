<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Format NumberBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Format NumberBox</h2>
	<p>Number formatting is the ability to control how a number is displayed.</p>
	<div style="margin:20px 0;"></div>
	<table>
		<tr>
			<td>Number in the United States</td>
			<td><input class="easyui-numberbox" value="1234567.89" data-options="precision:2,groupSeparator:','"></input></td>
		</tr>
		<tr>
			<td>Number in France</td>
			<td><input class="easyui-numberbox" value="1234567.89" data-options="precision:2,groupSeparator:' ',decimalSeparator:','"></input></td>
		</tr>
		<tr>
			<td>Currency:USD</td>
			<td><input class="easyui-numberbox" value="1234567.89" data-options="precision:2,groupSeparator:',',decimalSeparator:'.',prefix:'$'"></input></td>
		</tr>
		<tr>
			<td>Currency:EUR</td>
			<td><input class="easyui-numberbox" value="1234567.89" data-options="precision:2,groupSeparator:',',decimalSeparator:' ',prefix:'€'"></input></td>
		</tr>
		<tr>
			<td></td>
			<td><input class="easyui-numberbox" value="1234567.89" data-options="precision:2,groupSeparator:' ',decimalSeparator:',',suffix:'€'"></input></td>
		</tr>
	</table>

</body>
</html>