package org.newstanding.controller.base;

import java.io.FileNotFoundException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.FileUtil;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.ReflectHelper;
import org.newstanding.common.utils.ServiceHelper;
import org.newstanding.common.utils.data.JedisUtil;
import org.newstanding.common.utils.excel.ObjectExcelView;
import org.newstanding.common.utils.excel.ObjectExcelView2;
import org.newstanding.dao.JedisClient;
import org.newstanding.service.invoicing.SaleoutService;
import org.newstanding.service.systemset.AccSubjectsService;
import org.newstanding.service.systemset.AccountInfoService;
import org.newstanding.service.systemset.CustomerService;
import org.newstanding.service.systemset.GoodsService;
import org.newstanding.service.systemset.SupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import redis.clients.jedis.Jedis;

/** 
 * 类名称：HeadController
 * 创建人：FH 
 * 创建时间：2014年8月16日
 * @version
 */
@Controller
@RequestMapping(value="/export")
public class ExportController extends BaseController {
	@Autowired
	private AccountInfoService accountInfoService;
	@Resource(name = "accSubjectsService")
	private AccSubjectsService accSubjectsService;
	@Resource(name = "customerService")
	private CustomerService customerService;
	@Resource(name = "supplierService")
	private SupplierService supplierService;
	
	@Resource(name = "goodsService")
	private GoodsService goodsService;
	
	@Resource(name = "saleoutService")
	private SaleoutService saleoutService;
	@Autowired
	JedisClient jedisClient;
	
	@RequestMapping(value="/excel")
	public ModelAndView exportExcel(){
		ModelAndView mv = new ModelAndView();
		PageData pd = new PageData();
		pd = this.getPageData();
		try{
			String filename = pd.get("filename").toString();
			String title = pd.get("title").toString();
			String headers_str = pd.get("headers").toString();
			String columns_str = pd.get("columns").toString();
			String run_method = pd.get("run_method").toString();
			String method_type = pd.get("method_type").toString();
			Object isParent = pd.get("isParent");
			String cs = pd.get("cs").toString();
			String accountname = "";//报表中的账套名
			if( pd.get("accountname")!=null) {
				accountname = pd.get("accountname").toString();
			}
			String account_name = ""; //出纳明细中的账户名称
			if( pd.get("account_name")!=null) {
				account_name = pd.get("account_name").toString();
			}
			String begin_balance = ""; //出纳明细中的期初金额
			if( pd.get("begin_balance")!=null) {
				begin_balance = pd.get("begin_balance").toString();
			}
			pd.put("cs", cs);
			String service_name = pd.getString("service_name");
			Object service = ServiceHelper.getService(service_name+"Service");
			
			List<PageData> resList = null;
			Map<String,Object> resMap = new TreeMap<>();
			if("2".equals(method_type)) {//辅助明细账
				//查找所有的辅助核算  数据  将数据封装成一个map  key为表头数据  value 为对应的表数据
				List<PageData> subOrAuxList=accountInfoService.findAuxList(pd);
				if(subOrAuxList.size()>0){
					for(PageData subAuxPd : subOrAuxList){
						subAuxPd.put("database", pd.get("database"));
						String p = "";
						if(pd.get("params") !=null) {
							p=pd.get("params").toString();
						}
						pd.put("params", p.substring(0, p.lastIndexOf(","))+",assistid:"+subAuxPd.get("id"));
						resList =(List<PageData>)((PageData) ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class)).get("list");
						
						
						String thirdRow="科目："+pd.get("accsubject_name")+"             单位："+subAuxPd.getString("name")
						+"                     会计期间："+pd.getString("month_begin")+"至"+pd.getString("month_finish");
						resMap.put(accountname+";"+thirdRow, resList);
					}
				}
			}else if("1".equals(method_type)) {
				//查找所有的会计科目  数据  将数据封装成一个map  key为表头数据  value 为对应的表数据
				List<PageData> subOrAuxList= new ArrayList<>();
				if(isParent !=null && !"".equals(isParent.toString())) {
					subOrAuxList = accountInfoService.findFirstLevelSubList(pd);
				}else {
					subOrAuxList=accountInfoService.findSubList(pd);
				}
				
				if(subOrAuxList.size()>0){
					for(PageData subAuxPd : subOrAuxList){
						subAuxPd.put("database", pd.get("database"));
						String p = "";
						if(pd.get("params") !=null) {
							p=pd.get("params").toString();
						}
						pd.put("params", p.substring(0, p.lastIndexOf(",subjectid"))+",subjectid:"+subAuxPd.get("id")+",subjectcode:"+subAuxPd.get("code"));
						resList =(List<PageData>)((PageData) ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class)).get("list");
						
						
						String thirdRow="科目："+subAuxPd.get("code")+"   "+subAuxPd.get("aliasname")
						+"                     会计期间："+pd.getString("month_begin")+"-"+pd.getString("month_finish");
						resMap.put(accountname+";"+thirdRow, resList);
					}
				}
			}else if("0".equals(method_type)) {
				resList =(List<PageData>) ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class);
				if("cashiermx".equals(service_name)) {
					String thirdRow="账户："+account_name
					+"                     期初余额："+begin_balance;
					resMap.put(" ;"+thirdRow, resList);
				}
			}else {
				resList =(List<PageData>)((PageData) ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class)).get("list");
			}
/*			if("0".equals(method_type)){
				if("getGridData_CustomerSummary".equals(run_method)
						|| "getGridData_SupplierSummary".equals(run_method)) {
					resList =(List<PageData>) ((PageData)ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class)).get("customerList");
				}else {
					resList =(List<PageData>) ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class);
				}
				
			}else{
				List<PageData> allList = new ArrayList<>();
				List<PageData> subOrAuxList =new ArrayList<PageData>();
				//会计科目是按照code排序     辅助核算是按照id排序  所以要分开处理
				if("c_accsubjects".equals(pd.get("table_name")) && "getGridData_DetailedAccount".equals(run_method)){
					subOrAuxList=accountInfoService.findSubList(pd);
					if(subOrAuxList.size()>0){
						for(PageData subAuxPd : subOrAuxList){
							PageData resPd=new PageData();
							subAuxPd.put("database", pd.get("database"));
							String p = "";
							if(pd.get("params") !=null) {
								p=pd.get("params").toString();
							}
							pd.put("params", p.substring(0, p.lastIndexOf(","))+",subjectid:"+subAuxPd.get("id"));
							resList =(List<PageData>)((PageData) ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class)).get("list");
							allList.addAll(resList);
						}
						resList = allList;
					}
				}else {
					resList =(List<PageData>)((PageData) ReflectHelper.callMethod2(service, run_method, new Object[]{pd}, PageData.class)).get("list");
				}
			}*/
			
            String[] headers = headers_str.split(",");
            String[] columns = columns_str.split(",");  
            ObjectExcelView2 erv = new ObjectExcelView2();
			pd.clear();
			pd  = new PageData();
			pd.put("service_name", service_name);
			pd.put("filename", filename);
			pd.put("headers", headers);
			pd.put("columns", columns);
			pd.put("name",title);
			pd.put("resMap", resMap);
			//设置cookie form提交前端判斷成功與否 
			Cookie cookie = new Cookie("FLAG", "true");
	        cookie.setPath("/");
	        cookie.setMaxAge(60 * 10);
	        response.addCookie(cookie);

			mv = new ModelAndView(erv,pd);
		} catch(Exception e){
			logger.error(e.toString(), e);
		}
		return mv;
	}
}
