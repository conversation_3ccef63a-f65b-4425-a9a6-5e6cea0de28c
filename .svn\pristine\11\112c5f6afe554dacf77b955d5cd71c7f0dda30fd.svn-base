<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
</head>
<body style="background: #ebedf3;">

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
	
		<!-- 顶部功能栏 -->
		<div id="northToolDiv" data-options="region:'north'" style="background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4); background: -moz-linear-gradient(top,#fff,#f4f4f4);background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<a class="easyui-linkbutton gl_btns" style="float: left;" href="javascript:void(0)" onclick="$('#filter_pageId').window('open')"></a>
			<p class="refreshbtns" id="refreshbtn" onclick="refresh()" style="display:none"></p>
			<div style="float: right;width: 426px;margin: 0 0 0 100px;">
				<a class="easyui-linkbutton dc_btns" href="javascript:void(0)" onclick="ExportExcel()"></a>
				<a class="easyui-linkbutton dy_btns" href="javascript:void(0)" onclick=""></a>
				<a class="easyui-linkbutton yl_btns" href="javascript:void(0)" onclick=""></a>
				<a class="easyui-linkbutton dypz_btns" href="javascript:void(0)" onclick=""></a>
				<a class="easyui-linkbutton gb_btns" href="javascript:void(0)" onclick="closeIndexTabs({title:'折旧统计表'})"></a>
			</div>
		</div>
		
		<!-- 主面板 -->
		<div style="margin: 54px 0 0 0;border:1px #6f9ec2 solid;border-bottom:0" id="parcel_southToolDiv">
		    <div class="gridlist_top">
		        <p class="gridlist_top_le">
		        	<span>单位：</span>
		        	<input id="accountname" type="text" name="" class="" readonly="readonly" />
		        </p>
		        <p class="gridlist_top_cent">折旧统计表</p>
		        <p class="gridlist_top_ri" style="margin: 0;">
		        	<span>会计期间：</span>
		        	<span class="towsp" id="period"></span>
		        </p>
		    </div>
		    <!-- 数据面板 -->
			<div id="gridlist" data-options="region:'center'" style="width: 100%"></div>
		</div>
		
		<!--报表过滤弹窗 -->
		<div id="filter_pageId" class="easyui-window" title="过滤" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
	    	<div class="bgdivtitfixed" style="border: 0;margin: 0 0 0 0;height: 132px;width: 100%">
		    	<ul class="report_ul">
		    		<li style="margin: 32px 0 0 0;">
		    			<p style="width: 60px;">期间：</p>
	    				<input type='text' id="year" id="year" readonly="readonly" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" value="${year}" onclick="WdatePicker({skin:'whyGreen',startDate:'%y',dateFmt:'yyyy'})" />
						<input type="text" id="month_begin" class="invo_title_year" name="month_begin" style="float: left;margin: 0 3px 0 0;" value="${month}" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM'})" /> 
						<span style="float: left;margin: 6px 3px 0 0;">月&nbsp;&nbsp; 至</span>
						<input type="text" id="month_finish" name="month_finish" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" value="${month}" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM'})" />
						<span style="float: left;margin: 6px 0 0 0;">月</span>
		    		</li>
		    	</ul>
		    </div>	
		    <div style="height:40px;text-align:right;background:#ecf5fa; border-radius: 0 0 5px 5px;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="$('#filter_pageId').window('close');">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn"  onclick="queryData()">确定</a>
		    </div>
		</div>
	</div>

<script type="text/javascript">

	var accinfo = findAccInfo();
	$('#accountname').val(accinfo.compname);

	var width = $(window).width()-76;
	var w1 = width/30;
	var heights = $(window).height();
	$("#parcel_southToolDiv").height(heights-100);
	$("#gridlist").height(heights-100);
	//$("#gridlist").width(w1*10+42);
	var data = JSON.parse('${data}');
	var code = data.code;
	var columns = data.columns;
	//格式化columns
	columnsFormat(columns);
	
	var obj = new Object();
	obj["position"] = '#gridlist';
	obj["url"] = 'report/getGridData';
	obj["columns"] = [columns];
	obj["listDbClickFun"] = function (){};
	obj["fitColumns"] = false;
	obj["showFooter"] = true;
	//生成报表表格
	Grid.list_report(obj);
	
	$('#gridlist').datagrid({
		onLoadSuccess: function (data){
            $('#gridlist').datagrid('statistics'); //合计
		}
	});
	
	//定义变量
	var year, month_begin, month_finish, name, params;
	
	//初始化变量 并赋值
	function initData(){
		year = $("#year").val();
		month_begin = year + '-' + $("#month_begin").val();
		month_finish = year + '-' + $('#month_finish').val();

		var accinfo = findAccInfo();
		var startusedate = accinfo.startusedate;
		var startusedates = startusedate.split('-');
		if(month_begin < startusedate){
			$("#year").val(startusedates[0]);
			$("#month_begin").val(startusedates[1]);
			promptbox('success','最小月份已自动取账套开始使用月份！');
		}
		if(month_begin > month_finish){
			$("#month_finish").val($("#month_begin").val());
		}
		
		year = $("#year").val();
		month_begin = year + '-' + $("#month_begin").val();
		month_finish = year + '-' + $('#month_finish').val();
		
		$('#period').text(year + '年' + $("#month_begin").val() + '月至' + $('#month_finish').val() + '月');
		
		params = 'month_begin:'+month_begin+',month_finish:'+month_finish;
	}
	
	//过滤弹窗 确定
	function filterOk(){
		queryData();
	}
	
	//查询
	function queryData(){
		initData();
		$('#gridlist').datagrid('load',{code:code, params:params});
		$('#filter_pageId').window('close');
	}
	
	//导出
	function ExportExcel(){
		var obj=new Object;
		obj['renderid']='#gridlist';
		obj['title'] = '折旧统计表';
		obj['controllername'] = 'report';
		obj['run_method'] = 'findGridData';
		obj['method_type'] = '1';
		obj['cs'] = '';
		obj['pms'] = {code:code, params:params};
		toExcel(obj);
	}
	
	
</script>

</body>
</html>