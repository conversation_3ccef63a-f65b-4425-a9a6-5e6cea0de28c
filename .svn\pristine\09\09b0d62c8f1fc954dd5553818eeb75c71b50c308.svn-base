package org.newstanding.service.systemset;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.common.utils.excel.XLSXCovertCSVReader;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.financialhandle.buildaccount.GzdxyeService;
import org.newstanding.service.financialhandle.buildaccount.KmyeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

@SuppressWarnings("unchecked")
@Service("initialstoreService")
public class InitialstoreService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private AccountInfoService accountInfoService;
	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			if (!"[]".equals(pd.getString("initialstoremx")) && !"".equals(pd.getString("initialstoremx"))) {
				List<PageData> mxList= new ArrayList<>();
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(pd.getString("initialstoremx"));
				for (Object o : jay) {
					PageData mxpd = new PageData(JsonUtils.strToMap(o.toString()));
					mxList.add(mxpd);
				}
				if(mxList.size()>0) {
					pd.put("addList", mxList);
					dao.save("InitialstoreMapper.save", pd);
				}
			}
				resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.delete("InitialstoreMapper.delete", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			//如果 期初余额为空 或者 期初余额  = 0 或者 空字符串
			if (pd.get("initialstore")==null 
				|| pd.get("initialstore").toString().equals("") 
				|| pd.get("initialstore").toString().equals("0")) {
				
				dao.delete("InitialstoreMapper.delete", pd);
				
				resPd.put("state", "success");
				resPd.put("count", count);
				return resPd;
			}
			
			dao.update("InitialstoreMapper.edit", pd);
			resPd.put("id", pd.get("id"));
			
			Long initialstoreid= Long.parseLong(pd.get("id").toString());
			if (StringUtils.isUpdate(pd.getString("initialstoremx"))) {
				editTempl(initialstoreid, "Initialstore", pd, 0);
				
				pd.put("id", resPd.get("id"));
				dao.update("InitialstoreMapper.after", pd);
			}else if(StringUtils.isDelete(pd.getString("initialstoremx"))){
				dao.delete("InitialstoreMapper.deleteInitialstoremxByInitialstoreid", pd);
			}
			
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData list(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("InitialstoreMapper.datalist", pd);
			resPd.put("state","success");
			resPd.put("list",list);
			
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	/**
	 * 查询期初余额明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findInitialstoremxByInitialstoreid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("InitialstoreMapper.findInitialstoremxByInitialstoreid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	/**
	 * 获取期初账期
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findAccountperiod(PageData pd) throws Exception {
		return (PageData) dao.findForObject("InitialstoreMapper.findAccountperiod", pd);
	}
	

	
	@Override
	public PageData accountAll(PageData pd) throws Exception {
		String estatus =  pd.get("type").toString();
		PageData resPd = new PageData();
		try {
			PageData accInfo = accountInfoService.findAccInfo(pd);
			pd.put("startusedate", accInfo.get("startusedate"));
			List<PageData> list = (List<PageData>)dao.findForList("InitialstoreMapper.findList", pd);
			if ("1".equals(estatus)) {
				if(list.size()>0) {
					pd.put("addList", list);
					//保存到t_costaccounting表中
					dao.save("InitialstoreMapper.saveCostAccounting", pd);
				}
			}else if ("0".equals(estatus)) {
				//撤账控制：如果已有入库单或是出库单则不可以再进行撤账
				List<PageData> rkckList = (List<PageData>)dao.findForList("InitialstoreMapper.findRkckList", pd);
				if(rkckList != null && rkckList.size() > 0 && rkckList.get(0) != null 
						&& Integer.parseInt(rkckList.get(0).get("s_num").toString()) > 0){
					resPd.put("state", "error");
					resPd.put("message", "已经存在采购入库单或者销售出库单，不可以撤帐！");
					return resPd;
				}
				if(list.size()>0) {
					//从表中删除
					dao.save("InitialstoreMapper.deleteCostAccounting", pd);
				}
			}
			//更新estatus状态为1 或者 0
			dao.update("InitialstoreMapper.updateEstatus", pd);
		} catch (Exception e) {
			resPd.put("state", "error");
		}
		
		resPd.put("state", "success");
		return resPd;
	}
	/**
	 * 导入
	 */
	public PageData importExcel(PageData pd, MultipartFile file)throws Exception{
		PageData resPd = new PageData();
		try {
			//将接收到的文件保存成一个tmp临时文件存于项目根目录下
			CommonsMultipartFile cf = (CommonsMultipartFile)file; 
	        DiskFileItem fi = (DiskFileItem)cf.getFileItem(); 
	        File f = fi.getStoreLocation();
	        
        	String type = pd.get("type").toString();			//档案类型
        	String sheet = "期初余额";							//工作表名称
        	Random random = new Random();
        	
        	//解析工作表
        	List<String[]> dataList = XLSXCovertCSVReader.readerExcel(f.getAbsolutePath(), sheet, 2);
        	
        	dataList.remove(0);	//去除标题栏
        	
        	if(dataList.size() != 0 ){
        		List<PageData> list = new ArrayList<PageData>();
        		for (String[] strs : dataList) {
        			PageData p = new PageData();
    				p.put("recordname", strs[0]);
    				p.put("initialstore", strs[1].replaceAll(",", ""));
    				p.put("lx", type);
    				p.put("rowid", random.nextInt(1000000));
    				list.add(p);
    			}
        		resPd.put("rows", list);
        	}else{
        		resPd.put("state", "err");
        		resPd.put("message", "无有效数据");
        		return resPd;
        	}
	        resPd.put("state", "success");
		} catch (Exception e) {
			e.printStackTrace();
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	public PageData checkInitBalanceIsAcc(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			PageData pd1=(PageData) dao.findForObject("InitialstoreMapper.checkInitBalanceIsAcc", pd);
			if(Integer.parseInt(pd1.get("initbalanceisacc").toString()) ==1){
				resPd.put("state", "success");
			}else{
				resPd.put("state", "error");
				resPd.put("message", "期初余额尚未记账,请记账后再进行操作！");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
}
