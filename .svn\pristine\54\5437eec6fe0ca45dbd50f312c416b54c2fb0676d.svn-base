<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="./common_css_js.jspf"%>
</head>
<body>
		<!-- 编辑弹窗 -->
		<div id="edit_pageId" style="width:391px;height:300px;">
			<!-- 单据表单 -->
			<input type="hidden" id="accountingcode" name="accountingcode" value="${accountingcode}">
			<div class="bgdivtitfixed" style="height: 395px;width: 100%; border: 1px #cedae4 solid;">
				<form id="accsubjects_pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="width:370px;height:300px;padding:10px;">
					<input type="hidden" id="id" name="id" value="${pd.id}" />
					<!-- 又发生额时，不能修改辅助核算 -->
					<input type="hidden" id="auxbus1" name="auxbus1" value="${pd.auxbus1}" />
					<!-- 修改时判断，不能修改层级 -->
					<input type="hidden" id="code1" name="code1" value="${pd.code1}" />
					<input type="hidden" id="supercode"  name="supercode" value="${pd.supercode}" />
					<ul class="accsubjects_ul" style="height:371px">
					    <li style="margin: 13px 0 0 0;">
					        <p>科目编码：</p>
					        <input id="code" class="easyui-textbox" type="text" style="height: 25px;" name="code" value="${pd.code}" data-options="onChange:codeChange,required:true,validType:['repeat[\'accsubjects\',\'c_accsubjects\',\'code\',\'pageForm\']']" />
					    </li>
					    <li>
					        <p>科目名称：</p>
					        <input class="easyui-textbox" type="text" name="name" style="height: 25px;" value="${pd.name}" data-options="required:true" />
					    </li>
					    <li>
					        <p>科目类别：</p>
					        <select id="sub_class" class="easyui-combobox" name="sub_class" data-options="value:${(empty pd.sub_class)?0:pd.sub_class},required:true,editable:false,panelHeight:'auto'" style="width:225px;height:25px">
							    <option value="0">资产</option>
							    <option value="1">负债</option>
							    <option value="2">权益</option>
							    <option value="3">成本</option>
							    <option value="4">损益</option>
							</select>
					    </li>
					    <li>
					        <p>余额方向：</p>
					        <select id="sub_direction" class="easyui-combobox" name="sub_direction" data-options="value:${(empty pd.sub_direction)?0:pd.sub_direction},required:true,editable:false,panelHeight:'auto'" style="width:225px;height:25px">
							    <option value="0">借</option>
							    <option value="1">贷</option>
							</select>
					    </li>
					    <li>
					        <i></i>
					        <p class="changemass">辅助信息</p>
					        <em></em>
					    </li>
					    <li>
					        <p>是否日记账：</p>
					        <input id="isdayacc" type="checkbox" class="checkboxbgimg" name="isdayacc" <c:if test="${pd.isdayacc == 1}">checked="checked"</c:if>>
					    </li>
					    <li>
					        <p>现金类科目：</p>
					        <input id="sub_type_cash" type="checkbox" class="checkboxbgimg"  name="sub_type_cash" <c:if test="${pd.sub_type_cash == 1}">checked="checked"</c:if> onclick="cashClick(this)" />
					    </li>
					    <li>
					        <p>银行类科目：</p>
					        <input id="sub_type_bank" type="checkbox" class="checkboxbgimg"  name="sub_type_bank" <c:if test="${pd.sub_type_bank == 1}">checked="checked"</c:if> onclick="bankClick(this)" />
					    </li>
					    <li>
					        <p>辅助核算：</p>
					        <select id="auxbus" class="easyui-combobox" name="auxbus" data-options="value:${(empty pd.auxbus)?0:pd.auxbus},editable:false,panelHeight:'auto'" style="width:225px;height:25px">
					        	<option value="0"></option>
							    <option value="1">客户</option>
							    <option value="2">供应商</option>
							</select>
					    </li>
					    <li>
					        <p>现流量属性：</p>
					        <select id="cashflowatt" class="easyui-combobox" name="cashflowatt" data-options="value:${(empty pd.cashflowatt)?13:pd.cashflowatt},editable:false,panelHeight:'auto'" style="width:225px;height:25px">
							    <option value="13"></option>
							    <option value="0">经营活动-收款</option>
							    <option value="1">经营活动-付款</option>
							    <option value="2">经营活动-薪酬</option>
							    <option value="3">经营活动-税费</option>
							    <option value="4">经营活动-其他</option>
							    <option value="5">投资活动-投资</option>
							    <option value="6">投资活动-收益</option>
							    <option value="7">投资活动-投资资产</option>
							    <option value="8">投资活动-处置资产</option>
							    <option value="9">筹资活动-增资</option>
							    <option value="10">筹资活动-融资</option>
							    <option value="11">筹资活动-利息</option>
							    <option value="12">筹资活动-分配</option>
							</select>
					    </li>
					</ul>
			    </form>
			</div>    
		    <div style="height:35px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;padding:4px 0 0 0;width: 393px;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" style="margin: -1px 13px 0 0;" onclick="accsubjects_clearForm()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" style="margin: -1px 9px 0 0;" onclick="accsubjects_submitForm({code:'accsubjects',type:3,renderid:'#accsubjects_gridlist'})">确定</a>
		    </div>
		</div>
	
<script type="text/javascript">

$(function(){
	if(checkEm($('#accsubjects_pageForm #id').val())){
		$('#accsubjects_pageForm #sub_class').combobox('readonly',false);	
		$('#accsubjects_pageForm #sub_direction').combobox('readonly',false);
		$('#accsubjects_pageForm').form('clear');
	}
});

//如果科目已有发生额，辅助核算不可修改
//2018-01-12 需求修改为  查看凭证中是否被使用
$('#accsubjects_pageForm #auxbus').combobox({
	onClick : function(record){
			if(!checkEm($("#accsubjects_pageForm #id").val())){
				$.ajax({
					url:getUrl('accsubjects/findHaveKmye'),
					data:{id:$('#accsubjects_pageForm #id').val()},
					success : function(data){
						if(data.state=='error'){
							$('#accsubjects_pageForm #auxbus').combobox('setValue',$('#accsubjects_pageForm #auxbus1').val());
							$.messager.alert('提示','<span class="hintsp_e">提示</span>该科目已被凭证使用，辅助核算不可修改！','warnning');
							return;
						}
					}
				})
			}
	}
})

//code change事件
function codeChange(newValue,oldValue){
	//科目编码：不能修改层级（如原本是第一级，不能修改为第二级     原本是第二级不能修改为第一级。）   不能重复
	if(!checkEm($("#accsubjects_pageForm #id").val()) && !checkEm($("#accsubjects_pageForm #code1").val())){
		var val= $('#accsubjects_pageForm #code1').val();
		if(!checkEm(newValue) && newValue.length != val.length){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>不能修改科目层级！','warnning');
			return;
		}
	}
	
	var isValid = $('#accsubjects_pageForm #code').textbox('isValid');
	if (!isValid){
		return;// 如果表单是无效直接返回
	}
	var accountingcode=$("#accountingcode").val();
	var codeArray=accountingcode.split('-');
	var totallength=codeArray.length;
	//取得每一级的长度
	var firstlength=0;var secondlength=0;var thirdlength=0;var fourlength=0;var fivelength=0;
	var firsttotallength=0;var secondtotallength=0;var thirdtotallength=0;var fourtotallength=0;var fivetotallength=0;
	if(totallength >0){
		for(var i=0;i<totallength;i++){
			if(i==0){
				firstlength=Number(codeArray[0]);
				firsttotallength=Number(codeArray[0]);
			}else if(i==1){
				secondlength=Number(codeArray[1]);
				secondtotallength=firsttotallength+Number(codeArray[1]);
			}else if(i==2){
				thirdlength=Number(codeArray[2]);
				thirdtotallength =secondtotallength+Number(codeArray[2]);
			}else if(i==3){
				fourlength=Number(codeArray[3]);
				fourtotallength=thirdtotallength+Number(codeArray[3]);
			}else if(i==4){
				fivelength=Number(codeArray[4]);
				fivetotallength=fourtotallength+Number(codeArray[4]);
			}
		}
		var code=$("#accsubjects_pageForm #code").val();
		if(code.length==firsttotallength || code.length==secondtotallength
				|| code.length==thirdtotallength || code.length==fourtotallength
				|| code.length==fivetotallength){
			//符合规则 
			console.log("符合会计科目规则");
			var ruleslevel=0;
			//判断具体符合哪一级别规则，带出其上级的属性
			if(code.length==firsttotallength){
				ruleslevel=1;
			}else if( code.length==secondtotallength){
				ruleslevel=2;
			}else if( code.length==thirdtotallength){
				ruleslevel=3;
			}else if( code.length==fourtotallength){
				ruleslevel=4;
			}else if( code.length==fivetotallength){
				ruleslevel=5;
			}
			//根据级别找出其上级code
			if(ruleslevel>1){
				var levelLength=codeArray[ruleslevel-1];
				var superCode=code.substring(0,code.length-levelLength);
				//发送请求后台  返回上级科目的信息
				$.ajax({
				    url:getUrl('accsubjects/findByCode'),
				    type:'post',
				    async:false,
				    data: {'code':superCode},
				    dataType:'json',
				    success:function(data){
				    	if(!checkEm(data)){
				    		if(data.state == 'success'){
				    			//如果没有子节点，提示
				    			//您将改变上级科目的层级关系，如果上级科目存在发生额，系统将所有该科目下的账务数据转移至新增的会计科目中，是否确认此操作?
				    			if(data.hasChild=='否'){
				    				$.messager.confirm('确认消息','<span class="hintsp_w">提示</span>您将改变上级科目的层级关系，如果上级科目存在发生额，系统将所有该科目下的账务数据转移至新增的会计科目中，是否确认此操作?', function(r){
    									if (r){
    						    			//设置数据
    						    			$('#accsubjects_pageForm #sub_class').combobox('setValue', data.sub_class);
    						    			$('#accsubjects_pageForm #sub_direction').combobox('setValue', data.sub_direction);
    						    			$('#accsubjects_pageForm #isdayacc').val(data.isdayacc);
    						    			$('#accsubjects_pageForm #sub_type_cash').val(data.sub_type_cash);
    						    			$('#accsubjects_pageForm #sub_type_bank').val(data.sub_type_bank);
    						    			if(Number(data.isdayacc)==1){
    						    				$('#accsubjects_pageForm #isdayacc').prop("checked",true);
    						    			}else{
    						    				$('#accsubjects_pageForm #isdayacc').prop("checked",false);
    						    			}
    						    			if(Number(data.sub_type_cash)==1){
    						    				$('#accsubjects_pageForm #sub_type_cash').prop("checked",true);
    						    			}else{
    						    				$('#accsubjects_pageForm #sub_type_cash').prop("checked",false);
    						    			}
    						    			if(Number(data.sub_type_bank)==1){
    						    				$('#accsubjects_pageForm #sub_type_bank').prop("checked",true);
    						    			}else{
    						    				$('#accsubjects_pageForm #sub_type_bank').prop("checked",false);
    						    			}
    						    			$('#accsubjects_pageForm #sub_class').combobox('readonly');	
    						    			$('#accsubjects_pageForm #sub_direction').combobox('readonly');
    						    			//可编辑
    						    			$('#accsubjects_pageForm #cashflowatt').combobox('setValue', data.cashflowatt);
    						    			
    						    			//将上级编码code放到隐藏字段上  保存时候根据此获取上级id
    						    			$('#accsubjects_pageForm #supercode').val(data.code);
    									}
    								})
				    			}else{
					    			//设置数据
					    			//不可编辑
					    			$('#accsubjects_pageForm #sub_class').combobox('setValue', data.sub_class);
					    			$('#accsubjects_pageForm #sub_direction').combobox('setValue', data.sub_direction);
					    			$('#accsubjects_pageForm #isdayacc').val(data.isdayacc);
					    			$('#accsubjects_pageForm #sub_type_cash').val(data.sub_type_cash);
					    			$('#accsubjects_pageForm #sub_type_bank').val(data.sub_type_bank);
					    			if(Number(data.isdayacc)==1){
					    				$('#accsubjects_pageForm #isdayacc').prop("checked",true);
					    			}else{
					    				$('#accsubjects_pageForm #isdayacc').prop("checked",false);
					    			}
					    			if(Number(data.sub_type_cash)==1){
					    				$('#accsubjects_pageForm #sub_type_cash').prop("checked",true);
					    			}else{
					    				$('#accsubjects_pageForm #sub_type_cash').prop("checked",false);
					    			}
					    			if(Number(data.sub_type_bank)==1){
					    				$('#accsubjects_pageForm #sub_type_bank').prop("checked",true);
					    			}else{
					    				$('#accsubjects_pageForm #sub_type_bank').prop("checked",false);
					    			}
					    			$('#accsubjects_pageForm #sub_class').combobox('readonly');
					    			$('#accsubjects_pageForm #sub_direction').combobox('readonly');
					    			//可编辑
					    			$('#accsubjects_pageForm #cashflowatt').combobox('setValue', data.cashflowatt);
					    			
					    			//将上级编码code放到隐藏字段上  保存时候根据此获取上级id
					    			$('#accsubjects_pageForm #supercode').val(data.code);
				    			}
				    			
							}else if(data.state == 'error'){
								$.messager.alert('操作失败','<span class="hintsp_e">提示</span>获取上级科目信息失败！','error');
							}
				    	}else{
				    		$.messager.alert('操作失败','<span class="hintsp_e">提示</span>没有找到对应的上级科目信息！','error');
				    	}
		    			
				    }
				    
			    });
			}
		}else{
			//不符合规则
			console.log("不符合会计科目规则");
			$.messager.alert('提示消息',"<span class='hintsp_w'>提示</span>输入的科目编码不符合设定的科目编码规则！",'warning');
		}
	}else{
		$.messager.alert('提示消息',"<span class='hintsp_w'>提示</span>尚未设置科目编码规则！",'warning');
		$('#accsubjects_pageForm #code').textbox('clear');
	}
}
	
	//现金科目点击事件
	function cashClick(obj){
		if($("#accsubjects_pageForm #sub_type_cash")[0].checked){
			 //判断银行类科目是否勾选
			 if($('#accsubjects_pageForm #sub_type_bank').is(':checked')){
				 $('#accsubjects_pageForm #sub_type_bank').prop("checked",false);
			 }
		}
	}
	
	function bankClick(obj){
		if($("#accsubjects_pageForm #sub_type_bank")[0].checked){
			 //判断现金类科目是否勾选
			if($('#accsubjects_pageForm #sub_type_cash').is(':checked')){
				$('#accsubjects_pageForm #sub_type_cash').prop("checked",false);
			}
		}
	}
	
	//通用表单提交前触发方法
	function accsubjectsbeforeSubmitFormFun(){
		if(!checkEm($("#accsubjects_pageForm #id").val()) && !checkEm($("#accsubjects_pageForm #code1").val())){
			var val= $('#accsubjects_pageForm #code1').val();
			var code=$('#accsubjects_pageForm #code').textbox('getValue');
			if(!checkEm(code) && code.length != val.length){
				$.messager.alert('提示','<span class="hintsp_e">提示</span>不能修改科目层级！','warnning');
				return;
			}
		}
		return true;
	}
	
	function accsubjects_clearForm(){
		$('#accsubjects_edit_pageId').window('close');
	}
	
	function accsubjects_submitForm(obj){
		if(checkEm(obj)){return;}
		var id = $('#accsubjects_pageForm #id').val(),method;
		if(id == ''){
			method = 'save'
		}else{
			method = 'edit.do?id='+id
		}
		//有checkbox时，统一处理checkbox的赋值
		$('#accsubjects_pageForm input:checkbox').each(function() {
			if ($(this)[0].checked) {
				$(this).val(1);
			}else{
				$(this).val(0);
			}
		});
		$.messager.progress();	// 显示进度条
		$('#accsubjects_pageForm').form("submit", {"url": getUrl(obj.code + "/" + method),
					onSubmit: function(param){
						var isValid = $(this).form('enableValidation').form('validate');
						if (!isValid){
							$.messager.progress('close');// 如果表单是无效的则隐藏进度条
							return false;
						}
						//需在页面定义此方法
						isValid=accsubjectsbeforeSubmitFormFun();
						return isValid;	// 返回false终止表单提交
					},
					success: function(data){
						//后台返回数据处理逻辑
						if(!checkEm(data)){
							data = JSON.parse(data);
							if(data.state == 'success'){
								/* $.messager.show({
				    				title:'操作成功',
				    				msg:'操作成功',
				    				showType:'show',
				    				timeout:2000,
				    			}); */
								promptbox('success','操作成功！');
								if(obj.type==3){
									$(obj.renderid).tabs('select', Number(data.sub_class));
									$('#accsubjects_accTab_'+data.sub_class).tree('reload');
								}else{
									var tab = $(obj.renderid).tabs('getSelected');
									var index = $(obj.renderid).tabs('getTabIndex',tab);
									$('#accsubjects_accTab_'+index).tree('reload');
								}
								
								$('#accsubjects_edit_pageId').window('close');
							}else if(data.state == 'error'){
								$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败','error');
							}
						}
						$.messager.progress('close');// 如果提交成功则隐藏进度条
					}
			});
	}
	
</script>

</body>
</html>