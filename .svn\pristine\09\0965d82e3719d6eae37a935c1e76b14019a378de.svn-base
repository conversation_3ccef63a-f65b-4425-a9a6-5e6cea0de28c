<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="FixedAssetsCardMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.t_fixedassetscard(
			code,
			assetstypeid,
			assetstype_name,
			name,
			entrytime,
			ori_value,
			periods,
			dep_method,
			salvage,
			mon_dep_amount,
			dispose_type,
			fixed_id,
			dep_cum_id,
			dep_cost_id,
			dep_ed_periods,
			dep_ed,
			net_value,
			<if test="source != null and source != ''">
			source,
			sourceid,
			</if>
			tranmemo,
			createby,createtime
		) values (
			#{code},
			#{assetstypeid},
			#{assetstype_name},
			#{name},
			#{entrytime},
			#{ori_value},
			#{periods},
			#{dep_method},
			#{salvage},
			#{mon_dep_amount},
			#{dispose_type},
			#{fixed_id},
			#{dep_cum_id},
			#{dep_cost_id},
			#{dep_ed_periods},
			#{dep_ed},
			#{net_value},
			<if test="source != null and source != ''">
			#{source},
			#{sourceid},
			</if>
			#{tranmemo},
			#{createby},now()
		)
	</insert>
	<update id="saveAfter" parameterType="pd">
		update   
			  ${ database }.t_fixedassetscard a,
			  ${ database }.c_assetstype t
		set 
			  a.assetstypeid = t.id
		where 
			 a.assetstype_name = t.name 
			 and a.id = #{id}
	</update>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.t_fixedassetscard where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.t_fixedassetscard
		set 
			code = #{code},
			assetstypeid = #{assetstypeid},
			assetstype_name = #{assetstype_name},
			name = #{name},
			entrytime = #{entrytime},
			ori_value = #{ori_value},
			periods = #{periods},
			dep_method = #{dep_method},
			salvage = #{salvage},
			mon_dep_amount = #{mon_dep_amount},
			dispose_type = #{dispose_type},
			fixed_id = #{fixed_id},
			dep_cum_id = #{dep_cum_id},
			dep_cost_id = #{dep_cost_id},
			dep_ed_periods = #{dep_ed_periods},
			dep_ed = #{dep_ed},
			net_value = #{net_value},
			modifyby = #{modifyby},
			modifytime = now(),
			tranmemo = #{tranmemo},
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
			a.code,
			a.assetstypeid,
			type.name as assetstype_name,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.mon_dep_amount mon_dep_amount2,
			a.dispose_type,
			count(zjmx.id) dep_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			<!-- (a.ori_value - a.dep_ed) net_value, -->
			a.net_value,
			a.isaccount,
			a.source,
			a.estatus,
			a.closestatus,
			a.version,
			a.tranmemo,
			a.id
		from 
			${ database }.t_fixedassetscard a
			left join ${ database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ database }.t_fixedassetsdepreciationmx zjmx on a.id = zjmx.cardid
			left join ${ database }.c_assetstype type on a.assetstypeid = type.id
		where
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.code,
			a.assetstypeid,
			type.name as assetstype_name,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.mon_dep_amount mon_dep_amount2,
			a.dispose_type,
			count(zjmx.id) dep_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			<!-- (a.ori_value - a.dep_ed) net_value, -->
			a.net_value,
			a.isaccount,
			a.source,
			a.estatus,
			a.closestatus,
			a.version,
			a.tranmemo,
			a.id
		from 
			${ pd.database }.t_fixedassetscard a
			left join ${ pd.database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ pd.database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ pd.database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ pd.database }.t_fixedassetsdepreciationmx zjmx on a.id = zjmx.cardid
			left join ${ pd.database }.c_assetstype type on a.assetstypeid = type.id
		where
			a.closestatus = 0
			<if test="pd.dispose_type != null and pd.dispose_type !=''">
				and a.dispose_type = #{pd.dispose_type} 
			</if>
			<if test="pd.q != null and pd.q !=''">
				and (a.code like '%${ pd.q }%' or a.name like '%${ pd.q }%')
			</if>
		group by a.id
	</select>
	
	<select id="listAll" parameterType="pd" resultType="pd">
		select
			a.code,
			a.assetstypeid,
			type.name as assetstype_name,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.mon_dep_amount mon_dep_amount2,
			a.dispose_type,
			count(zjmx.id) dep_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			a.net_value,
			a.isaccount,
			a.source,
			a.estatus,
			a.closestatus,
			a.version,
			a.tranmemo,
			a.id
		from 
			${ database }.t_fixedassetscard a
			left join ${ database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ database }.t_fixedassetsdepreciationmx zjmx on a.id = zjmx.cardid
			left join ${ database }.c_assetstype type on a.assetstypeid = type.id
		where
			a.closestatus = 0
			<if test="dispose_type != null and dispose_type !=''">
				and a.dispose_type = #{dispose_type} 
			</if>
		group by a.id
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.code,
			a.assetstypeid,
			type.name as assetstype_name,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.mon_dep_amount mon_dep_amount2,
			a.dispose_type,
			count(zjmx.id) dep_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			a.net_value,
			a.isaccount,
			a.estatus,
			a.closestatus,
			a.version,
			a.tranmemo,
			a.id
		from 
			${ pd.database }.t_fixedassetscard a
			left join ${ pd.database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ pd.database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ pd.database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ pd.database }.t_fixedassetsdepreciationmx zjmx on a.id = zjmx.cardid
			left join ${ pd.database }.c_assetstype type on a.assetstypeid = type.id
		group by a.id
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	</select>
	
	<!-- 查询期初余额 -->
	<select id="findIntialbalanceYe" parameterType="pd" resultType="pd">
		select 
			b.id,
			a.fixed_id as accsubjectsid,
			ifnull(b.initialbalance,0)-ifnull(b.fixedasset_val,0)+ifnull(sum(a.ori_value),0) as initialbalance,
			ifnull(sum(a.ori_value),0) as fixedasset_val,
			0 isEdit,'' auxbus
		from ${ database }.t_fixedassetscard as a
		left join ${ database }.t_initialbalance as b on b.accsubjectsid = a.fixed_id
		where source = 'initialbalance' GROUP BY fixed_id
		union all
		select 
			b.id,
			dep_cum_id as accsubjectsid,
			ifnull(b.initialbalance,0)-ifnull(b.fixedasset_val,0)+ifnull(sum(dep_ed),0) as initialbalance,
			ifnull(sum(dep_ed),0) as fixedasset_val,
			0 isEdit,'' auxbus
		from ${ database }.t_fixedassetscard as a
		left join ${ database }.t_initialbalance as b on b.accsubjectsid = a.dep_cum_id
		where source = 'initialbalance' GROUP BY dep_cum_id
	</select>
	
	<!-- 查询删除固定资产卡片期初余额 -->
	<select id="findDeletefixedassetscardIntialbalanceYemx" parameterType="pd" resultType="pd">
		select 
			b.id,
			ifnull(b.initialbalance,0)-ifnull(a.ori_value,0) initialbalance,
			ifnull(b.fixedasset_val,0)-ifnull(a.ori_value,0) fixedasset_val 
		from ${ database }.t_fixedassetscard as a
		left join ${ database }.t_initialbalance as b on b.accsubjectsid = a.fixed_id where a.id = #{id}
		union
		select 
			b.id,
			ifnull(b.initialbalance,0)-IFNULL(a.dep_ed,0) initialbalance,
			ifnull(b.fixedasset_val,0)-IFNULL(a.dep_ed,0) fixedasset_val 
		from ${ database }.t_fixedassetscard as a
		left join ${ database }.t_initialbalance as b on b.accsubjectsid = a.dep_cum_id where a.id = #{id}
	</select>
	
	<!-- 查询固定资产是否做了折旧 -->
	<select id="findDepreciation" parameterType="pd" resultType="pd">
		select
			count(a.id) dep_type
		from 
			${ database }.t_fixedassetsdepreciationmx a 
		where
			a.cardid = #{ id }
	</select>
	
</mapper>