package org.newstanding.controller.systemset;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.systemset.InitialstoreService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;
/**
 * deptdoc
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "initialstore")
public class InitialstoreController extends BaseController {
	@Resource(name = "initialstoreService")
	private InitialstoreService initialstoreService;

	/**
	 * 新增
	 * 
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/save")
	@ResponseBody
	public PageData save() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return initialstoreService.save(pd);
	}

	/**
	 * 删除
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/delete")
	@ResponseBody
	public Object delete() throws Exception {
		PageData pd = this.getPageData();
		return initialstoreService.delete(pd);
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit")
	@ResponseBody
	public Object edit() throws Exception {
		PageData pd = this.getPageData();
		return initialstoreService.edit(pd);
	}

	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list() throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData =  initialstoreService.list(pd);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", varList.size());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return resultMap;
	}
	

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		mv.setViewName("system/systemset/initialstore");
		PageData pd = this.getPageData();
		mv.addObject("pd",pd);
		return mv;
	}

	/**
	 * 验证重复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkRepeatByParam")
	public boolean checkRepeatByParam() throws Exception {
		PageData pd = this.getPageData();
		if( initialstoreService.checkRepeatByParam(pd) ){
			return false;
		}
		return true;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "t_initialstore");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = initialstoreService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要关闭的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 批量记账
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/accountAll")
	public Object accountAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("accountby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "t_initialstore");
		pd = initialstoreService.accountAll(pd);
		return pd;
	}
	

	/**
	 * 导入
	 * @param file
	 * @param templet
	 * @return
	 */
	@RequestMapping(value="/importExcel")
	@ResponseBody
	public Object importExcel(@RequestParam(value="file_name") MultipartFile file, @RequestParam(value="type") String type) {
		PageData pd = this.getPageData();
		try {
			pd.put("code", "purchaseinvoice");
			pd.put("type", type);
			pd = initialstoreService.importExcel(pd,file);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return pd;
	}
	
}




