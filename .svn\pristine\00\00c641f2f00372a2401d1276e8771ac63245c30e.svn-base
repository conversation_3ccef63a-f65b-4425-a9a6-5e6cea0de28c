package org.newstanding.common.utils;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


public class SendsmsUtil {
	
	private static String appKey="57638a88fa9da025169895d3b2709067";
	
	private static String appSecret="1269fdc60f05";
	
	private static Random random = new Random();
	
	/**
	 * 发送验证码
	 * @param mobile 目标手机号
	 * @param codeLen 验证码长度，范围4～10，默认为4
	 * @param templateid 模板编号(如不指定则使用配置的默认模版)
	 * @param deviceId 目标设备号，可选参数
	 * @return msg  此次发送的sendid
	 * 		   obj  此次发送的验证码
	 * 			
	 * @throws Exception
	 */
	public static Map Sendcode(String mobile,String codeLen,String templateid,String deviceId) throws Exception {
		DefaultHttpClient httpClient = new DefaultHttpClient();
        String url = "https://api.netease.im/sms/sendcode.action";
        HttpPost httpPost = new HttpPost(url);

        String nonce =  random.nextInt(1000000)+"";
        String curTime = String.valueOf((new Date()).getTime() / 1000L);
        String checkSum = CheckSumBuilder.getCheckSum(appSecret, nonce ,curTime);//参考 计算CheckSum的java代码

        // 设置请求的header
        httpPost.addHeader("AppKey", appKey);
        httpPost.addHeader("Nonce", nonce);
        httpPost.addHeader("CurTime", curTime);
        httpPost.addHeader("CheckSum", checkSum);
        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求的参数
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        nvps.add(new BasicNameValuePair("mobile", mobile));
        //验证码长度
        if(codeLen != null && !codeLen.equals("")){
        	nvps.add(new BasicNameValuePair("codeLen", codeLen));
        }
        //模版编号
        if(templateid != null && !templateid.equals("")){
        	nvps.add(new BasicNameValuePair("templateid", templateid));
        }
        //目标设备号
        if(deviceId != null && !deviceId.equals("")){
        	nvps.add(new BasicNameValuePair("deviceId", deviceId));
        }
        httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));

        // 执行请求
        HttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String msg=EntityUtils.toString(entity, "utf-8");
        Map map = JsonUtils.strToMap(msg);
        return map;
	}
	
	
	/**
	 * 校验验证码
	 * @param mobile 目标手机号
	 * @param code 验证码
	 * @return
	 * 		code 状态码 -200(表示成功)
	 * @throws Exception
	 */
	public static Map Checkcode(String mobile,String code) throws Exception {
		DefaultHttpClient httpClient = new DefaultHttpClient();
        String url = "https://api.netease.im/sms/verifycode.action";
        HttpPost httpPost = new HttpPost(url);

        String nonce =  random.nextInt(1000000)+"";
        String curTime = String.valueOf((new Date()).getTime() / 1000L);
        String checkSum = CheckSumBuilder.getCheckSum(appSecret, nonce ,curTime);//参考 计算CheckSum的java代码

        // 设置请求的header
        httpPost.addHeader("AppKey", appKey);
        httpPost.addHeader("Nonce", nonce);
        httpPost.addHeader("CurTime", curTime);
        httpPost.addHeader("CheckSum", checkSum);
        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求的参数
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        nvps.add(new BasicNameValuePair("mobile", mobile));
        //验证码长度
        nvps.add(new BasicNameValuePair("code", code));
        httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));

        // 执行请求
        HttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String msg=EntityUtils.toString(entity, "utf-8");
        Map map = JsonUtils.strToMap(msg);
        return map;
	}
	
	
	/**
	 * 发送通知类和运营类短信
	 * @param mobile 接收者号码列表，JSONArray格式,如["186xxxxxxxx","186xxxxxxxx"]，限制接收者号码个数最多为100个
	 * @param templateid 模板编号(由客户顾问配置之后告知开发者)
	 * @param params 短信参数列表，用于依次填充模板，JSONArray格式，每个变量长度不能超过30字，
	 * 					如["xxx","yyy"];对于不包含变量的模板，不填此参数表示模板即短信全文内容
	 * @return
	 * 			obj 此次发送的sendid(long),用于查询发送结果
	 * @throws Exception
	 */
	public static Map Sendmsg(String mobile,String templateid,String params) throws Exception {
		DefaultHttpClient httpClient = new DefaultHttpClient();
        String url = "https://api.netease.im/sms/sendtemplate.action";
        HttpPost httpPost = new HttpPost(url);

        String nonce = random.nextInt(1000000)+"";
        String curTime = String.valueOf((new Date()).getTime() / 1000L);
        String checkSum = CheckSumBuilder.getCheckSum(appSecret, nonce ,curTime);//参考 计算CheckSum的java代码

        // 设置请求的header
        httpPost.addHeader("AppKey", appKey);
        httpPost.addHeader("Nonce", nonce);
        httpPost.addHeader("CurTime", curTime);
        httpPost.addHeader("CheckSum", checkSum);
        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求的参数
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        nvps.add(new BasicNameValuePair("mobile", mobile));
        //模板编号
        nvps.add(new BasicNameValuePair("templateid", templateid));
        //短信参数列表
        nvps.add(new BasicNameValuePair("params", params));
        httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));

        // 执行请求
        HttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String msg=EntityUtils.toString(entity, "utf-8");
        Map map = JsonUtils.strToMap(msg);
        return map;
	}
	
	/**
	 * 发送通知类和运营类短信
	 * @param sendid 发送短信的编号sendid
	 * @return
	 * 			obj中返回JSONArray,格式如下(其中status取值:0-未发送,1-发送成功,2-发送失败,3-反垃圾)：
	 *       {
			  "code": 200,
			  "obj": [
			    {
			      "status": 1,
			      "mobile": "13812341234",
			      "updatetime": 1471234567812
			    }
			  ]
			  }
	 * @throws Exception
	 */
	public static Map Findmsg(String sendid) throws Exception {
		DefaultHttpClient httpClient = new DefaultHttpClient();
        String url = "https://api.netease.im/sms/querystatus.action";
        HttpPost httpPost = new HttpPost(url);

        String nonce = random.nextInt(1000000)+"";
        String curTime = String.valueOf((new Date()).getTime() / 1000L);
        String checkSum = CheckSumBuilder.getCheckSum(appSecret, nonce ,curTime);//参考 计算CheckSum的java代码

        // 设置请求的header
        httpPost.addHeader("AppKey", appKey);
        httpPost.addHeader("Nonce", nonce);
        httpPost.addHeader("CurTime", curTime);
        httpPost.addHeader("CheckSum", checkSum);
        httpPost.addHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求的参数
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        nvps.add(new BasicNameValuePair("sendid", sendid));
        httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));

        // 执行请求
        HttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String msg=EntityUtils.toString(entity, "utf-8");
        Map map = JsonUtils.strToMap(msg);
        return map;
	}
	
}
