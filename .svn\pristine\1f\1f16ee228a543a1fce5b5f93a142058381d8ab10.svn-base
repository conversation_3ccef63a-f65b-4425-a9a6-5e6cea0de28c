package org.newstanding.common.utils.excel;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;



import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.FileUtil;
import org.newstanding.common.utils.Tools;
import org.springframework.web.servlet.view.document.AbstractExcelView;


/**
* 导入到EXCEL
* 类名称：ObjectExcelView.java
* 类描述： 
* <AUTHOR>
* 作者单位： 
* 联系方式：
* @version 1.0
 */

@SuppressWarnings({"unchecked" })
public class ObjectExcelView2 extends AbstractExcelView{

	private String[] headers,columns = null; 
	private String name;
	
	public String[] getHeaders() {
		return headers;
	}

	public void setHeaders(String[] headers) {
		this.headers = headers;
	}

	public String[] getColumns() {
		return columns;
	}

	public void setColumns(String[] columns) {
		this.columns = columns;
	}

	@Override
	protected void buildExcelDocument(Map<String, Object> model,
			HSSFWorkbook workbook, HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		
		Date date = new Date();
		String aa = Tools.date2Str(date, "yyyyMMddHHmmss"),servername="";
		String filename="";
		if (model.get("filename")!=null) {
			filename = model.get("filename").toString();
		}
		if (model.get("service_name")!=null) {
			servername = model.get("service_name").toString();
		}
		if (model.get("headers")!=null) {
			headers = (String[]) model.get("headers");
		}
		if (model.get("columns")!=null) {
			columns = (String[]) model.get("columns");
		}
		if (model.get("name")!=null) {
			name =  (String) model.get("name");
		}
		
		boolean isTp = false;
		if (model.get("isTp")!= null) {
			isTp =  (Boolean) model.get("isTp");
		}
		
		String img_str = "";
		if (model.get("img_str")!= null) {
			img_str = (String) model.get("img_str");
		}
		
		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition", "attachment;filename="+aa+".xls");
		Map<String,Object> map = (Map<String,Object>) model.get("resMap");
		String path1 = this.getClass().getClassLoader().getResource("/").getPath();
		String path = path1.substring(0,path1.indexOf("/WEB-INF"));
		FileUtil.createDir(path +"/WEB-INF/jsp/report/"+servername+"/list/");
		String output = path +"/WEB-INF/jsp/report/"+servername+"/list/"+filename+".xls";
		
		ExportToExcelUtil2<PageData> excelUtil = new ExportToExcelUtil2<PageData>();
        excelUtil.setTitle(name);
        excelUtil.setTp(isTp);
        excelUtil.setImg_str(img_str);
        excelUtil.exportExcel( headers, columns, map,output,null, null, "");
		
		FileUtil.outFile(new FileInputStream(output), response);
	}

}
