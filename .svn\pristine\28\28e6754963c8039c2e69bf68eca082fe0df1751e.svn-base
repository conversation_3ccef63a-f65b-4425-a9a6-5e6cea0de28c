package org.newstanding.controller.financialhandle;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.financialhandle.MonthendcloseService;
import org.newstanding.service.invoicing.PurchaseInvoiceService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
/**
 * deptdoc
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "monthendclose")
public class MonthendcloseController extends BaseController {
	@Resource(name = "monthendcloseService")
	private MonthendcloseService monthendcloseService;
	@Resource(name = "purchaseInvoiceService")
	private PurchaseInvoiceService purchaseInvoiceService;
	/**
	 * 新增
	 * 
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/save")
	@ResponseBody
	public PageData save() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return monthendcloseService.save(pd);
	}

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		PageData pd=this.getPageData();
		String accountperiod = monthendcloseService.getAccountperiod(pd);
		mv.addObject("year", accountperiod.split("-")[0]);
		mv.addObject("month", accountperiod.split("-")[1]);
		mv.setViewName("system/financialhandle/monthendclose");
		return mv;
	}
	
	/**
	 * 去反结账列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goUncloseList")
	public ModelAndView goUncloseList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		PageData pd=this.getPageData();
		String accountperiod = monthendcloseService.getAccountperiod(pd);
		String accountperiod1= DateUtil.addMonth(accountperiod, -1);
		mv.addObject("year", accountperiod1.split("-")[0]);
		mv.addObject("month", accountperiod1.split("-")[1]);
		mv.setViewName("system/financialhandle/monthendunclose");
		return mv;
	}
	/**
	 * 月末结账
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/closeStep")
	@ResponseBody
	public PageData closeStep() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return monthendcloseService.saveStep(pd);
	}
	
	/**
	 * 验证采购发票
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/checkInvoice")
	@ResponseBody
	public PageData checkInvoice() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return monthendcloseService.checkPurchaseInvoice(pd);
	}
	
	/**
	 * 月末结账
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/uncloseStep")
	@ResponseBody
	public PageData uncloseStep() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return monthendcloseService.editStep(pd);
	}
	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "c_monthendclose");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = monthendcloseService.deleteAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "c_monthendclose");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = monthendcloseService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要关闭的数据,请检查！");
		}
		return pd;
	}
	/**
	 * 批量记账
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/accountAll")
	public Object accountAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("accountby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "t_fillvouchers");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = monthendcloseService.accountAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要过账的数据,请检查！");
		}
		return pd;
	}
	/**
	 * 搜索 导出 查询 部门列表
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = monthendcloseService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	@ResponseBody
	@RequestMapping(value = "/checkUnAccountVouchers")
	public PageData checkUnAccountVouchers() throws Exception {
			PageData pd = this.getPageData();
			List<PageData> list = monthendcloseService.checkUnAccountVouchers(pd);
			if(list.size()>0){
				pd.put("state", "error");
				pd.put("message", "本月存在未过账凭证，请过账后重新结转损益。");
			}else{
				pd.put("state", "success");
			}
			return pd;
		}
}




