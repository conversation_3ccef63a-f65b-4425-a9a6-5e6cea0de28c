<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SupplierMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_supplier(
			name,
			createby,createtime
		) values (
			#{name},
			#{createby},now()
		)
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_supplier where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_supplier
		set 
			name = #{name},
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_supplier as a
		where 
			a.id = #{ id }
	</select>
	
	<!-- 通过NAME获取数据 -->
	<select id="findByName" parameterType="pd" resultType="pd">
		select 
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_supplier as a
		where 
			a.name = #{ name }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_supplier a
		<where>
			<choose>
				<when test="pd.isclose !=null and pd.isclose !='' and pd.isclose==1">
				</when>
				<otherwise>
				and a.closestatus=0
				</otherwise>
			</choose>
			
			<if test="pd.q != null and pd.q !=''">
				and (a.name like '%${ pd.q }%')
			</if>
		</where>
		order by a.id+0 asc
	</select>
	
	<select id="assistAccountlistPage" parameterType="page" resultType="pd">
		select
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			<!-- (ifnull(b.debitmoney,0)-ifnull(b.creditmoney,0)) as balance, -->
			case when c.sub_direction = '0' then ifnull(sum(b.debitmoney),0)-ifnull(sum(b.creditmoney),0)
			 else ifnull(sum(b.creditmoney),0)-ifnull(sum(b.debitmoney),0) end as balance,
			a.id
		from 
			${ pd.database }.c_supplier as a 
			left join ${ pd.database }.i_gzdxye as b on a.id=b.assistid and (b.assisttype='供应商' or b.assisttype=2)
			left join  ${ pd.database }.c_accsubjects as c on b.subjectid=c.id
		where 
			a.closestatus=0 <!-- and b.assisttype=2 --> <!-- and b.subjectid=(select id from ${ pd.database }.c_accsubjects where flag='2202') -->
			<if test="pd.q != null and pd.q !=''">
				and (a.name like '%${ pd.q }%')
			</if>
			group by a.id
	</select>
	
	<!-- 列表(全部) -->
	<select id="listAll" parameterType="pd" resultType="pd">
		select
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_supplier a
	</select>

	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.c_supplier a
	</select>
	
	<select id="checkDelete" parameterType="pd" resultType="pd">
		select sum(t.num) s_num from (
			<!-- 凭证 -->
			select count(0) num from ${ database }.t_fillvouchersmx as a 
			left join ${ database }.c_accsubjects b on a.subjectid=b.id 
			where b.auxbus=2 and a.assistaccountid in (${DATA_IDS}) 
			union
			<!-- 采购发票 -->
			select count(0) num from ${ database }.t_purchaseinvoice 
			where supplier_id in (${DATA_IDS}) 
			union
			<!-- 辅助核算明细账 -->
			select count(0) num from ${ database }.i_gzdxyemx 
			where assisttype=2 and assistid in (${DATA_IDS}) 
			<!-- union -->
			<!-- 期初余额单  TODO-->
			
		) t
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkName" parameterType="pd" resultType="pd">
		select count(*) num from ${ database }.c_supplier where name = #{name}
	</select>
	
</mapper>