<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<script type="text/javascript" src="static/plugin/jquery-easyui-1.5.3/datagrid-cellediting.js"></script>
</head>
<body>
	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div id="gridlist" data-options="region:'center'" style="border-left: 0px solid rgb(206, 218, 228);width:404px;height:305px">
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true">
				<input type="hidden" id="id" name="id">
				<div id="accinfo_tab" class="easyui-tabs" style="width:404px;height:305px;margin:10px 14px 0 12px;background:#fff;">   
					<div title="公司资料">
					    <ul class="accountin_ul">
					        <li style="margin: 15px 0 0 0">
					            <p>公司名称：</p>
					            <input class="easyui-textbox" type="text" name="compname"  />
					        </li>
					        <li>
					            <p>税号：</p>
					            <input class="easyui-textbox" type="text" name="taxnum" />
					        </li>
					        <li>
					            <p>开户行：</p>
					            <input class="easyui-textbox" type="text" name="accbank"  />
					        </li>
					        <li>
					            <p>银行账号：</p>
					            <input class="easyui-textbox" type="text" name="bankaccount" />
					        </li>
					        <li>
					            <p>公司地址：</p>
					            <input class="easyui-textbox" type="text" name="compaddress" />
					        </li>
					        <li>
					            <p>联系电话：</p>
					            <input class="easyui-textbox" type="text" name="telno" />
					        </li>
					    </ul>
					</div>
					<div title="账套信息">
					    <ul class="accountin_ul">
					        <li>
					            <p>账套编号：</p>
					            <input class="easyui-textbox" type="text" name="accountcode" readonly="readonly" />
					        </li>
					        <li>
					            <p>账套名称：</p>
					            <input class="easyui-textbox" type="text" name="accountname" readonly="readonly" />
					        </li>
					        <li>
					            <p>会计制度：</p>
					            <input class="easyui-textbox" type="text" name="accountingsys" readonly="readonly" />
					        </li>
					        <li>
					            <p>企业性质：</p>
					            <select class="easyui-combobox" name="comptype" data-options="editable:false,panelHeight:'auto'" style="width:258px">
								    <option value="0">一般纳税人</option>
								    <option value="1">小规模纳税人</option>
								</select>
					        </li>
					        <li>
					            <p>科目编码规则：</p>
					            <input class="easyui-textbox" type="text" name="accountingcode" readonly="readonly"/>
					        </li>
					        <li>
					            <p>账套开始使用：</p>
					            <input class="easyui-textbox" type="text" name="startusedate" readonly="readonly" />
					        </li>
					        <li>
					            <p>当前账套期间：</p>
					            <input class="easyui-textbox" type="text" name="periodofaccount" readonly="readonly" />
					        </li>
					    </ul>
					</div>
					<div title="账套设置">
						<ul  class="accountin_ul">
							<li>
								<p style="width:200px;text-align:left;">录入凭证是否自动取断号</p>
								<input type="checkbox" name="autopickno" class="checkboxbgimg" style="float:left;margin: 22px 12px 0 34px;" value="true" />
							</li>
							<li>
							    <p style="width:200px;text-align:left">凭证制单、审核是否允许为同一人</p>
								<input type="checkbox" name="sameperson" class="checkboxbgimg" style="float:left;margin: 22px 12px 0 34px;" value="true" />
							</li>
						</ul>
					</div>
					<div title="其他">
						<ul  class="accountin_ul">
							<li>
							    <p >所得税税率：</p>
								<input class="easyui-textbox" type="text" name="incometaxrate" /> %
							</li>
							<li style="position: relative;">
							    <p>损益结转科目：</p>
							    <input type="hidden" id= "plsubject_id" name="plsubject_id" />
								<input class="easyui-textbox" type="text" id= "plsubject_name" style=" padding:0 24px 0 2px;width:259px" name="plsubject_name" />
								<div class="kjkm_btn" style=" right: 56px;top: 24px;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;plsubject_id:id,plsubject_name:name')"></div>
							</li>
							
							<li>
					            <p>成本计算方法：</p>
					            <select class="easyui-combobox" name="calmethod" data-options="editable:false,panelHeight:'auto'" style="width:258px">
								    <option value="0">全月加权平均</option>
								</select>
					        </li>
						</ul>
					</div>
				</div>
				<div style="text-align:center;background:#fff;width: 404px;height: 41px; margin: 0 0 0 12px;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton  cancel-btn qxNew_btn" onclick="cancelAccInfoForm()">取消</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton  sure-btn sureNew_btn" onclick="submitForm({code:'accountinfo',type:4})">确定</a>
			    </div>
			</form>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
		
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('accountinfo')">关闭</a>
		</div>
		
	</div>

<script type="text/javascript">
	debugger;
	var version_ = getUrlPar('version_');
	$('#pageForm').form('load', getUrl('accountinfo/findAccInfo'));
	function cancelAccInfoForm(){
		$('#pageForm').form('load', getUrl('accountinfo/findAccInfo'));
	}
	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){return true;}
	//通用保存后触发方法
	function afterSaveFun(id){}
	
</script>

</body>
</html>