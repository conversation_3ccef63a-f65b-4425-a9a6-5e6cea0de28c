<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/fillvouchers.css">


<style type="text/css">
 .pageForm_topul li{position:relative}
 .pageForm_topul li .textbox{width:150px !important}
 #make_date{width:145px !important}
 .textbox #_easyui_textbox_input1{width:100% !important}
 .kjkm_btn{position:absolute;top:18px;right:14px;}
 .pageForm_topul li{width:25%;}
 .fillvoucher_topul li p{margin-left:0 !important;}
 .pageForm_topul li .textbox input{width:100% !important;}
 .datagrid-cell-c3-instockcode{width:110px !important;}
 #printedit_pageId{height:auto !important}
  #southToolDiv .easyui-linkbutton{margin:3px 9px 10px 0 !important}
</style>


</head>
<body style="background: #ebedf3;">
	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<!-- 顶部功能栏 -->
		<div id="eastToolDiv" data-options="region:'north'" style="background-color: #f4f4f4; background: -webkit-linear-gradient(top,#fff,#f4f4f4);background: -moz-linear-gradient(top,#fff,#f4f4f4);background: -o-linear-gradient(top,#fff,#f4f4f4);
		background: linear-gradient(to bottom,#fff,#f4f4f4); background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<span class="invo_title">会计期间</span>
			<input type='text' id="year" id="year" class="invo_title_year" value="${year}" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%y',dateFmt:'yyyy',onpicked:accountperiodChange})" />
			<input type="text" id="month" name="month" class="invo_title_month" value="${month}" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM',onpicked:accountperiodChange})" />
			<span style="margin: 15px 0 0 5px;float: left;display:inline-block">期</span>
			<a class="easyui-linkbutton dr_btns" href="javascript:void(0)" style="margin: 8px 0 0 40px;" onclick="$('#import_panel').window('open')"><i></i></a>
			<a class="easyui-menubutton scpz_btns" href="javascript:void(0)" style="margin: 8px 0 0 0;" data-options="menu:'#createvoucherDiv'"></a>
			<div id="createvoucherDiv" style="width:150px;display:none">
				<div onclick="beforeVoucherCreate({position:'#gridlist',type:1,code:'purchaseinvoice'})">逐笔生成</div>
				<div>
					<span>按时间汇总</span>
					<div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaseinvoice',day:1})">1天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaseinvoice',day:3})">3天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaseinvoice',day:5})">5天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaseinvoice',day:7})">7天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaseinvoice',day:10})">10天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaseinvoice',day:15})">15天</div>
					</div>
				</div>
				<div>
					<span>按类型汇总</span>
					<div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:3,code:'purchaseinvoice',day:1})">1天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:3,code:'purchaseinvoice',day:3})">3天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:3,code:'purchaseinvoice',day:5})">5天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:3,code:'purchaseinvoice',day:7})">7天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:3,code:'purchaseinvoice',day:10})">10天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:3,code:'purchaseinvoice',day:15})">15天</div>
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:3,code:'purchaseinvoice',day:31})">全部</div>
					</div>							
				</div>
				<div onclick="beforeVoucherCreate({position:'#gridlist',type:4,code:'purchaseinvoice'})">全月汇总</div>
			</div>
			<a class="easyui-linkbutton cz_btns" style="margin: 8px 0 0 0;color: #6d62af;" href="javascript:void(0)" onclick="beforeVoucherRevoke({position:'#gridlist',code:'purchaseinvoice'})"><i></i></a>
			<a class="easyui-linkbutton dc_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="ExportExcel()"><i></i></a>
			<a class="easyui-linkbutton cx_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="$('#search_panel').dialog('open')"><i></i></a>
			<!-- 打印配置、批量打印、预览 -->
			<!-- <a class="easyui-linkbutton dypz_btns" href="javascript:void(0)" onclick="printallocation({width:320,height:490,menucode:'purchaselist'})"></a>
			<a class="easyui-linkbutton pldypz_btns" href="javascript:void(0)" onclick="printAndPreview('批量打印')"></a>
	        <a class="easyui-linkbutton yl_btns" href="javascript:void(0)" onclick="printAndPreview('预览')"></a> -->
			
			<a class="easyui-linkbutton gb_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="closeIndexTabs({title:'采购发票'})"><i></i></a>
		    <p class="refreshbtns" id="refreshbtn" onclick="refresh()"></p>
		</div>
		
		
		
		<!----   打印配置   ---->
		<div id="printedit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div style="width:316px;border: 1px #cedae4 solid;">
		        <div class="bgdivtit" style="border: 0;height:47px">
			        <span style="margin:0 10px;line-height: 46px;display: inline-block;float: left;">模板名称</span>
					<select id="temp_name"  name="temp_name"  class="easyui-combobox"  data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="width:120px;margin: 0;height: 27px;">
					    
					</select>
					<a class="delelte_fillvouchers" href="javascript:void(0)" onclick="del_temp()">删除</a>
					<a class="add_fillvouchers" href="javascript:void(0)" onclick="add_temp()">新增</a>
				</div>	
		    	<div class="centerpagediv">
		    	     <div class="tablepage">
			    	     <p class="paper_btn paper_btns" onclick="switchoverF(1)">纸张</p>
			    	     <p class="page_btn page_btns" onclick="switchoverF(2)">页面</p>
		    	     </div>
		    	     <div class="paper_div">
		    	     <div id="divMMHeight" style="height: 1mm; width: 1mm; display: none;"></div>
		    	     <input type="hidden" id="menucode" name="menucode"> 
		    	     <input type="hidden" id="tempid" name="tempid">  
		    	      <input type="hidden" id="name" name="name">    
		    	      <input type="hidden" id="content" name="content">
		    	      <input type="hidden" id="html" name="html">  
		    	          <ul>
		    	             <li style="margin: 22px 0 0 0;">
		    	                 <span>纸张类型：</span>
		    	                 <div style="background:#fff;width: 149px;height: 25px;margin: 1px 0 0 62px; border-radius: 3px;">
			    	                 <div id="selectStyle" class="selectStyle">
							             <select class="select" id="pagelx" name="pagelx">
											<option value="A5(横向)">A5(横向)</option>
											<option value="A4">A4</option>
											<option value="自定义">自定义</option>
										</select>
									</div>
									<div class="input-group-addon"><p class="glyphicon actips"></p></div>
		    	                 </div>
		    	             </li>
		    	             <li>
		    	                 <span>页面属性</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>高度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="height" type="text" id="height" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>宽度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="width" type="text" id="width" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>页边距</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>上边距：</span>
		    	                 <input name="page_top" type="text" id="page_top" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>下边距：</span>
		    	                 <input name="page_bottom" type="text" id="page_bottom" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>左边距：</span>
		    	                 <input name="page_left" type="text" id="page_left" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>右边距：</span>
		    	                 <input name="page_right" type="text" id="page_right" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	          </ul>
		    	     </div> 
		    	     <div class="pape_div">
		    	         <ul class="page_t_ul page_t_ulspec">
		    	             <li>
		    	                <span>列项目</span>
		    	                <span>列宽（毫米）</span>
		    	                 <span>是否打印</span>
		    	             </li>
		    	             <li>
		    	                <span>商品名称</span>
		    	                <span>
		    	                    <input  name="goods_namewidth" type="text" id="goods_namewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="goods_nameSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             
		    	             <li>
		    	                <span>规格型号</span>
		    	                <span>
		    	                    <input  name="modelwidth" type="text" id="modelwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="modelSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>单位</span>
		    	                <span>
		    	                    <input  name="unitwidth" type="text" id="unitwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="unitSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>数量</span>
		    	                <span>
		    	                    <input  name="quantitywidth" type="text" id="quantitywidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="quantitySel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             
		    	             
		    	              <li>
		    	                <span>单价</span>
		    	                <span>
		    	                    <input  name="pricewidth" type="text" id="pricewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="priceSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>金额</span>
		    	                <span>
		    	                    <input  name="moneywidth" type="text" id="moneywidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="moneySel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>税率</span>
		    	                <span>
		    	                    <input  name="taxratewidth" type="text" id="taxratewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="taxrateSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>税额</span>
		    	                <span>
		    	                    <input  name="tax_moneywidth" type="text" id="tax_moneywidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="tax_moneySel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>价税合计</span>
		    	                <span>
		    	                    <input  name="taxtotalwidth" type="text" id="taxtotalwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="taxtotalSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	         </ul>
		    	         <ul class="page_b_ul">
		    	             <li style="margin: 14px 0 0 0;">
		    	                 <span>行高：</span>
		    	                 <input name="rowheight" type="text" id="rowheight" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>每页循环行数：</span>
		    	                 <input name="hs" type="text" id="hs" onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')" class=""/>
		    	                 <p>（单位：行）</p>
		    	             </li>
		    	         </ul>
		    	     </div>
		    	</div>
		    </div>
		    <div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
				<!-- 底部功能栏 -->
				<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="closeclearF()">取消</a>
				<a class="easyui-linkbutton sure-dialog" href="javascript:void(0)" onclick="templateSure()">确定</a>
			</div>
		</div>
		
		<div id="edit_pageIdm" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="overflow: hidden;display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的模板</p>
		    	<select class="easyui-combobox" id="template_name" name="template_name" data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="overflow: hidden;width: 125px;height:30px">
   	       			
				</select>
				<input type="hidden" id="tempurl" />
				<input type="hidden" id="opertype">
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdm')">取消</a>
			    	<a href="javascript:void(0)" id="next_btn" class="easyui-linkbutton cancel-btn sureNew_btn" onclick="nextprint()">下一步</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn sureNew_btn print_btns" style="display:none" onclick="printTemp('edit')">打印</a>
		    	</div>
		    </div>
		</div>
		
		<div id="edit_pageIdprintb" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的操作</p>
		    	<input type="hidden" id="selecttempurl" />
		    	<input type="hidden" id="tempname" />
		    	<!-- <a href="javascript:void(0)" class="print_btn" onclick="exportTemp()">导出</a> -->
		    	<a href="javascript:void(0)" class="print_btn" style="margin:-5px 0 0 28px" onclick="printTemp('edit')">打印</a>
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdprintb')">取消</a>
		    	</div>
		    </div>
		</div>
		
		<div id="edit_pageIdprintbtime" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请输入起止凭证号</p>
		    	<input type="hidden" id="selecttempurl1" />
		    	<input type="hidden" id="tempname1" />
		    	<input  type="text" id="startcode"  name=""  style="width: 77px;padding-left:3px; height: 22px;border-radius: 3px;border: 1px #808080 solid;box-shadow: rgba(4,21,26,0.07) 0 1px 1px inset;float: left;margin: 0 0 0 60px;"/>
		    	<span style="display: inline-block;float: left;margin: 4px 6px 0 6px;">至</span>
		    	<input  type="text" id="endcode"  name="" style="width: 77px;padding-left:3px; height: 22px;border-radius: 3px;border: 1px #808080 solid;box-shadow: rgba(4,21,26,0.07) 0 1px 1px inset;float: left;"/>
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdprintbtime')">取消</a>
		            <a href="javascript:void(0)" class="easyui-linkbutton cancel-btn sureNew_btn" onclick="printTemp('list')">打印</a>
		    	</div>
		    </div>
		</div>
		
		
		
		

		
		<!-- 主面板 -->
		<div style="margin: 54px 0 0 0;border:1px #cedae4 solid;" id="parcel_southToolDiv">
		    <div id="gridlist" data-options="region:'center'" ></div>
		
			<!-- 底部功能栏 -->
			<div id="southToolDiv" data-options="region:'south'" style="margin: -6px 0 0 -8px;position: absolute;">
				<a class="easyui-linkbutton close-dialog zhc_btn" href="javascript:void(0)" onclick="addrow()">增行</a>
				<a class="easyui-linkbutton close-dialog shc_btn" href="javascript:void(0)" onclick="removerow()">删行</a>
			</div>
		</div>
		
		<!-- 导入弹出框 -->
		<div id="import_panel" class="easyui-window" style="width: 400px;height:200px;display:none" title="导入" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<form id="importFileForm" method="post" enctype="multipart/form-data" data-options="novalidate:true" style="height: 116px;">
			    <div class="bgdivtit" style="height: 116px;    border: 0;">
					<ul class="tolead_ul">
					    <li>
					    	<p>选择文件：</p>
					        <input class="easyui-filebox" id="file_name" name="file_name" data-options="buttonText:'浏览'" style="width:200px; height: 26px;">
					    </li>
					    <li style="margin: -4px 0 0 0;">
					    	<p>是否自动记入供应商档案：</p>
					        <select class="easyui-combobox" id="is_in_archives" name="is_in_archives" data-options="editable:false,panelHeight:'auto'" style="width: 125px;height:28px">
		    	       			<option value="0">否</option>
		    	       			<option value="1">是</option>
							</select>
						</li>
					</ul>
				</div>
			</form>
			<div style="text-align:center;height: 44px;background:#ecf5fa; border-radius: 0 0 5px 5px;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" style="margin: -1px 13px 0 0;" onclick="$('#import_panel').window('close')">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" style="margin: -1px 8px 0 0;" onclick="importExcel()">确定</a>
		    </div>
		</div>
		
		
		<!-- 查询弹出框 -->
		<div id="search_panel" class="easyui-window" title="查找" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false,modal:true" style="width:400px;height:178;display:none">
			<div class="search_pel search_peldiv" style="height:86px">
				<p style="float:Left;width:105px;margin:35px 0 0 15px">输入查询内容：</p>
				<input id="search_name" class="easyui-textbox" />
				<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" onclick="ButtonFun.findList('#gridlist','#search_name')" style="margin:28px 6px 0 0;">搜索</a>
			</div>
			<div style="height:42px;text-align:right;background:#ecf5fa; border-radius: 0 0 5px 5px;">
				<div style="float: right;">
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="$('#search_panel').dialog('close')">关闭</a> 
				</div>
			</div>
		</div>
		
		<!-- 编辑页面弹框 start -->
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px #cedae4 solid;">
				
				<input type="hidden" id="voucher_number" name="voucher_number">
				<input type="hidden" id="invoice_money" name="invoice_money">
				<input type="hidden" id="invoice_tax" name="invoice_tax">
				<input type="hidden" id="invoice_advalorem" name="invoice_advalorem">
				
				
				<input type="hidden" id="id" name="id">
				<input type="hidden" id="supplierid" name="supplierid">
				<input type="hidden" id="total_money" name="total_money">
				<input type="hidden" id="total_tax_money" name="total_tax_money">
				<input type="hidden" id="total_taxtotal" name="total_taxtotal">
				<input type="hidden" id="accountperiod" name="accountperiod">
				<input type="hidden" id="purchaselistmx" name="purchaselistmx">
			    <span style=" text-align: center;position: absolute;top: 9px;font-weight: 600; font-size: 14px;color: #fff;left: 470px;">采购入库单<span id="name" name="name"></span></span>
			    <ul class="pageForm_topul fillvoucher_topul pageForm_topults" style="border-bottom: 1px #6f9ec2 solid;">
	    		    <li >
	    		       <p>供应商：</p>
	    		       <input id="supplier_name" class="easyui-textbox" name="supplier_name" style="height: 27px;margin:8px 0 0 0" data-options="readonly:true,required:true" />
					  <!--  <div class="kjkm_btn" id="supplier_btn" onclick="javascript:Dialog.archives_supplierdoc('1;pageForm;supplierid:id,supplier_name:name')"></div> -->
	    		    </li>
		    		<li>
	    		       <p>入库日期：</p>
	    		       <input  id="make_date"  type="text" name="make_date" style="height: 25px;"  data-options="readonly:true,required:true" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M-%d',dateFmt:'yyyy-MM-dd',onpicked:voucherPickedFunc})"/>
	    		    </li>
		    		<li>
	    		       <p >入库单号：</p>
	    		       <input id="code" class="easyui-textbox" name="code" style="width:103px;height: 27px;margin:8px 0 0 0" data-options="required:true,validType:['repeatMultiParameter[\'purchaselist\',\'t_purchaselist\',\'code\',\'pageForm\',\'make_date\']']" />
	    		    </li>
	    		    
	    		    <li>
	    		       <p >发票号码：</p>
	    		       <input id="invoicecode" class="easyui-textbox" name="invoicecode" style="width:103px;height: 27px;margin:8px 0 0 0" data-options="readonly:true,required:true" />
	    		    </li>
	    		</ul>
	    		<div style="border: 0px #cedae4 solid;border-left:0;border-right:0;"><div id="purchaselistmx_gridlist" style="width:996px;height:218px"></div></div>
		   		  <!-- 	<a href="javascript:void(0)" class="easyui-linkbutton zhc_btn zhch_btn" onclick="ButtonFun.insertRow({type:2,renderid:'#purchaselistmx_gridlist'})">增行</a>
		    		<a href="javascript:void(0)" class="easyui-linkbutton shc_btn shch_btn" onclick="ButtonFun.removeRow({type:2,renderid:'#purchaselistmx_gridlist'})">删行</a>	 -->
		    </form>
		    <div style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
		        <div style="float: left;margin: 2px 0 0 5px;">
			       	<a href="javascript:void(0)" class="easyui-linkbutton xz_btn" onclick="ButtonFun.insertRow({type:2,renderid:'#purchaselistmx_gridlist'})">增行</a>
		    		<a href="javascript:void(0)" class="easyui-linkbutton xz_btn" onclick="ButtonFun.removeRow({type:2,renderid:'#purchaselistmx_gridlist'})">删行</a>	
			    	<a href="javascript:void(0)" class="easyui-linkbutton bc_btn" onclick="submitForm({code:'purchaselist',type:2,renderid:'#gridlist',noClose:true})">保存</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton xg_btn" onclick="editVoucher()">修改</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton sc_btn" onclick="closeOrDelete('删除')">删除</a>
		    	</div>
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="clearForm()" style="margin: 4px 7px 0 0;">关闭</a>
		    </div>
		</div>
		<!-- 编辑页面弹框 end -->
	</div>

<script type="text/javascript">

//查询列设定
var searchCols = [
                  	'accountperiod','invoice_number','invoice_date','tmp_supplier','money',
                  	'tax','advalorem','supplier_name','purchase_type','voucher_number'
                 ];
var width = $(window).width()-24;
var w1 = width/30;
var heights = $(window).height();
$("#parcel_southToolDiv").height(heights-54);

$("#gridlist").height(heights-54 -30);

$(function(){

	var gridObj1 = new Object();
	gridObj1["position"] = "#purchaselistmx_gridlist";
	gridObj1["url"] = 'purchaselist/listPurchaselistmx?type=purchaseinvoice&isList=1&invoicecode='+$("#invoicecode").textbox('getValue');
	gridObj1["columns"] = [[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'zt',title:'zt',hidden:true},
							{field:'rowid',title:'rowid',hidden:true},
							{field:'purchaselistid',title:'purchaselistid',hidden:true},
							/* {field:'type_name',title:'商品类别',width:w1*2}, */
							{field:'goodsid',title:'商品id',hidden:true},
							{field:'goods_name',title:'商品名称',width:w1*4,formatter:formatOper_goods},
							{field:'model',title:'规格型号',editor:{type:'text'},width:w1*3},
							{field:'unit',title:'单位' ,width:w1*1},
							{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
							{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:w1*2},
							{field:'money',title:'金额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2,
							formatter:function(value,row,index){
								return formatMoney(value);
							}},
							{field:'taxrate',title:'税率' ,editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
							{field:'tax_money',title:'税额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
							{field:'taxtotal',title:'价税合计',width:w1*2},
				         ]];
	//gridObj1["onEndEdit"]= onEndFunc;
	gridObj1["idField"] = 'id';
	gridObj1["showFooter"] = true;
	gridObj1["onAfterEdit"] = onAfterEdit_1;
	Grid.edit_cell_grid_url(gridObj1);
	
	
	var obj = new Object();
	obj["position"] = '#gridlist';
	obj["idField"] = 'id';
	obj["url"] = 'purchaseinvoice/list';
	obj["columns"] = [[ 
						{field:'',checkbox:true},
						{field:'accountperiod',title:'记账日期',align: 'left', halign: 'center',width:w1*2,total:true,
							editor:{type:'datebox', 
									options: {
										required:true,
										validType:['period']
									}
							}
						},
						{field:'invoice_number',title:'发票号码',align: 'left', halign: 'center',width:w1*2,
							editor:{
								type: 'textbox',
								options: {
									required:true,
									validType:['repeatmx[\'purchaseinvoice\',\'t_purchaseinvoice\',\'invoice_number\',\'gridlist\']']
								}
							}
						},
						{field:'invoice_date',title:'发票日期',align: 'left', halign: 'center',width:w1*2,
							editor:{type:'datebox', 
								options: {
									required:true
								}
							}
						},
						{field:'tmp_supplier',title:'供应商',align: 'left', halign: 'center',width:w1*4,editor:{type:'textbox'}},
						{field:'money',title:'金额',align: 'right', halign: 'center',width:w1*1.6,sum:true,
							editor:{type:'numberbox', 
								options: {
									precision:2,
									required:true,
									onChange:editorCal
								}
							},
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'tax',title:'税额',align: 'right', halign: 'center',width:w1*1.6,sum:true,
							editor:{type:'numberbox',
								options: {
									precision:2,
									required:true,
									onChange:editorCal
								}
							},
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'advalorem',title:'价税合计',align: 'right', halign: 'center',width:w1*1.6,sum:true,
							editor:{type:'numberbox', 
								options: {
									precision:2,
									readonly:true
								}
							},
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'supplier_name',title:'供应商档案',align: 'left', halign: 'center',width:w1*4,editor:{type:'textbox'}},
						{field:'purchase_type',title:'采购类型',align: 'left', halign: 'center',width:w1*2,
							editor:{type: 'combogrid',
								options: {
									required:true,
									url: getUrl('purchasesubquickset/list'),
									panelWidth: 400,
									panelHeight: 200,
									delay:100,
									idField: 'quickcode',
									textField: 'item',
									mode: 'remote',
									autoRowHeight: false,
									columns:[[
									           	{field:'quickcode',title:'快速编码',align: 'left', halign: 'center',width:80},
							             		{field:'item',title:'事项',align: 'left', halign: 'center',width:120},
							             		{field:'accsubject_name',title:'会计科目',align: 'left', halign: 'center',width:200}
											]],
									onSelect:function(index, row){
										debugger
										var rowIndex = '';
										//当然页面所有可编辑combogrid
										var com_inputs = $('input.datagrid-editable-input.combogrid-f.combo-f.textbox-f');
										for(var i = 0 ; i < com_inputs.length ; i++){
											if( this == $(com_inputs[i]).combogrid('grid')[0] ){
												rowIndex = getRowIndex(com_inputs[i]);
												break;
											}
										}
										
										//当前 采购类型 编辑器
										var target_purchase_type = $($('#gridlist').datagrid('getEditor', {index:rowIndex,field:'purchase_type'}).target);
										var tmp_id = $(target_purchase_type.next()).find("input[name='tmp_id']");			//采购类型id输入框
										var tmp_item = $(target_purchase_type.next()).find("input[name='tmp_item']");		//采购类型item输入框
										tmp_id.val(row.id);			//赋值 采购类型id
										tmp_item.val(row.item);		//赋值 采购类型item
									},
									onChange:function(newValue, oldValue){
										debugger
										if(!checkEm(newValue)){
											var rows = $($(this).combogrid('grid')).datagrid('getRows');
											for(var i in rows){
												if(rows[i].item == newValue || rows[i].quickcode == newValue){
													$($(this).combogrid('grid')).datagrid('selectRow', i);
													return;
												}else{
													var rowIndex = getRowIndex(this);
													//当前 销售类型 编辑器
													var target_purchase_type = $($('#gridlist').datagrid('getEditor', {index:rowIndex,field:'purchase_type'}).target);
													var tmp_item = $(target_purchase_type.next()).find("input[name='tmp_item']");		//销售类型item输入框
													var tmp_id = $(target_purchase_type.next()).find("input[name='tmp_id']");			//采购类型id输入框
													tmp_id.val('');		//赋空 采购类型id
													tmp_item.val(newValue);	//赋值 销售类型item
												}
											}
										}else{
											var rowIndex = getRowIndex(this);
											//当前 采购类型 编辑器
											var target_purchase_type = $($('#gridlist').datagrid('getEditor', {index:rowIndex,field:'purchase_type'}).target);
											var tmp_id = $(target_purchase_type.next()).find("input[name='tmp_id']");			//采购类型id输入框
											var tmp_item = $(target_purchase_type.next()).find("input[name='tmp_item']");		//采购类型item输入框
											tmp_id.val('');		//赋空 采购类型id
											tmp_item.val('');	//赋值 采购类型item
										}
									}
								}
							}
						},
						{field:'voucher_number',title:'凭证号码',align: 'left', halign: 'center',width:w1*2,
							formatter:function(value,row,index){
								if(!checkEm(row.voucher_id)){
									return '<a href="javascript:void(0)" onclick="Dialog.linkVoucher('+ row.voucher_id +')">'+ row.voucher_number +'</a>';
								}
								return '';
							}
						},
						{field:'instockcode',title:'入库单号',align: 'left', halign: 'center',width:w1*3},
					/* 	{field:'un_instock',title:'未入库',align: 'left', halign: 'center',width:w1*1.6}, */
						{field:'is_stockcontrol',title:'是否库存管制',hidden:true},
						{field:'action',title:'操作',width:w1*2.5,align:'center',
							formatter:function(value,row,index){
								if (row.editing){
									var s = '<div style="width: 73px;height: 28px;margin: 0 auto;text-align: center;"><a href="javascript:void(0)" class="mxqd_btns" style="margin:0" onclick="saverow(this)">确定</a> ';
									var c = '<a href="javascript:void(0)" class="mxqx_btns" style="margin:0" onclick="cancelrow(this)">取消</a></div>';
									return s+c;
								} else {
									var e = '<div style="width: 73px;height: 28px;text-align: center;padding-left:20px"><a href="javascript:void(0)" class="mxxg_btn" style="margin:3px 0 0 0" onclick="editrow(this)">修改</a>';
									var f = '<a href="javascript:void(0)" class="mxxg_btn" style="margin:3px 0 0 5px" onclick="writeoffrow(this,'+index+')">入库</a>';
									var d = '<a href="javascript:void(0)"  class="mxsc_btn" style="margin:3px 0 0 4px" onclick="deleterow(this)">删除</a></div>';
									return e+f+d;
								}
							}
						}
					]];
	
	obj["pagination"] = false;
	obj["showFooter"] = true;
	//生成可编辑表格
	Grid.edit_row_grid(obj);
	
	$('#gridlist').datagrid({
		onLoadSuccess: function (data){
            $('#gridlist').datagrid('statistics'); //合计
            $(".datagrid-footer").find(".mxxg_btn").hide();
    		$(".datagrid-footer").find(".mxsc_btn").hide();
		}
	});
	
})

	//入库日期选择，只能选择当前期间内的日期
	function voucherPickedFunc(){
		debugger
		var make_date=$('#make_date').val().substring(0,7);
		var accountinfo = findAccInfo();
		var accountperiod=accountinfo.periodofaccount;
		if(accountperiod!=make_date){
			$.messager.alert("提示","<span class='hintsp_w'>提示</span>只能选择当前期间内的日期","warn");
			$('#make_date').val('');
		}
	}
	function formatOper_goods(val,row,index){
		if(checkEm(val)){val = ''}
		if(val !='合计'){
			 return '<div style="width: 73px;height: 28px;text-align:left;">'+val+'<div class="choose_btnimg"  style="margin: 0px 0 0 0;"  onclick="javascript:Dialog.archives_goods(\'2;pageForm;goodsid:id,goods_name:name,type_name:type_name,unit:unit,quantity:quantity,price:price,money:price,taxrate:taxrate,tax_money:tax_money,taxtotal:taxtotal;purchaselistmx_gridlist;'+index+'\')"></div></div>';  
		}else{
			return '合计';
		}
	}
	function doGoodsSearch(value){
		$('#customer_gridlist').datagrid('load',{
			q:value
		})
	}
	//编辑后事件，计算
	//编辑后事件，计算
	function onAfterEdit_1(index,row,changes){
		debugger;
		var quantity=row.quantity || 0;
		var price=row.price || 0;
		var money=row.money || 0;
		var tax_money = row.tax_money || 0;
		var taxrate=row.taxrate || 0;
		var taxtotal=row.taxtotal || 0;
		if(!checkEm(changes.quantity || changes.price)){
			money=(parseFloat(quantity) * parseFloat(price)).toFixed(2);
		}
		if(!checkEm(changes.money)){
			if(quantity != 0){
				price=(parseFloat(money) / parseFloat(quantity)).toFixed(4);
			}
		}
		
		tax_money=(parseFloat(money) * (parseFloat(taxrate)/100)).toFixed(2);
		/* if(!checkEm(changes.taxrate)){
			
		}else{ */
			if(!checkEm(changes.tax_money)){
				tax_money=parseFloat(changes.tax_money).toFixed(2);
				taxtotal = (parseFloat(money) + parseFloat(changes.tax_money)).toFixed(2);
			}else{
				taxtotal = (parseFloat(money) + parseFloat(tax_money)).toFixed(2);
			}
			
		/* } */
		
		$('#purchaselistmx_gridlist').datagrid('updateRow',{
			index: index,
			row: {
				money: money,
				price: price,
				quantity : quantity,
				taxrate : taxrate,
				tax_money: tax_money,
				taxtotal: taxtotal
			}
		});
	}
	//点击核销按钮
	function writeoffrow(row,index){
		debugger
		$('#gridlist').datagrid('clearSelections'); 
		$('#gridlist').datagrid('selectRow',index);
		var node = getSelectedData('#gridlist',2);
		if(parseInt(node.is_stockcontrol) == 0){
			$.messager.alert('操作失败','<span class="hintsp_e">提示</span>所选数据的采购类型库存管制为否,不可进行入库！','error');
			return;
		}
		var obj=new Object();
		obj["renderid"]='#gridlist';
		obj["type"]=2;
		obj["width"]='1000px';
		obj["height"]='392px';
		ButtonFun.editFun(obj)
	}
	
	//编辑通用赋值前触发方法
	function beforeEditFun(node){
		
	}
	
	function afterEditFun(node){
		debugger
		
		$("#voucher_number").val(node.voucher_number);
		$("#invoice_money").val(node.money);
		$("#invoice_tax").val(node.tax);
		$("#invoice_advalorem").val(node.advalorem);
		
		$('#make_date').val(node.accountperiod);
		$('#supplierid').val(node.supplier_id);
		$('#supplier_name').textbox("setValue",node.supplier_name);
		$('#invoicecode').textbox("setValue",node.invoice_number);
		$('#purchaselistmx_gridlist').datagrid('options').editIndex = undefined;

		var invoicecode=node.invoice_number;
		
 		 $.ajax({
			url:getUrl('purchaselist/listPurchaselistmx?type=purchaseinvoice&invoicecode='+invoicecode),
			async:false,
			success:function(data){
				if(!checkEm(data)){
					debugger
					$("#accountperiod").val(data.accountperiod);
					if(!checkEm(data.code)){
						$('#code').textbox("setValue",data.code);
					}
					if(data.rows.length>0){
						if(!checkEm(data.rows[0].code)){
							$('#code').textbox("setValue",data.rows[0].code);
							$('#id').val(data.rows[0].purchaselistid);
						}
						$('#purchaselistmx_gridlist').datagrid('load',{invoicecode:invoicecode});
						$("#purchaselistmx_gridlist").datagrid({
										columns:[[
											{field:'id',title:'ID',width:100,hidden:true},
											{field:'zt',title:'zt',hidden:true},
											{field:'rowid',title:'rowid',hidden:true},
											{field:'purchaselistid',title:'purchaselistid',hidden:true},
											/* {field:'type_name',title:'商品类别',width:w1*2}, */
											{field:'goodsid',title:'商品id',hidden:true},
											{field:'goods_name',title:'商品名称',width:w1*4,formatter:formatOper_goods},
											{field:'model',title:'规格型号',editor:{type:'text'},width:w1*3},
											{field:'unit',title:'单位' ,width:w1*1},
											{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
											{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:w1*2},
											{field:'money',title:'金额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2,
											formatter:function(value,row,index){
												return formatMoney(value);
											}},
											{field:'taxrate',title:'税率' ,editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
											{field:'tax_money',title:'税额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
											{field:'taxtotal',title:'价税合计',width:w1*2},
								         ]],
							         onClickCell: function(index, field){
										 
									 }
						})
					}else{
						$('#id').val('');
						$("#purchaselistmx_gridlist").datagrid({
							         onAfterEdit:onAfterEdit_1 || function(){},
									 onClickCell: function(index, field){
									    	debugger;
											if ($("#purchaselistmx_gridlist").datagrid('options').editIndex == undefined){
												$("#purchaselistmx_gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
												$("#purchaselistmx_gridlist").datagrid('options').editIndex = index;
												var ed = $("#purchaselistmx_gridlist").datagrid('getEditor', {index:index,field:field});  
												if($(ed.target).next().find(".textbox-text").length>0){
													$(ed.target).next().find(".textbox-text").focus();
												}else{
													$(ed.target).focus();
												}
											}else if ($("#purchaselistmx_gridlist").datagrid('validateRow', $("#purchaselistmx_gridlist").datagrid('options').editIndex)){
												$("#purchaselistmx_gridlist").datagrid('endEdit', $("#purchaselistmx_gridlist").datagrid('options').editIndex);
												$("#purchaselistmx_gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
												$("#purchaselistmx_gridlist").datagrid('options').editIndex = index;
												var ed = $("#purchaselistmx_gridlist").datagrid('getEditor', {index:index,field:field});
												if($(ed.target).next().find(".textbox-text").length>0){
													$(ed.target).next().find(".textbox-text").focus();
												}else{
													$(ed.target).focus();
												}
											}
										}
						});
						$('#purchaselistmx_gridlist').datagrid('load',{invoicecode:''});
						
					}
					
				}
			}
			
		})  
	
	}
	
	
	function editVoucher(){
		debugger
		var make_date=$('#make_date').val().substring(0,7);
		var accountperiod=$('#accountperiod').val().substring(0,7);
		if(CompareDate(accountperiod,make_date)){
			$.messager.alert("提示","<span class='hintsp_w'>提示</span>凭证所属月份已经结账,不可修改","info");
			return;
		}
		
		/* if(!checkEm($("#voucher_number").val())){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库单所属发票已生成凭证，不可修改！','warning');  
			return false;
		} */
		$("#code").textbox({"editable":true});
		$("#make_date").attr("disabled",false);
		
			$("#purchaselistmx_gridlist").datagrid({
				columns:[[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'zt',title:'zt',hidden:true},
							{field:'rowid',title:'rowid',hidden:true},
							{field:'purchaselistid',title:'purchaselistid',hidden:true},
							/* {field:'type_name',title:'商品类别',width:w1*2}, */
							{field:'goodsid',title:'商品id',hidden:true},
							{field:'goods_name',title:'商品名称',width:w1*4,formatter:formatOper_goods},
							{field:'model',title:'规格型号',editor:{type:'text'},width:w1*3},
							{field:'unit',title:'单位' ,width:w1*1},
							{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
							{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:w1*2},
							{field:'money',title:'金额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2,
							formatter:function(value,row,index){
								return formatMoney(value);
							}},
							{field:'taxrate',title:'税率' ,editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
							{field:'tax_money',title:'税额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
							{field:'taxtotal',title:'价税合计',width:w1*2},
				         ]],
				         onAfterEdit:onAfterEdit_1 || function(){},
						 onClickCell: function(index, field){
						    	debugger;
								if ($("#purchaselistmx_gridlist").datagrid('options').editIndex == undefined){
									$("#purchaselistmx_gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
									$("#purchaselistmx_gridlist").datagrid('options').editIndex = index;
									var ed = $("#purchaselistmx_gridlist").datagrid('getEditor', {index:index,field:field});  
									if($(ed.target).next().find(".textbox-text").length>0){
										$(ed.target).next().find(".textbox-text").focus();
									}else{
										$(ed.target).focus();
									}
								}else if ($("#purchaselistmx_gridlist").datagrid('validateRow', $("#purchaselistmx_gridlist").datagrid('options').editIndex)){
									$("#purchaselistmx_gridlist").datagrid('endEdit', $("#purchaselistmx_gridlist").datagrid('options').editIndex);
									$("#purchaselistmx_gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
									$("#purchaselistmx_gridlist").datagrid('options').editIndex = index;
									var ed = $("#purchaselistmx_gridlist").datagrid('getEditor', {index:index,field:field});
									if($(ed.target).next().find(".textbox-text").length>0){
										$(ed.target).next().find(".textbox-text").focus();
									}else{
										$(ed.target).focus();
									}
								}
							}
			})
	}
	
	
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){
		//再次验证编号不能重复
		if($("#make_date").prop("disabled")){
			return false;
		}
		var obj=new Object();
		var code=$('#code').textbox("getValue");
		obj["code"]='purchaselist';
		obj["table"]='t_purchaselist';
		obj["id"]= $("#id").val();
		if(checkEm($('#make_date').val())){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库日期不能为空！','warning'); 
			return false;
		}
		//验证凭证日期是否已经结账
		var make_date=$('#make_date').val().substring(0,7);
		var accountperiod=$('#accountperiod').val();
		if(CompareDate(accountperiod,make_date)){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>凭证所属月份已经结账！','warning'); 
			return false;
		}
		debugger
		//保存时如果有未关闭的编辑框   将编辑框关闭，否则取不到值
		var indexs=$('#purchaselistmx_gridlist').datagrid('getEditingRowIndexs');
		if(indexs.length>0){
			for(var i=0;i<indexs.length;i++){
				 $('#purchaselistmx_gridlist').datagrid('refreshRow', indexs[i]);
				$('#purchaselistmx_gridlist').datagrid('endEdit',indexs[i]);
			}
		}
		var data = $('#purchaselistmx_gridlist').datagrid('getData');
		var rows=data.rows;
		for(var i=0;i<rows.length;i++){
			//删除空行
			if(checkEm(rows[i].goodsid)){
				$('#purchaselistmx_gridlist').datagrid('deleteRow',i);
				i--;
				continue;
			}
			if(checkEm(rows[i].quantity) || parseFloat(rows[i].quantity) == 0){
				$.messager.alert('提示','<span class="hintsp_w">提示</span>入库数量不能为0或者空！','warning');  
				return false;
			}
		}
		
		if(rows.length==0){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库明细不能为空！','warning');  
			return false;
		}

		var money_total=calTotalByCol('#purchaselistmx_gridlist','money');
		var total_tax_money=calTotalByCol('#purchaselistmx_gridlist','tax_money');
		var total_taxtotal=calTotalByCol('#purchaselistmx_gridlist','taxtotal');
		
		
		/* if(parseFloat($("#invoice_money").val()) != parseFloat(money_total)){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库金额与发票金额不符，请检查后重新保存！','warning');  
			return false;
		}
		if(parseFloat($("#invoice_tax").val()) != parseFloat(total_tax_money)){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库金额与发票金额不符，请检查后重新保存！','warning');  
			return false;
		}
		if(parseFloat($("#invoice_advalorem").val()) != parseFloat(total_taxtotal)){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库金额与发票金额不符，请检查后重新保存！','warning');  
			return false;
		} */
	
		$("#total_money").val(money_total);
		$("#total_tax_money").val(total_tax_money);
		$("#total_taxtotal").val(total_taxtotal);
		$('#purchaselistmx').val(JSON.stringify(rows));
		return true;
	}
	
	
	
	//通用保存后触发方法
	function afterSaveFun(id){
		

		var invoicecode=$("#invoicecode").textbox('getValue');
		
 		 $.ajax({
			url:getUrl('purchaselist/listPurchaselistmx?type=purchaseinvoice&isList=1&invoicecode='+invoicecode),
			async:false,
			success:function(data){
				if(!checkEm(data)){
					debugger
					$("#accountperiod").val(data.accountperiod);
					if(!checkEm(data.code)){
						$('#code').textbox("setValue",data.code);
					}
					if(data.rows.length>0){
						if(!checkEm(data.rows[0].code)){
							$('#code').textbox("setValue",data.rows[0].code);
							$('#id').val(data.rows[0].purchaselistid);
						}
						$('#purchaselistmx_gridlist').datagrid('load',{invoicecode:invoicecode});
						$("#purchaselistmx_gridlist").datagrid({
							columns:[[
										{field:'id',title:'ID',width:100,hidden:true},
										{field:'zt',title:'zt',hidden:true},
										{field:'rowid',title:'rowid',hidden:true},
										{field:'purchaselistid',title:'purchaselistid',hidden:true},
										/* {field:'type_name',title:'商品类别',width:w1*2}, */
										{field:'goodsid',title:'商品id',hidden:true},
										{field:'goods_name',title:'商品名称',width:w1*4,formatter:formatOper_goods},
										{field:'model',title:'规格型号',editor:{type:'text'},width:w1*3},
										{field:'unit',title:'单位' ,width:w1*1},
										{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
										{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:w1*2},
										{field:'money',title:'金额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2,
										formatter:function(value,row,index){
											return formatMoney(value);
										}},
										{field:'taxrate',title:'税率' ,editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
										{field:'tax_money',title:'税额',editor:{type:'numberbox',options:{min:0,precision:2}},width:w1*2},
										{field:'taxtotal',title:'价税合计',width:w1*2},
							         ]],
							         onClickCell: function(index, field){
										 
									 }
						})
					}else{
						$("#id").val('');
						$('#purchaselistmx_gridlist').datagrid('loadData',{total:0,rows:[],footer:[]})
					}
					
				}
			}
			
		})  
	}
	
	function closeOrDelete(operType){
		debugger
		var make_date=$('#make_date').val().substring(0,7);
		var accountperiod=$('#accountperiod').val();
		if(CompareDate(accountperiod,make_date)){
			$.messager.alert("提示","<span class='hintsp_w'>提示</span>凭证所属月份已经结账,不可删除","info");
			return;
		}
		/* if(!checkEm($("#voucher_number").val())){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库单所属发票已生成凭证，不可删除！','warning');  
			return;
		} */
		var nodes=$('#gridlist').datagrid('getSelected');
		//var id= $("#id").val();
		var url='';
		if(operType=='关闭'){
			url='purchaselist/closeAll?closestatus=1';
		}else{
			url='purchaselist/delete';
		}
		if(!checkEm(nodes)){
			$.messager.confirm('提示', '<span class="hintsp_w">提示</span>确定是否删除该单据？', function(r){
				if (r){
					ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
					ButtonFun.addFun({width:1000,height:392})
				}
			})
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}

	function doSupplierSearch(value){
		$('#supplier_gridlist').datagrid('load',{
			q:value
		})
	}
//新增采购入库单end



//计算
function editorCal(newValue, oldValue){
	var index = getRowIndex(this);
	var money = $($('#gridlist').datagrid('getEditor', {index:index,field:'money'}).target).val();
	var tax = $($('#gridlist').datagrid('getEditor', {index:index,field:'tax'}).target).val();
	var advalorem = parseFloat(money) + parseFloat(tax);	//计算 价税合计
	var ed = $('#gridlist').datagrid('getEditor', {index:index,field:'advalorem'});
	$($(ed.target).next().find(".textbox-text")).val( advalorem );	//赋值给 价税合计 编辑器
}

//获得标记行下标
function getRowIndex(target){
    var tr = $(target).closest('tr.datagrid-row');
    return parseInt(tr.attr('datagrid-row-index'));
}








//启动编辑行
function editrow(target, index){
	if(!checkEm(target)){
		index = getRowIndex(target);
	}
	var row = $('#gridlist').datagrid('getRows')[index];
	if( !checkEm(row.voucher_id) ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>请先撤账后再修改！','error');
		return;
	}
	$('#gridlist').datagrid('beginEdit', index);
	
	//采购类型 编辑器
	var target_purchase_type = $($('#gridlist').datagrid('getEditor', {index:index,field:'purchase_type'}).target);
	//构造 采购类型id输入框
	$(target_purchase_type.next()).append('<input type="hidden" name="tmp_id" value="'+ (row.purchase_type_id || '') +'">');
	//构造 采购类型item输入框
	$(target_purchase_type.next()).append('<input type="hidden" name="tmp_item" value="'+ (row.purchase_type || '') +'">');
	
	//供应商档案 编辑器
	var ed = $($('#gridlist').datagrid('getEditor', {index:index,field:'supplier_name'}).target);
	var s_supplier_id = row.tmp_supplier_id || '';
	var s_name = row.supplier_name || '';
	var s_id = row.supplier_id || '';
	var a = '<a class="easyui-linkbutton mxqd_btn" style="float: right;margin: 0 3px 0 4px;right: 39px;" href="javascript:void(0)" onclick="javascript:mx_supplier_add('+index+')"><i></i>增加</a>';
	var r = '<a class="easyui-linkbutton mxqx_btn" style="float: right;margin: 0;right: 2px;" href="javascript:void(0)" onclick="javascript:mx_supplier_replace('+index+')"><i></i>替换</a>'; 
	var h_supplier_id = '<input type="hidden" name="tmp_supplier_id" value="'+ s_supplier_id +'">';
	var h_id = '<input type="hidden" name="tmp_id" value="'+ s_id +'">';
	var h_name = '<input type="hidden" name="tmp_name" value="'+ s_name +'">';
	var span_sn = '<span name="sn" >' + s_name + '</span>';
	var html = '';
	//判断是否临时供应商是否存在于档案
	if(checkEm(row.tmp_supplier_id)){
		html = span_sn + h_supplier_id + h_id + h_name + r + a;		//若不存在则 +替换按钮 +增加按钮
	}else{
		html = span_sn + h_supplier_id + h_id + h_name + r;			//若不存在则 +替换按钮
	}
	$(ed.next()).html(html);	//重构供应商档案编辑框
	$(ed.next()).css({"line-height":"20px","text-align":"left"});
}

//编辑行确定
function saverow(target){
	var index = getRowIndex(target);
	if(!$('#gridlist').datagrid('validateRow', index)){
		/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>未验证成功！',showType:'slide',timeout:1000,style:{
			right : '', bottom : '',
			top : document.body.scrollTop + document.documentElement.scrollTop
		}}); */
		promptbox('success','未验证成功！');
		return;
	}
	
	//获取编辑器 价格合计
	var advalorem = $($($('#gridlist').datagrid('getEditor', {index:index,field:'advalorem'}).target).next().find(".textbox-text")).val();
	
	//当前 采购类型 编辑器
	var target_purchase_type = $($('#gridlist').datagrid('getEditor', {index:index,field:'purchase_type'}).target);
	
	var purchase_type_id = $(target_purchase_type.next()).find("input[name='tmp_id']").val();		//采购类型id输入框
	
	if(checkEm(purchase_type_id)){
		promptbox('success','未验证成功,采购类型只能从档案中选择！');
		return;
	}
	var purchase_type = $(target_purchase_type.next()).find("input[name='tmp_item']").val();		//采购类型item输入框
	
	//供应商档案编辑器
	var target_supplier_name = $($('#gridlist').datagrid('getEditor', {index:index,field:'supplier_name'}).target);
	
	var tmp_supplier_id = $(target_supplier_name.next()).find("input[name='tmp_supplier_id']").val();	//获取编辑器 临时供应商id
	var supplier_id = $(target_supplier_name.next()).find("input[name='tmp_id']").val();				//获取编辑器 供应商id
	var supplier_name = $(target_supplier_name.next()).find("input[name='tmp_name']").val();			//获取编辑器 供应商档案名称
	
	$('#gridlist').datagrid('endEdit', index);	//结束编辑器
	
	var row = $('#gridlist').datagrid('getRows')[index];
	row.advalorem = advalorem;						//赋值行 价税合计
	
	row.purchase_type_id = purchase_type_id;		//赋值行 采购类型id
	row.purchase_type = purchase_type;				//赋值行 采购类型名称
	
	row.tmp_supplier_id = tmp_supplier_id;			//赋值行 临时供应商档案id
	row.supplier_id = supplier_id;					//赋值行 供应商档案id
	row.supplier_name = supplier_name;				//赋值行 供应商档案名称
	
	$.ajax({
	    url: getUrl('purchaseinvoice/edit'),
	    type: 'post', async: false, data: row, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>保存成功！',showType:'slide',timeout:1000,style:{
    				right : '', bottom : '',
    				top : document.body.scrollTop + document.documentElement.scrollTop
    			}}); */
    			promptbox('success','保存成功！');
    			row.id = data.id;	//赋值id
    			$('#gridlist').datagrid('refreshRow', index);	//刷新行
    			$('#gridlist').datagrid('reload');
			}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
		}
    });
}

//编辑行取消
function cancelrow(target){
	var index = getRowIndex(target);
	var row = $('#gridlist').datagrid('getRows')[index];
	if(checkEm(row.id)){
		$('#gridlist').datagrid('deleteRow', index);
	}else{
	    $('#gridlist').datagrid('cancelEdit', index);
	}
}

//删除行
function deleterow(target){
	var index = getRowIndex(target);
	var row = $('#gridlist').datagrid('getRows')[index];
	if(checkEm(row.id)){
		$('#gridlist').datagrid('deleteRow', index);
		$('#gridlist').datagrid('clearSelections');
	}else{
		if( !checkEm(row.instockcode) ){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>所选发票已存在入库单不可删除！','error');
			return;
		}
		
		if( !checkEm(row.voucher_id) ){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>该记录已生成凭证，请先撤账后再删除！','error');
			return;
		}
		$.messager.confirm('提示','<span class="hintsp_e">提示</span>确定要删除一条数据吗?',function(r){
			if (r){
				$.ajax({
				    url: getUrl('purchaseinvoice/delete'),
				    type: 'post', async: false, data: {id:row.id}, dataType:'json',
				    success:function(data){
			   			if(data.state == 'success'){
			    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>删除成功！',showType:'slide',timeout:1000,style:{
			    				right : '', bottom : '',
			    				top : document.body.scrollTop + document.documentElement.scrollTop
			    			}}); */
			    			promptbox('success','删除成功！');
			    			$('#gridlist').datagrid('deleteRow', index);
			    			$('#gridlist').datagrid('clearSelections');
			    			$('#gridlist').datagrid('reload');
						}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
					}
			    });
			}
		});
	}
}

//指定位置添加行
function addrow(){
	if(!checkInitBalanceIsAccBeforeAdd()){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>期初没有结账！','error');
		return;
	}
	var year = $('#year').val();
	var month = $('#month').val();
	var accinfo = findAccInfo();
	if( (year + '-' + month) < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前会计期间（'+year + '-' + month+'）已结账，不可新增！','error');
		return;
	}
	var row = $('#gridlist').datagrid('getSelected');
	if(row != null){
		var index = $('#gridlist').datagrid('getRowIndex',row);
		$('#gridlist').datagrid('insertRow',{index: index + 1,row: {}});
		editrow('', index + 1);
	}else{
		$('#gridlist').datagrid('appendRow',{});
		editrow('', $('#gridlist').datagrid('getRows').length - 1);
	}
}

//删除多行
function removerow(){
	var rows = $('#gridlist').datagrid('getSelections');
	var copyrows = new Array();
    for(var i in rows){
    	copyrows.push(rows[i]);
    }
	if(rows.length > 0){
		var ids = '';
		for(var i in rows){
			if( !checkEm(rows[i].id) ){
				ids += rows[i].id + ',';
			}
			if( !checkEm(rows[i].voucher_id) ){
				$.messager.alert('提示','<span class="hintsp_e">提示</span>所选记录中存在已生成凭证的记录，请先撤账后再删除！','error');
				return;
			}
		}
		if(ids == ''){
			for(var i in copyrows){
				var index = $('#gridlist').datagrid('getRowIndex',copyrows[i]);
				$('#gridlist').datagrid('deleteRow', index);
			}
			$('#gridlist').datagrid('clearSelections');
		}else{
			ids = ids.substring(0,ids.length-1);
			$.messager.confirm('提示','<span class="hintsp_w">提示</span>确定要删除所选数据吗?',function(r){
				if (r){
					$.ajax({
					    url: getUrl('purchaseinvoice/deleteAll'),
					    type: 'post', async: false, data: {DATA_IDS:ids}, dataType:'json',
					    success:function(data){
				   			if(data.state == 'success'){
				    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>删除成功！',showType:'slide',timeout:1000,style:{
				    				right : '', bottom : '',
				    				top : document.body.scrollTop + document.documentElement.scrollTop
				    			}}); */
				    			promptbox('success','删除成功！');
				    			for(var i in copyrows){
				    				var index = $('#gridlist').datagrid('getRowIndex',copyrows[i]);
				    				$('#gridlist').datagrid('deleteRow', index);
				    			}
				    			$('#gridlist').datagrid('clearSelections');
							}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
						}
				    });
				}
			});
		}
	}else{
		$.messager.alert('提示','<span class="hintsp_e">提示</span>请选择所要删除的行！','error')
	}
}

//切换会计期间
function accountperiodChange(){
	var year = $('#year').val();
	var month = $('#month').val();
	$('#gridlist').datagrid('load',{ accountperiod : year + '-' + month });
}

//导入excel
function importExcel(){
	if(!checkInitBalanceIsAccBeforeAdd()){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>期初没有结账！','error');
		return;
	}
	$.messager.progress();	// 显示进度条
	var year = $('#year').val();
	var month = $('#month').val();
	var acc_time = year + '-' + month;
	$('#importFileForm').form('submit', {
			url: getUrl('purchaseinvoice/importExcel?acc_time='+acc_time),
			onSubmit: function(){
				var file_name = $('#file_name').filebox('getText');
				if(file_name == ''){
					$.messager.alert('提示','<span class="hintsp_e">提示</span>请选择要导入的文件！','error');
					$.messager.progress('close');
					return false;
				}
				var accinfo = findAccInfo();
				if( acc_time < accinfo.periodofaccount){
					$.messager.alert('提示','<span class="hintsp_e">提示</span>会计期间不能是已结账月份（'+ accinfo.periodofaccount +'）前的日期！','error');
					$.messager.progress('close');
					return false;
				}
			},
			success: function (result) {
				var strs = result.split('!~!');
				if(strs[0] == 'success'){
					$.messager.alert('提示','<span class="hintsp_w">提示</span>导入成功！','success');
					$('#import_panel').window('close');
				}else{
					var str = strs[1].replace(/\\/g, "%");
					str = unescape(str);
					$.messager.alert('提示','<span class="hintsp_w">提示</span>'+ str,'success');
				}
				/* $('#gridlist').datagrid('load');
				$.messager.progress('close');// 如果表单是无效的则隐藏进度条 */
				
				setTimeout(function(){
					location.reload();
				},3000)
			}
	});
}


/**
 * 明细 供应商 选择_增加
 */
function mx_supplier_add(index){
	var row = $('#gridlist').datagrid('getRows')[index];	//行数据
	var target_tmp_supplier = $($('#gridlist').datagrid('getEditor', {index:index,field:'tmp_supplier'}).target);	//临时供应商编辑器
	var tmp_supplier = $(target_tmp_supplier).val();		//编辑框内供应商的值
	
	$.ajax({
	    url: getUrl('purchaseinvoice/addRowSupplier'),
	    type: 'post', async: false, data: {id:row.id, name:tmp_supplier}, dataType:'json',
		success:function(data){
   			if(data.state == 'success'){
    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>增加成功！',showType:'slide',timeout:1000,style:{
    				right : '', bottom : '',
    				top : document.body.scrollTop + document.documentElement.scrollTop
    			}}); */
    			promptbox('success','增加成功！');
    			//重构 供应商档案编辑器显示
    			var target_supplier_name = $($('#gridlist').datagrid('getEditor', {index:index,field:'supplier_name'}).target);
    			var h_supplier_id = '<input type="hidden" name="tmp_supplier_id" value="'+ data.id +'">';
    			var h_id = '<input type="hidden" name="tmp_id" value="'+ data.id +'">';
    			var h_name = '<input type="hidden" name="tmp_name" value="'+ tmp_supplier +'">';
    			var r = '<a class="easyui-linkbutton mxqx_btn" style="float: right;margin:0;right: 2px;" href="javascript:void(0)" onclick="javascript:mx_supplier_replace('+index+')"><i></i>替换</a>'; 
    			var span_sn = '<span name="sn" >' + tmp_supplier + '</span>';
    			$(target_supplier_name.next()).html(span_sn + h_supplier_id + h_id + h_name + r);
    			$(target_supplier_name.next()).css({"line-height":"20px","text-align":"left"});
    			
			}else {
				$.messager.alert('提示','<span class="hintsp_e">提示</span>增加失败','error');
			}
		}
    });
}

/**
 * 明细供应商选择_替换
 */
function mx_supplier_replace(index){
	var row = $('#gridlist').datagrid('getRows')[index];
	if( !checkEm(row.voucher_id) ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>请先撤账后再替换档案！','error');
		return;
	}
	var obj = new Object();
	obj["code"] = 'supplier';
	obj["name"] = '供应商';
	obj["okFun"] = 'supplier_replace_ok('+index+')';
	obj["dbFun"] = function (){supplier_replace_ok(index)};
	Dialog.archives_selector(obj);
}

/**
 * 明细供应商选择_替换  确定
 */
function supplier_replace_ok(index){
	var node = $('#supplier_gridlist').datagrid('getSelected');
	
	var supplier_id = node['id'] || '';		//替换的供应商id
	var supplier_name = node['name'] || '';	//替换的供应商name
	
	//重构 供应商档案编辑器显示
	var target_supplier_name = $($('#gridlist').datagrid('getEditor', {index:index,field:'supplier_name'}).target);
	var s_supplier_id = $(target_supplier_name.next()).find("input[name='tmp_supplier_id']").val() || '';		//获取输入框 供应商临时id
	var h_supplier_id = '<input type="hidden" name="tmp_supplier_id" value="'+ s_supplier_id +'">';
	var h_id = '<input type="hidden" name="tmp_id" value="'+ supplier_id +'">';
	var h_name = '<input type="hidden" name="tmp_name" value="'+ supplier_name +'">';
	var r = '<a class="easyui-linkbutton mxqx_btn" style="float: right;margin:0;right: 2px;" href="javascript:void(0)" onclick="javascript:mx_supplier_replace('+index+')"><i></i>替换</a>'; 
	var span_sn = '<span name="sn" >' + supplier_name + '</span>';
	$(target_supplier_name.next()).html(span_sn + h_supplier_id + h_id + h_name + r);
	$(target_supplier_name.next()).css({"line-height":"20px","text-align":"left"});
	
	$('#supplier_dialog').dialog('destroy');
}

/**
 * 生成凭证前验证
 */
function beforeVoucherCreate(obj){
	//1、所选择的明细的记账日期是否为记账月份前的日期，如果是则提示：所选明细的记账日期已结账，不可生成凭证
	var year = $('#year').val();
	var month = $('#month').val();
	var accinfo = findAccInfo();
	if( (year + '-' + month) < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前会计期间（'+year + '-' + month+'）已结账，不可生成凭证！','error');
		return;
	}
	var lastday=getDaysInMonth(year,month);
	if(Number(obj.type)==3 && Number(obj.day)==31){//如果是按类型，并且是全部的时候，重新查找当月最后一天的天数。
		obj["day"]=lastday;
	}
	var rows = $(obj.position).datagrid('getSelections');	//所选数据
	if(rows.length == 0){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>请先选择数据！','error');
		return;
	}
	var flag =0;
	for(var i in rows){
		//2、所选明细的供应商档案是否已选择，如果否则提示：明细中存在无供应商档案的档案，不可生成凭证，请修改后重新生成
		if( checkEm(rows[i].supplier_id) ){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>所选明细中存在无供应商档案的数据，不可生成凭证，请选择后供应商档案重新生成！','error');
			return;
		}
		//3、如果所选明细有存在编辑状态时，生成失败，并且提示：所选明细中有未保存的纪录，请保存后再生成凭证
		if(rows[i].editing){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>所选明细中有未保存的纪录，请保存后再生成凭证！','error');
			return;
		}
		//4、所选明细的采购类型是否已选择，如果否则提示：采购类型为空，请选择采购类型后再生成凭证。
		if( checkEm(rows[i].purchase_type_id) ){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>所选明细中存在无采购类型的数据，不可生成凭证，请选择采购类型后重新生成！','error');
			return;
		}
		//1：检查是否有库存管制的采购类型，且未入库不=0的，如果有则提示：存在未入库的发票，确认是否继续生成凭证
		if(parseInt(rows[i].is_stockcontrol) ==1 && parseFloat(rows[i].un_instock) !=0){
			debugger
			flag = 1;
		}
	}
	if(flag == 1){
		$.messager.confirm('提示','<span  class="hintsp_e">提示</span>存在未入库的发票，确认是否继续生成凭证?',function(r){
			if (r){
				voucherCreate(obj);		//生成凭证
			}else{
				return;
			}
		});
	}else{
		voucherCreate(obj);
	}
	
	
}

/**
 * 撤账前验证
 */
function beforeVoucherRevoke(obj){
	var rows = $(obj.position).datagrid('getSelections');	//所选数据
	if(rows.length == 0){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>请先选择数据！','error');
		return;
	}
	voucherRevoke(obj);		//撤账
}

//通用撤账后触发方法
function afterVoucherRevokeFun(){}
//通用生成凭证后触发方法
function afterVoucherCreateFun(){}

//导出
function ExportExcel(){
	var obj=new Object;
	obj['renderid']='#gridlist';
	obj['title'] = '采购发票';
	obj['controllername']='purchaseInvoice';
	obj['cs']='left(a.accountperiod,7)="'+$('#year').val()+'-'+$('#month').val()+'"';
	toExcel(obj);
}





//新增的打印配置、批量打印、预览..............
//选择模板名称后
//模板修改确定
function templateSure(){
	//将毫米转化成px  
	var pheight = $('#divMMHeight').height(), pwidth = $('#divMMHeight')
	.width();
	var height=$("#height").val();
	var width=$("#width").val()
	var hs=$("#hs").val();
	var page_bottom=$("#page_bottom").val();
	var page_left=$("#page_left").val();
	var page_right=$("#page_right").val();
	var page_top=$("#page_top").val();
	var pagelx=$("#pagelx").val();
	var rowheight=$("#rowheight").val();
	var title='采购入库单';
	var pheight = $('#divMMHeight').height(), pwidth = $('#divMMHeight').width();
	var rowheight=$("#rowheight").val();
	
	var goods_namewidth = $("#goods_namewidth").val() || 0;
	var modelwidth = $("#modelwidth").val() || 0;
	var unitwidth=$("#unitwidth").val() || 0;
	var quantitywidth=$("#quantitywidth").val() || 0;
	var pricewidth=$("#pricewidth").val() || 0;
	var moneywidth=$("#moneywidth").val() || 0;
	var taxratewidth=$("#taxratewidth").val() || 0;
	var tax_moneywidth=$("#tax_moneywidth").val() || 0;
	var taxtotalwidth=$("#taxtotalwidth").val() || 0;
	
	var goods_nameSel=$("#goods_nameSel").val();
	var modelSel=$("#modelSel").val();
	var unitSel=$("#unitSel").val();
	var quantitySel=$("#quantitySel").val();
	var priceSel=$("#priceSel").val();
	var taxrateSel=$("#taxrateSel").val();
	var tax_moneySel=$("#tax_moneySel").val();
	
	var taxtotalSel=$("#taxtotalSel").val();
	var moneySel=$("#moneySel").val();
	var menucode="purchaselist";
	var name=/* $('#name').val() */$("#temp_name").combobox("getValue");
	var tdf=1;

	var html=$('#html').val();

	var widthp = ((parseInt(goods_namewidth)+parseInt(modelwidth)+parseInt(unitwidth)+parseInt(quantitywidth)
			+parseInt(pricewidth)+parseInt(moneywidth)+parseInt(taxratewidth)
			+parseInt(tax_moneywidth)+parseInt(taxtotalwidth)) * pwidth)+parseInt(30)
	
	var columnstr="goods_namewidth:"+goods_namewidth+",modelwidth:"+modelwidth
	+",unitwidth:"+unitwidth+",quantitywidth:"+quantitywidth+",pricewidth:"+pricewidth
	+",moneywidth:"+moneywidth+",taxratewidth:"+taxratewidth+",tax_moneywidth:"+tax_moneywidth
	+",taxtotalwidth:"+taxtotalwidth+",goods_nameSel:"+goods_nameSel+",modelSel:"+modelSel
	+",unitSel:"+unitSel+",quantitySel:"+quantitySel+",priceSel:"+priceSel
	+",moneySel:"+moneySel+",taxrateSel:"+taxrateSel+",tax_moneySel:"+tax_moneySel
	+",taxtotalSel:"+taxtotalSel;
	
	var contentstr='<style type="text/css"></style>'
		+'<p style="text-align: center;">'
		+'    <span style="font-size: 20px;">采购入库单</span>'
		+'</p>'   
		+'<p style="width:'+widthp+'px;margin: 0 auto;">'
		+'    <span style="font-size: 12px;"></span><span style="font-size: 12px;width: 100%;display: inline-block;">'
		+'<span style="font-size: 12px;display: block;">'
		+'    供应商：<textarea class="laowang" readonly="readonly" id="supplier_name" '
		+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 280px; height: 15px; border: 1px dashed rgb(0, 0, 0); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;outline: none; margin:  2px 0 0 0;" '
		+'    title="~{main.supplier_name}">供应商</textarea>'
		+'</span><span style="font-size: 12px;display: inline-block;float:right">'
		+'    入库日期：<textarea class="laowang" readonly="readonly" id="make_date" '
		+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 148px; height: 18px; border: 1px dashed rgb(0, 0, 0); font-size: 12px;'
		+'font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 2px 0 0 0;" title="~{main.make_date}">入库日期</textarea>'
		+'</span>'
		+'<span style="font-size: 12px;display: inline-block;float:left;width:61%">'
		+'    <span style="float:left">入库单号：</span>' + '<textarea class="laowang" readonly="readonly" id="code" '
		+'    style="vertical-align: top;float:left; overflow: hidden;display:inline-block;width:80%; padding: 0px; height: 16px; border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
		+'    font-family: &quot;Microsoft YaHei&quot;outline: none; margin:  2px 0 0 0;" title="~{main.code}">入库单号</textarea></span>'
		+'<span style="font-size: 12px;display: inline-block;float:left;width:61%">'
		+'    <span style="float:left">发票号码：</span>' + '<textarea class="laowang" readonly="readonly" id="invoicecode" '
		+'    style="vertical-align: top;float:left; overflow: hidden;display:inline-block;width:80%; padding: 0px; height: 16px; border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
		+'    font-family: &quot;Microsoft YaHei&quot;outline: none; margin:  2px 0 0 0;" title="~{main.invoicecode}">发票号码</textarea></span></span>'
		+'</p>'
		+'<hr/>'
	+'<table style="margin: 0 auto;border: 1px #000 solid;"><tbody style="border: 2px #000 solid;"><tr class="firstRow">';
	
	var firstTr='';
	var secondtr='';
	var hjtr='';
	
	if(goods_nameSel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+goods_namewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">商品名称</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+goods_namewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="goods_name" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+goods_namewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.goods_name}">商品名称</textarea>'
		+'            </td>';
		hjtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+goods_namewidth * pwidth+'px;height:'+rowheight * pwidth +'px; word-break: break-all;" valign="top">'
		+'               &nbsp; &nbsp; &nbsp;合计'
		+'            </td>';
	}
	if(modelSel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+modelwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">规格型号</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+modelwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="model" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+modelwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.model}">规格型号</textarea>'
		+'            </td>';
		hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+modelwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
	}
	if(unitSel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+unitwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">单位</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+unitwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="unit" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+unitwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.unit}">单位</textarea>'
		+'            </td>';
		hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+unitwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
	}
	if(quantitySel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+quantitywidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">数量</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+quantitywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="debitmoney" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+quantitywidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.quantity}">数量</textarea>'
		+'            </td>';
		hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+quantitywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="quantity" '
			+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+quantitywidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
			+'		title="~{mx.quantity}">数量</textarea>'
			+'            </td>'
	}
	if(priceSel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+pricewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">单价</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+pricewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="price" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+pricewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.price}">单价</textarea>'
		+'            </td>';
		hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+pricewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
	}
	
	if(moneySel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">金额</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="money" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; text-align:right;'
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.money}">金额</textarea>'
		+'            </td>';
		hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="money" '
		+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
		+'		title="~{mx.money}">金额</textarea>'
		+'            </td>';
	}
	if(taxrateSel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+taxratewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">税率</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+taxratewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="taxrate" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+pricewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.taxrate}">税率</textarea>'
		+'            </td>';
		hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+taxratewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
	}
	if(tax_moneySel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+tax_moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">税额</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+tax_moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="tax_money" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; text-align:right;'
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+tax_moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.tax_money}">税额</textarea>'
		+'            </td>';
		hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+tax_moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="tax_money" '
		+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+tax_moneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
		+'		title="~{mx.tax_money}">税额</textarea>'
		+'            </td>';
	}
	if(taxtotalSel=='是'){
		firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+taxtotalwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">价税合计</td>';
		secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+taxtotalwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
		+'                <textarea class="laowang" readonly="readonly" id="taxtotal" '
		+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
		+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+taxtotalwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.taxtotal}">价税合计</textarea>'
		+'            </td>';
		hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+taxtotalwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
			+'                <textarea class="laowang" readonly="readonly" id="taxtotal" '
			+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+taxtotalwidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
			+'		title="~{mx.taxtotal}">价税合计</textarea>'
			+'            </td>'
			firstTr +='</tr><tr>';
		secondtr +='</tr><tr>';
		hjtr +='</tr>'
			+'</tbody>'
			+'</table>'
			+'<hr/>'
			+'<p>'
			+'<span style="font-size: 12px;"></span><br/>'
			+'</p>';
	}else{
		firstTr +='</tr><tr>';
		secondtr +='</tr><tr>';
		hjtr +='</tr>'
			+'</tbody>'
			+'</table>'
			+'<hr/>'
			+'<p>'
			+'<span style="font-size: 12px;"></span><br/>'
			+'</p>';
	};
	var content=contentstr + firstTr +secondtr + hjtr;
	debugger;
	//保存
	if(name == ''){
		var name = prompt('请输入文件名', '');
	if(name != null && name != ''){
		//保存模板
		$.post(getUrl('printSet/save'),{menucode:menucode,name:name,title:title,content:content,
												   pagelx:pagelx,width:width,height:height,
												   page_top:page_top,page_bottom:page_bottom,
												   page_left:page_left,page_right:page_right,rowheight:rowheight,pagegs:7,
												   hs:hs,columnstr:columnstr},function(data){
										    if(data.state == 'success'){
										    	$('#printcode').val(data.code);
							    				promptbox('success','操作成功！')
							    			}else{
							    				alert(data.msg);
							    				promptbox('error',data.msg)
							    			}
		})
	}
	}else{
		$.post(getUrl('printSet/edit'),{menucode:menucode,name:name,title:title,content:content,
				   pagelx:pagelx,width:width,height:height,
				   page_top:page_top,page_bottom:page_bottom,
				   page_left:page_left,page_right:page_right,rowheight:rowheight,
			   		hs:hs,columnstr:columnstr},function(data){
			    if(data.state == 'success'){
			    	$('#printcode').val(data.code);
				promptbox('success','操作成功！')
			}else{
				alert(data.msg);
				promptbox('error',data.msg)
			}
})
	}
	$('#printedit_pageId').window('close');
}

//点击取消，关闭弹出框
function closeclearF(){
	$('#printedit_pageId').window('close');
}

//批量打印
function printAndPreview(opertype){
	debugger
	var obj=new Object;
	obj['opertype']=opertype;
	if(opertype == '批量打印'){
		obj['table_name']='t_purchaseinvoice';
		obj['accountperiod'] = $("#year").val()+'-'+$("#month").val();
		obj['source'] = 'purchaseinvoice';
	}
	obj['operpage']='report';
	obj['menucode']='purchaseinvoice';
	obj['controllername'] = 'purchaseinvoice';
	obj['run_method'] = 'findPurchaselistmxByPurchaselistid';
	obj['method_type'] = '1';
	obj['cs'] = '';
	obj['pms'] = {code:'purchaseinvoice', params:'accountperiod:'+$("#year").val()+'-'+$("#month").val()+',table_name:t_purchaseinvoice'};
	obj['id'] = $('#id').val();
	previewClick_report(obj);
}

//temp_name 事件
$("#temp_name").combobox({
	onSelect:function(record){
		$.ajax({
		    url:getUrl('printSet/findByCode.do'),
		    type:'post',
		    async:false,
		    data: {"menucode":"purchaseinvoice","name" :record.text},
		    dataType:'json',
		    success:function(data){
				$("#menucode").val(data.menucode);
				$('#tempid').val(data.id);
				$("#height").val(data.height);
				$("#width").val(data.width);
				$("#hs").val(data.hs);
				$("#page_bottom").val(data.page_bottom);
				$("#page_left").val(data.page_left);
				$("#page_right").val(data.page_right);
				$("#page_top").val(data.page_top);
				$("#pagelx").val(data.pagelx);
				$("#rowheight").val(data.rowheight);
				$("#title").val(data.title);
				
				$("#content").val(data.content);
				$("#html").val(data.html);
				var columnstr=data.columnstr;
				var cols=columnstr.split(',');
				for(var i=0;i<cols.length;i++){
					var str=cols[i].split(':');
					$("#"+str[0]).val(str[1]);
				}
		    }
		})
	}
})

$("#pagelx").change(function(e){
	var lx= $("#pagelx").val();
	if(lx=="A5(横向)"){
		$("#height").val(148);
		$("#width").val(210);
		$("#height").attr("readOnly",true);
		$("#width").attr("readOnly",true);
	}else{
		$("#height").val(297);
		$("#width").val(210);
		if(lx=="A4"){
			$("#height").attr("readOnly",true);
			$("#width").attr("readOnly",true);
		}else{
			$("#height").attr("readOnly",false);
			$("#width").attr("readOnly",false);
		}
	}
	
})













</script>

</body>
</html>