<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
	
		<!-- 顶部功能栏 -->
		<%-- <div id="eastToolDiv" data-options="region:'north'" style="height:54px;border:1px #cedae4 solid;">
			<span class="invo_title">会计年度：</span>
			<input type='text' id="year" class="invo_title_year" value="${year}" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%y',dateFmt:'yyyy',onpicked:accountperiodChange})" />
			<input type="hidden" id="month" value="${month}" />
		</div> --%>
		
		<!-- 左侧功能栏 -->
		<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background:#fff;">
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="addFun({width:900,height:374})">新增</a>
			<a class="easyui-linkbutton compile-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:900,height:374})">编辑</a>
			<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')">删除</a>
		</div>
		
		<!-- 主面板 -->
		<div class="parceldivs" style="width: 725px;height: 337px;">
			<div class="parceldiv_tow" style="width: 725px;height: 337px;">
				<div class="bgdivtit">
					<span class="invo_title">会计年度：</span>
					<input type='text' id="year" class="invo_title_year" value="${year}" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%y',dateFmt:'yyyy',onpicked:accountperiodChange})" />
					<input type="hidden" id="month" value="${month}" />
				</div>
				<div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);height: 293px;background: #fff;"></div>
			</div>
		</div>
		
		<!-- 底部功能栏 -->
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('fixedassetsdepreciation')">关闭</a>
		</div>
		
		<!-- 编辑弹窗 -->
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true">
				<input type="hidden" id="id" name="id">
				<input type="hidden" id="voucher_id" name="voucher_id">
				<input type="hidden" id="fixedassetsdepreciationmx" name="fixedassetsdepreciationmx">
				<input type="hidden" id="dep" name="dep">
				<ul class="cost_ul" style="height: 42px;border-bottom:0;width: 896px;">
				    <li>
				        <p>折旧月份：</p>
				        <input class="easyui-textbox" type="text" id="fiscal_period" name="fiscal_period" readonly="readonly" data-options="required:true,validType:['repeat[\'fixedassetsdepreciation\',\'t_fixedassetsdepreciation\',\'fiscal_period\',\'pageForm\']']" style="width:120px;height:27px"/>
				    </li>
				    <li>
				        <p>制单人：&nbsp;&nbsp;&nbsp;</p>
				        <input type="hidden" id="createby_id" name="createby_id">
				        <input class="easyui-textbox" type="text" name="createby_name" readonly="readonly" style="width:120px;height:27px"/>
				    </li>
				    <li style="width: 242px;">
				        <p>制单日期：</p>
				        <input class="easyui-datebox" id="accountperiod" name="accountperiod" data-options="required:true" style="width:120px;height:27px"/>
				    </li>
				</ul>
		    </form>
			<div class="parceldivsc">
				<div class="parceldiv_tow" style="height: 249px; width: 898px;">
					<div id="mxList" style="height: 249px;"></div>
				</div>
			</div>
		    <div style="text-align:center;">
		        <div style="float: left; padding: 6px 0 0 0;" class="btnsdiv">
			        <a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="revokeVoucherBtn" onclick="beforeVoucherRevoke({code:'fixedassetsdepreciation'})">撤账</a>
			        <a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="createVoucherBtn" onclick="beforeVoucherCreate({code:'fixedassetsdepreciation'})" style="width: 74px;" >生成凭证</a>
			        <a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="deleteBtn" onclick="deleteData()">删除</a>
			        <a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="editBtn" onclick="editData()">修改</a>
			        <a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="saveBtn" onclick="submitForm({code:'fixedassetsdepreciation',type:2,renderid:'#gridlist',noClose:false})">保存</a>
		        </div>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn closeNew_btn" onclick="clearForm()">关闭</a>
		    </div>
		</div>
		
	</div>

<script type="text/javascript">

$(function(){
	var obj = new Object();
	obj["position"] = '#gridlist';
	obj["idField"] = 'id';
	obj["url"] = 'fixedassetsdepreciation/list';
	obj["columns"] = [[
						{field:'id',title:'id',hidden:true},
						{field:'accountperiod',title:'折旧日期',align: 'left', halign: 'center',width:114},
						{field:'fiscal_period',title:'折旧月份',align: 'left', halign: 'center',width:114},
						{field:'dep',title:'折旧金额',align: 'right', halign: 'center',width:134,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'voucher_number',title:'凭证号码',align: 'left', halign: 'center',width:114},
						{field:'createby_name',title:'制单人',align: 'left', halign: 'center',width:114},
						{field:'accountperiod',title:'制单日期',align: 'left', halign: 'center',width:114}
					]];
	obj["listDbClickFun"] = listDbClickFun;
	obj["pagination"] = false;
	Grid.list_grid(obj);
	
	var refer_obj = new Object();
	refer_obj["position"] = '#mxList';
	refer_obj["idField"] = 'item';
	refer_obj["columns"] = [[
							{field:'mxid',title:'mxid',hidden:true},
							{field:'cardid',title:'cardid',hidden:true},
							{field:'code', title:'资产编号',align: 'left', halign: 'center',width:110},
							{field:'name', title:'资产名称',align: 'left', halign: 'center',width:120},
							{field:'ori_value',title:'原值',align: 'right', halign: 'center',width:125,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							},
							{field:'mon_dep_amount',title:'本月折旧',align: 'right', halign: 'center',width:125,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							},
							{field:'dep_cum',title:'累计折旧',align: 'right', halign: 'center',width:125,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							},
							{field:'net_value',title:'资产净值',align: 'right', halign: 'center',width:122,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							},
							{field:'dep_cost_id',title:'dep_cost_id',hidden:true},
							{field:'dep_cost_name', title:'费用科目',align: 'left', halign: 'center',width:140},
						]];
	refer_obj["url"] = 'fixedassetsdepreciation/mxList';
	refer_obj["listDbClickFun"] = function(){};
	refer_obj["pagination"] = false;
	Grid.list_grid(refer_obj);
});

//双击行的触发事件
function listDbClickFun(row){
	$('#pageForm').form('load', row);
	$('#edit_pageId').window({title:'编辑',width:900,height:374});
	$('#edit_pageId').window('open');
	$.ajax({
	    url: getUrl('fixedassetsdepreciation/goEdit'),
	    type: 'post', async: true, data:{id:row.id}, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
   				$('#pageForm').form('load', data);
    			$('#mxList').datagrid('reload',{id:row.id, accountperiod:row.fiscal_period});
			}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
		}
    });
	
	//双击编辑时控制btn
	disableControl(['#saveBtn']);
	enableControl(['#editBtn','#deleteDisposeBtn']);
	if(checkEm(row.voucher_id)){
		disableControl(['#revokeVoucherBtn']);
		enableControl(['#createVoucherBtn']);
	}else{
		enableControl(['#revokeVoucherBtn']);
		disableControl(['#createVoucherBtn']);
	}
	
	//双击编辑时控制控件
	cannotEditable();
}

//编辑页面 可编辑状态
function canEditable(){
	$('#accountperiod').datebox('readonly',false);
}

//编辑页面 不可编辑状态
function cannotEditable(){
	$('#accountperiod').datebox('readonly',true);
}

//新增按钮点击方法
function addFun(){
	var exist = true;
	$.ajax({
	    url: getUrl('fixedassetsdepreciation/checkAdd'),
	    type: 'post', async: false, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
   				exist = data.exist;
			}else {
				$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');
			}
		}
    });
	if(exist){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前会计期间已经计提折旧，不可重复计提！','error');
		return;
	}
	ButtonFun.addFun({width:900,height:404})
}

//新增按钮点击后通用触发方法
function afterAddFun(){
	$.ajax({
	    url: getUrl('fixedassetsdepreciation/goAdd'),
	    type: 'post', async: false, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
   				$('#pageForm').form('load', data);
    			$('#mxList').datagrid('reload',{id:'', accountperiod:''});
			}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
		}
    });
	
	//点击新增时控制btn
	enableControl(['#saveBtn']);
	disableControl(['#editBtn','#deleteBtn','#createVoucherBtn','#revokeVoucherBtn']);
	
	//点击新增时控制控件
	canEditable();
}

//编辑通用赋值前触发方法
function beforeEditFun(node){
	//点击编辑时控制btn
	disableControl(['#saveBtn']);
	enableControl(['#editBtn','#deleteBtn']);
	
	if(checkEm(node.voucher_id)){
		disableControl(['#revokeVoucherBtn']);
		enableControl(['#createVoucherBtn']);
	}else{
		enableControl(['#revokeVoucherBtn']);
		disableControl(['#createVoucherBtn']);
	}
	
	//点击编辑时控制控件
	cannotEditable();
}

//编辑通用赋值后触发方法
function afterEditFun(node){
	$.ajax({
	    url: getUrl('fixedassetsdepreciation/goEdit'),
	    type: 'post', async: true, data:{id:node.id}, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
   				$('#pageForm').form('load', data);
    			$('#mxList').datagrid('reload',{id:node.id, accountperiod:node.fiscal_period});
			}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
		}
    });
}

//通用表单提交前触发方法
function beforeSubmitFormFun(){
	var data = $('#mxList').datagrid('getData');
	var rows = data.rows;
	$('#fixedassetsdepreciationmx').val(JSON.stringify(rows));
	var dep = calTotalByCol('#mxList','mon_dep_amount');
	$('#dep').val(dep);
	return true;
}

//通用保存后触发方法
function afterSaveFun(id){
	$('#id').val(id);	//赋值id
	
	//保存后时控制btn
	disableControl(['#saveBtn','#revokeVoucherBtn']);
	enableControl(['#editBtn','#deleteBtn','#createVoucherBtn']);
	
	//保存后控制控件
	cannotEditable();
}

//通用撤账后触发方法
function afterVoucherRevokeFun(){
	var id = $('#id').val();
	$('#pageForm').form('load',getUrl('fixedassetsdepreciation/findById?id='+id));
	disableControl(['#revokeVoucherBtn']);
	enableControl(['#createVoucherBtn']);
}

//通用生成凭证后触发方法
function afterVoucherCreateFun(){
	var id = $('#id').val();
	$('#pageForm').form('load',getUrl('fixedassetsdepreciation/findById?id='+id));
	disableControl(['#createVoucherBtn']);
	enableControl(['#revokeVoucherBtn']);
}

//通用删除前触发方法
function beforeDeleteFun(rows){
	//1、所选择的单据的折旧日期是否已生成凭证，如果是则提示：当前折旧单据已生成凭证，不可删除
	if( !checkEm(rows.voucher_id) ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前折旧单据已生成凭证，不可删除！','error');
		return false;
	}
	//2、所选择的单据的折旧日期是否为记账月份前的日期，如果是则提示：当前折旧单据的折旧日期已结账，不可删除
	var accinfo = findAccInfo();
	if( rows.accountperiod < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前折旧单据的折旧日期（'+accountperiod+'）已结账，不可删除！','error');
		return false;
	}
	return true;
}
function closeOrDelete(operType){
	var nodes=$('#gridlist').datagrid('getSelected');
	var voucher_id=nodes.voucher_id;
	if(!checkEm(voucher_id)){
		$.messager.alert('提示','<span class="hintsp_w">提示</span>当前单据已生成凭证，不可删除！','info');
		return;
	}
	ButtonFun.deleteFun({code:'fixedassetsdepreciation',renderid:'#gridlist'});
}

//开启修改
function editData(){
	var voucher_id = $('#voucher_id').val();
	//1、所选择的单据的折旧日期是否已生成凭证，如果是则提示：当前折旧单据已生成凭证，不可修改
	if( !checkEm(voucher_id) ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前折旧单据已生成凭证，不可修改！','error');
		return;
	}
	//2、所选择的单据的折旧日期是否为记账月份前的日期，如果是则提示：当前折旧单据的折旧日期已结账，不可修改
	var accountperiod = $('#accountperiod').datebox('getValue');
	var accinfo = findAccInfo();
	if( accountperiod < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前折旧单据的折旧日期（'+accountperiod+'）已结账，不可修改！','error');
		return;
	}
	
	//点击修改时控制btn
	enableControl(['#saveBtn','#deleteBtn']);
	disableControl(['#editBtn']);
	if(checkEm($('#voucher_id').val())){
		disableControl(['#revokeVoucherBtn']);
		enableControl(['#createVoucherBtn']);
	}else{
		enableControl(['#revokeVoucherBtn']);
		disableControl(['#createVoucherBtn']);
	}
	
	//点击修改时控制控件
	canEditable();
}

//删除数据
function deleteData(){
	var voucher_id = $('#voucher_id').val();
	//1、所选择的单据的折旧日期是否已生成凭证，如果是则提示：当前折旧单据已生成凭证，不可删除
	if( !checkEm(voucher_id) ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前折旧单据已生成凭证，不可删除！','error');
		return;
	}
	//2、所选择的单据的折旧日期是否为记账月份前的日期，如果是则提示：当前折旧单据的折旧日期已结账，不可删除
	var accountperiod = $('#accountperiod').datebox('getValue');
	var accinfo = findAccInfo();
	if( accountperiod < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前折旧单据的折旧日期（'+accountperiod+'）已结账，不可删除！','error');
		return;
	}
	$.messager.confirm('提示','<span class="hintsp_w">提示</span>确定要删除吗?',function(r){
		if (r){
			$.ajax({
			    url: getUrl('fixedassetsdepreciation/delete'),
			    type: 'post', async: false, data: {id:$('#id').val()}, dataType:'json',
			    success:function(data){
		   			if(data.state == 'success'){
		    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>删除成功！',showType:'slide',timeout:1000,style:{
		    				right : '', bottom : '',
		    				top : document.body.scrollTop + document.documentElement.scrollTop
		    			}}); */
		    			promptbox('success','删除成功！');
		    			$('#gridlist').datagrid('reload');
		    			$('#edit_pageId').window('close');
					}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
				}
		    });
		}
	});
}

//切换会计年度
function accountperiodChange(){
	var year = $('#year').val();
	$('#gridlist').datagrid('load',{ accountperiod : year });
}

/**
 * 生成凭证前验证
 */
function beforeVoucherCreate(obj){
	//1、所选择的明细的记账日期是否为记账月份前的日期，如果是则提示：所选明细的记账日期已结账，不可生成凭证
	var year = $('#year').val();
	var month = $('#month').val();
	var accinfo = findAccInfo();
	if( (year + '-' + month) < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前会计期间（'+year + '-' + month+'）已结账，不可生成凭证！','error');
		return;
	}
	
	voucherCreate(obj);		//生成凭证
}

/**
 * 撤账前验证
 */
function beforeVoucherRevoke(obj){
	voucherRevoke(obj);		//撤账
}

</script>

</body>
</html>