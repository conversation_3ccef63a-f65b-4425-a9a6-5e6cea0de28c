package org.newstanding.service.invoicing;

import java.io.File;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.common.utils.excel.XLSXCovertCSVReader;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.financialhandle.FillvouchersService;
import org.newstanding.service.systemset.AccountInfoService;
import org.newstanding.service.systemset.MenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;


@Service("purchaselistService")
public class PurchaselistService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private AccountInfoService accountInfoService;
	@Autowired
	private FillvouchersService fillvouchersService;
	@Autowired
	private MenuService menuService;;
	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			Long purchaselistid = 0L;
			int count = 0;
			if (!"[]".equals(pd.getString("purchaselistmx")) && !"".equals(pd.getString("purchaselistmx"))) {
				count = dao.save("PurchaselistMapper.save", pd);
				purchaselistid = Long.parseLong(pd.get("id").toString());
				saveTempl(purchaselistid,"Purchaselist", pd,0);
				}else{
					resPd.put("state", "error");
					return resPd;
				}
			if (count > 0) {
				String accountperiod = getAccountperiod(pd);
				pd.put("accountperiod", accountperiod);
				pd.put("menu_code", "purchaselist");
				 menuService.getCode1(pd);
				resPd.put("id",purchaselistid);
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	
	public List<PageData> findKcByid(PageData pd) throws Exception {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PurchaselistMapper.findKcByid", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			dao.delete("PurchaselistMapper.deletePurchaselistmxByPurchaselistid", pd);
			int count = dao.delete("PurchaselistMapper.delete", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	public PageData deleteAll(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			dao.delete("PurchaselistMapper.deletePurchaselistmxByPurchaselistids", pd);
			int count = dao.delete("PurchaselistMapper.deleteAll", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			int count = dao.update("PurchaselistMapper.edit", pd);
			Long purchaselistid = Long.parseLong(pd.get("id").toString());
			if (StringUtils.isUpdate(pd.getString("purchaselistmx"))) {
				editTempl(purchaselistid, "Purchaselist", pd, 0);
			}else if(StringUtils.isDelete(pd.getString("purchaselistmx"))){
				dao.delete("PurchaselistMapper.deletePurchaselistmxByPurchaselistid", pd);
			}
			
			if (!"[]".equals(pd.getString("purchaselistmx")) && !"".equals(pd.getString("purchaselistmx"))) {
				List<PageData> mxList= new ArrayList<>();
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(pd.getString("purchaselistmx"));
				for (Object o : jay) {
					PageData mxpd = new PageData(JsonUtils.strToMap(o.toString()));
					mxList.add(mxpd);
				}
				if(mxList.size()>0) {
					pd.put("addList", mxList);
					//删除库存中的  数据
					pd.put("purchaselistid", purchaselistid);
					dao.delete("PurchaselistMapper.deleteKcBySaleoutid", pd);
					//插入库存中的数据
					dao.save("PurchaselistMapper.insertKcye", pd);
				}
			}
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PurchaselistMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PurchaselistMapper.findById", pd);
			if(resPd !=null){
				PageData accPd=accountInfoService.findAccInfo(pd);
				resPd.put("companyname", accPd.get("compname"));
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findPurchaselistmxByPurchaselistid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PurchaselistMapper.findPurchaselistmxByPurchaselistid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	
	public PageData findPurchaselistmxByInvoicecodeAndAccountperiod(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PurchaselistMapper.findPurchaselistmxByInvoicecodeAndAccountperiod", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findPurchaselistmxByInvoicecode(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PurchaselistMapper.findPurchaselistmxByInvoicecode", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PurchaselistMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 查询是否被其他单据引用
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public List<PageData> checkIsUsed(PageData pd) {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("PurchaselistMapper.checkIsUsed", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	
	/**
	 * 生成凭证
	 * @param pd
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData voucherCreate(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			//验证所选数据是否可审核
			PageData check_before = checkVoucherCreateBefore(pd);
			if( !"success".equals(check_before.get("state").toString()) ){
				return check_before;
			}
			
			PageData vcPd = new PageData();		//存放构造出的凭证数据的容器
			//取凭证模板
			List<PageData> vmlist = getVoucherteMplate(pd.getString("code"),pd);
			//构造凭证数据
			List<PageData> main_list = new ArrayList<PageData>();
			List<PageData> mx_list = new ArrayList<PageData>();
			
			pd.put("createby", getCurrentUserByCatch(pd).getUsername());
			
			switch (pd.getString("type")) {
			case "1":	//逐笔生成
				main_list = (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreate1", pd);
				for (PageData vm : vmlist) {
					vm.put("database", pd.getString("database"));
					vm.put("ids", pd.getString("ids"));
					switch (vm.getString("businessmatters")) {
					case "采购入库": 	//逐笔生成  模板  采购入库  数据
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx1_1", vm) );
						break;
					case "应付账款":		//逐笔生成  模板  应付账款  数据
						/*	科目有辅助核算，并且与模版给定的辅助核算相符，才生成辅助核算明细	*/
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx1_3", vm) );
						break;
					case "冲回在途":		//逐笔生成  模板  冲回在途  数据
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx1_2", vm) );
						break;
					default:
						break;
					}
				}
				break;
			case "2":	//按时间汇总（并汇总科目）
				main_list = (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreate2", pd);
				for (PageData vm : vmlist) {
					vm.put("database", pd.getString("database"));
					vm.put("ids", pd.getString("ids"));
					vm.put("day", pd.getString("day"));
					switch (vm.getString("businessmatters")) {
					case "采购入库": 	//按时间汇总  模板  采购入库  数据
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx2_1", vm) );
						break;
					case "冲回在途":		//按时间汇总  模板  进项税金  数据
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx2_2", vm) );
						break;
					case "应付账款":		//逐笔生成  模板  应付账款  数据
						/*	科目有辅助核算，并且与模版给定的辅助核算相符，才生成辅助核算明细	*/
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx1_3", vm) );
						break;
					default:
						break;
					}
				}
				break;
			case "4":	//全月汇总（并汇总科目）
				main_list = (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreate3", pd);
				for (PageData vm : vmlist) {
					vm.put("database", pd.getString("database"));
					vm.put("ids", pd.getString("ids"));
					switch (vm.getString("businessmatters")) {
					case "采购入库": 	//全月汇总  模板  采购入库  数据
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx3_1", vm) );
						break;
					case "冲回在途":		//全月汇总  模板  进项税金  数据
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx3_2", vm) );
						break;
					case "应付账款":		//逐笔生成  模板  应付账款  数据
						/*	科目有辅助核算，并且与模版给定的辅助核算相符，才生成辅助核算明细	*/
						mx_list.addAll( (List<PageData>) dao.findForList("PurchaselistMapper.voucherCreateMx1_3", vm) );
						break;
					default:
						break;
					}
				}
				break;
			}
			vcPd.put("dataList", main_list);
			vcPd.put("datamxList", mx_list);
			if(main_list.size()==0) {
				resPd.put("state", "err");
				resPd.put("message", "所选入库单据没有要生成凭证的数据！");
				return resPd;
			}
			vcPd.put("database", pd.getString("database"));
			vcPd.put("accountperiod", pd.getString("accountperiod"));
			
			//验证 生成凭证数据
			PageData check_data = checkVoucherCreateData(vcPd);
			if( !"success".equals(check_data.get("state").toString()) ){
				return check_data;
			}
			
			//生成凭证
			fillvouchersService.saveMore(vcPd);
			//生成凭证后回写ID
			dao.update("PurchaselistMapper.voucherCreateAfter", pd);
			//将生成的凭证状态改为可用
			fillvouchersService.saveMoreFinsh(vcPd);
			
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 生成凭证 验证是否可生成
	 * 		1. 验证是否存在单据已审核
	 * 
	 * @param pd
	 * @throws Exception
	 */
	public PageData checkVoucherCreateBefore(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			//1.验证是否存在单据已审核
			PageData check = (PageData) dao.findForObject("PurchaselistMapper.checkVoucherCreateBefore", pd);
			if( !"0".equals(check.get("num").toString()) ){
				resPd.put("state", "err");
				resPd.put("message", "所选单据中存在单据已生成凭证！");
				return resPd;
			}
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 撤账
	 * @param pd
	 * @throws Exception
	 */
	public PageData voucherRevoke(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			pd.put("accountperiod", getAccountperiod(pd));	//获取当前记账日期
			
			//查询ids对应的凭证id
			PageData vs = (PageData) dao.findForObject("PurchaselistMapper.findVoucherId", pd);
			
			pd.put("DATA_IDS", vs.getString("ids"));
			
			//验证所选数据的凭证是否可撤账
			PageData check = checkVoucherRevoke(pd);
			if( !"success".equals(check.get("state").toString()) ){
				return check;
			}
			
			//撤销凭证
			fillvouchersService.revokeMore(pd);
			//撤销凭证后清除单据凭证ID
			dao.update("PurchaselistMapper.voucherRevokeAfter", pd);
			
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	public List<PageData> getExcelData(PageData pd) throws Exception {
		return (List<PageData>) dao.findForList("PurchaselistMapper.listAll", pd);
	}
	
	
	
	/**
	 * 导入采购入库单  同时核销采购发票方法
	 * @param pd
	 * @param f
	 * @return
	 */
	public PageData saveImportExcel(PageData pd,File f) {
		PageData resPd = new PageData();
		String accountperiod = getAccountperiod(pd);
		pd.put("accountperiod", accountperiod);
		try {
			  //解析工作表
	    	List<String[]> dataList = XLSXCovertCSVReader.readerExcel(f.getAbsolutePath(), "采购入库单", 12);
	    	
	    	if(dataList == null || dataList.size() == 0){
	    		resPd.put("message", "无导入数据，请检查导入文件工作表名称是否正确");
	    		throw new RuntimeException();
	    	}
	    	dataList.remove(0);	//去除标题栏]
	    	PageData response =new PageData();
	    	for(int i = 0;i<dataList.size();i++) {
	    	
	    		String[] dataPd = dataList.get(i);
	    		String[] lastPd = new String[12];
	    		if(i!=0) {
	    			lastPd = dataList.get(i-1);
	    		}
	    		if(dataPd.length !=12) {
		    		resPd.put("message", "导入数据的列数不正确,应为12列。请检查后重试");
		    		throw new RuntimeException();
	    		}
	    		if(dataPd[0] == null) {
	    			pd.put("menu_code", "purchaselist");
					PageData d = menuService.getCode(pd);
	    			dataPd[0]=d.get("addcode").toString();
	    		}
	    	
	    		//验证数量不能为空
	    		if(dataPd[6] == null) {
	    			resPd.put("message", "导入数据的第"+(i+1)+"行没有找到数量信息。请检查后重试");
	        		throw new RuntimeException();
	    		}
	    		
	    		//[PC201901230001, 2019-01-23, 广州南宁和美科技有限公司, 测试商品1, 1, 88, 88, uiyuit]
	    		//[null, 1111, 北京环岛清障救援有限公司, 6300投影机, 1, 5000, 5000, 10, 500, 5000]
	    		//如果发票号码和上一条相同，直接保存明细，更新主表中的合计金额
	    		
	    		if(dataPd[8] ==null) {
					dataPd[8] ="0";
				}
				if(dataPd[10] ==null) {
					dataPd[10] ="0";
				}
				String taxtotal = CalculateUtil.add(dataPd[8], dataPd[10]);
				String price = CalculateUtil.divide(dataPd[8], dataPd[6]);
				
				
				//如果有商品名称   没有发票号码的话，发票号码，销售日期  客户  同上一条
				if(dataPd[1] ==null && dataPd[4] !=null) {
					dataPd[0] =lastPd[0];
					dataPd[1] =lastPd[1];
					dataPd[2] = lastPd[2];
					dataPd[3] = lastPd[3];
					
				}
				//出库时间为非当前期间，请检查后重新导入
	    		if( !accountperiod.equals(DateUtil.addZeroBeforeMonth(dataPd[2].substring(0, 7)))) {
	    			resPd.put("message", "导入数据的第"+(i+1)+"行入库时间为非当前期间，请检查后重新导入");
		    		throw new RuntimeException();
	    		}
	    		if(dataPd[1] !=null &&lastPd[1] !=null && dataPd[1].equals(lastPd[1])) {
	    			PageData mxPd = new PageData();
	    			//明细数据
	    			mxPd.putAll(pd);
					mxPd.put("goods_name",dataPd[4].trim());
					PageData goodsPd = findGoodsByName(mxPd);
					if(goodsPd !=null) {
						mxPd.putAll(goodsPd);
					}else {
		        		resPd.put("message", "导入数据的第"+(i+1)+"行没有找到商品档案信息。请检查后重试");
		        		throw new RuntimeException();
					}
					mxPd.put("make_date", dataPd[2]);
					mxPd.put("code", dataPd[0]);
					mxPd.put("model",dataPd[5]);
					
					mxPd.put("quantity",dataPd[6]);
					mxPd.put("price",price);
					mxPd.put("money",dataPd[8]==null?0:dataPd[8]);
					mxPd.put("taxrate",dataPd[9]==null?0:dataPd[9]);
					mxPd.put("tax_money",dataPd[10]==null?0:dataPd[10]);
					mxPd.put("taxtotal",taxtotal);
					mxPd.put("purchaselistid", response.get("id"));
					dao.save("PurchaselistMapper.saveImportmx", mxPd);
					
					//更新主表金额
					dao.update("PurchaselistMapper.updateImportmxMainMoney", mxPd);
	    		}else {
	    			//入库单数据构造保存开始
					//构造采购入库单的主表和明细表数据
		    		
		    		PageData mainPd = new PageData();
		    		PageData mxPd = new PageData();
		    		mainPd.putAll(pd);
		    		mxPd.putAll(pd);
		    		List<PageData> mxList = new ArrayList<>();
					mainPd.put("code", dataPd[0]);
					//验证code是否重复
					PageData purchaselistPd = findPuchaselistByCode(mainPd);
					if(purchaselistPd != null ) {
		        		resPd.put("message", "导入数据的第"+(i+1)+"行入库单号已经存在。请检查后重试");
		        		throw new RuntimeException();
					}
					mainPd.put("make_date", dataPd[2]);
					mainPd.put("account_period", dataPd[2].substring(0, 7));
					
					//检查发票号码是否有对应的采购发票单据
					if(dataPd[1]!=null) {
						mainPd.put("invoicecode", dataPd[1].trim());
						PageData purchaseinvoicePd = findPuchaseInvoiceByInvoicecode(mainPd);
						if(purchaseinvoicePd == null ) {
			        		resPd.put("message", "导入数据的第"+(i+1)+"行采购发票号码在采购发票单中不存在。请检查后重试");
			        		throw new RuntimeException();
						}
					}
					
					mainPd.put("supplier_name", dataPd[3].trim());
					PageData supplierPd = findSupplieridByName(mainPd);
					if(supplierPd !=null) {
						mainPd.putAll(supplierPd);
					}else {
		        		resPd.put("message", "导入数据的第"+(i+1)+"行没有找到供应商档案信息或被禁用。请检查后重试");
		        		throw new RuntimeException();
					}
					
					//明细数据
					mxPd.put("goods_name",dataPd[4].trim());
					PageData goodsPd = findGoodsByName(mxPd);
					if(goodsPd !=null) {
						mxPd.putAll(goodsPd);
					}else {
		        		resPd.put("message", "导入数据的第"+(i+1)+"行没有找到商品档案信息或被禁用。请检查后重试");
		        		throw new RuntimeException();
					}
					mxPd.put("model",dataPd[5]);
					mxPd.put("quantity",dataPd[6]);
					mxPd.put("price",price);
					mxPd.put("money",dataPd[8]==null?0:dataPd[8]);
					mxPd.put("taxrate",dataPd[9]==null?0:dataPd[9]);
					mxPd.put("tax_money",dataPd[10]==null?0:dataPd[10]);
					mxPd.put("taxtotal",taxtotal);
					mainPd.put("total_money",dataPd[8]);
					mainPd.put("total_tax_money",dataPd[10]);
					mainPd.put("total_taxtotal",taxtotal);
					mxList.add(mxPd);
					mainPd.put("purchaselistmx", JsonUtils.PageDataToJSONArray(mxList).toString());
					
					response = save(mainPd);
	    		}
	    		if(!"success".equals(response.get("state"))) {
	        		resPd.put("message", "导入数据的第"+(i+1)+"行保存采购入库单失败。请联系管理员");
	        		throw new RuntimeException();
				}
				
				if("success".equals(response.get("state"))) {
					resPd.put("state", "success");
	        		resPd.put("message", "导入成功");
				}else {
	        		resPd.put("message", "导入数据的第"+(i+1)+"行保存核销失败。未知错误，请联系管理员");
	        		throw new RuntimeException();
				}
	    	}
	    	
		} catch (Exception e) {
			e.printStackTrace();
			resPd.put("state", "err");
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
    		return resPd;
		}
    	return resPd;
	}
	
	/**
	 * 导入时，查找采购发票
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findPuchaseInvoiceByInvoicecode(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PurchaseInvoiceMapper.findPuchaseInvoiceByInvoicecode", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	/**
	 * 导入时，根据供应商名称查询供应商id
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findSupplieridByName(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PurchaseInvoiceMapper.findSupplieridByName", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 导入时，根据商品名称查询商品id
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findGoodsByName(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PurchaseInvoiceMapper.findGoodsidByName", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	//查询入库单号是否已存在
	public PageData findPuchaselistByCode(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PurchaseInvoiceMapper.findPuchaselistByCode", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	//导入时，根据发票号码查找对应的单据
	
	public PageData findByCode(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PurchaseInvoiceMapper.findByCode", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	public PageData findPurchaseByPurchaseid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("PurchaseInvoiceMapper.findPurchaseByPurchaseid", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
}
