package org.newstanding.service.salary;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.financialhandle.FillvouchersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("caloutsourceService")
public class CaloutsourceService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private FillvouchersService fillvouchersService;
	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.save("CaloutsourceMapper.save", pd);
			String id = pd.getString("id");
			
			String mxdata = pd.get("caloutsourcemxdata").toString();
			List<PageData> mxdatalist = JsonUtils.strToListPd(mxdata);
			
			String data = pd.get("caloutsourcemx").toString();
			Map<String, Object> map = JsonUtils.strToMap(data);
			
			List<PageData> itemlist = JsonUtils.strToListPd(map.get("itemNames").toString());
			
			List<PageData> mxlist = new ArrayList<PageData>();
			for (PageData mx : mxdatalist) {
				for (PageData item : itemlist) {
					String code = item.get("insuranceitemscode").toString();
					PageData tmp = new PageData();
					tmp.put("caloutsourceid", id);
					tmp.put("empdocid", mx.get("empdocid").toString());
					tmp.put("item", code);
					if(mx.get(code) != null){
						tmp.put("money", mx.get(code).toString());
					}else{
						tmp.put("money", 0);
					}
					mxlist.add(tmp);
				}
			}
			
			pd.put("mxList", mxlist);
			dao.save("CaloutsourceMapper.saveCaloutsourcemx", pd);
			
			if (count > 0) {
				resPd.put("id", id);
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.delete("CaloutsourceMapper.delete", pd);
			dao.delete("CaloutsourceMapper.deleteCaloutsourcemx", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			String id = pd.getString("id");
			int count = dao.update("CaloutsourceMapper.edit", pd);
			
			pd.put("DATA_IDS", id);
			dao.delete("CaloutsourceMapper.deleteCaloutsourcemx", pd);
			
			String mxdata = pd.get("caloutsourcemxdata").toString();
			List<PageData> mxdatalist = JsonUtils.strToListPd(mxdata);
			
			String data = pd.get("caloutsourcemx").toString();
			Map<String, Object> map = JsonUtils.strToMap(data);
			
			List<PageData> itemlist = JsonUtils.strToListPd(map.get("itemNames").toString());
			
			List<PageData> mxlist = new ArrayList<PageData>();
			for (PageData mx : mxdatalist) {
				for (PageData item : itemlist) {
					String code = item.get("insuranceitemscode").toString();
					PageData tmp = new PageData();
					tmp.put("caloutsourceid", id);
					tmp.put("empdocid", mx.get("empdocid").toString());
					tmp.put("item", code);
					if(mx.get(code) != null){
						tmp.put("money", mx.get(code).toString());
					}else{
						tmp.put("money", 0);
					}
					mxlist.add(tmp);
				}
			}
			
			pd.put("mxList", mxlist);
			dao.save("CaloutsourceMapper.saveCaloutsourcemx", pd);
			
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CaloutsourceMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CaloutsourceMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	/**
	 * 查找最大月份
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findMaxMonth(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CaloutsourceMapper.findMaxMonth", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	/**
	 * 计算外包保险
	 * @param pd
	 * @return
	 */
	public PageData calinsurance(PageData pd){
		PageData resPd=new PageData();
		try {
			//获取外包保险维护中的  职员 中外包保险类型为  正常的职员 
			List<PageData> emplist =  (List<PageData>)dao.findForList("CaloutsourceMapper.fixedinsuempdoclist", pd);
			//获取 外包保险信息维护  中所有的 项目
			List<PageData> itemsList = findItems(pd);
			//在单个职员pd中插入此职员的所有  外包保险项目
			if(emplist.size()>0){
				for (PageData empd : emplist) {
					//获取职员所有保险项的值
					empd.put("database", getDatabase(pd));
					List<PageData> levellist = findEmpdocSalaryItems(empd);
					if(!levellist.isEmpty()){
						for (PageData pageData : levellist) {
							for (int j = 0; j < itemsList.size(); j++) {
								if(itemsList.get(j).get("insuranceitemscode").equals(pageData.get("insuranceitemscode"))){
									empd.put("basemoney", pageData.get("basemoney").toString());
									empd.put(itemsList.get(j).get("insuranceitemscode").toString(), pageData.get("money").toString());
								}
							}
						}
					}else{
						resPd.put("state","error");
						resPd.put("message","计算存在错误,没有保险信息维护数据,请检查！");
						return resPd;
					}
				}
			}else{
				resPd.put("state","error");
				resPd.put("message","计算存在错误,没有保险信息维护数据,请检查！");
				return resPd;
			}
			//合计行  数据构建
			List<PageData> footerList=new ArrayList<PageData>();
			PageData footerPd=new PageData();
			footerPd.put("empdoc_name", "合计");
			for(Map<String,String> empPd :emplist){
				for (String key : empPd.keySet()) {  
					  if(key.contains("-") || key.equals("basemoney")){
						 if(footerPd.get(key) !=null){
							 footerPd.put(key, 
									 CalculateUtil.add(footerPd.get(key).toString()
											 , empPd.get(key)));

						 }else{
							 footerPd.put(key, empPd.get(key));
						 }
					  }
				  
				} 
			}
			footerList.add(footerPd);
			resPd.put("footer", footerList);
			resPd.put("rows", emplist);
			resPd.put("total", emplist.size());
			resPd.put("state","success");
			resPd.put("message","计算成功");
		} catch (Exception e) {
			e.printStackTrace();
			resPd.put("state","error");
			resPd.put("message","计算存在错误,请联系管理员");
		}
		
		return resPd;
	}
	public List<PageData> findItems(PageData pd)throws Exception{
		List<PageData> list = (List<PageData>)dao.findForList("CaloutsourceMapper.findItems", pd);
		return list;
	}
	public List<PageData> findEmpdocSalaryItems(PageData pd)throws Exception{
		return (List<PageData>)dao.findForList("CaloutsourceMapper.findEmpdocSalaryItems", pd);
	}
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findCaloutsourcemxByCaloutsourceid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CaloutsourceMapper.findCaloutsourcemxByCaloutsourceid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CaloutsourceMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 查询是否被其他单据引用
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public List<PageData> checkIsUsed(PageData pd) {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CaloutsourceMapper.checkIsUsed", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	public boolean checkRepeatByParam2(PageData pd) throws Exception {
		String check_param = pd.get("check_param").toString();
		if (StringUtils.getEncoding(check_param).equals("GB2312")) {
			check_param = URLDecoder.decode(URLDecoder.decode(check_param,"UTF-8"),"UTF-8");
		}
		String[] check_params = {check_param}; 
		PageData param = new PageData();
		param.put("database", pd.get("database").toString());
		param.put("table_name", pd.get("table_name").toString());
		param.put("id", pd.get("id").toString());
		PageData p = new PageData();
		for (String str : check_params) {
			String[] strs = str.split(";");
			param.put("str", strs[0]);
			param.put("value", strs[1]);
			p = (PageData)dao.findForObject("CommonMapper.checkRepeatByParam", param);
			if(p != null && Integer.parseInt(p.get("num").toString()) != 0){
				return true;
			}
		}
		return false;
	}
	
	public List<PageData> getExcelData(PageData pd) throws Exception {
		List<PageData> list = (List<PageData>) dao.findForList("CaloutsourceMapper.listAll", pd);
		PageData p = list.get(0);
		String data = p.get("caloutsourcemxdata").toString();
		List<PageData> datalist = JsonUtils.strToListPd(data);
		return datalist;
	}
	
	/**
	 * 生成凭证
	 * @param pd
	 * @return
	 */
	public PageData createVoucher(PageData pd) {
		PageData resPd = new PageData();
		try {
			//按科目汇总后List
			List<PageData> subTotalList = new ArrayList<PageData>();
			//取凭证模板
			List<PageData> vmlist = getVoucherteMplate(pd.getString("code"),pd);
			//将mx转换成List
			List<PageData> addList = new ArrayList<PageData>();
			if (!"[]".equals(pd.getString("caloutsourcemx"))) {
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(pd.getString("caloutsourcemx"));
				for (Object o : jay) {
					PageData caloutsourcemx = new PageData(JsonUtils.strToMap(o.toString()));
					addList.add(caloutsourcemx);
				}
			}else{
				resPd.put("state", "error");
				resPd.put("message", "没有获取到外包保险数据，请检查");
				return resPd;
			}
			//构造凭证数据
			PageData mainPd = new PageData();
			List<PageData> mx_list = new ArrayList<PageData>();
			mainPd.put("accountperiod", pd.get("accountperiod"));
			mainPd.put("createby", getCurrentUserByCatch(pd).getUsername());
			mainPd .put("source", "caloutsource");
			mainPd .put("attachcount", "0");
			mainPd.put("database", getDatabase(pd));
			mainPd.put("voucherdate", pd.get("createdate"));
			PageData codePd=fillvouchersService.findMaxCodeAndEtc(mainPd);
			if(codePd != null && codePd.get("code")!=null){
				mainPd.put("code", codePd.get("code"));
			}
			for (PageData vm : vmlist) {
				PageData mxPd = new PageData();
				switch (vm.getString("businessmatters")) {
				case "公司部分": 	
			/*		for(PageData ywPd : addList){
						boolean state=false;
						for(PageData subPd : subTotalList){
							if(ywPd.get("insurancesubid").toString().equals(subPd.get("insurancesubid").toString())){
							 	double money = Double.parseDouble(subPd.get("compparttotal").toString());    
			                    money = CalculateUtil.add(money, Double.parseDouble(ywPd.get("compparttotal").toString())); 
			                    subPd.put("compparttotal", money);
			                    state = true; 
							}
						}
						if(!state){    
							subTotalList.add(ywPd);    
			            }    
					}*/
					
					//查询所有的人员及公司部分薪资项。然后按照所属累别对应的科目合计金额
					List<PageData> comppartlist = (List<PageData>) dao.findForList("CaloutsourceMapper.findComppart", pd);
					for(PageData ywPd : comppartlist){
						String subid = "";
						if("基本社保".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("insurancesubid").toString();
						}else if("公积金".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("fund_insurancesubid").toString();
						}else if("补充养老".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("oldage_insurancesubid").toString();
						}else if("补充医疗".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("medical_insurancesubid").toString();
						}
							
						boolean state=false;
						for(PageData subPd : subTotalList){
							if(subid.equals(subPd.get("insurancesubid").toString())){
							 	double money = Double.parseDouble(subPd.get("compparttotal").toString());    
			                    money = CalculateUtil.add(money, Double.parseDouble(ywPd.get("money").toString()),2); 
			                    subPd.put("compparttotal", money);
			                    state = true; 
							}
						}
						if(!state){    
							PageData dpd = new PageData();
							dpd.put("insurancesubid", subid);
							dpd.put("compparttotal", ywPd.get("money"));
							subTotalList.add(dpd);    
			            }    
					}
					
					for( PageData afterSubPd: subTotalList){
						if(afterSubPd.get("compparttotal") != null && Double.parseDouble(afterSubPd.get("compparttotal").toString()) != 0){
							PageData comppartPd=new PageData();
							comppartPd.put("debitmoney", afterSubPd.get("compparttotal"));
							comppartPd.put("rowid",(int)(Math.random()*100000));
							comppartPd.put("subjectid",afterSubPd.get("insurancesubid"));
							comppartPd.put("abstracta", vm.get("defaulttranmemo"));
							mx_list.add(comppartPd);
						}
					}
					break;
				case "个人部分":	
					if(pd.get("personparttotal") != null && Double.parseDouble(pd.get("personparttotal").toString()) != 0){
						mxPd.put("debitmoney", pd.get("personparttotal"));
						mxPd.put("rowid",(int)(Math.random()*100000));
						mxPd.put("subjectid",vm.get("accsubjectsid"));
						mxPd.put("abstracta", vm.get("defaulttranmemo"));
						mainPd.put("perssubjectid",vm.get("accsubjectsid"));//將个人部分的  会计科目更新到业务表中，工资单生成凭证时使用
						mx_list.add(mxPd);
					}
					break;
				case "应付外包保险":
					if(pd.get("totalmoney") != null && Double.parseDouble(pd.get("totalmoney").toString()) != 0){
						mxPd.put("creditmoney", pd.get("totalmoney"));
						mxPd.put("rowid",(int)(Math.random()*100000));
						mxPd.put("subjectid",vm.get("accsubjectsid"));
						mxPd.put("abstracta", vm.get("defaulttranmemo"));
						mx_list.add(mxPd);
					}
					break;
				default:
					break;
				}
			}
			mainPd.put("fillvouchersmx", JsonUtils.PageDataToJSONArray(mx_list).toString());
			//生成凭证
			resPd=fillvouchersService.save(mainPd);
			if(resPd .get("state") !=null && "success".equals(resPd .get("state").toString())){
				resPd.put("message",  "已生成凭证,凭证号码："+mainPd.get("code").toString());
				mainPd.put("id", pd.get("id"));
				mainPd.put("fillvouchersid", resPd.get("id"));
				dao.update("CaloutsourceMapper.updateVouchercodeAndIsAccount", mainPd);
			}else{
				resPd.put("message",  "生成凭证失败，请联系管理员！");
			}
				
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
}
