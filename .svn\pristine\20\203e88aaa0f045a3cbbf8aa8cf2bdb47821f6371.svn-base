<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
</head>
<body style="background: #ebedf3;">

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<!-- 顶部功能栏 -->
		<div id="eastToolDiv" data-options="region:'north'" style="background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4);
    background: -moz-linear-gradient(top,#fff,#f4f4f4);
    background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);
    background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<span class="invo_title">资产状态</span>
			<select class="easyui-combobox" id="cardTypeSelect" data-options="onChange:cardTypeSelect,editable:false,panelHeight:'auto'" style="width:100px">
				<option value="0">在用</option>
				<option value="1">处置</option>
				<option value="">全部</option>
			</select>
			<a class="easyui-linkbutton xzadd_btns" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({isCheckInitBalance:false,width:720,height:466,title:'固定资产卡片'})"></a>
			<a class="easyui-linkbutton ck_btns" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:720,height:466})"><i></i></a>
			<a class="easyui-linkbutton scc_btns" href="javascript:void(0)" onclick="javascript:ButtonFun.deleteFun({code:'fixedassetscard',renderid:'#gridlist'})"><i></i></a>
			<a class="easyui-linkbutton dc_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="ExportExcel()"><i></i></a>
			<a class="easyui-linkbutton gb_btns" style="margin: 9px 0 0 0;" href="javascript:void(0)" onclick="closeIndexTabs({title:'固定资产卡片'})"><i></i></a>
			<p class="refreshbtns" id="refreshbtn" onclick="refresh()"></p>
		</div>
		
		<div style="margin: 54px 0 0 0;border:1px #6f9ec2 solid;" id="parcel_southToolDiv">
			<!-- 主面板 -->
		    <div id="gridlist" data-options="region:'center'" ></div>
		</div>
		
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
			<!-- 单据表单 -->
			<input type="hidden" id="year" name="year" value="${year}" />
			<input type="hidden" id="month" name="month" value="${month}" />
			<div class="parceldiv" style="height:383px">
			    <div class="bgdivtitfixed">
					<div class="parcelformt">
						<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true">
							<input type="hidden" id="id" name="id">
							<!--******** wang 来源 -->
							<input type="hidden" id="source" name="source"/>
							<input type="hidden" id="sourceid" name="sourceid"/>
							<input type="hidden" id="sourcetype" name="sourcetype"/>
							<input type="hidden" id="isaccount" name="isaccount">
					    	<div class="card_top">
					    	    <div class="card_title"><i></i><p>基本信息</p><em></em></div>
					    	    <ul>
					    	       <li>
					    	           <p>资产编号：</p>
					    	           <input class="easyui-textbox" id="code" name="code" data-options="required:true,validType:['repeat[\'fixedassetscard\',\'t_fixedassetscard\',\'code\',\'pageForm\']']" style="width: 120px;height:30px" />
					    	       </li>
					    	        <li>
								      <p>资产类型：</p>
								      <input class="easyui-textbox" type="text" id="assetstype_name" name="assetstype_name" data-options="required:true"  style="width: 120px;height:30px"/>
								   </li>
					    	       <li>
					    	           <p>资产名称：</p>
					    	           <input class="easyui-textbox" id="name" name="name" data-options="required:true,validType:['repeat[\'fixedassetscard\',\'t_fixedassetscard\',\'name\',\'pageForm\']']" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
										<p>入账时间：</p>
					    	       		<input class="easyui-datebox" id="entrytime" name="entrytime" data-options="required:true" style="width:120px;height:30px"/>
					    	       </li>
					    	       <li>
					    	           <p>资产原值：</p>
					    	           <input class="easyui-numberbox" id="ori_value" name="ori_value" data-options="required:true,precision:2,min:0.01,onChange:monDepCal" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
										<p>使用期数：</p>
					    	           <input class="easyui-numberbox" id="periods" name="periods" data-options="required:true,min:1,onChange:monDepCal" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
					    	           <p>折旧方法：</p>
										<select class="easyui-combobox" id="dep_method" name="dep_method"  data-options="editable:false,panelHeight:'auto'" style="width: 123px;height:30px">
					    	       			<option value="0">平均年限法</option>
										</select>
					    	       </li>
					    	       <li>
					    	           <p>预计残值：</p>
					    	           <input class="easyui-numberbox" id="salvage" name="salvage" data-options="required:true,precision:2,min:0,onChange:monDepCal" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
										<p>月折旧额：</p>
										<input class="easyui-textbox" id="mon_dep_amount" name="mon_dep_amount" data-options="readonly:true" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
										<p>资产状态：</p>
										<select class="easyui-combobox" id="dispose_type" name="dispose_type" data-options="readonly:true,editable:false,panelHeight:'auto'" style="width: 123px;height:30px">
					    	       			<option value="0">在用</option>
					    	       			<option value="1">处置</option>
										</select>
					    	       </li>
					    	       
					    	        <li style="width:445px">
					    	           <p>补充说明：</p>
					    	           <input class="easyui-textbox" id="tranmemo" name="tranmemo" data-options="" style="width: 345px;height:30px" />
					    	       </li>
					    	    </ul>
					    	</div>
					    	<div class="card_midd">
					    	    <div  class="card_title"><i></i><p>科目核算</p><em></em></div>
					    	    <ul>
									<li>
										<p>固定资产：</p>
										<input type="hidden" id="fixed_id" name="fixed_id"/>
										<input class="easyui-textbox" id="fixed_name" name="fixed_name" data-options="readonly:true,required:true" style="width: 120px;height:30px; padding:0 24px 0 2px;"/>
										<div class="kjkm_btn" id="fixed_btn" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;fixed_id:id,fixed_name:name')"></div>
									</li>
									<li>
										<p>累计折旧：</p>
										<input type="hidden" id="dep_cum_id" name="dep_cum_id"/>
										<input class="easyui-textbox" id="dep_cum_name" name="dep_cum_name" data-options="readonly:true,required:true" style="width: 120px;height:30px; padding:0 24px 0 2px;"/>
										<div class="kjkm_btn" id="dep_cum_btn" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;dep_cum_id:id,dep_cum_name:name')"></div>
									</li>
									<li>
										<p>折旧费用：</p>
										<input type="hidden" id="dep_cost_id" name="dep_cost_id"/>
										<input class="easyui-textbox" id="dep_cost_name" name="dep_cost_name" data-options="readonly:true,required:true" style="width: 120px;height:30px; padding:0 24px 0 2px;"/>
										<div class="kjkm_btn" id="dep_cost_btn" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;dep_cost_id:id,dep_cost_name:name')"></div>
									</li>
					    	    </ul>
					    	</div>
					    	<div class="card_bott">
					    	    <div  class="card_title"><i></i><p>折旧信息</p><em></em></div>
					    	    <ul>
					    	       <li>
										<p>月折旧额：</p>
										<input class="easyui-textbox" id="mon_dep_amount2" name="mon_dep_amount2" data-options="readonly:true" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
					    	           <p>已折旧期数：</p>
					    	           <input class="easyui-numberbox" id="dep_ed_periods" name="dep_ed_periods" data-options="required:true,min:0,onChange:depEdCal" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
										<p>已折旧：</p>
										<input class="easyui-numberbox" id="dep_ed" name="dep_ed" data-options="required:true,precision:2,onChange:netCal" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
					    	           <p>资产净值：</p>
					    	           <input class="easyui-textbox" id="net_value" name="net_value" data-options="readonly:true" style="width: 120px;height:30px"/>
					    	       </li>
					    	    </ul>
					    	</div>
						</form>
					</div>
				</div>
			</div>
			<div style="text-align:center;height: 41px;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
				<div style="float: left; padding: 6px 0 0 0;" class="btnsdiv">
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="deleteBtn" onclick="deleteData()">删除</a>
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="editBtn" onclick="editData()">修改</a>
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="saveBtn" onclick="submitForm({code:'fixedassetscard',type:2,renderid:'#gridlist',noClose:false})">保存</a>
				</div>
				<a href="javascript:void(0)" class="easyui-linkbutton sure-btn closeNew_btn" onclick="clearForm()">关闭</a>
		    </div>
		</div>
		
	</div>

<script type="text/javascript">
$(function(){
	var obj = new Object();
	var width = $(window).width()-30;
	var w1 = width/30;
	var heights = $(window).height();
	$("#gridlist").height(heights-55);
	obj["position"] = '#gridlist';
	obj["idField"] = 'id';
	obj["url"] = 'fixedassetscard/list';
	obj["columns"] = [[
						{field:'id',title:'id',hidden:true},
						{field:'code',title:'固定资产编号',align: 'left', halign: 'center',width:w1*2,total:true},
						{field:'assetstype_name',title:'资产类型',align: 'left', halign: 'center',width:w1*1.5},
						{field:'name',title:'资产名称',align: 'left', halign: 'center',width:w1*3},
						{field:'entrytime',title:'入账时间',align: 'left', halign: 'center',width:w1*2},
						{field:'ori_value',title:'资产原值',align: 'right', halign: 'center',width:w1*3,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
						    }
						},
						{field:'periods',title:'使用期数',align: 'right', halign: 'center',width:w1*2},
						{field:'dep_method',title:'折旧方法',align: 'right', halign: 'center',width:w1*2,
							formatter:function(value){
								var arr = [{label: '平均年限法', value: 0}];
								for(var i=0; i<arr.length; i++){
									if (arr[i].value == value){
										return arr[i].label;
									}
								}
								return value;
							}
						},
						{field:'salvage',title:'预计残值',align: 'right', halign: 'center',width:w1*2,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
						    }
						},
						{field:'mon_dep_amount',title:'月折旧额',align: 'right', halign: 'center',width:w1*2,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'dep_ed_periods',title:'已折旧期数',align: 'right', halign: 'center',width:w1*2},
						{field:'dep_ed',title:'已折旧金额',align: 'right', halign: 'center',width:w1*3,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'net_value',title:'资产净值',align: 'right', halign: 'center',width:w1*3-1,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'dep_cost_name',title:'折旧费用科目',align: 'left', halign: 'center',width:w1*4}
					]];
	obj["listDbClickFun"] = listDbClickFun;
	obj["showFooter"] = true;
	Grid.list_grid(obj);
	
	$('#gridlist').datagrid({onLoadSuccess: 
		function (data){
            $('#gridlist').datagrid('statistics'); //合计
		}
	});
	
});
//部门选择框
var obj = new Object();
obj["idColName"] = 'name';
obj["url"] = 'assetstype/list';
obj["cgId"] = 'assetstype_name';
obj["cgColumns"] = 	[[	
	             		{field:'name',title:'类型名称',align: 'left', halign: 'center',width:100}
					]];
Dialog.archives_selector_list(obj);
//双击行的触发事件
function listDbClickFun(row){
	$('#pageForm').form('load', row);
	$('#edit_pageId').window({title:'编辑'});
	$('#edit_pageId').window('open');
	
	$('#pageForm').form('load',getUrl('fixedassetscard/findById?id='+row.id));
	
	//双击编辑时控制btn
	disableControl(['#saveBtn']);
	enableControl(['#editBtn','#deleteBtn']);
	
	//双击编辑时控制控件
	cannotEditable();
}

//编辑页面 可编辑状态
function canEditable(){
	$('#code').textbox('readonly',false);
	$('#name').textbox('readonly',false);
	$('#entrytime').datebox('readonly',false);
	$('#ori_value').numberbox('readonly',false);
	$('#periods').numberbox('readonly',false);
	$('#dep_method').combobox('readonly',false);
	$('#salvage').numberbox('readonly',false);
	$('#dep_ed').numberbox('readonly',false);
	
	$('#tranmemo').textbox('readonly',false);
	$('#assetstype_name').textbox('readonly',false);
	$('#fixed_btn').show();
	$('#dep_cum_btn').show();
	$('#dep_cost_btn').show();
	$('#dep_ed_periods').numberbox('readonly',false);
}

//编辑页面 不可编辑状态
function cannotEditable(){
	$('#code').textbox('readonly',true);
	$('#name').textbox('readonly',true);
	$('#entrytime').datebox('readonly',true);
	$('#ori_value').numberbox('readonly',true);
	$('#periods').numberbox('readonly',true);
	$('#dep_method').combobox('readonly',true);
	$('#salvage').numberbox('readonly',true);
	$('#dep_ed').numberbox('readonly',true);
	$('#tranmemo').textbox('readonly',true);
	$('#assetstype_name').textbox('readonly',true);
	$('#fixed_btn').hide();
	$('#dep_cum_btn').hide();
	$('#dep_cost_btn').hide();
	$('#dep_ed_periods').numberbox('readonly',true);
}

//新增按钮点击后通用触发方法
function afterAddFun(){
	$('#dep_method').combobox("select", '0');
	$('#dispose_type').combobox("select", '0');
	//******** wang 添加 固定资产来源
	var source_val = '${pd.source}';
	if(!checkEm(source_val)){
		$('#source').val('${pd.source}');
		$('#sourceid').val('${pd.sourceid}');
		$('#sourcetype').val('${pd.sourcetype}');
		$('#entrytime').datebox('setValue','${pd.voucherdate}');
	}
	
	//点击新增时控制btn
	enableControl(['#saveBtn']);
	disableControl(['#editBtn','#deleteBtn']);
	
	//点击新增时控制控件
	canEditable();
}

//编辑通用赋值前触发方法
function beforeEditFun(node){
	//点击编辑时控制btn
	disableControl(['#saveBtn']);
	enableControl(['#editBtn','#deleteBtn']);
	
	//点击编辑时控制控件
	cannotEditable();
}

//编辑通用赋值后触发方法
function afterEditFun(node){
	$('#pageForm').form('load',getUrl('fixedassetscard/findById?id='+node.id));
}

//通用表单提交前触发方法
function beforeSubmitFormFun(){return true;}

//通用保存后触发方法
function afterSaveFun(id){
	$('#id').val(id);	//赋值id
	
	//保存后时控制btn
	disableControl(['#saveBtn']);
	enableControl(['#editBtn','#deleteBtn']);
	
	//保存后控制控件
	cannotEditable();
}

//通用删除前触发方法
function beforeDeleteFun(rows){return true;}
//通用撤账后触发方法
function afterVoucherRevokeFun(){}
//通用生成凭证后触发方法
function afterVoucherCreateFun(){}

//计算月折旧额
function monDepCal(){
	var dep_method = $('#dep_method').combobox("getValue");	//折旧方法
	switch(dep_method){
	case '0': 	//平均年限法
		var ori_value = $('#ori_value').numberbox("getValue");		//资产原值
		var periods = $('#periods').numberbox("getValue");			//使用期数
		var salvage = $('#salvage').numberbox("getValue");			//预计残值
		if(!checkEm(ori_value) && !checkEm(periods) && !checkEm(salvage)){
			var mon_dep_amount = ( ori_value - salvage ) / periods;		//月折旧额 =（资产原值-预计残值）/ 使用期数
			$('#mon_dep_amount').textbox('setValue', mon_dep_amount.toFixed(2) );
			$('#mon_dep_amount2').textbox('setValue', mon_dep_amount.toFixed(2) );
			depEdCal();	//计算已折旧、资产净值
		}
		break;
	}
}

//计算已折旧、资产净值
function depEdCal(){
	var ori_value = $('#ori_value').numberbox("getValue");		//资产原值
	var mon_dep_amount = $('#mon_dep_amount').textbox("getValue");		//月折旧额
	var dep_ed_periods = $('#dep_ed_periods').numberbox("getValue");	//已折旧期数
	if(!checkEm(ori_value) && !checkEm(mon_dep_amount) && !checkEm(dep_ed_periods)){
		var dep_ed = ( mon_dep_amount * dep_ed_periods ).toFixed(2);	//已折旧 = 月折旧额 * 已折旧期数
		$('#dep_ed').numberbox('setValue', dep_ed);
		var net_value = ori_value - dep_ed;			//资产净值 = 资产原值 - 已折旧
		$('#net_value').textbox('setValue', net_value.toFixed(2));
	}
}

//计算资产净值
function netCal(){
	var ori_value = $('#ori_value').numberbox("getValue");		//资产原值
	var dep_ed = $('#dep_ed').numberbox("getValue");			//已折旧
	var net_value = ori_value - dep_ed;							//资产净值 = 资产原值 - 已折旧
	$('#net_value').textbox('setValue', net_value.toFixed(2));
}

//切换固定资产卡片状态
function cardTypeSelect(){
	$('#gridlist').datagrid('load',{ dispose_type : $('#cardTypeSelect').val() });
}

//开启修改
function editData(){
	//2021-12-02修改     如果是期初资产  或者已经折旧了 处置了  可以修改资产类型和备注。其他不可修改。没有折旧过的，全部可以修改。
	
 	var dispose_type = $('#dispose_type').combobox('getValue');
	if( dispose_type == 1 ){
		/* $.messager.alert('提示','<span class="hintsp_e">提示</span>该固定资产已被处置，不可修改！','error');
		return; */
		cannotEditable();
		$('#tranmemo').textbox('readonly',false);
		$('#assetstype_name').textbox('readonly',false);
		enableControl(['#saveBtn','#deleteBtn']);
		disableControl(['#editBtn']);
		return;
	}
	var isaccount = $('#isaccount').val();
	if( isaccount == 1 ){
		/* $.messager.alert('提示','<span class="hintsp_e">提示</span>该固定资产卡片为期初建账卡片，不可修改！','error');
		return; */
		cannotEditable();
		$('#tranmemo').textbox('readonly',false);
		$('#assetstype_name').textbox('readonly',false);
		enableControl(['#saveBtn','#deleteBtn']);
		disableControl(['#editBtn']);
		return;
	} 
	var dep_type = true;
	$.ajax({
	    url: getUrl('fixedassetscard/findDepreciation'),
	    type: 'post', async: false, data: {id:$('#id').val()}, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
    			dep_type = data.dep_type;
			}else {
				$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');
			}
		}
    });
	if( dep_type ){
		/* $.messager.alert('提示','<span class="hintsp_e">提示</span>该固定资产已发生折旧，不可修改！','error');
		return; */
		cannotEditable();
		$('#tranmemo').textbox('readonly',false);
		$('#assetstype_name').textbox('readonly',false);
		enableControl(['#saveBtn','#deleteBtn']);
		disableControl(['#editBtn']);
		return;
	} 
	
	//点击修改时控制btn
	enableControl(['#saveBtn','#deleteBtn']);
	disableControl(['#editBtn']);
	
	//点击修改时控制控件
	canEditable();
}

//删除数据
function deleteData(){
	var dispose_type = $('#dispose_type').combobox('getValue');
	if( dispose_type == 1 ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>该固定资产已被处置，不可删除！','error');
		return;
	}
	var isaccount = $('#isaccount').val();
	if( isaccount == 1 ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>该固定资产卡片为期初建账卡片，不可删除！','error');
		return;
	}
	var dep_type = true;
	$.ajax({
	    url: getUrl('fixedassetscard/findDepreciation'),
	    type: 'post', async: false, data: {id:$('#id').val()}, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
    			dep_type = data.dep_type;
			}else {
				$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');
			}
		}
    });
	if( dep_type ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>该固定资产已发生折旧，不可删除！','error');
		return;
	}
	
	//******** wang 期初余额产生的 
	var source = $('#source').val();
	
	$.messager.confirm('提示','<span class="hintsp_w">提示</span>确定要删除吗?',function(r){
		if (r){
			$.ajax({
			    url: getUrl('fixedassetscard/delete'),
			    type: 'post', async: false, data: {id:$('#id').val(),source:source}, dataType:'json',
			    success:function(data){
		   			if(data.state == 'success'){
		    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>删除成功！',showType:'slide',timeout:1000,style:{
		    				right : '', bottom : '',
		    				top : document.body.scrollTop + document.documentElement.scrollTop
		    			}}); */
		    			promptbox('success','删除成功！');
		    			$('#gridlist').datagrid('reload');
		    			ButtonFun.addFun({width:720,height:466,title:'固定资产卡片'})
					}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
				}
		    });
		}
	});
}

//导出
function ExportExcel(){
	var obj=new Object;
	obj['renderid'] = '#gridlist';
	obj['title'] = '固定资产卡片';
	obj['controllername'] = 'fixedAssetsCard';
	obj['cs'] = '';
	obj['pms'] = {dispose_type : $('#cardTypeSelect').combobox('getValue')};
	toExcel(obj);
}

</script>

</body>
</html>