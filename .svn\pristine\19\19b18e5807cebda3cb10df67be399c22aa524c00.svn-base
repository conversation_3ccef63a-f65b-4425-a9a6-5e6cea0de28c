package org.newstanding.service.financialhandle;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("recpayoffService")
public class RecpayoffService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private FillvouchersService fillvouchersService;
	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			StringBuffer sb=new StringBuffer("");
			if (!"[]".equals(pd.getString("recpayoffmx"))) {
		    	com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(pd.getString("recpayoffmx"));
		    	List<PageData> addList = new ArrayList<PageData>();
				for (Object o : jay) {
					PageData mxpd = new PageData(JsonUtils.strToMap(o.toString()));
					addList.add(mxpd);
				}
				if(addList.size()>0){
					PageData voucherPd=new PageData();
					//封装主表数据
					voucherPd.put("accountperiod", pd.get("accountperiod"));
					voucherPd.put("voucherdate", pd.get("voucherdate1"));
					voucherPd.put("attachcount", 0);
					voucherPd.put("source", "recpayoff");
					voucherPd.put("database", getDatabase(pd));
					voucherPd.put("createby", getCurrentUserByCatch(pd).getUsername());
					PageData codePd=fillvouchersService.findMaxCodeAndEtc(voucherPd);
					if(codePd != null && codePd.get("code")!=null){
						voucherPd.put("code", codePd.get("code"));
					}
					List<PageData> allList=new ArrayList<PageData>();
					//循环生成凭证
					for(PageData tempPd : addList){
						//先存到表中，生成完凭证删除表中数据
						tempPd.put("database", getDatabase(pd));
						dao.save("RecpayoffMapper.save", tempPd);
						Long recpayoffid = Long.parseLong(tempPd.get("id").toString());
						pd.put("recpayoffid", recpayoffid);
						//封装明细数据
						List<PageData> vouchermxList=findVoucherMxListById(pd);
						allList.addAll(vouchermxList);
						dao.delete("RecpayoffMapper.delete", pd);
						
					}
					voucherPd.put("fillvouchersmx", JsonUtils.PageDataToJSONArray(allList).toString());
					PageData resPd1=fillvouchersService.save(voucherPd);
					if(resPd1 .get("state") !=null && "success".equals(resPd1 .get("state").toString())){
						sb.append(voucherPd.get("code").toString());
					}
				}else{
					resPd.put("state", "error");
					resPd.put("message", "没有需要操作的数据,请检查！");
				}
				}
				resPd.put("state", "success");
				resPd.put("message",  "已生成凭证,凭证号码："+sb.toString());
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	@SuppressWarnings("unchecked")
	private List<PageData> findVoucherMxListById(PageData pd) throws Exception {
		return (List<PageData>) dao.findForList("RecpayoffMapper.findVoucherMxListById", pd);
	}
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			dao.delete("RecpayoffMapper.deleteRecpayoffmxByRecpayoffid", pd);
			int count = dao.delete("RecpayoffMapper.delete", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			int count = dao.update("RecpayoffMapper.edit", pd);
			Long recpayoffid = Long.parseLong(pd.get("id").toString());
			if (StringUtils.isUpdate(pd.getString("recpayoffmx"))) {
				editTempl(recpayoffid, "Recpayoff", pd, 0);
			}else if(StringUtils.isDelete(pd.getString("recpayoffmx"))){
				dao.delete("RecpayoffMapper.deleteRecpayoffmxByRecpayoffid", pd);
			}
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("RecpayoffMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("RecpayoffMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findRecpayoffmxByRecpayoffid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("RecpayoffMapper.findRecpayoffmxByRecpayoffid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("RecpayoffMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 查询是否被其他单据引用
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public List<PageData> checkIsUsed(PageData pd) {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("RecpayoffMapper.checkIsUsed", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
}
