package org.newstanding.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.newstanding.common.entity.User;
import org.newstanding.common.utils.Const;
import org.newstanding.common.utils.UserPdSingleton;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * 
* 类名称：LoginHandlerInterceptor.java
* 类描述： 
* <AUTHOR>
* 作者单位： 
* 联系方式：
* 创建时间：2015年1月1日
* @version 1.6
 */
@SuppressWarnings("unused")
public class LoginHandlerInterceptor extends HandlerInterceptorAdapter{

	private UserPdSingleton catachePd = UserPdSingleton.getInstance();
	
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		// TODO Auto-generated method stub
		String path = request.getServletPath();
		if(path.matches(Const.NO_INTERCEPTOR_PATH)){
			return true;
		}else{
			//shiro管理的session
			Subject currentUser = SecurityUtils.getSubject();  
			Session session = currentUser.getSession();
			User user = null/*(User)session.getAttribute(Const.SESSION_USER)*/;
			if(user!=null){
				boolean b = false;
				
				if (catachePd.get(user.getUsername()) == null) {
					System.err.println("-----------------------------------------------------------------用户丢失--------------------------------------------------------");
				}
				
				if (catachePd.get(user.getUsername())!= null && catachePd.get(user.getUsername()).equals(request.getRequestedSessionId())) {
					b = true;
				}
				
				if(!b){
					response.sendRedirect(request.getContextPath() + Const.LOGIN);
				}
				return b;
			}else{
				System.err.println("-----------------------------------------------------------------无session用户丢失--------------------------------------------------------");
				//登陆过滤
				response.sendRedirect(request.getContextPath() + Const.LOGIN);
				return false;		
				//return true;
			}
		}
	}
	
}
