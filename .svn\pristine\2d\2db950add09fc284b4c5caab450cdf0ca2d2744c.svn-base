<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
</head>
<body>
	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background: #fff;">
		
			<!-- 左侧功能栏 -->
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({isCheckInitBalance:false,width:370,height:470})">新增</a>
			<a class="easyui-linkbutton compile-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:3,renderid:'#gridlist',width:370,height:470})">编辑</a>
			<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')">删除</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="searchSubject()">查找</a>
			<!-- <a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="updateAliasName()">更新别名</a>  -->
		</div>
		<div class="parceldivs">
			<div class="parceldiv_tow parceldiv_towt" style=" width: 575px;height: 287px;margin:26px">
				<div id="gridlist" class="easyui-tabs" data-options="region:'center'" style="background: #fff;height: 284px;"></div>
			</div>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('accsubjects')">关闭</a>
		</div>
		
		<!-- 编辑弹窗 -->
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:370px;height:500px;">
			<!-- 单据表单 -->
			<input type="hidden" id="accountingcode" name="accountingcode" value="${accountingcode }">
			<div class="bgdivtitfixed" style="height: 400px;width: 100%; border: 0;border-bottom: 1px solid rgb(206, 218, 228);">
				<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="padding:10px;">
					<input type="hidden" id="id" name="id" />
					<!-- 又发生额时，不能修改辅助核算 -->
					<input type="hidden" id="auxbus1" name="auxbus1" />
					<!-- 修改时判断，不能修改层级 -->
					<input type="hidden" id="code1" name="code1" />
					<input type="hidden" id="supercode"  name="supercode" />
					<input type="hidden" id="superid"  name="superid" />
					<input type="hidden" id="is_super_have_used"  name="is_super_have_used" /><!-- 上级是否被使用？ -->
					<ul class="accsubjects_ul" style="height:371px;margin: 10px 0 0 0;">
					    <li>
					        <p>科目编码：</p>
					        <input id="code" class="easyui-textbox" type="text" name="code" data-options="onChange:codeChange,required:true,validType:['repeat[\'accsubjects\',\'c_accsubjects\',\'code\',\'pageForm\']']" />
					    </li>
					    <li>
					        <p>科目名称：</p>
					        <input class="easyui-textbox" type="text" name="name" data-options="required:true" />
					    </li>
					    <li>
					        <p>科目类别：</p>
					        <select id="sub_class" class="easyui-combobox" name="sub_class" data-options="required:true,editable:false,panelHeight:'auto'" style="width:225px">
							    <option value="0">资产</option>
							    <option value="1">负债</option>
							    <option value="2">权益</option>
							    <option value="3">成本</option>
							    <option value="4">损益</option>
							</select>
					    </li>
					    <li>
					        <p>余额方向：</p>
					        <select id="sub_direction" class="easyui-combobox" name="sub_direction"  data-options="required:true,editable:false,panelHeight:'auto'" style="width:225px">
							    <option value="0">借</option>
							    <option value="1">贷</option>
							</select>
					    </li>
					    <li>
					        <i></i>
					        <p class="changemass">辅助信息</p>
					        <em></em>
					    </li>
					    <li>
					        <p>是否日记账：</p>
					        <input id="isdayacc" type="checkbox" class="checkboxbgimg" name="isdayacc" value='1' <c:if test="${pd.isdayacc == 1}">checked="checked"</c:if>>
					    </li>
					    <li>
					        <p>现金类科目：</p>
					        <input id="sub_type_cash" type="checkbox" class="checkboxbgimg" name="sub_type_cash" value='1' <c:if test="${pd.sub_type_cash == 1}">checked="checked"</c:if> onclick="cashClick(this)" />
					    </li>
					    <li>
					        <p>银行类科目：</p>
					        <input id="sub_type_bank" type="checkbox" class="checkboxbgimg" name="sub_type_bank" value='1' <c:if test="${pd.sub_type_bank == 1}">checked="checked"</c:if> onclick="bankClick(this)" />
					    </li>
					    <li>
					        <p>辅助核算：</p>
					        <select id="auxbus" class="easyui-combobox" name="auxbus"  data-options="editable:false,panelHeight:'auto'" style="width:225px">
					        	<option value="0"></option>
							    <option value="1">客户</option>
							    <option value="2">供应商</option>
							</select>
					    </li>
					    <li>
					        <p>现流量属性：</p>
					        <select id="cashflowatt" class="easyui-combobox" name="cashflowatt"  data-options="editable:false,panelHeight:'auto'" style="width:225px">
							    <option value="13"></option>
							    <option value="0">经营活动-收款</option>
							    <option value="1">经营活动-付款</option>
							    <option value="2">经营活动-薪酬</option>
							    <option value="3">经营活动-税费</option>
							    <option value="4">经营活动-其他</option>
							    <option value="5">投资活动-投资</option>
							    <option value="6">投资活动-收益</option>
							    <option value="7">投资活动-投资资产</option>
							    <option value="8">投资活动-处置资产</option>
							    <option value="9">筹资活动-增资</option>
							    <option value="10">筹资活动-融资</option>
							    <option value="11">筹资活动-利息</option>
							    <option value="12">筹资活动-分配</option>
							</select>
					    </li>
					</ul>
			    </form>
			</div>    
		    <div style="height:39px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;padding:0px 0px 0 0;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="clearForm()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton  sure-btn sureNew_btn" onclick="submitForm({code:'accsubjects',type:3,renderid:'#gridlist'})">确定</a>
		    </div>
		</div>
		
		<div id="edit_pageId1" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="height:80px;display:none">
		    <div class="search_pel search_peldiv" style="height:94px">
		        <p style="float:Left;width:105px;margin:35px 0 0 15px">输入编码或名称：</p>
		        <input id="subjectnamea" class="easyui-textbox" type="text" name="subjectnamea"  />
		        <a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" onclick="findSubject()" style="margin:28px 6px 0 0;">搜索</a>
		    </div>
		    <div class="windowbottom" style="height:42px">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton closeNew_btn" onclick="closeKmye()">关闭</a>
		    	</div>
		    </div>
		</div>
		
	</div>
	
<script type="text/javascript">

	$(function(){
		var subClass = ['资产', '负债', '权益', '成本', '损益'];
		for(var i = 0 ; i < 5 ; i++){
			$('#gridlist').tabs('add', {title:subClass[i], content:'<div id="accTab_'+ i +'"></div>'});
			Tree.tree({'position':'#accTab_'+i, 'url':'accsubjects/treeList?class='+i, 'idField':'id', 'treeField':'name'});
			$('#accTab_'+i).tree('collapseAll');
		}
		$('#gridlist').tabs('select',0);
	});
	
	//打开查找弹框
	function searchSubject(){
		$('#edit_pageId1').window({title:'查找', width:400, height:178, modal:true });
		$('#edit_pageId1').window('open');
	}
	
	var treenum=0,nodenum=0;
	//查找功能实现方法
	function findSubject(){
		var name = $("#subjectnamea").textbox('getValue').trim();
		if(!checkEm(name)){
			for(var i = treenum ; i < 5 ; i++){
			    var node = $('#accTab_'+i).tree('getChildren');    //获取Tree的所有节点
		        $('#accTab_'+i).tree('expandAll', node.target);    //展开所有节点
		        //查找相应节点并滚动到该节点，高亮显示
		        for (j = nodenum; j < node.length; j++) {
		            var treeId = node[j].id;
		            var treeName = node[j].text;
		            if(j==node.length-1){
                    	treenum=i+1;
                    	nodenum=0;
                    	if(treenum==5){
                    		i=0;
                    		treenum=0;
                    		return;
                    	}
                    }else{
                    	treenum=i;
                    }
		            if (treeName.indexOf(name) >= 0 ) {
		            	$('#gridlist').tabs('select',i);
	                    var nodes = $('#accTab_'+i).tree('find', treeId);   //找到当前的节点
	                    $('#accTab_'+i).tree('scrollTo', nodes.target);     //滚动到当前节点
	                    $('#accTab_'+i).tree('select', nodes.target);       //高亮显示
	                    nodenum=j+1;
	                    return;
		            }
		        }
			}
		}else{
			$.messager.alert('提示','<span class="hintsp_e">提示</span>请输入要查找的科目编码或名称！','warnning');
		}
	}
	
	//关闭查找弹框
	function closeKmye(){
		$('#edit_pageId1').dialog('close');
	}
	
	//如果科目已有发生额，辅助核算不可修改
	//2018-01-12 需求修改为  查看凭证中是否被使用
	$('#auxbus').combobox({
		onClick : function(record){
				if(!checkEm($("#id").val())){
					$.ajax({
						url:getUrl('accsubjects/findHaveKmye'),
						data:{id:$('#id').val()},
						success : function(data){
							if(data.state=='error'){
								$('#auxbus').combobox('setValue',$('#auxbus1').val());
								$.messager.alert('提示','<span class="hintsp_e">提示</span>该科目已被凭证使用，辅助核算不可修改！','warnning');
								return;
							}
						}
					})
				}
		}
	})
	
		//code change事件
		function codeChange(newValue,oldValue){
		debugger
			//科目编码：不能修改层级（如原本是第一级，不能修改为第二级     原本是第二级不能修改为第一级。）   不能重复
			if(!checkEm($("#id").val()) && !checkEm($("#code1").val())){
				var val= $('#code1').val();
				if(!checkEm(newValue) && newValue.length != val.length){
					$.messager.alert('提示','<span class="hintsp_e">提示</span>不能修改科目层级！','warnning');
					return;
				}
			}
			
			var isValid = $('#code').textbox('isValid');
			if (!isValid){
				return;// 如果表单是无效直接返回
			}
			var accountingcode=$("#accountingcode").val();
			var codeArray=accountingcode.split('-');
			var totallength=codeArray.length;
			//取得每一级的长度
			var firstlength=0;var secondlength=0;var thirdlength=0;var fourlength=0;var fivelength=0;
			var firsttotallength=0;var secondtotallength=0;var thirdtotallength=0;var fourtotallength=0;var fivetotallength=0;
			if(totallength >0){
				for(var i=0;i<totallength;i++){
					if(i==0){
						firstlength=Number(codeArray[0]);
						firsttotallength=Number(codeArray[0]);
					}else if(i==1){
						secondlength=Number(codeArray[1]);
						secondtotallength=firsttotallength+Number(codeArray[1]);
					}else if(i==2){
						thirdlength=Number(codeArray[2]);
						thirdtotallength =secondtotallength+Number(codeArray[2]);
					}else if(i==3){
						fourlength=Number(codeArray[3]);
						fourtotallength=thirdtotallength+Number(codeArray[3]);
					}else if(i==4){
						fivelength=Number(codeArray[4]);
						fivetotallength=fourtotallength+Number(codeArray[4]);
					}
				}
				var code=$("#code").val();
				if(code.length==firsttotallength || code.length==secondtotallength
						|| code.length==thirdtotallength || code.length==fourtotallength
						|| code.length==fivetotallength){
					//符合规则 
					console.log("符合会计科目规则");
					var ruleslevel=0;
					//判断具体符合哪一级别规则，带出其上级的属性
					if(code.length==firsttotallength){
						ruleslevel=1;
					}else if( code.length==secondtotallength){
						ruleslevel=2;
					}else if( code.length==thirdtotallength){
						ruleslevel=3;
					}else if( code.length==fourtotallength){
						ruleslevel=4;
					}else if( code.length==fivetotallength){
						ruleslevel=5;
					}
					//根据级别找出其上级code
					if(ruleslevel>1){
						var levelLength=codeArray[ruleslevel-1];
						var superCode=code.substring(0,code.length-levelLength);
						//发送请求后台  返回上级科目的信息
						$.ajax({
						    url:getUrl('accsubjects/findByCode'),
						    type:'post',
						    async:false,
						    data: {'code':superCode},
						    dataType:'json',
						    success:function(data){
						    	debugger
						    	if(!checkEm(data)){
						    		if(data.state == 'success'){
						    			//如果没有子节点，提示
						    			//您将改变上级科目的层级关系，如果上级科目存在发生额，系统将所有该科目下的账务数据转移至新增的会计科目中，是否确认此操作?
						    			if(data.hasChild=='否'){
						    				//判断是否已经被使用
						    				$.ajax({
						    					 url:getUrl('accsubjects/checkDelete'),
												    type:'post',
												    async:false,
												    data: {'DATA_IDS':data.id},
												    dataType:'json',
												    success:function(data1){
												    	if(data1.state=='error'){
												    		/* $.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data1.message,'error');
												    		return; */
											   				$.messager.confirm('确认消息','<span class="hintsp_w">提示</span>上级科目已被使用，请确认是否需要在此科目下增加明细科目。如果增加，则该科目下的所有发生额将全部转至新增科目，该操作不可逆，请谨慎处理!', function(r){
						    									if (r){
						    						    			$.messager.confirm('确认消息','<span class="hintsp_w">提示</span> 请再次确认是否需要继续新增会计科目?', function(r){
						    											if (r){
						    												//设置数据
								    						    			$('#sub_class').combobox('setValue', data.sub_class);
								    						    			$('#sub_direction').combobox('setValue', data.sub_direction);
								    						    			$('#isdayacc').val(data.isdayacc);
								    						    			$('#sub_type_cash').val(data.sub_type_cash);
								    						    			$('#sub_type_bank').val(data.sub_type_bank);
								    						    			if(Number(data.isdayacc)==1){
															    				document.getElementById("isdayacc").checked=true;
															    			}else{
															    				document.getElementById("isdayacc").checked=false;
															    			}
															    			if(Number(data.sub_type_cash)==1){
															    				document.getElementById("sub_type_cash").checked=true;
															    			}else{
															    				document.getElementById("sub_type_cash").checked=false;
															    			}
															    			if(Number(data.sub_type_bank)==1){
															    				document.getElementById("sub_type_bank").checked=true;
															    			}else{
															    				document.getElementById("sub_type_bank").checked=false;
															    			}
								    						    			$('#sub_class').combobox('readonly');	
								    						    			$('#sub_direction').combobox('readonly');
								    						    			//可编辑
								    						    			$('#cashflowatt').combobox('setValue', data.cashflowatt);
								    						    			
								    						    			//将上级编码code放到隐藏字段上  保存时候根据此获取上级id
								    						    			$('#supercode').val(data.code);
								    						    			$('#superid').val(data.id);
								    						    			$("#is_super_have_used").val("是");
						    											}else{
						    												$('#code').textbox('clear');
						    											}
						    											
						    						    			})
						    						    					 
						    									}else{
				    												$('#code').textbox('clear');
				    											}
						    								})
												    	
												    	}else{
										    				$.messager.confirm('确认消息','<span class="hintsp_w">提示</span>您将改变上级科目的层级关系，如果上级科目存在发生额，系统将所有该科目下的账务数据转移至新增的会计科目中，是否确认此操作?', function(r){
						    									if (r){
						    						    			//设置数据
						    						    			$('#sub_class').combobox('setValue', data.sub_class);
						    						    			$('#sub_direction').combobox('setValue', data.sub_direction);
						    						    			$('#isdayacc').val(data.isdayacc);
						    						    			$('#sub_type_cash').val(data.sub_type_cash);
						    						    			$('#sub_type_bank').val(data.sub_type_bank);
						    						    			if(Number(data.isdayacc)==1){
													    				document.getElementById("isdayacc").checked=true;
													    			}else{
													    				document.getElementById("isdayacc").checked=false;
													    			}
													    			if(Number(data.sub_type_cash)==1){
													    				document.getElementById("sub_type_cash").checked=true;
													    			}else{
													    				document.getElementById("sub_type_cash").checked=false;
													    			}
													    			if(Number(data.sub_type_bank)==1){
													    				document.getElementById("sub_type_bank").checked=true;
													    			}else{
													    				document.getElementById("sub_type_bank").checked=false;
													    			}
						    						    			$('#sub_class').combobox('readonly');	
						    						    			$('#sub_direction').combobox('readonly');
						    						    			//可编辑
						    						    			$('#cashflowatt').combobox('setValue', data.cashflowatt);
						    						    			
						    						    			//将上级编码code放到隐藏字段上  保存时候根据此获取上级id
						    						    			$('#supercode').val(data.code);
						    									}else{
				    												$('#code').textbox('clear');
				    											}
						    								})
												    	}
												    }
						    				})
						
						    			}else{
							    			//设置数据
							    			//不可编辑
							    			$('#sub_class').combobox('setValue', data.sub_class);
							    			$('#sub_direction').combobox('setValue', data.sub_direction);
							    			$('#isdayacc').val(data.isdayacc);
							    			$('#sub_type_cash').val(data.sub_type_cash);
							    			$('#sub_type_bank').val(data.sub_type_bank);
							    			if(Number(data.isdayacc)==1){
							    				document.getElementById("isdayacc").checked=true;
							    			}else{
							    				document.getElementById("isdayacc").checked=false;
							    			}
							    			if(Number(data.sub_type_cash)==1){
							    				document.getElementById("sub_type_cash").checked=true;
							    			}else{
							    				document.getElementById("sub_type_cash").checked=false;
							    			}
							    			if(Number(data.sub_type_bank)==1){
							    				document.getElementById("sub_type_bank").checked=true;
							    			}else{
							    				document.getElementById("sub_type_bank").checked=false;
							    			}
							    			$('#sub_class').combobox('readonly');	
							    			$('#sub_direction').combobox('readonly');
							    			//可编辑
							    			$('#cashflowatt').combobox('setValue', data.cashflowatt);
							    			
							    			//将上级编码code放到隐藏字段上  保存时候根据此获取上级id
							    			$('#supercode').val(data.code);
						    			}
						    			
									}else if(data.state == 'error'){
										$.messager.alert('操作失败','<span class="hintsp_e">提示</span>获取上级科目信息失败！','error');
									}
						    	}else{
						    		$.messager.alert('操作失败','<span class="hintsp_e">提示</span>没有找到对应的上级科目信息！','error');
						    	}
				    			
						    }
						    
					    });
					}
				}else{
					//不符合规则
					console.log("不符合会计科目规则");
					$.messager.alert('提示消息',"<span class='hintsp_w'>提示</span>输入的科目编码不符合设定的科目编码规则！",'warning');
				}
			}else{
				$.messager.alert('提示消息',"<span class='hintsp_w'>提示</span>尚未设置科目编码规则！",'warning');
				$('#code').textbox('clear');
			}
		}
	
	//现金科目点击事件
	function cashClick(obj){
		if($("#sub_type_cash")[0].checked){
			 //判断银行类科目是否勾选
			 if(document.getElementById("sub_type_bank").checked==true){
				 document.getElementById("sub_type_bank").checked=false;
			 }
		}
	}
	
	function bankClick(obj){
		 if($("#sub_type_bank")[0].checked){
			 //判断现金类科目是否勾选
			 if(document.getElementById("sub_type_cash").checked==true){
				 document.getElementById("sub_type_cash").checked=false;
			 }
		 }
	}
	//双击树节点的触发事件
	function treeDbClickFun(node){
		$('#pageForm').form('load', node);
		$('#edit_pageId').window({title:'编辑'});
		$('#edit_pageId').window('open');
	}
	
	//新增按钮点击后通用触发方法
	function afterAddFun(){
		if(checkEm($('#id').val())){
			$('#sub_class').combobox('readonly',false);	
			$('#sub_direction').combobox('readonly',false);
		}else{
			$('#sub_class').combobox('readonly');	
			$('#sub_direction').combobox('readonly');
		}
	}
	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	//编辑通用赋值后触发方法
	function afterEditFun(node){
		 if(!checkEm(node.isdayacc) && Number(node.isdayacc) ==1){
			$("#isdayacc").prop("checked",true);
		}else{
			$("#isdayacc").prop("checked",false);
		}
		if(!checkEm(node.sub_type_cash) && Number(node.sub_type_cash) ==1){
			$("#sub_type_cash").prop("checked",true);
		}else{
			$("#sub_type_cash").prop("checked",false);
		}
		if(!checkEm(node.sub_type_bank) && Number(node.sub_type_bank) ==1){
			$("#sub_type_bank").prop("checked",true);
		}else{
			$("#sub_type_bank").prop("checked",false);
		}
	}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){
		if(!checkEm($("#id").val()) && !checkEm($("#code1").val())){
			var val= $('#code1').val();
			var code=$('#code').textbox('getValue');
			if(!checkEm(code) && code.length != val.length){
				$.messager.alert('提示','<span class="hintsp_e">提示</span>不能修改科目层级！','warnning');
				return;
			}
		}
		return true;
	}
	//通用保存后触发方法
	function afterSaveFun(id){}
	//通用撤账后触发方法
	function afterVoucherRevokeFun(){}
	//通用生成凭证后触发方法
	function afterVoucherCreateFun(){}
	
	//恢复按钮初始化参数
	function initRecovery(){
		var obj= {};
		obj["title"] = "恢复";
		obj["code"] = "accsubjects";
		obj["position"] = "#grid_selectrlist";
		obj["columns"] = [[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'name',title:'客户名称',align: 'left', halign: 'center',width:400},
		                    {field:'operation',title:'操作',width:100,formatter:formatOper_renew,no_select:true}
	                     ]];
		return obj;
	}
	
	//渲染恢复按钮
	function formatOper_renew(val,row,index){
		var url = "accsubjects/closeAll.do?DATA_IDS="+row["id"]+"&closestatus=0&table_name=c_accsubjects";
	    return '<a href="javascript:void(0)" class="easyui-linkbutton" onclick="ButtonFun.renew(\''+url+'\')">恢复</a>';  
	}
	//刷新视图
	function reshView(){
		$('#grid_selectrlist').datagrid('reload');
		$('#gridlist').datagrid('reload');
	}
	
	//侧栏关闭按钮方法 operType：str  关闭   删除
	function closeOrDelete(operType){
		debugger
		var tab = $('#gridlist').tabs('getSelected');
		var index = $('#gridlist').tabs('getTabIndex',tab);
		var nodes=$('#accTab_'+index).tree('getSelected');
		//如果是预设的  固定科目   则，不可以删除
		if(nodes.isfixed=='1' || nodes.isfixed==1){
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>固定科目，不可删除！','error');
			return;
		}
		var url='';
		if(operType=='关闭'){
			url='accsubjects/closeAll?closestatus=1';
		}else{
			url='accsubjects/deleteAll';
		}
		if(!checkEm(nodes)){
			$.messager.confirm(operType+'提示', '<span class="hintsp_w">提示</span>确定是否对该科目执行'+operType+'，'+operType+'后不再被引用？', function(r){
				if (r){
					ButtonFun.closeAndRemoveFun(3,'#gridlist',url);
				}
			})
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}
	function updateAliasName(){
		$.ajax({
			url:getUrl('accsubjects/updateAliasName'),
			data:{},
			success : function(data){
				if(data.state=='success'){
					$.messager.alert('提示', '<span class="hintsp_w">提示</span>批量修改成功','info');
				}else if(data.state=='error'){
					$.messager.alert('提示', '<span class="hintsp_w">提示</span>批量修改失败！','error');
					return;
				}
			}
		})
	}
</script>

</body>
</html>