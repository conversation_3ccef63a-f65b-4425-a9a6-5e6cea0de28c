<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Initialize Value for ComboGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Initialize Value for ComboGrid</h2>
	<div class="demo-info">
		<div class="demo-tip icon-tip"></div>
		<div>Initialize value when ComboGrid is created.</div>
	</div>
	<div style="margin:10px 0"></div>
	<input class="easyui-combogrid" style="width:250px" value="EST-12" data-options="
			panelWidth: 500,
			idField: 'itemid',
			textField: 'productname',
			url: 'datagrid_data1.json',
			method: 'get',
			columns: [[
				{field:'itemid',title:'Item ID',width:80},
				{field:'productname',title:'Product',width:120},
				{field:'listprice',title:'List Price',width:80,align:'right'},
				{field:'unitcost',title:'Unit Cost',width:80,align:'right'},
				{field:'attr1',title:'Attribute',width:200},
				{field:'status',title:'Status',width:60,align:'center'}
			]],
			fitColumns: true
		">
</body>
</html>