操作初始化
-- 删除业务表 wang
select 
concat('delete from ' , table_name,';' )
from information_schema.tables where table_schema = 'hlwh' and left(table_name,2) = 't_'
and  table_name not in ('t_vouchertemplate','t_vouchertemplatemx','t_vouchersubjectset','t_salaryitemset','t_senioritywageset' );

-- 删除记账表 wang
select 
concat('delete from ' , table_name,';' )
from information_schema.tables where table_schema = 'hlwh' and left(table_name,2) = 'i_'
and  table_name not in ('t_vouchertemplate','t_vouchertemplatemx','t_vouchersubjectset' );

-- 删除基础表 wang
select 
concat('delete from ' , table_name,';' )
from information_schema.tables where table_schema = 'hlwh' and left(table_name,2) = 'c_'
and  table_name not in ('c_kjkm','c_kmlb','c_costcategory',  'c_businessmater' ,'c_menu','c_menumx' ,'c_formproperty','t_settleacount' ,'c_bmdn' ,'c_gsdn' ,'c_zydn','c_role'          );


delete from c_bmdn where id != 1;
delete from c_zydn where id != 1;
delete from sys_user where user_id !=1;
