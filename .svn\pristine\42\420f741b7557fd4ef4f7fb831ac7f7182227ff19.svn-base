package org.newstanding.service.cashier;

import java.util.ArrayList;
import java.util.List;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("subQuickSetService")
public class SubQuickSetService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;

	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.save("SubQuickSetMapper.save", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.delete("SubQuickSetMapper.delete", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			int count = dao.update("SubQuickSetMapper.edit", pd);
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("SubQuickSetMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("SubQuickSetMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询是否被其他单据引用
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public List<PageData> checkIsUsed(PageData pd) {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("SubQuickSetMapper.checkIsUsed", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("SubQuickSetMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
}

