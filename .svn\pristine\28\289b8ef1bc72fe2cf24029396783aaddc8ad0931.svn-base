<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Basic PasswordBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
	<script type="text/javascript" src="../../source/jquery.passwordbox.js"></script>
	<script type="text/javascript">
		$(function(){
			$('#pb').passwordbox({
				lastDelay: 400,
				checkInterval: 5000,
				inputEvents1: $.extend({}, $.fn.passwordbox.defaults.inputEvents, {
					keyup: function(e){
						var s = String.fromCharCode(e.which);
						console.log(s)
						var text = $(e.data.target).passwordbox('getText');
						console.log('1:'+text)
						convert(e.data.target, text, true);
						var text = $(e.data.target).passwordbox('getText');
						console.log('2:'+text)
					}
				})
			})
		})
	</script>
</head>
<body>
	<h2>Basic PasswordBox</h2>
	<p>The passwordbox allows a user to enter passwords.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:400px;padding:50px 60px">
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" prompt="Username" iconWidth="28" style="width:100%;height:34px;padding:10px;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" type="password">
			<input id="pb" class="easyui-passwordbox" prompt="Password" iconWidth="28" style="width:100%;height:34px;padding:10px">
		</div>
	</div>
</body>
</html>