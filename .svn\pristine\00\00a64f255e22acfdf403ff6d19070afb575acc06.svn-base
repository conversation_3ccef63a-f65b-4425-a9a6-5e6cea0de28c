<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="CalvataxMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.t_calvatax(
			 outcustomerid,
			 outcus_balance,
			 outmoney,
			 incustomerid,
			 incus_balance
			
		) values 
		(
			 #{outcustomerid},
			 #{outcus_balance},
			 #{outmoney},
			 #{incustomerid},
			 #{incus_balance}
		)
	</insert>
	<select id="findVoucherMxListById" parameterType="pd" resultType="pd">
		select 
			a.incustomerid as assistaccountid,
			b.name as assistaccount, 
			 a.outmoney as debitmoney,
			 '' as creditmoney,
			 '往来账户调整' as abstracta,
			 '76' as subjectid,
			 FLOOR(RAND()*500000 + 500000) as rowid
		from 
			${ database }.t_calvatax as a
			left join ${ database }.c_customer as b on a.incustomerid = b.id
		where 
			a.id = #{calvataxid}
		union 
		select 
			a.outcustomerid as assistaccountid,
			b.name as assistaccount, 
			'' as debitmoney,
			 a.outmoney as creditmoney,
			 '往来账户调整' as abstracta,
			 '76' as subjectid,
			 FLOOR(RAND()*500000 + 500000) as rowid
		from 
			${ database }.t_calvatax as a
			left join ${ database }.c_customer as b on a.outcustomerid = b.id
		where 
			a.id = #{calvataxid}
	</select>
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.t_calvatax
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.t_calvatax
			set 
				 accountperiod = #{accountperiod},
				 code = #{code},
				 voucherdate = #{voucherdate},
				attachcount =  #{attachcount}
			where 
				id =  #{id}
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.voucherdate,
			a.attachcount,
			a.isaudit,
			a.isaccount,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_calvatax as a
		where 
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select 
			
			a.id
		from 
			${ pd.database }.t_calvatax as a 
	</select>
	<select id="findMaxCodeAndEtc" parameterType="pd" resultType="pd">
		select 
			a.code,
			0 as attachcount,
			a.id
		from 
			${database }.t_fillvouchers as a 
		order by a.code desc limit 1
	</select>
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.voucherdate,
			a.attachcount,
			a.isaudit,
			a.isaccount,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_calvatax as a
		where 
			a.accountperiod = #{ pd.accountperiod }
	</select>
	<insert id="saveCalvataxmx" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		
		insert into ${ database }.t_calvataxmx(
			calvataxid,
			abstracta,
			subjectid,
			assistaccount,
			debitmoney,
			creditmoney,
			rowid
		) values
		<foreach item="item" index="index" collection="addList" separator=",">
		 (
		 	#{item.calvataxid},
			#{item.abstracta},
			#{item.subjectid},
			#{item.assistaccount},
			<if test="item.debitmoney != null and item.debitmoney != '' " >
				#{item.debitmoney},
			</if>
			<if test="item.debitmoney == null or item.debitmoney == '' " >
				null,
			</if>
			<if test="item.creditmoney != null and item.creditmoney != '' " >
				#{item.creditmoney},
			</if>
			<if test="item.creditmoney == null or item.creditmoney == '' " >
				null,
			</if>
			#{item.rowid}
		)
		</foreach>
	</insert>
	<!-- 通过模板ID获取明细数据 -->
	<select id="findCalvataxmxByCalvataxid" parameterType="pd" resultType="pd">
		select 
			a.calvataxid,
			a.id as calvataxmxid,
			a.abstracta,
			a.subjectid,
			b.code as subjectcode,
			b.name as subjectname,
			a.assistaccount,
			a.debitmoney,
			a.creditmoney,
			1 as zt,
			rowid
		from 
			${ database }.t_calvataxmx as a
			left join ${ database }.c_accsubjects as b on b.id = a.subjectid
		where 
			a.calvataxid = #{ id }
	</select>
	<delete id="deleteCalvataxmxByCalvataxid" parameterType="pd">
		delete from t_calvataxmx
		where 
			calvataxid = #{DATA_IDS} and exists (select 1 from t_calvatax where id = #{DATA_IDS} and estatus = 1 and closestatus = 0)
	</delete>
	
	<!-- 删除不存在明细数据-->
	<delete id="deleteCalvataxmxNotIn" parameterType="pd">
		delete from t_calvataxmx
		where  calvataxid = #{calvataxid} and
			rowid not in
			<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
	              #{item}
			</foreach>
	</delete>
	
	<!-- 删除多个XX-->
	<delete id="deleteCalvataxmxByCalvataxids" parameterType="pd">
		delete from t_calvataxmx where calvataxid in (select id from t_calvatax where estatus = 1 and closestatus = 0 
			and id in
			<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
	              #{item}
			</foreach>
		) 
	</delete>
	
	<!-- 批量修改XX单明细 -->
	<update id="editCalvataxmx" parameterType="pd">
		<foreach item="item" index="index" collection="modList" separator=";">
			update t_calvataxmx
				set 
					calvataxid = #{item.calvataxid},
					abstracta = #{item.abstracta},
					subjectid = #{item.subjectid},
					assistaccount = #{item.assistaccount},
					<if test="item.debitmoney != null and item.debitmoney != '' " >
						debitmoney = #{item.debitmoney},
					</if>
					<if test="item.debitmoney == null or item.debitmoney == '' " >
						debitmoney = null,
					</if>
					<if test="item.creditmoney != null and item.creditmoney != '' " >
						creditmoney = #{item.creditmoney},
					</if>
					<if test="item.creditmoney == null or item.creditmoney == '' " >
						creditmoney = null,
					</if>
					rowid = #{item.rowid}
				where 
					id =  #{item.calvataxmxid}
		</foreach>
	</update>
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	
	</select>
	<!-- 通过会计期间查询  222101下的期初余额  -->
	<select id="findQcyeByPeriod" parameterType="pd" resultType="pd">
		select 
			sum(ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0)) begin_inventory
		from 
			${ database }.i_kmye i
			left join ${ database }.c_accsubjects c on i.subjectid = c.id
		where 
			(i.subjectid=(select id from ${ database }.c_accsubjects where flag='addedtax_1') 
			or c.parentid =(select id from ${ database }.c_accsubjects where flag='addedtax_1'))
			and str_to_date( i.accountperiod, '%Y-%m') &lt; str_to_date( #{accountperiod}, '%Y-%m')	
					
	</select>
	
	<!-- 通过会计期间查询  222101下的所有明细科目及当前会计期间的借方发生额、贷方发生额（含未记账凭证） -->
	<select id="findMxListByPeriod" parameterType="pd" resultType="pd">
		select 
			t.subjectid,
				t.subjectcode,
				t.subjectname,
				sum(t.debitmoney) as debitmoney,
				sum(t.creditmoney) as creditmoney
		from(
			select 
				b.id as subjectid,
				b.code as subjectcode,
				b.name as subjectname,
				a.debitmoney,
				a.creditmoney
			from 
				${ database }.i_kmye as a
				left join ${ database }.c_accsubjects as b on b.id = a.subjectid
			where 
				(a.subjectid=(select id from ${ database }.c_accsubjects where flag='addedtax_1') 
				or b.parentid =(select id from ${ database }.c_accsubjects where flag='addedtax_1'))
				and a.accountperiod=#{accountperiod}
			union all
			select
				c.id as subjectid,
				c.code as subjectcode,
				c.name as subjectname,
				ifnull(a.debitmoney,0) as debitmoney,
				ifnull(a.creditmoney,0) as creditmoney
			from 
				${ database }.t_fillvouchersmx as a
				left join ${ database }.t_fillvouchers as b on a.fillvouchersid=b.id
				left join ${ database }.c_accsubjects as c on a.subjectid = c.id
			where
				(c.id=(select id from ${ database }.c_accsubjects where flag='addedtax_1') 
				or c.parentid =(select id from ${ database }.c_accsubjects where flag='addedtax_1'))
				and b.accountperiod=#{accountperiod} and b.isaccount=0) as t group by t.subjectid
	</select>
	
		<!-- 通过会计期间查询  222101下的所有明细科目及当前会计期间的借方发生额、贷方发生额（含未记账凭证） 合计-->
	<select id="findTotalByPeriod" parameterType="pd" resultType="pd">
		select 
			ifnull(sum(t.debitmoney),0) as  debitmoneytotal,
			ifnull(sum(t.creditmoney),0) as creditmoneytotal
		from(
			select 
				b.id as subjectid,
				b.code as subjectcode,
				b.name as subjectname,
				a.debitmoney,
				a.creditmoney
			from 
				${ database }.i_kmye as a
				left join ${ database }.c_accsubjects as b on b.id = a.subjectid
			where 
				(a.subjectid=(select id from ${ database }.c_accsubjects where flag='addedtax_1') 
				or b.parentid =(select id from ${ database }.c_accsubjects where flag='addedtax_1'))
				and a.accountperiod=#{accountperiod}
			union 
			
				select
				c.id as subjectid,
				c.code as subjectcode,
				c.name as subjectname,
				ifnull(a.debitmoney,0) as debitmoney,
				ifnull(a.creditmoney,0) as creditmoney
			from 
				${ database }.t_fillvouchersmx as a
				left join ${ database }.t_fillvouchers as b on a.fillvouchersid=b.id
				left join ${ database }.c_accsubjects as c on a.subjectid = c.id
			where
				(c.id=(select id from ${ database }.c_accsubjects where flag='addedtax_1') 
				or c.parentid =(select id from ${ database }.c_accsubjects where flag='addedtax_1'))
				and b.accountperiod=#{accountperiod} and b.isaccount=0) as t
	</select>
	
	<!--计算附加税   查询取科目“addedtax_2”的本期贷方发生额。（含未记账凭证） -->
	
	<select id="getCaltaxbase" parameterType="pd" resultType="pd">
		select 
			ifnull(sum(ifnull(t.creditmoney,0)),0) as creditmoneytotal
		from(
			select 
				b.id as subjectid,
				b.code as subjectcode,
				b.name as subjectname,
				a.debitmoney,
				a.creditmoney
			from 
				${ database }.i_kmye as a
				left join ${ database }.c_accsubjects as b on b.id = a.subjectid
			where 
				(a.subjectid=(select id from ${ database }.c_accsubjects where flag='addedtax_2') 
				or b.parentid =(select id from ${ database }.c_accsubjects where flag='addedtax_2'))
				and a.accountperiod=#{accountperiod}
			union 
			
				select
				c.id as subjectid,
				c.code as subjectcode,
				c.name as subjectname,
				ifnull(a.debitmoney,0) as debitmoney,
				ifnull(a.creditmoney,0) as creditmoney
			from 
				${ database }.t_fillvouchersmx as a
				left join ${ database }.t_fillvouchers as b on a.fillvouchersid=b.id
				left join ${ database }.c_accsubjects as c on a.subjectid = c.id
			where
				(c.id=(select id from ${ database }.c_accsubjects where flag='addedtax_2') 
				or c.parentid =(select id from ${ database }.c_accsubjects where flag='addedtax_2'))
				and b.accountperiod=#{accountperiod} and b.isaccount=0) as t
	</select>
	<!-- 计算附加税   查找税率设置 -->
	<select id="findTaxrateSet" parameterType="pd" resultType="pd">
		select
			a.name as taxname,
			a.taxrate,
			a.subject_debit_id,
			a.subject_credit_id,
			c1.name subject_debit_name,
			c2.name subject_credit_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id as taxid
		from 
			${database }.c_taxrateset a
			left join ${database }.c_accsubjects c1 on a.subject_debit_id = c1.id
			left join ${database }.c_accsubjects c2 on a.subject_credit_id = c2.id
		where 
			a.closestatus=0
	</select>
	<!-- 计算所得税  根据期间查找净利润 -->
	<select id="findProfitByPeriod" parameterType="pd" resultType="pd">
		select 
			ifnull(sum(ifnull(t.creditmoney,0)) -sum(ifnull(t.debitmoney,0)),0) as  profit
		from(
			select 
				b.id as subjectid,
				b.code as subjectcode,
				b.name as subjectname,
				a.debitmoney,
				a.creditmoney
			from 
				${ database }.i_kmye as a
				left join ${ database }.c_accsubjects as b on b.id = a.subjectid
			where 
				(a.subjectid=(select id from ${ database }.c_accsubjects where flag='profit') 
				or b.parentid =(select id from ${ database }.c_accsubjects where flag='profit'))
				and a.accountperiod=#{accountperiod}
			union 
			
				select
				c.id as subjectid,
				c.code as subjectcode,
				c.name as subjectname,
				ifnull(a.debitmoney,0) as debitmoney,
				ifnull(a.creditmoney,0) as creditmoney
			from 
				${ database }.t_fillvouchersmx as a
				left join ${ database }.t_fillvouchers as b on a.fillvouchersid=b.id
				left join ${ database }.c_accsubjects as c on a.subjectid = c.id
			where
				(c.id=(select id from ${ database }.c_accsubjects where flag='profit') 
				or c.parentid =(select id from ${ database }.c_accsubjects where flag='profit'))
				and b.accountperiod=#{accountperiod} and b.isaccount=0) as t
	</select>
	
	<!-- 附加税生成凭证，查找税率设置中的借方科目和贷方科目 -->
	<select id="findTaxratesetById" parameterType="pd" resultType="pd">
		select 
			a.subject_debit_id,
			a.subject_credit_id
		from 
			${ database }.c_taxrateset as a
		where
			a.id=#{taxid}
	</select>
</mapper>