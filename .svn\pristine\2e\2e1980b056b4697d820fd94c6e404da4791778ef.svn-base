package org.newstanding.common.utils;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.RandomAccessFile;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.imageio.stream.FileImageOutputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

import org.newstanding.common.entity.PageData;

import Decoder.BASE64Decoder;
import Decoder.BASE64Encoder;

public class FileUtil {

	/**
	 * 创建目录
	 * 
	 * @param destDirName
	 *            目标目录名
	 * @return 目录创建成功返回true，否则返回false
	 */
	public static boolean createDir(String destDirName) {
		File dir = new File(destDirName);
		if (dir.exists()) {
			return false;
		}
		if (!destDirName.endsWith(File.separator)) {
			destDirName = destDirName + File.separator;
		}
		// 创建单个目录
		if (dir.mkdirs()) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * @param path
	 *            文件路径
	 * @param suffix
	 *            后缀名, 为空则表示所有文件
	 * @param isdepth
	 *            是否遍历子目录
	 * @return list
	 */
	public static List<String> getListFiles(String path, String suffix,
			boolean isdepth) {
		List<String> lstFileNames = new ArrayList<String>();
		File file = new File(path);
		return listFile(lstFileNames, file, suffix, isdepth);
	}

	private static List<String> listFile(List<String> lstFileNames, File f,
			String suffix, boolean isdepth) {
		// 若是目录, 采用递归的方法遍历子目录
		if (f.isDirectory()) {
			File[] t = f.listFiles();

			for (int i = 0; i < t.length; i++) {
				if (isdepth || t[i].isFile()) {
					listFile(lstFileNames, t[i], suffix, isdepth);
				}
			}
		} else {
			String filePath = f.getAbsolutePath();
			if (!suffix.equals("")) {
				int begIndex = filePath.lastIndexOf("."); // 最后一个.(即后缀名前面的.)的索引
				String tempsuffix = "";

				if (begIndex != -1) {
					tempsuffix = filePath.substring(begIndex + 1,
							filePath.length());
					if (tempsuffix.equals(suffix)) {
						lstFileNames.add(filePath);
					}
				}
			} else {
				lstFileNames.add(filePath);
			}
		}
		return lstFileNames;
	}

	/**
	 * JAVA截取字符串右侧指定长度的字符串
	 *
	 * @param input
	 *            输入字符串
	 * @param count
	 *            截取长度
	 * @return 截取字符串
	 */
	public static String right(String input, int count) {
		if ("".equals(input)) {
			return "";
		}
		count = (count > input.length()) ? input.length() : count;
		return input.substring(input.length() - count, input.length());
	}

	/**
	 * 删除文件
	 * 
	 * @param filePathAndName
	 *            String 文件路径及名称 如c:/fqf.txt
	 * @param fileContent
	 *            String
	 * @return boolean
	 */
	public static void delFile(String filePathAndName) {
		try {
			String filePath = filePathAndName;
			filePath = filePath.toString();
			java.io.File myDelFile = new java.io.File(filePath);
			myDelFile.delete();

		} catch (Exception e) {
			System.out.println("删除文件操作出错");
			e.printStackTrace();

		}

	}

	/**
	 * 读取到字节数组0
	 * 
	 * @param filePath
	 *            //路径
	 * @throws IOException
	 */
	public static byte[] getContent(String filePath) throws IOException {
		File file = new File(filePath);
		long fileSize = file.length();
		if (fileSize > Integer.MAX_VALUE) {
			System.out.println("file too big...");
			return null;
		}
		FileInputStream fi = new FileInputStream(file);
		byte[] buffer = new byte[(int) fileSize];
		int offset = 0;
		int numRead = 0;
		while (offset < buffer.length
				&& (numRead = fi.read(buffer, offset, buffer.length - offset)) >= 0) {
			offset += numRead;
		}
		// 确保所有数据均被读取
		if (offset != buffer.length) {
			throw new IOException("Could not completely read file "
					+ file.getName());
		}
		fi.close();
		return buffer;
	}

	/**
	 * 读取到字节数组1
	 * 
	 * @param filePath
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray(String filePath) throws IOException {

		File f = new File(filePath);
		if (!f.exists()) {
			throw new FileNotFoundException(filePath);
		}
		ByteArrayOutputStream bos = new ByteArrayOutputStream((int) f.length());
		BufferedInputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(f));
			int buf_size = 1024;
			byte[] buffer = new byte[buf_size];
			int len = 0;
			while (-1 != (len = in.read(buffer, 0, buf_size))) {
				bos.write(buffer, 0, len);
			}
			return bos.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			bos.close();
		}
	}

	/**
	 * 读取到字节数组2
	 * 
	 * @param filePath
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray2(String filePath) throws IOException {

		File f = new File(filePath);
		if (!f.exists()) {
			throw new FileNotFoundException(filePath);
		}

		FileChannel channel = null;
		FileInputStream fs = null;
		try {
			fs = new FileInputStream(f);
			channel = fs.getChannel();
			ByteBuffer byteBuffer = ByteBuffer.allocate((int) channel.size());
			while ((channel.read(byteBuffer)) > 0) {
				// do nothing
				// System.out.println("reading");
			}
			return byteBuffer.array();
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				channel.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			try {
				fs.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * Mapped File way MappedByteBuffer 可以在处理大文件时，提升性能
	 * 
	 * @param filename
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray3(String filePath) throws IOException {

		FileChannel fc = null;
		RandomAccessFile rf = null;
		try {
			rf = new RandomAccessFile(filePath, "r");
			fc = rf.getChannel();
			MappedByteBuffer byteBuffer = fc.map(MapMode.READ_ONLY, 0,
					fc.size()).load();
			// System.out.println(byteBuffer.isLoaded());
			byte[] result = new byte[(int) fc.size()];
			if (byteBuffer.remaining() > 0) {
				// System.out.println("remain");
				byteBuffer.get(result, 0, byteBuffer.remaining());
			}
			return result;
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				rf.close();
				fc.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * 输出文件流
	 * 
	 * @param in
	 * @param response
	 * @throws IOException
	 */
	public static void outFile(InputStream in, HttpServletResponse response)
			throws IOException {
		byte[] b = new byte[1024];
		int len = -1;
		while ((len = in.read(b, 0, 1024)) != -1) {
			response.getOutputStream().write(b, 0, len);
		}
	}

	public static String outFile(InputStream in) throws IOException {
		byte[] b = new byte[1024];
		StringBuffer out = new StringBuffer();
		int len = -1;
		while ((len = in.read(b, 0, 1024)) != -1) {
			// response.getOutputStream().write(b, 0, len);
			out.append(new String(b, 0, len));
		}
		return out.toString();
	}

	public static InputStream getInputStream(String src) {

		InputStream inputStream = new ByteArrayInputStream(src.getBytes());

		return inputStream;
	}

	/*public static List<String> getTemplateFileNamesByServiceName(
			String servicename) {

		String ppth = "";
		String path1 = FileUtil.class.getClassLoader().getResource("/")
				.getPath(), path = path1
				.substring(0, path1.indexOf("/WEB-INF"));

		ppth = path + "/WEB-INF/jsp/public/jxls/template/" + servicename
				+ "/list/";
		List<String> list = new ArrayList<String>();
		if ("".equals(ppth)) {
			return list;
		}
		ppth = ppth.replace("file:/", "");
		List<String> fileNameList = getListFiles(ppth, "xls", false);

		for (String fileName : fileNameList) {
			String name = fileName.replace("\\", "/");
			String aa = right(name, name.length() - name.lastIndexOf("/") - 1);
			String temp = aa.substring(0, aa.indexOf("."));
			list.add(temp);
			System.out.println(temp);
		}
		return list;
	}
*/
	public static boolean exists(String path) {
		File dir = new File(path);
		if (dir.exists()) {
			return true;
		} else {
			return false;
		}
	}

	public static String streamToString(InputStream in) throws IOException {
		StringBuffer out = new StringBuffer();
		byte[] b = new byte[4096];
		for (int n; (n = in.read(b)) != -1;) {
			out.append(new String(b, 0, n));
		}
		System.err.println(out.toString());
		return out.toString();
	}

	public static String convertStreamToString(InputStream is) {
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		StringBuilder sb = new StringBuilder();
		String line = null;
		try {
			while ((line = reader.readLine()) != null) {
				sb.append(line + "/n");
			}
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				is.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		return sb.toString();
	}

	public static void uploadFile(byte[] fs, String path) {
		try {
			FileImageOutputStream imageOutput = new FileImageOutputStream(
					new File(path));
			imageOutput.write(fs, 0, fs.length);
			imageOutput.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static byte[] HexToByte(String hexString) {
		int len = hexString.length();
		byte[] b = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			// 两位一组，表示一个字节,把这样表示的16进制字符串，还原成一个字节
			b[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4) + Character
					.digit(hexString.charAt(i + 1), 16));
		}
		return b;
	}

	/****************************************** 图片转换成 16进制 字符串 ***********************************************************/
	/*
	 * 实现字节数组向十六进制的转换方法一
	 */
	public static String byte2HexStr(byte[] b) {
		String hs = "";
		String stmp = "";
		for (int n = 0; n < b.length; n++) {
			stmp = (Integer.toHexString(b[n] & 0XFF));
			if (stmp.length() == 1)
				hs = hs + "0" + stmp;
			else
				hs = hs + stmp;
		}
		return hs.toUpperCase();
	}

	private static byte uniteBytes(String src0, String src1) {
		byte b0 = Byte.decode("0x" + src0).byteValue();
		b0 = (byte) (b0 << 4);
		byte b1 = Byte.decode("0x" + src1).byteValue();
		byte ret = (byte) (b0 | b1);
		return ret;
	}

	/*
	 * 实现字节数组向十六进制的转换的方法二
	 */
	public static String bytesToHexString(byte[] src) {

		StringBuilder stringBuilder = new StringBuilder("");
		if (src == null || src.length <= 0) {
			return null;
		}
		for (int i = 0; i < src.length; i++) {
			int v = src[i] & 0xFF;
			String hv = Integer.toHexString(v);
			if (hv.length() < 2) {
				stringBuilder.append(0);
			}
			stringBuilder.append(hv);
		}
		return stringBuilder.toString();

	}

	public static String saveStrFile(FileInputStream fis) {

		try {
			StringBuffer sb = new StringBuffer();
			/* FileInputStream fis = new FileInputStream("d:/123.png"); */
			BufferedInputStream bis = new BufferedInputStream(fis);
			java.io.ByteArrayOutputStream bos = new java.io.ByteArrayOutputStream();

			byte[] buff = new byte[1024];
			int len = 0;

			while ((len = fis.read(buff)) != -1) {
				bos.write(buff, 0, len);
			}

			// 得到图片的字节数组
			byte[] result = bos.toByteArray();
			/* System.out.println("++++"+byte2HexStr(result)); */
			// 字节数组转成十六进制
			return byte2HexStr(result);
			/* uploadFile(HexToByte(str), ""); */
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public static String saveStrFile2(FileInputStream fis) {

		try {
			StringBuffer sb = new StringBuffer();
			/* FileInputStream fis = new FileInputStream("d:/123.png"); */
			BufferedInputStream bis = new BufferedInputStream(fis);
			java.io.ByteArrayOutputStream bos = new java.io.ByteArrayOutputStream();

			byte[] buff = new byte[1024];
			int len = 0;

			while ((len = fis.read(buff)) != -1) {
				bos.write(buff, 0, len);
			}

			// 得到图片的字节数组
			byte[] result = bos.toByteArray();
			/* System.out.println("++++"+byte2HexStr(result)); */
			// 字节数组转成十六进制
			return binary(result,2);
			/* uploadFile(HexToByte(str), ""); */
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}
	
	/** 
     * 将byte[]转为各种进制的字符串 
     * @param bytes byte[] 
     * @param radix 基数可以转换进制的范围，从Character.MIN_RADIX到Character.MAX_RADIX，超出范围后变为10进制 
     * @return 转换后的字符串 
     */  
    public static String binary(byte[] bytes, int radix){  
        return new BigInteger(1, bytes).toString(radix);// 这里的1代表正数  
    }  
    
	public static void downloadFile(String imageStr,
			HttpServletResponse response) throws IOException {
		InputStream in = new ByteArrayInputStream(
				HexToByte(binaryString2hexString(imageStr)));
		outFile(in, response);
		/* uploadFile(HexToByte(imageStr), ""); */
	}

	public static String binaryString2hexString(String bString) {
		if (bString == null || bString.equals("") || bString.length() % 8 != 0) {
			return null;
		}
		StringBuffer tmp = new StringBuffer();
		int iTmp = 0;
		for (int i = 0; i < bString.length(); i += 4) {
			iTmp = 0;
			for (int j = 0; j < 4; j++) {
				iTmp += Integer.parseInt(bString.substring(i + j, i + j + 1)) << (4 - j - 1);
			}
			tmp.append(Integer.toHexString(iTmp));
		}
		return tmp.toString();
	}

	public static void main(String[] args) {
		/*
		 * String dirName = "d:/FH/topic/";// 创建目录 FileUtil.createDir(dirName);
		 */
		try {
			StringBuffer sb = new StringBuffer();
			FileInputStream fis = new FileInputStream("d:/123.png");
			BufferedInputStream bis = new BufferedInputStream(fis);
			java.io.ByteArrayOutputStream bos = new java.io.ByteArrayOutputStream();

			byte[] buff = new byte[1024];
			int len = 0;
			while ((len = fis.read(buff)) != -1) {
				bos.write(buff, 0, len);
			}
			// 得到图片的字节数组
			byte[] result = bos.toByteArray();

			System.out.println("++++" + byte2HexStr(result));
			// 字节数组转成十六进制
			String str = byte2HexStr(result);

			uploadFile(HexToByte(str), "");
			/*
			 * 将十六进制串保存到txt文件中
			 * 
			 * PrintWriter pw = new PrintWriter(new
			 * FileWriter("d://today.txt")); pw.println(str); pw.close();
			 */
		} catch (IOException e) {
		}

	}
	
	/**
	 * 将ListMap 复制成 字符串
	 * @param _dataList
	 * @param headers
	 * @param columns
	 * @return
	 * @throws IOException
	 */
	public static String copyToStr(List<PageData> _dataList,String headers,String columns) throws IOException {

		String res_content = "";
		if (_dataList != null) {

			String content = "";
			String[] headers_strs = headers.split(",");
			String[] columns_strs = columns.split(",");
			int i = 0,n = headers_strs.length;
			//添加 title
			for (String head : headers_strs) {
				
				if (i == n-1) {
					content += head+"\r\n";;
				}else{
					content += head+"\t";
				}
				i++;
					
			}
			res_content += content;
			//添加 内容
			n = columns_strs.length;
			for (int j = 0; j < _dataList.size(); j++) {
				Map row = (Map) _dataList.get(j);
				// 写入文件
				i = 0;
				content = "";
				for (String col : columns_strs) {
					if (row.get(col) != null) {
						if (content.equals("")) {
							content += row.get(col).toString();
						}else{
							content += "\t"+row.get(col).toString();
						}
					}
					
					if (i == n-1) {
						content += "\r\n";;
					}
					i++;
				}
				res_content += content;
			}
		}
		
		return res_content;
	}
	
	private static String filenameTemp;
	private static String path = "D://";
	
	 /**
     * 创建文件
     * @param fileName  文件名称
     * @param filecontent   文件内容
     * @return  是否创建成功，成功则返回true
     */
    public static boolean createFile(String fileName,String filecontent){
        Boolean bool = false;
        File file = new File(fileName);
        try {
            //如果文件不存在，则创建新的文件
            if(file.exists()){
            	
            	delFile(fileName);
            }
            file.createNewFile();
            bool = true;
            System.out.println("success create file,the file is "+fileName);
            //创建文件成功后，写入内容到文件里
            writeFileContent(fileName, filecontent);
            bool = true;
            
        } catch (Exception e) {
            e.printStackTrace();
            bool = false;
        }
        
        return bool;
    }
    
    /**
     * 向文件中写入内容
     * @param filepath 文件路径与名称
     * @param newstr  写入的内容
     * @return
     * @throws IOException
     */
    public static boolean writeFileContent(String filepath,String newstr) throws IOException{
        Boolean bool = false;
        String filein = newstr+"\r\n";//新写入的行，换行
        String temp  = "";
        
        FileInputStream fis = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        FileOutputStream fos  = null;
        PrintWriter pw = null;
        try {
            File file = new File(filepath);//文件路径(包括文件名称)
            //将文件读入输入流
            fis = new FileInputStream(file);
            isr = new InputStreamReader(fis, "UTF-8");
            br = new BufferedReader(isr);
            StringBuffer buffer = new StringBuffer();
            
            //文件原有内容
            for(int i=0;(temp =br.readLine())!=null;i++){
                buffer.append(temp);
                // 行与行之间的分隔符 相当于“\n”
                buffer = buffer.append(System.getProperty("line.separator"));
            }
            buffer.append(filein);
            
            fos = new FileOutputStream(file);
            pw = new PrintWriter(fos);
            pw.write(buffer.toString().toCharArray());
            pw.flush();
            bool = true;
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }finally {
            //不要忘记关闭
            if (pw != null) {
                pw.close();
            }
            if (fos != null) {
                fos.close();
            }
            if (br != null) {
                br.close();
            }
            if (isr != null) {
                isr.close();
            }
            if (fis != null) {
                fis.close();
            }
        }
        return bool;
    }
    
    
    //图片转化成base64字符串 
    public static String GetImageStr() 
    {//将图片文件转化为字节数组字符串，并对其进行Base64编码处理 
      String imgFile = "d://test.jpg";//待处理的图片 
      InputStream in = null; 
      byte[] data = null; 
      //读取图片字节数组 
      try 
      { 
        in = new FileInputStream(imgFile);     
        data = new byte[in.available()]; 
        in.read(data); 
        in.close(); 
      }  
      catch (IOException e)  
      { 
        e.printStackTrace(); 
      } 
      //对字节数组Base64编码 
      BASE64Encoder encoder = new BASE64Encoder(); 
      return encoder.encode(data);//返回Base64编码过的字节数组字符串 
    } 
    
    
    //base64字符串转化成图片 
    public static InputStream GenerateImage(String imgStr) 
    {  //对字节数组字符串进行Base64解码并生成图片 
      if (imgStr == null) //图像数据为空 
        return null; 
      BASE64Decoder decoder = new BASE64Decoder(); 
      try 
      { 
        //Base64解码 
        byte[] b = decoder.decodeBuffer(imgStr); 
        for(int i=0;i<b.length;++i) 
        { 
          if(b[i]<0) 
          {//调整异常数据 
            b[i]+=256; 
          } 
        } 
        
        return new ByteArrayInputStream(b);
      }  
      catch (Exception e)  
      { 
        return null; 
      } 
    }
    
    //16进制转二进制
    public static String hexString2binaryString(String hexString){  
        if (hexString == null || hexString.length() % 2 != 0)  
            return null;  
        String bString = "", tmp;  
        for (int i = 0; i < hexString.length(); i++)  
        {  
            tmp = "0000"  
                    + Integer.toBinaryString(Integer.parseInt(hexString  
                            .substring(i, i + 1), 16));  
            bString += tmp.substring(tmp.length() - 4);  
        }  
        return bString;  
    }

	
}