<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="PayableMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.t_payable(
			 outsupplierid,
			 outsup_balance,
			 outmoney,
			 insupplierid,
			 insup_balance
			
		) values 
		(
			 #{outsupplierid},
			 #{outsup_balance},
			 #{outmoney},
			 #{insupplierid},
			 #{insup_balance}
		)
	</insert>
	<select id="findVoucherMxListById" parameterType="pd" resultType="pd">
		select 
			a.outsupplierid as assistaccountid,
			b.name as assistaccount, 
			 a.outmoney as debitmoney,
			 '' as creditmoney,
			 '往来账户调整' as abstracta,
			 (select id from ${ database }.c_accsubjects where flag='2202')  as subjectid,
			 FLOOR(RAND()*500000 + 500000) as rowid
		from 
			${ database }.t_payable as a
			left join ${ database }.c_supplier as b on a.outsupplierid = b.id
		where 
			a.id = #{payableid}
		union 
		select 
			a.insupplierid as assistaccountid,
			b.name as assistaccount, 
			'' as debitmoney,
			 a.outmoney as creditmoney,
			 '往来账户调整' as abstracta,
			 (select id from ${ database }.c_accsubjects where flag='2202') as subjectid,
			 FLOOR(RAND()*500000 + 500000) as rowid
		from 
			${ database }.t_payable as a
			left join ${ database }.c_supplier as b on a.insupplierid = b.id
		where 
			a.id = #{payableid}
	</select>
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.t_payable
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.t_payable
			set 
				 accountperiod = #{accountperiod},
				 code = #{code},
				 voucherdate = #{voucherdate},
				attachcount =  #{attachcount}
			where 
				id =  #{id}
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.voucherdate,
			a.attachcount,
			a.isaudit,
			a.isaccount,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_payable as a
		where 
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select 
			
			a.id
		from 
			${ pd.database }.t_payable as a 
	</select>
	<select id="findMaxCodeAndEtc" parameterType="pd" resultType="pd">
		select 
			a.code,
			#{createby} as createby,
			0 as attachcount,
			a.id
		from 
			${database }.t_payable as a 
		order by a.code desc limit 1
	</select>
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.voucherdate,
			a.attachcount,
			a.isaudit,
			a.isaccount,
			a.createby,
			a.createtime,
			a.accountby,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_payable as a
		where 
			a.accountperiod = #{ pd.accountperiod }
	</select>
	<insert id="savePayablemx" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		
		insert into ${ database }.t_payablemx(
			payableid,
			abstracta,
			subjectid,
			assistaccount,
			debitmoney,
			creditmoney,
			rowid
		) values
		<foreach item="item" index="index" collection="addList" separator=",">
		 (
		 	#{item.payableid},
			#{item.abstracta},
			#{item.subjectid},
			#{item.assistaccount},
			<if test="item.debitmoney != null and item.debitmoney != '' " >
				#{item.debitmoney},
			</if>
			<if test="item.debitmoney == null or item.debitmoney == '' " >
				null,
			</if>
			<if test="item.creditmoney != null and item.creditmoney != '' " >
				#{item.creditmoney},
			</if>
			<if test="item.creditmoney == null or item.creditmoney == '' " >
				null,
			</if>
			#{item.rowid}
		)
		</foreach>
	</insert>
	<!-- 通过模板ID获取明细数据 -->
	<select id="findPayablemxByPayableid" parameterType="pd" resultType="pd">
		select 
			a.payableid,
			a.id as payablemxid,
			a.abstracta,
			a.subjectid,
			b.code as subjectcode,
			b.name as subjectname,
			a.assistaccount,
			a.debitmoney,
			a.creditmoney,
			1 as zt,
			rowid
		from 
			${ database }.t_payablemx as a
			left join ${ database }.c_accsubjects as b on b.id = a.subjectid
		where 
			a.payableid = #{ id }
	</select>
	<delete id="deletePayablemxByPayableid" parameterType="pd">
		delete from t_payablemx
		where 
			payableid = #{DATA_IDS} and exists (select 1 from t_payable where id = #{DATA_IDS} and estatus = 1 and closestatus = 0)
	</delete>
	
	<!-- 删除不存在明细数据-->
	<delete id="deletePayablemxNotIn" parameterType="pd">
		delete from t_payablemx
		where  payableid = #{payableid} and
			rowid not in
			<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
	              #{item}
			</foreach>
	</delete>
	
	<!-- 删除多个XX-->
	<delete id="deletePayablemxByPayableids" parameterType="pd">
		delete from t_payablemx where payableid in (select id from t_payable where estatus = 1 and closestatus = 0 
			and id in
			<foreach item="item" index="index" collection="array" open="(" separator="," close=")">
	              #{item}
			</foreach>
		) 
	</delete>
	
	<!-- 批量修改XX单明细 -->
	<update id="editPayablemx" parameterType="pd">
		<foreach item="item" index="index" collection="modList" separator=";">
			update t_payablemx
				set 
					payableid = #{item.payableid},
					abstracta = #{item.abstracta},
					subjectid = #{item.subjectid},
					assistaccount = #{item.assistaccount},
					<if test="item.debitmoney != null and item.debitmoney != '' " >
						debitmoney = #{item.debitmoney},
					</if>
					<if test="item.debitmoney == null or item.debitmoney == '' " >
						debitmoney = null,
					</if>
					<if test="item.creditmoney != null and item.creditmoney != '' " >
						creditmoney = #{item.creditmoney},
					</if>
					<if test="item.creditmoney == null or item.creditmoney == '' " >
						creditmoney = null,
					</if>
					rowid = #{item.rowid}
				where 
					id =  #{item.payablemxid}
		</foreach>
	</update>
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	
	</select>
</mapper>