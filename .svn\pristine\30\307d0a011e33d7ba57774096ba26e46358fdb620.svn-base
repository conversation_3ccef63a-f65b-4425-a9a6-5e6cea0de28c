<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="PurchaseInvoiceMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.t_purchaseinvoice(
			accountperiod,
			invoice_number,
			invoice_date,
			tmp_supplier,
			money,
			tax,
			advalorem,
			<if test="purchase_type_id != null and purchase_type_id !=''">purchase_type_id,</if>
			<if test="supplier_id != null and supplier_id !=''">supplier_id,</if>
			createby,createtime
		) values (
			#{accountperiod},
			#{invoice_number},
			#{invoice_date},
			#{tmp_supplier},
			#{money},
			#{tax},
			#{advalorem},
			<if test="purchase_type_id != null and purchase_type_id !=''">#{purchase_type_id},</if>
			<if test="supplier_id != null and supplier_id !=''">#{supplier_id},</if>
			#{createby},now()
		)
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.t_purchaseinvoice where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.t_purchaseinvoice
		set 
			accountperiod = #{accountperiod},
			invoice_number = #{invoice_number},
			invoice_date = #{invoice_date},
			tmp_supplier = #{tmp_supplier},
			money = #{money},
			tax = #{tax},
			advalorem = #{advalorem},
			<choose>
				<when test="purchase_type_id != null and purchase_type_id !=''">purchase_type_id = #{purchase_type_id},</when>
				<otherwise>purchase_type_id = null,</otherwise>
			</choose>
			<choose>
				<when test="supplier_id != null and supplier_id !=''">supplier_id = #{supplier_id},</when>
				<otherwise>supplier_id = null,</otherwise>
			</choose>
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
			a.accountperiod,
			a.invoice_number,
			a.invoice_date,
			a.tmp_supplier,
			c2.id tmp_supplier_id,
			a.money,
			a.tax,
			a.advalorem,
			a.supplier_id,
			ifnull(c1.name,'') supplier_name,
			a.purchase_type_id,
			ifnull(s.item,'') purchase_type,
			a.voucher_id,
			t.code voucher_number,
			a.has_instock,
			a.this_instock,
			a.pre_instock,
			ifnull(a.money,0)-ifnull(a.has_instock,0) as un_instock,
			ifnull(a.instockcode,'') instockcode,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.t_purchaseinvoice a
			left join ${ pd.database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ pd.database }.c_supplier c1 on a.supplier_id = c1.id
			left join ${ pd.database }.c_supplier c2 on a.tmp_supplier = c2.name
			left join ${ pd.database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where 
			a.id = #{ id }
	</select>
	
	<!-- 通过ID获取数据 -->
	<select id="findById1" parameterType="pd" resultType="pd">
		select
			a.accountperiod,
			a.invoice_number,
			a.invoice_date,
			a.tmp_supplier,
			c2.id tmp_supplier_id,
			a.money,
			a.tax,
			a.advalorem,
			a.supplier_id,
			ifnull(c1.name,'') supplier_name,
			a.purchase_type_id,
			ifnull(s.item,'') purchase_type,
			a.voucher_id,
			t.code voucher_number,
			0 now_instock,
			a.has_instock,
			a.this_instock,
			a.pre_instock,
			ifnull(a.money,0)-ifnull(a.has_instock,0) as un_instock,
			ifnull(a.instockcode,'') instockcode,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_purchaseinvoice a
			left join ${ database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ database }.c_supplier c1 on a.supplier_id = c1.id
			left join ${ database }.c_supplier c2 on a.tmp_supplier = c2.name
			left join ${ database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where 
			a.id = #{ id }
	</select>
	<select id="listPurchaselistmx" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.make_date,
			a.make_date as make_date1,
			a.code as code1,
			a.supplierid,
			b.name supplier_name,
			a.total_money,
			a.has_invoice,
			0 as now_invoice,
			ifnull(a.total_money,0) - ifnull(a.has_invoice,0) as un_invoice,
			a.this_invoice,
			a.pre_invoice,
			ifnull(a.invoicecode,'')  invoicecode,
			a.vouchercode,
			a.createby,
			a.createtime,
			a.estatus,
			a.closestatus,
			a.id,
			0 as ismodify,
			a.version
			
		from 
			${ database }.t_purchaselist as a 
			left join ${ database }.c_supplier as b on a.supplierid=b.id
		where 
				a.supplierid = #{supplierid } and left(a.accountperiod,7) &lt;=#{accountperiod}
				and a.id not in (select purchaseid from ${ database }.t_purchaseinvoice_instockmx where purchaseinvoiceid =#{id})
			<if test="q != null and q !=''">
				and (a.code like '%${ q }%'  or a.accountperiod like '%${ q }%' or b.name like '%${ q }%')
			</if>
	</select>
	
	<select id="listPurchaseInvoicemx" parameterType="pd" resultType="pd">
		select 
			a.id as instockmxid,
			a.purchaseinvoiceid,
			a.purchaseid as id,
			b.accountperiod,
			b.code,
			b.make_date,
			b.total_money,
			a.has_invoice,
			ifnull(b.total_money,0) - ifnull(b.has_invoice,0) as un_invoice,
			a.now_invoice,
			a.now_invoice as pre_now_invoice,
			ifnull(b.invoicecode,'') invoicecode,
			a.rowid,
			1 as ismodify
		from 
			${ database }.t_purchaseinvoice_instockmx as a 
			left join ${ database }.t_purchaselist as b on a.purchaseid = b.id
		where 
			a.purchaseinvoiceid = #{id}
			
	</select>
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.accountperiod,
			a.invoice_number,
			a.invoice_date,
			a.tmp_supplier,
			c2.id tmp_supplier_id,
			a.money,
			a.tax,
			a.advalorem,
			a.supplier_id,
			ifnull(c1.name,'') supplier_name,
			a.purchase_type_id,
			ifnull(s.item,'') purchase_type,
			a.voucher_id,
			t.code voucher_number,
			ifnull((select t.total_money from ${ pd.database }.t_purchaselist as t where t.invoicecode=a.invoice_number limit 1),0) as has_instock,
			case ifnull(s.is_stockcontrol,'0')
			when '1' then 
			ifnull(a.money,0)- ifnull((select ifnull(t.total_money,0) from ${ pd.database }.t_purchaselist as t where t.invoicecode=a.invoice_number limit 1),0)
			else 0 end as un_instock,
			ifnull(b.code,'') instockcode,
			ifnull(s.is_stockcontrol,0) as is_stockcontrol,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.t_purchaseinvoice a
			left join ${ pd.database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ pd.database }.c_supplier c1 on a.supplier_id = c1.id
			left join ${ pd.database }.c_supplier c2 on a.tmp_supplier = c2.name
			left join ${ pd.database }.c_purchasesubquickset s on a.purchase_type_id = s.id
			left join ${ pd.database }.t_purchaselist b on a.invoice_number= b.invoicecode
		where 
			left(a.accountperiod, 7) = #{pd.accountperiod}
		order by a.accountperiod, a.id
	</select>
	
	<select id="listAll" parameterType="pd" resultType="pd">
		select
			a.accountperiod,
			a.invoice_number,
			a.invoice_date,
			a.tmp_supplier,
			c2.id tmp_supplier_id,
			a.money,
			a.tax,
			a.advalorem,
			a.supplier_id,
			ifnull(c1.name,'') supplier_name,
			a.purchase_type_id,
			ifnull(s.item,'') purchase_type,
			a.voucher_id,
			t.code voucher_number,
			
				a.has_instock,
			a.this_instock,
			a.pre_instock,
			ifnull(a.money,0)-ifnull(a.has_instock,0) as un_instock,
			ifnull(a.instockcode,'') instockcode,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_purchaseinvoice a
			left join ${ database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ database }.c_supplier c1 on a.supplier_id = c1.id
			left join ${ database }.c_supplier c2 on a.tmp_supplier = c2.name
			left join ${ database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where
			<if test="cs !=null and cs !='' ">
				${cs}
			</if>
		order by a.accountperiod, a.id
	</select>
	
	
	<!-- 更新采购入库中的本次收票金额 -->
	<update id="updatePurchaseNowInvoiceByPurchaseid" parameterType="pd">
			update ${ database }.t_purchaselist
				set 
					this_invoice = ifnull(this_invoice,0)+ifnull(#{now_invoice},0) - ifnull(#{pre_now_invoice},0)
				where 
					id =  #{id}
	</update>
	
		<update id="updatePurchaseNowInvoiceByPurchaseid1" parameterType="pd">
			update ${ database }.t_purchaselist
				set 
					this_invoice = ifnull(this_invoice,0)-ifnull(#{now_invoice},0)
				where 
					id =  #{id}
	</update>

	<!-- 更新采购入库中的的已收票金额 -->
	<update id="updatePurchaseHasInvoiceByPurchaseid" parameterType="pd">
			update ${ database }.t_purchaselist
				set 
					has_invoice = ifnull(has_invoice,0)+ifnull(#{now_invoice},0) - ifnull(#{pre_now_invoice},0)
					<if test="invoice_number !='' ">
					,
					invoicecode = concat(ifnull(invoicecode,''),#{invoice_number},';')
					</if>
					
				where 
					id =  #{id}
	</update>
		<!-- 更新采购入库中的的已收票金额 -->
	<update id="updatePurchaseHasInvoiceByPurchaseid1" parameterType="pd">
			update ${ database }.t_purchaselist
				set 
					has_invoice = ifnull(has_invoice,0)-ifnull(#{now_invoice},0),
					invoicecode = #{invoicecode}
				where 
					id =  #{id}
	</update>

	<!-- 更新采购发票中的  本期入库 前期入库  入库单号 -->
	<update id="updateInvoiceHasInstockByInvoiceid" parameterType="pd">
			update ${ database }.t_purchaseinvoice
				set 
					has_instock = ifnull(has_instock,0) + ifnull(#{now_instock},0) + ifnull(#{pre_instock},0),
					this_instock = ifnull(this_instock,0)+ifnull(#{now_instock},0),
					pre_instock = ifnull(pre_instock,0)+ifnull(#{pre_instock},0),
					instockcode = #{instock_str}
				where 
					id =  #{id}
	</update>
	
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.accountperiod,
			a.invoice_number,
			a.invoice_date,
			a.tmp_supplier,
			c2.id tmp_supplier_id,
			a.money,
			a.tax,
			a.advalorem,
			a.supplier_id,
			ifnull(c1.name,'') supplier_name,
			ifnull(c1.name,'') supplier_name_tmp,
			a.purchase_type_id,
			s.item purchase_type,
			a.voucher_id,
			t.code voucher_number,
				a.has_instock,
			a.this_instock,
			a.pre_instock,
			ifnull(a.money,0)-ifnull(a.has_instock,0) as un_instock,
			ifnull(a.instockcode,'') instockcode,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_purchaseinvoice a
			left join ${ database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ database }.c_supplier c1 on a.supplier_id = c1.id
			left join ${ database }.c_supplier c2 on a.tmp_supplier = c2.name
			left join ${ database }.c_purchasesubquickset s on a.purchase_type_id = s.id
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	</select>
	
	<!-- 采购入库明细start -->
	
	<insert id="saveInstockmx" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		
		insert into ${ database }.t_purchaseinvoice_instockmx(
			purchaseinvoiceid,
			purchaseid,
			total_money,
			has_invoice,
			now_invoice,
			un_invoice,
			rowid
		) values
		<foreach item="item" index="index" collection="addList" separator=",">
		 (
		 	#{item.purchaseinvoiceid},
			#{item.id},
			#{item.total_money},
			#{item.has_invoice},
			#{item.now_invoice},
			#{item.un_invoice},
			#{item.rowid}
		)
		</foreach>
	</insert>
	
	<!-- 批量修改XX单明细 -->
	<update id="editInstockmx" parameterType="pd">
		<foreach item="item" index="index" collection="modList" separator=";">
			update ${ database }.t_purchaseinvoice_instockmx
				set 
					purchaseinvoiceid = #{item.purchaseinvoiceid},
					purchaseid = #{item.id},
					total_money = #{item.total_money},
					has_invoice = #{item.has_invoice},
					now_invoice = #{item.now_invoice},
					un_invoice = #{item.un_invoice},
					rowid = #{item.rowid}
				where 
					id =  #{item.instockmxid}
		</foreach>
	</update>
	
	<delete id="deletePurchaseinstockmxByid" parameterType="pd">
		delete from ${ database }.t_purchaseinvoice_instockmx
		where  purchaseinvoiceid = #{id}
	</delete>
	<delete id="deleteInstockmxNotIn" parameterType="pd">
		delete from ${ database }.t_purchaseinvoice_instockmx
		where  purchaseinvoiceid = #{purchaseinvoiceid} and
			rowid not in
			<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
	              #{item}
			</foreach>
	</delete>
	<!-- 采购入库明细end -->
	<!-- 验证是否存在单据已审核 -->
	<select id="checkVoucherCreateBefore" parameterType="pd" resultType="pd">
		select count(*) num from ${ database }.t_purchaseinvoice a where a.id in ( ${ids} ) and ifnull(a.voucher_id,'') != '' 
	</select>
	
	<!-- 生成凭证  逐笔生成  主表 -->
	<select id="voucherCreate1" parameterType="pd" resultType="pd">
		select 
				#{accountperiod} accountperiod,
				a.accountperiod voucherdate,
				1 attachcount,
				#{createby} createby,
				'purchaseinvoice' source,
				a.id source_ids
		from
				${ database }.t_purchaseinvoice a
		where 
				a.id in ( ${ids} ) 
	</select>
	
	<update id="updateVocherAuditTime" parameterType="pd">
		update  ${ database }.t_purchaseinvoice a
			set a.audittime = now()
		where 
				a.id in ( ${ids} ) 
	</update>
	<!-- 生成凭证  逐笔生成  明细表  采购入库 -->
	<select id="voucherCreateMx1_1" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				s.accsubject_id subjectid,
				c.name subject_name,
				c.auxbus,
				case ifnull(s.is_stockcontrol,'0') when '1' then ifnull(a.this_instock,0) else ifnull(a.money,0) end as debitmoney, 
				'purchaseinvoice' source,
				a.id source_ids,
				a.invoice_number source_code,
				a.invoice_date source_date,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a,
				${ database }.c_purchasesubquickset s,
				${ database }.c_accsubjects c
		where
				a.purchase_type_id = s.id and s.accsubject_id = c.id
				and a.id in ( ${ids} ) 
				and (case ifnull(s.is_stockcontrol,'0') when '1'
				then  ifnull(a.this_instock,0) 
				else ifnull(a.money,0) end ) !=0
	</select>
	
	<!-- 生成凭证  逐笔生成  明细表  进项税金-->
	<select id="voucherCreateMx1_2" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				#{accsubjectsid} subjectid,
				#{subject_name} subject_name,
				#{auxbus} auxbus,
				a.tax debitmoney, 
				'purchaseinvoice' source,
				a.id source_ids,
				a.invoice_number source_code,
				a.invoice_date source_date,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a
		where
				a.id in ( ${ids} ) and ifnull(a.tax,0) !=0
	</select>
	
	<!-- 生成凭证  逐笔生成  明细表  应付账款-->
	<select id="voucherCreateMx1_3" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				#{accsubjectsid} subjectid, 
				#{subject_name} subject_name,
				#{auxbus} auxbus,
				case #{auxbus} when 2 then r.id else '' end assistaccountid, 
				case #{auxbus} when 2 then r.name else '' end assistaccount,
				ifnull(a.advalorem,0)-ifnull(a.pre_instock,0) creditmoney,
				'purchaseinvoice' source,
				a.id source_ids,
				a.invoice_number source_code,
				a.invoice_date source_date,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a 
				left join ${ database }.c_supplier r on a.supplier_id = r.id
		where
				a.id in ( ${ids} ) and ifnull(a.advalorem,0)-ifnull(a.pre_instock,0) != 0
	</select>
	<!-- 生成凭证  逐笔生成  明细表  商品未入库-->
	<select id="voucherCreateMx1_4" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				#{accsubjectsid} subjectid,
				#{subject_name} subject_name,
				#{auxbus} auxbus,
				case ifnull(s.is_stockcontrol,'0')
				when '1' then ifnull(a.money,0)-ifnull(a.pre_instock,0)-ifnull(a.this_instock,0)
				else 0 end as debitmoney, 
				'purchaseinvoice' source,
				a.id source_ids,
				a.invoice_number source_code,
				a.invoice_date source_date,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a
				left join ${ database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where
				a.id in ( ${ids} ) and ifnull(a.money,0)-ifnull(a.pre_instock,0)-ifnull(a.this_instock,0) !=0
	</select>
	<!-- 生成凭证  按时间汇总  主表 -->
	<select id="voucherCreate2" parameterType="pd" resultType="pd">
		select 
				#{accountperiod} accountperiod,
				concat(#{accountperiod},'-',
					case 
						when (floor((day(accountperiod)-1)/${day})+1)*${day} &lt; 10 then concat('0',(floor((day(accountperiod)-1)/${day})+1)*${day})
						when (floor((day(accountperiod)-1)/${day})+1)*${day} &gt; 30 and month(accountperiod) != 2 then '31'
						when (floor((day(accountperiod)-1)/${day})+1)*${day} &gt;= 30 and month(accountperiod) = 2 then day(last_day(accountperiod))
						else (floor((day(accountperiod)-1)/${day})+1)*${day} 
					end) voucherdate,
				count(*) attachcount,
				#{createby} createby,
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids
		from 
				${ database }.t_purchaseinvoice a
		where
				a.id in ( ${ids} ) 
		group by floor((day(a.accountperiod)-1)/ ${day} )
	</select>
	
	
	<!-- 生成凭证  按类型+时间汇总  主表 -->
	<select id="voucherCreate4" parameterType="pd" resultType="pd">
		select 
				#{accountperiod} accountperiod,
				concat(#{accountperiod},'-',
					case 
						when (floor((day(accountperiod)-1)/${day})+1)*${day} &lt; 10 then concat('0',(floor((day(accountperiod)-1)/${day})+1)*${day})
						when (floor((day(accountperiod)-1)/${day})+1)*${day} &gt; 30 and month(accountperiod) != 2 then '31'
						when (floor((day(accountperiod)-1)/${day})+1)*${day} &gt;= 30 and month(accountperiod) = 2 then day(last_day(accountperiod))
						else (floor((day(accountperiod)-1)/${day})+1)*${day} 
					end) voucherdate,
				count(*) attachcount,
				#{createby} createby,
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids
		from 
				${ database }.t_purchaseinvoice a
		where
				a.id in ( ${ids} ) 
		group by a.purchase_type_id,floor((day(a.accountperiod)-1)/ ${day} ) order by a.accountperiod asc,purchase_type_id asc
	</select>
	<!-- 生成凭证  按时间汇总  明细表  采购入库 -->
	<select id="voucherCreateMx2_1" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				s.accsubject_id subjectid, 
				c.name subject_name,
				c.auxbus,
				case ifnull(s.is_stockcontrol,'0') when '1' then sum(ifnull(a.this_instock,0)) else sum(ifnull(a.money,0)) end as debitmoney, 
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from 
				${ database }.t_purchaseinvoice a,
				${ database }.c_purchasesubquickset s,
				${ database }.c_accsubjects c
		where
				a.purchase_type_id = s.id and s.accsubject_id = c.id and ifnull(c.auxbus,0) = 0
				and a.id in ( ${ids} ) and (case ifnull(s.is_stockcontrol,'0') when '1'
				then  ifnull(a.this_instock,0) 
				else ifnull(a.money,0) end ) !=0
		group by floor((day(a.accountperiod)-1)/ ${day} ),s.accsubject_id
		
		union all
		
		select 
				#{defaulttranmemo} abstracta, 
				s.accsubject_id subjectid,
				c.name subject_name,
				c.auxbus,
				case ifnull(s.is_stockcontrol,'0') when '1' then sum(ifnull(a.this_instock,0)) else sum(ifnull(a.money,0)) end as debitmoney, 
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from 
				${ database }.t_purchaseinvoice a,
				${ database }.c_purchasesubquickset s,
				${ database }.c_accsubjects c
		where
				a.purchase_type_id = s.id and s.accsubject_id = c.id and ifnull(c.auxbus,0) != 0
				and a.id in ( ${ids} ) and (case ifnull(s.is_stockcontrol,'0') when '1'
				then  ifnull(a.this_instock,0) 
				else ifnull(a.money,0) end ) !=0
		group by floor((day(a.accountperiod)-1)/ ${day} ),a.id
	</select>
	
	<!-- 生成凭证  按时间汇总  明细表  进项税金-->
	<select id="voucherCreateMx2_2" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				#{accsubjectsid} subjectid, 
				#{subject_name} subject_name,
				#{auxbus} auxbus,
				sum(a.tax) debitmoney, 
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a
		where
				a.id in ( ${ids} ) and ifnull(a.tax,0) !=0
		group by floor((day(a.accountperiod)-1)/ ${day} )
	</select>
		<!-- 生成凭证  按时间汇总  明细表  商品未入库-->
	<select id="voucherCreateMx2_4" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				#{accsubjectsid} subjectid, 
				#{subject_name} subject_name,
				#{auxbus} auxbus,
				case ifnull(s.is_stockcontrol,'0')
				when '1' then sum(ifnull(a.money,0)-ifnull(a.pre_instock,0)-ifnull(a.this_instock,0))
				else 0 end as debitmoney, 
			
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a
				left join ${ database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where
				a.id in ( ${ids} )  and ifnull(a.money,0)-ifnull(a.pre_instock,0)-ifnull(a.this_instock,0) !=0
		group by floor((day(a.accountperiod)-1)/ ${day} )
	</select>
	
	<!-- 生成凭证  全月汇总  主表 -->
	<select id="voucherCreate3" parameterType="pd" resultType="pd">
		select 
				#{accountperiod} accountperiod,
				last_day( concat(#{accountperiod},'_1') ) voucherdate,
				count(*) attachcount,
				#{createby} createby,
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids
		from 
				${ database }.t_purchaseinvoice a
		where
				a.id in ( ${ids} )
	</select>
	
	<!-- 生成凭证  全月汇总  明细表  采购入库 -->
	<select id="voucherCreateMx3_1" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				s.accsubject_id subjectid, 
				c.name subject_name,
				c.auxbus,
				case ifnull(s.is_stockcontrol,'0') when '1' then sum(ifnull(a.this_instock,0)) else sum(ifnull(a.money,0)) end as debitmoney,  
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a,
				${ database }.c_purchasesubquickset s,
				${ database }.c_accsubjects c
		where
				a.purchase_type_id = s.id and s.accsubject_id = c.id and ifnull(c.auxbus,0) = 0
				and a.id in ( ${ids} ) 
				and (case ifnull(s.is_stockcontrol,'0') when '1'
				then  ifnull(a.this_instock,0) 
				else ifnull(a.money,0) end ) !=0
		group by s.accsubject_id
		
		union all
		
		select 
				#{defaulttranmemo} abstracta, 
				s.accsubject_id subjectid, 
				c.name subject_name,
				c.auxbus,
				case ifnull(s.is_stockcontrol,'0') when '1' then sum(ifnull(a.this_instock,0)) else sum(ifnull(a.money,0)) end as debitmoney, 
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a,
				${ database }.c_purchasesubquickset s,
				${ database }.c_accsubjects c
		where
				a.purchase_type_id = s.id and s.accsubject_id = c.id and ifnull(c.auxbus,0) != 0
				and a.id in ( ${ids} ) and (case ifnull(s.is_stockcontrol,'0') when '1'
				then  ifnull(a.this_instock,0) 
				else ifnull(a.money,0) end ) !=0
		group by a.id
	</select>
	
	<!-- 生成凭证  全月汇总  明细表  进项税金-->
	<select id="voucherCreateMx3_2" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				#{accsubjectsid} subjectid, 
				#{subject_name} subject_name,
				#{auxbus} auxbus,
				sum(a.tax) debitmoney, 
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a 
		where
				a.id in ( ${ids} ) and ifnull(a.tax,0) !=0
	</select>
		<!-- 生成凭证  按时间汇总  明细表  商品未入库-->
	<select id="voucherCreateMx3_4" parameterType="pd" resultType="pd">
		select 
				#{defaulttranmemo} abstracta, 
				#{accsubjectsid} subjectid, 
				#{subject_name} subject_name,
				#{auxbus} auxbus,
				case ifnull(s.is_stockcontrol,'0')
				when '1' then sum(ifnull(a.money,0)-ifnull(a.pre_instock,0)-ifnull(a.this_instock,0))
				else 0 end as debitmoney, 
				'purchaseinvoice' source,
				group_concat(a.id order by a.id) source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_purchaseinvoice a
				left join ${ database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where
				a.id in ( ${ids} )  and ifnull(a.money,0)-ifnull(a.pre_instock,0)-ifnull(a.this_instock,0) !=0
	</select>
	<!-- 生成凭证后回写id -->
	<update id="voucherCreateAfter" parameterType="pd">
		update 
				${ database }.t_purchaseinvoice a,
				${ database }.t_fillvouchers t
		set 
				a.voucher_id = t.id
		where
				locate( concat(',', a.id, ','), concat(',', t.source_ids, ',')) != 0
				and t.source = 'purchaseinvoice'
				and a.id in ( ${ids} ) and t.estatus = 2
	</update>
	
	<!-- 查询单据凭证ID -->
	<select id="findVoucherId" parameterType="pd" resultType="pd">
		select 
				group_concat(a.voucher_id) ids
		from
				${ database }.t_purchaseinvoice a
		where
				a.id in ( ${ids} )
	</select>
	
	<!-- 撤销凭证后清除单据凭证ID -->
	<update id="voucherRevokeAfter" parameterType="pd">
		update 
				${ database }.t_purchaseinvoice a
		set 
				a.voucher_id = null
		where
				a.id in ( ${ids} )
	</update>
	
	
	<select id="findSupplieridByName" parameterType="pd" resultType="pd">
	select 
				id as supplierid
		from
				${ database }.c_supplier a
		where
				a.name =#{supplier_name} and a.closestatus =0
	</select>
	
	<select id="findGoodsidByName" parameterType="pd" resultType="pd">
	select 
				id as goodsid,
				floor((rand() * 1000000)) rowid
		from
				${ database }.c_goods a
		where
				a.name =#{goods_name} and a.closestatus = 0
	</select>
	<select id="findPuchaselistByCode" parameterType="pd" resultType="pd">
		select
			a.*
		from 
			${ database }.t_purchaselist a
		where 
			a.code = #{ code }
	</select>
	<select id="findByCode" parameterType="pd" resultType="pd">
		select
			a.accountperiod,
			a.invoice_number,
			a.invoice_date,
			a.tmp_supplier,
			c2.id tmp_supplier_id,
			a.money,
			a.tax,
			a.advalorem,
			a.supplier_id,
			ifnull(c1.name,'') supplier_name,
			a.purchase_type_id,
			ifnull(s.item,'') purchase_type,
			a.voucher_id,
			t.code voucher_number,
			0 now_instock,
			a.has_instock,
			a.this_instock,
			a.pre_instock,
			ifnull(a.money,0)-ifnull(a.has_instock,0) as un_instock,
			ifnull(a.instockcode,'') instockcode,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_purchaseinvoice a
			left join ${ database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ database }.c_supplier c1 on a.supplier_id = c1.id
			left join ${ database }.c_supplier c2 on a.tmp_supplier = c2.name
			left join ${ database }.c_purchasesubquickset s on a.purchase_type_id = s.id
		where 
			a.invoice_number = #{ invoicecode }
	</select>
	
	<select id="findPurchaseByPurchaseid" parameterType="pd" resultType="pd">
		select 
			a.accountperiod,
			a.code,
			a.make_date,
			a.make_date as make_date1,
			a.code as code1,
			a.supplierid,
			b.name supplier_name,
			a.total_money,
			a.has_invoice,
			#{total_money} as now_invoice,
			0 as un_invoice,
			a.this_invoice,
			a.pre_invoice,
			ifnull(a.invoicecode,'')  invoicecode,
			a.vouchercode,
			a.createby,
			a.createtime,
			a.estatus,
			a.closestatus,
			a.id,
			0 as ismodify,
			floor((rand() * 1000000)) rowid,
			a.version
			
		from 
			${ database }.t_purchaselist as a 
			left join ${ database }.c_supplier as b on a.supplierid=b.id
		where 
			a.id=#{purchselistid}
	</select>
	
	
	
	
	<select id="findPuchaseInvoiceByInvoicecode" parameterType="pd" resultType="pd">
		select 
			a.*
		from 
			${ database }.t_purchaseinvoice as a 
		where 
			a.invoice_number=#{invoicecode}
	</select>
</mapper>