<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:aop="http://www.springframework.org/schema/aop" 
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/aop 
						http://www.springframework.org/schema/aop/spring-aop.xsd
						http://www.springframework.org/schema/context 
						http://www.springframework.org/schema/context/spring-context.xsd
						http://www.springframework.org/schema/tx 
						http://www.springframework.org/schema/tx/spring-tx.xsd">
	
	<!-- 启用注解 -->
	<context:annotation-config />
	<aop:aspectj-autoproxy />
	
	<bean name="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">   
    	<property name="dataSource" ref="dataSource"></property>
 	</bean>

	<bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">  
		<property name="locations">  
			<list>  
                 <value>classpath:dbconfig.properties</value>  
            </list>  
        </property>  
	</bean> 
	
	<!-- 阿里 druid数据库连接池 -->
	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close">  
         <!-- 数据库基本信息配置 -->
         <property name="url" value="${url}" />  
         <property name="username" value="${username}" />  
         <property name="password" value="${password}" />  
         <property name="driverClassName" value="${driverClassName}" />  
         <property name="filters" value="${filters}" />  
   		 <!-- 最大并发连接数 -->
         <property name="maxActive" value="${maxActive}" />
         <!-- 初始化连接数量 -->
         <property name="initialSize" value="${initialSize}" />
         <!-- 配置获取连接等待超时的时间 -->
         <property name="maxWait" value="${maxWait}" />
         <!-- 最小空闲连接数 -->
         <property name="minIdle" value="${minIdle}" />  
   		 <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
         <property name="timeBetweenEvictionRunsMillis" value="${timeBetweenEvictionRunsMillis}" />
         <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
         <property name="minEvictableIdleTimeMillis" value="${minEvictableIdleTimeMillis}" />  
         <property name="validationQuery" value="${validationQuery}" />  
         <property name="testWhileIdle" value="${testWhileIdle}" />  
         <property name="testOnBorrow" value="${testOnBorrow}" />  
         <property name="testOnReturn" value="${testOnReturn}" />  
         <property name="maxOpenPreparedStatements" value="${maxOpenPreparedStatements}" />
         <!-- 打开removeAbandoned功能 -->
         <property name="removeAbandoned" value="${removeAbandoned}" />
         <!-- 1800秒，也就是30分钟 -->
         <property name="removeAbandonedTimeout" value="${removeAbandonedTimeout}" />
         <!-- 关闭abanded连接时输出错误日志 -->   
         <property name="logAbandoned" value="${logAbandoned}" />
	</bean>  
	
	<tx:advice id="txAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="delete*" propagation="REQUIRED" read-only="false" 
			           rollback-for="java.lang.Exception"/>
			<tx:method name="insert*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="update*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="edit*" propagation="REQUIRED"  read-only="false"  
			           rollback-for="java.lang.Exception" />
			<tx:method name="save*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="add*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="change*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="importExcel*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="voucherCreate*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="accountAll*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			<tx:method name="calucation*" propagation="REQUIRED"  read-only="false" 
			           rollback-for="java.lang.Exception" />
			           
		</tx:attributes>
	</tx:advice>
	
	<aop:aspectj-autoproxy proxy-target-class="true"/>
	
	<!-- 启动组件扫描，排除@Controller组件，该组件由SpringMVC配置文件扫描 -->
	<!-- 自动扫描组件，这里要把controler下面的 controller去除，他们是在spring3-servlet.xml中配置的，如果不去除会影响事务管理的。   -->
	 <context:component-scan base-package="org.newstanding">
	      <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" /> 
	 </context:component-scan>
	
	<!-- 事物处理 -->
	<aop:config>
		<aop:pointcut id="pc" expression="execution(* org.newstanding.service..*(..))" />
		<aop:advisor pointcut-ref="pc" advice-ref="txAdvice" />
	</aop:config>
	
	<!-- 配置mybatis -->
	<bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
    	<property name="dataSource" ref="dataSource" />
        <property name="configLocation" value="classpath:mybatis/mybatis-config.xml"></property>
        <!-- mapper扫描 -->
        <property name="mapperLocations" value="classpath:mybatis/*/*.xml"></property>
    </bean>
    
    <bean id="sqlSessionTemplate" class="org.mybatis.spring.SqlSessionTemplate" scope="prototype">
		<constructor-arg ref="sqlSessionFactory" />
	</bean>
	
	<!-- ================ Shiro start ================ -->
		<bean id="securityManager" class="org.apache.shiro.web.mgt.DefaultWebSecurityManager">
			<property name="realm" ref="ShiroRealm" />
		</bean>
		
		<!-- 項目自定义的Realm -->
	    <bean id="ShiroRealm" class="org.newstanding.interceptor.shiro.ShiroRealm" ></bean>
		
		<!-- Shiro Filter -->
		<bean id="shiroFilter" class="org.apache.shiro.spring.web.ShiroFilterFactoryBean">
			<property name="securityManager" ref="securityManager" />
			
			<property name="loginUrl" value="/" />
			
			<property name="successUrl" value="/main/index" />
			
			<property name="unauthorizedUrl" value="/toLogin" />
			
			<property name="filterChainDefinitions">
				<value>
				/common/**                    = anon
				/plugins/**                 = anon
				/static/**/** 		        = anon
				/uploadFiles/uploadImgs/** 	= anon
	           	/code.do 					= anon
	           	/webService2/**             = anon
	           	/login/**                   = anon
	           	/login_login	 			= anon
	           	/**							= authc
				</value>
			</property>
		</bean>
	<!-- ================ Shiro end ================ -->
	
	<!-- <bean class="org.newstanding.interceptor.AuditAllInterceptor" />
	<bean class="org.newstanding.interceptor.SaveInterceptor" />
	<bean class="org.newstanding.interceptor.DeleteInterceptor" />
	<bean class="org.newstanding.interceptor.DeleteAllInterceptor" />
	<bean class="org.newstanding.interceptor.EditInterceptor" />
	<bean class="org.newstanding.interceptor.GoAddInterceptor" />
	<bean class="org.newstanding.interceptor.GoEditInterceptor" /> -->
	<import resource="classpath:cxf/cxf.xml" />
	
	<!-- 连接池配置 -->
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
		<!-- 最大连接数 -->
		<property name="maxTotal" value="30" />
		<!-- 最大空闲连接数 -->
		<property name="maxIdle" value="10" />
		<!-- 每次释放连接的最大数目 -->
		<property name="numTestsPerEvictionRun" value="1024" />
		<!-- 释放连接的扫描间隔（毫秒） -->
		<property name="timeBetweenEvictionRunsMillis" value="30000" />
		<!-- 连接最小空闲时间 -->
		<property name="minEvictableIdleTimeMillis" value="1800000" />
		<!-- 连接空闲多久后释放, 当空闲时间>该值 且 空闲连接>最大空闲连接数 时直接释放 -->
		<property name="softMinEvictableIdleTimeMillis" value="10000" />
		<!-- 获取连接时的最大等待毫秒数,小于零:阻塞不确定的时间,默认-1 -->
		<property name="maxWaitMillis" value="1500" />
		<!-- 在获取连接的时候检查有效性, 默认false -->
		<property name="testOnBorrow" value="true" />
		<!-- 在空闲时检查有效性, 默认false -->
		<property name="testWhileIdle" value="true" />
		<!-- 连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true -->
		<property name="blockWhenExhausted" value="false" />
	</bean>	
	<!-- jedis客户端单机版    ************* -->
	<bean id="redisClient" class="redis.clients.jedis.JedisPool">
		<constructor-arg name="host" value="*************"></constructor-arg>
		<constructor-arg name="port" value="6379"></constructor-arg>
		<constructor-arg name="poolConfig" ref="jedisPoolConfig"></constructor-arg>
	</bean>	
	<bean id="jedisClient" class="org.newstanding.dao.impl.JedisClientSingle"></bean>
</beans>
