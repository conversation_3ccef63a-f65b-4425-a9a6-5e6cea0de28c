<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TimeSpinner Actions - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>TimeSpinner Actions</h2>
	<p>Click the buttons below to perform actions.</p>
	<div style="margin:20px 0;">
		<a href="#" class="easyui-linkbutton" onclick="getValue()">GetValue</a>
		<a href="#" class="easyui-linkbutton" onclick="setValue()">SetValue</a>
		<a href="#" class="easyui-linkbutton" onclick="disable()">Disable</a>
		<a href="#" class="easyui-linkbutton" onclick="enable()">Enable</a>
	</div>
	<input id="dt" class="easyui-timespinner" style="width:80px;">
	<script>
		function getValue(){
			var val = $('#dt').timespinner('getValue');
			alert(val);
		}
		function setValue(){
			$('#dt').timespinner('setValue', '09:45');
		}
		function disable(){
			$('#dt').timespinner('disable');
		}
		function enable(){
			$('#dt').timespinner('enable');
		}
	</script>
</body>
</html>