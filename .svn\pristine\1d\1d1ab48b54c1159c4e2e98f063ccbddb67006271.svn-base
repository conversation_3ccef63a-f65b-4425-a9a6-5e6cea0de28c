<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="PrintSetMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_printset(
			menucode,
			name,
			title,
			content,
			html,
			pagelx,
			width,
			height,
			page_top,
			page_bottom,
			page_left,
			page_right,
			rowheight,
			abstractawidth,
			subjectnamewidth,
			debitmoneywidth,
			creditmoneywidth,
			pagegs,
			columnstr,
			hs
		) values (
			#{menucode},
			#{name},
			#{title},
			#{content},
			#{html},
			#{pagelx},
			#{width},
			#{height},
			#{page_top},
			#{page_bottom},
			#{page_left},
			#{page_right},
			#{rowheight},
			#{abstractawidth},
			#{subjectnamewidth},
			#{debitmoneywidth},
			#{creditmoneywidth},
			#{pagegs},
			#{columnstr},
			#{hs}
		)
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_printset where id = #{ id } 
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_printset
		set 
			menucode=#{menucode},
			name=#{name},
			title = #{title},
			<if test="content !='' and  content !=null">
			content = #{content},
			</if>
			pagelx = #{pagelx},
			width = #{width},
			height = #{height},
			page_top = #{page_top},
			page_bottom = #{page_bottom},
			page_left = #{page_left},
			page_right = #{page_right},
			<if test="rowheight !='' and  rowheight !=null">
			rowheight = #{rowheight},
			</if>
			<if test="abstractawidth !='' and  abstractawidth !=null">
			abstractawidth =#{abstractawidth},
			</if>
			<if test="subjectnamewidth !='' and  subjectnamewidth !=null">
			subjectnamewidth =#{subjectnamewidth},
			</if>
			<if test="debitmoneywidth !='' and  debitmoneywidth !=null">
			debitmoneywidth =#{debitmoneywidth},
			</if>
			<if test="creditmoneywidth !='' and  creditmoneywidth !=null">
			creditmoneywidth =#{creditmoneywidth},
			</if>
			columnstr = #{columnstr},
			hs = #{hs}
		where 
			name = #{ name } and menucode=#{menucode}
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			menucode,
			name,
			title,
			content,
			html,
			pagelx,
			width,
			height,
			page_top,
			page_bottom,
			page_left,
			page_right,
			rowheight,
			abstractawidth,
			subjectnamewidth,
			debitmoneywidth,
			creditmoneywidth,
			columnstr,
			hs
		from 
			${ database }.c_printset
		where 
			id = #{ id }
	</select>
	
	<!-- 通过NAME获取数据 -->
	<select id="findByCode" parameterType="pd" resultType="pd">
		select 
			id,
			menucode,
			name,
			title,
			content,
			html,
			pagelx,
			width,
			height,
			page_top,
			page_bottom,
			page_left,
			page_right,
			rowheight,
			abstractawidth,
			subjectnamewidth,
			debitmoneywidth,
			creditmoneywidth,
			columnstr,
			hs
		from 
			${ database }.c_printset
		where 
			name = #{ name } and menucode=#{menucode}
	</select>
	
		<select id="findPrintSetByMenucode" parameterType="pd" resultType="pd">
		select 
			id,
			menucode,
			name,
			title,
			content,
			html,
			pagelx,
			width,
			height,
			page_top,
			page_bottom,
			page_left,
			page_right,
			rowheight,
			abstractawidth,
			subjectnamewidth,
			debitmoneywidth,
			creditmoneywidth,
			hs,
			css,
			html,
			tdf,
			columnstr,
			pagegs
		from 
			${ database }.c_printset
		where 
			menucode = #{ menucode } 
			<if test="name !=null and name !='' ">
			and  name=#{name}
			</if>
	</select>
	
	<select id="findNamesByMenucode" parameterType="pd" resultType="pd">
		select 
			name
		from 
			${ database }.c_printset
		where 
			menucode = #{ menucode } 
	</select>
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select 
			menucode,
			name,
			title,
			content,
			html,
			pagelx,
			width,
			height,
			page_top,
			page_bottom,
			page_left,
			page_right,
			rowheight,
			abstractawidth,
			subjectnamewidth,
			debitmoneywidth,
			creditmoneywidth,
			columnstr,
			hs
		from 
			${ database }.c_printset
	</select>
	
</mapper>