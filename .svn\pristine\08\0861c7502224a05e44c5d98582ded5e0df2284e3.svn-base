package org.newstanding.interceptor;

import java.util.ArrayList;
import java.util.List;



import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@Aspect
@SuppressWarnings("unused")
public class DeleteInterceptor {
    /** 执行前拦截 */
    @Before("execution(* org.newstanding.service..*.delete(..))")
    public void before(JoinPoint point) throws Throwable {
        System.out.println("执行方法：" + point.getSignature().getDeclaringTypeName() + "." + point.getSignature().getName());
    }
 
    /** 执行后拦截 */
    @After("execution(* org.newstanding.service..*.delete(..))")
    public void after(JoinPoint point) throws Throwable {
        System.out.println("执行完成：" + point.getSignature().getDeclaringTypeName() + "." + point.getSignature().getName());
    }
 
    /**
     * @param point
     * @return
     * @throws Throwable
     */
	@Around("execution(* org.newstanding.service..*.delete(..))")
    public Object aroundDelete(ProceedingJoinPoint point) throws Throwable {
        return point.proceed();
    }
}
