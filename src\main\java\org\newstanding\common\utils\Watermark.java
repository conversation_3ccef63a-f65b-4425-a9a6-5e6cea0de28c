package org.newstanding.common.utils;

import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;

import javax.imageio.ImageIO;

import com.sun.image.codec.jpeg.JPEGCodec;
import com.sun.image.codec.jpeg.JPEGImageEncoder;

public class Watermark {
	
		private	static String strFWATERM,strIWATERM;
		
		static{
			strFWATERM = Tools.readTxtFile(Const.FWATERM);	//读取文字水印配置
			strIWATERM = Tools.readTxtFile(Const.IWATERM);	//读取图片水印配置
		}
		
		/**
		 * 刷新
		*/
		public static void fushValue(){
			strFWATERM = Tools.readTxtFile(Const.FWATERM);	//读取文字水印配置
			strIWATERM = Tools.readTxtFile(Const.IWATERM);	//读取图片水印配置
		}
			
		/**
		 * @param imagePath 图片全路径
		*/
	  	public static void setWatemark(String imagePath){ 
	  		//文字水印
	  		if(null != strFWATERM && !"".equals(strFWATERM)){
				String strFW[] = strFWATERM.split(",fh,");
				if(strFW.length == 5){
					if("yes".equals(strFW[0])){
						pressText(strFW[1].toString(), imagePath, "", 1, Color.RED,Integer.parseInt(strFW[2]), Integer.parseInt(strFW[3]), Integer.parseInt(strFW[4]));	//文字
					}
				}
			}
	  		//图片水印
			if(null != strIWATERM && !"".equals(strIWATERM)){
				String strIW[] = strIWATERM.split(",fh,");
				if(strIW.length == 4){
//					if("yes".equals(strIW[0])){
//						pressImage(PathUtil.getClasspath() + Const.FILEPATHIMG+strIW[1], imagePath, Integer.parseInt(strIW[2]), Integer.parseInt(strIW[3]));
//					}
				}
			}
		  } 
	
	  
	  
	    /**
	     * 把图片印刷到图片上
	     * 
	     * @param pressImg --
	     *            水印文件
	     * @param targetImg --
	     *            目标文件
	     * @param x
	     *            --x坐标
	     * @param y
	     *            --y坐标
	     */
	    public final static void pressImage(String pressImg, String targetImg,
	            int x, int y) {
	        try {
	            //目标文件
	            File _file = new File(targetImg);
	            Image src = ImageIO.read(_file);
	            int wideth = src.getWidth(null);
	            int height = src.getHeight(null);
	            BufferedImage image = new BufferedImage(wideth, height,
	                    BufferedImage.TYPE_INT_RGB);
	            Graphics g = image.createGraphics();
	            g.drawImage(src, 0, 0, wideth, height, null);

	            //水印文件
	            File _filebiao = new File(pressImg);
	            Image src_biao = ImageIO.read(_filebiao);
	            int wideth_biao = src_biao.getWidth(null);
	            int height_biao = src_biao.getHeight(null);
	            //g.drawImage(src_biao, (wideth - wideth_biao) / 2,(height - height_biao) / 2, wideth_biao, height_biao, null);
	            g.drawImage(src_biao, x, y, wideth_biao, height_biao, null);
	            //水印文件结束
	            g.dispose();
	            FileOutputStream out = new FileOutputStream(targetImg);
	            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
	            encoder.encode(image);
	            out.close();
	        } catch (Exception e) {
	            e.printStackTrace();
	        }
	    }

	    /**
	     * 打印文字水印图片
	     * 
	     * @param pressText
	     *            --文字
	     * @param targetImg --
	     *            目标图片
	     * @param fontName --
	     *            字体名
	     * @param fontStyle --
	     *            字体样式
	     * @param color --
	     *            字体颜色
	     * @param fontSize --
	     *            字体大小
	     * @param x --
	     *            偏移量
	     * @param y
	     */

	    public static void pressText(String pressText, String targetImg,
	    		String fontName, int fontStyle, Color color, int fontSize, int x,int y) {
	        try {
	            File _file = new File(targetImg);
	            Image src = ImageIO.read(_file);
	            int wideth = src.getWidth(null);
	            int height = src.getHeight(null);
	            BufferedImage image = new BufferedImage(wideth, height,
	                    BufferedImage.TYPE_INT_RGB);
	            Graphics g = image.createGraphics();
	            g.drawImage(src, 0, 0, wideth, height, null);
	            g.setColor(color);
	            g.setFont(new Font(fontName, fontStyle, fontSize));
	            g.drawString(pressText, x, y);
	            g.dispose();
	            FileOutputStream out = new FileOutputStream(targetImg);
	            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
	            encoder.encode(image);
	            out.close();
	        } catch (Exception e) {
	            System.out.println(e);
	        }
	    }

    /**
     * Remove the last icon from PNG image
     * Keep the original image dimensions and pixels unchanged
     *
     * @param sourcePath -- Source PNG file path
     * @param targetPath -- Target PNG file path
     * @param iconWidth -- Width of icon to remove
     * @param iconHeight -- Height of icon to remove
     * @param backgroundColor -- Background color for filling (use transparent if null)
     */
    public static void removeLastIcon(String sourcePath, String targetPath,
            int iconWidth, int iconHeight, Color backgroundColor) {
        try {
            // Read source PNG file
            File sourceFile = new File(sourcePath);
            BufferedImage sourceImage = ImageIO.read(sourceFile);

            int width = sourceImage.getWidth();
            int height = sourceImage.getHeight();

            // Create new BufferedImage, keep original type to support transparency
            BufferedImage targetImage = new BufferedImage(width, height,
                    sourceImage.getType());

            // Create Graphics2D object for drawing
            Graphics2D g2d = targetImage.createGraphics();

            // Set high quality rendering
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                    RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                    RenderingHints.VALUE_RENDER_QUALITY);

            // Copy original image
            g2d.drawImage(sourceImage, 0, 0, null);

            // Calculate position of last icon (assume bottom-right corner)
            int iconX = width - iconWidth;
            int iconY = height - iconHeight;

            // Remove last icon: fill area with background color or transparent
            if (backgroundColor != null) {
                g2d.setColor(backgroundColor);
                g2d.fillRect(iconX, iconY, iconWidth, iconHeight);
            } else {
                // Use transparent fill
                g2d.setComposite(java.awt.AlphaComposite.Clear);
                g2d.fillRect(iconX, iconY, iconWidth, iconHeight);
            }

            g2d.dispose();

            // Save as PNG file
            File targetFile = new File(targetPath);
            ImageIO.write(targetImage, "PNG", targetFile);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Remove icon at specified position from PNG image
     *
     * @param sourcePath -- Source PNG file path
     * @param targetPath -- Target PNG file path
     * @param iconX -- Icon X coordinate
     * @param iconY -- Icon Y coordinate
     * @param iconWidth -- Icon width
     * @param iconHeight -- Icon height
     * @param backgroundColor -- Background color (null for transparent)
     */
    public static void removeIconAtPosition(String sourcePath, String targetPath,
            int iconX, int iconY, int iconWidth, int iconHeight, Color backgroundColor) {
        try {
            // Read source PNG file
            File sourceFile = new File(sourcePath);
            BufferedImage sourceImage = ImageIO.read(sourceFile);

            int width = sourceImage.getWidth();
            int height = sourceImage.getHeight();

            // Create new BufferedImage, keep original type to support transparency
            BufferedImage targetImage = new BufferedImage(width, height,
                    sourceImage.getType());

            // Create Graphics2D object for drawing
            Graphics2D g2d = targetImage.createGraphics();

            // Set high quality rendering
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                    RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                    RenderingHints.VALUE_RENDER_QUALITY);

            // Copy original image
            g2d.drawImage(sourceImage, 0, 0, null);

            // Remove icon at specified position
            if (backgroundColor != null) {
                g2d.setColor(backgroundColor);
                g2d.fillRect(iconX, iconY, iconWidth, iconHeight);
            } else {
                // Use transparent fill
                g2d.setComposite(java.awt.AlphaComposite.Clear);
                g2d.fillRect(iconX, iconY, iconWidth, iconHeight);
            }

            g2d.dispose();

            // Save as PNG file
            File targetFile = new File(targetPath);
            ImageIO.write(targetImage, "PNG", targetFile);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }



}
