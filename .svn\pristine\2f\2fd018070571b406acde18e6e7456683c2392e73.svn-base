<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="InitialstoreMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		delete from ${ database }.t_initialstore;
		insert into ${ database }.t_initialstore(
			goodsid,
			qmsl,
			qmje
		) values
		<foreach item="item" index="index" collection="addList" separator=",">
		 (
		 	#{item.goodsid},
			#{item.qmsl},
			#{item.qmje}
		)
		</foreach>
	</insert>

	<!-- 列表 -->
	<select id="datalist" parameterType="pd" resultType="pd">
		select
			b.id as goodsid,	
			c.name as goodstype_name,
			b.name as goods_name,
			ifnull(a.qmsl,0) qmsl,
			ifnull(a.qmje,0) qmje,
			ifnull(a.estatus,0) as estatus,
			a.id
		from 
			 ${ database }.c_goods as b 
			 left join ${ database }.c_goodstype as c on b.typeid = c.id
			 left join ${ database }.t_initialstore a  on a.goodsid = b.id
		order by b.code
	</select>
	<select id="findList" parameterType="pd" resultType="pd">
		select
			a.*
		from  ${ database }.t_initialstore a 
		where a.estatus !=#{type}
	</select>
	
	<select id="findRkckList" parameterType="pd" resultType="pd">
		select sum(t.num) s_num from (
			<!-- 采购入库 -->
			select count(0) num from ${ database }.t_purchaselist 
			union
			<!-- 销售出库   -->
			select count(0) num from ${ database }.t_saleoutmx  
		) t
	</select>
	
		<insert id="saveCostAccounting" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.t_costaccounting(
			accountperiod,
			goodsid,
			qmsl,
			qmje,
			djtype
		) values
		<foreach item="item" index="index" collection="addList" separator=",">
		 (
		 	#{startusedate},
		 	#{item.goodsid},
			#{item.qmsl},
			#{item.qmje},
			'期初库存'
		)
		</foreach>
	</insert>
	
	<delete id="deleteCostAccounting" parameterType="pd">
		delete from ${ database }.t_costaccounting where djtype='期初库存'
	</delete>
	
	
	<update id="updateEstatus" parameterType="pd">
		update ${ database }.t_initialstore set estatus = #{type}
	</update>
</mapper>