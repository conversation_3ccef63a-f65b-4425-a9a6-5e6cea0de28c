<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="SaleSubQuickSetMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_salesubquickset(
			item,
			quickcode,
			accsubject_id,
			is_stockcontrol,
			createby,createtime
		) values (
			#{item},
			#{quickcode},
			#{accsubject_id},
			#{is_stockcontrol},
			#{createby},now()
		)
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_salesubquickset where id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_salesubquickset
		set 
			item = #{item},
			quickcode = #{quickcode},
			accsubject_id = #{accsubject_id},
			is_stockcontrol = #{is_stockcontrol},
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.item,
			a.quickcode,
			a.accsubject_id,
			t.name accsubject_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.is_stockcontrol,
			a.id
		from 
			${ database }.c_salesubquickset as a
			left join ${ database }.c_accsubjects as t on a.accsubject_id = t.id
		where 
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select 
			a.item,
			a.quickcode,
			a.accsubject_id,
			t.aliasname accsubject_name,
			a.estatus,
			a.closestatus,
			a.version,
			case ifnull(a.is_stockcontrol,'0') when '0' then '否'   when '1' then '是' end as is_stockcontrol,
			a.id
		from 
			${ pd.database }.c_salesubquickset as a
			left join ${ pd.database }.c_accsubjects as t on a.accsubject_id = t.id
		where 
			a.closestatus = 0
			<if test="pd.q != null and pd.q !=''">
				and (a.item like '%${ pd.q }%' or a.quickcode like '%${ pd.q }%')
			</if>
	</select>
	
	<!-- 列表 -->
	<select id="gridlistPage" parameterType="pd" resultType="pd">
		select 
			a.item,
			a.quickcode,
			a.accsubject_id,
			t.name accsubject_name,
			a.estatus,
			a.closestatus,
			a.version,
			case ifnull(a.is_stockcontrol,'0') when '0' then '否'   when '1' then '是' end as is_stockcontrol,
			a.id
		from 
			${ database }.c_salesubquickset as a
			left join ${ database }.c_accsubjects as t on a.accsubject_id = t.id
		where 
			a.closestatus = 0
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select 
			a.item,
			a.quickcode,
			a.accsubject_id,
			t.name accsubject_name,
			a.estatus,
			a.closestatus,
			a.version,
			case ifnull(a.is_stockcontrol,'0') when '0' then '否'   when '1' then '是' end as is_stockcontrol,
			a.id
		from 
			${ database }.c_salesubquickset as a
			left join ${ database }.c_accsubjects as t on a.accsubject_id = t.id
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	</select>
</mapper>