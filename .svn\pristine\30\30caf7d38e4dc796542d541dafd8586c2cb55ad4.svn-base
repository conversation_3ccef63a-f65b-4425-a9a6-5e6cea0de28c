package org.newstanding.common.utils.excel;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.CellRangeAddress;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.FileUtil;
import org.newstanding.common.utils.JsonUtils;

import com.alibaba.fastjson.JSONObject;
import com.hp.hpl.sparta.xpath.ThisNodeTest;


/** 
* 2015-4-29 
* DES:POI导出Excel
* author:JiBaoLe 
*/ 

@SuppressWarnings({ "unchecked", "unused", "rawtypes", "resource" })
public class ExportToExcelUtil<T> {  
	//每次设置导出数量
	public static int  NUM=65000;
	
	private static String fileName="";
	
	public  void setFileName(String fileName) {
		this.fileName = fileName;
	}

	
	//是否添加图片
	private boolean isTp;
	
    public boolean isTp() {
		return isTp;
	}
	public void setTp(boolean isTp) {
		this.isTp = isTp;
	}
	
	//图片字符串
	private String img_str;
	
	public String getImg_str() {
		return img_str;
	}
	public void setImg_str(String img_str) {
		this.img_str = img_str;
	}


	//标题
	private String title="";
	
	public  String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	/** 
     * 导出Excel的方法 
     * @param title excel中的sheet名称 
     * @param headers 表头 
     * @param result 结果集 
     * @param out 输出流 
     * @param pattern 时间格式 
     * @throws Exception 
     */   
    
	public void exportExcel( String[] headers,String[] columns, List<T> result,String file,OutputStream out,HttpServletRequest request, String pattern) throws Exception{   
    	
    	//File zip = new File(request.getRealPath("/files") + "/" +getFileName() + ".zip");// 压缩文件
    	int n=0;
		if (!CollectionUtils.isEmpty(result)) {
			if (result.size() % NUM == 0) {
				n = result.size() / NUM;
			} else {
				n = result.size() / NUM + 1;
			}
		}else{
    		n=1;
    	}
    	List<String> fileNames = new ArrayList();// 用于存放生成的文件名称s
    	//文件流用于转存文件
    	// 声明一个工作薄   
		Workbook workbook = new HSSFWorkbook();

    	for (int j = 0; j < n; j++) {
    		Collection<T> result1=null;
    	//切取每5000为一个导出单位，存储一个文件
    	//对不足5000做处理；
			if (!CollectionUtils.isEmpty(result)) {
				if (j == n - 1) {
					if (result.size() % NUM == 0) {
						result1 = result.subList(NUM * j, NUM * (j + 1));
					} else {
						result1 = result.subList(NUM * j,
								NUM * j + result.size() % NUM);
					}
				} else {
					result1 = result.subList(NUM * j, NUM * (j + 1));
				}
			}
        
        // 生成一个表格   
    	String sheet_title = getFileName()+ "-" +j;
        HSSFSheet sheet = (HSSFSheet) workbook.createSheet(sheet_title);   
        // 设置表格默认列宽度为18个字节   
        sheet.setDefaultColumnWidth((short)18);   
           
		FileOutputStream o = new FileOutputStream(file);
           
        // 生成一个样式   
        HSSFCellStyle style = (HSSFCellStyle) workbook.createCellStyle();   
        // 设置这些样式   
        style.setFillForegroundColor(HSSFColor.GOLD.index);   
        style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);   
        style.setBorderBottom(HSSFCellStyle.BORDER_THIN);   
        style.setBorderLeft(HSSFCellStyle.BORDER_THIN);   
        style.setBorderRight(HSSFCellStyle.BORDER_THIN);   
        style.setBorderTop(HSSFCellStyle.BORDER_THIN);   
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);   
        // 生成一个字体   
        HSSFFont font = (HSSFFont) workbook.createFont();   
        font.setColor(HSSFColor.VIOLET.index);   
        //font.setFontHeightInPoints((short) 12);   
        font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);   
        // 把字体应用到当前的样式   
        style.setFont(font);   
           
        // 指定当单元格内容显示不下时自动换行   
        style.setWrapText(true);   
         
        // 声明一个画图的顶级管理器  
        HSSFPatriarch patriarch = sheet.createDrawingPatriarch(); 
      
        // 产生表格标题行   
        //表头的样式 
        HSSFCellStyle titleStyle = (HSSFCellStyle) workbook.createCellStyle();// 创建样式对象 
        titleStyle.setAlignment(HSSFCellStyle.ALIGN_CENTER_SELECTION);// 水平居中 
        titleStyle.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);// 垂直居中 
        // 设置字体 
        HSSFFont titleFont = (HSSFFont) workbook.createFont(); // 创建字体对象 
        titleFont.setFontHeightInPoints((short) 15); // 设置字体大小 
        titleFont.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);// 设置粗体 
      //  titleFont.setFontName("黑体"); // 设置为黑体字 
        titleStyle.setFont(titleFont); 
        
        CellRangeAddress cra=new CellRangeAddress(0,(short)0,0,(short)(headers.length-1));//指定合并区域          
        sheet.addMergedRegion(cra);
        
        HSSFRow rowHeader = sheet.createRow(0);   
        HSSFCell cellHeader = rowHeader.createCell((short)0);   //只能往第一格子写数据，然后应用样式，就可以水平垂直居中 
        HSSFRichTextString textHeader = new HSSFRichTextString(title);   
        cellHeader.setCellStyle(titleStyle); 
        cellHeader.setCellValue(textHeader); 
        
        HSSFRow row = sheet.createRow(1);   
        for (int i = 0; i < headers.length; i++) {
            HSSFCell cell = row.createCell((short)i);   
            cell.setCellStyle(style);   
            HSSFRichTextString text = new HSSFRichTextString(headers[i]);   
            cell.setCellValue(text);   
         }   
         // 遍历集合数据，产生数据行   
         if(result1 != null){
             int index = 2;   
             for(T t:result1){  
                 row = sheet.createRow(index);   
                 index++; 
                 for(short i = 0; i < columns.length; i++) { 
                     HSSFCell cell = row.createCell(i); 
                     String fieldName = columns[i]; 
                     String getMethodName = "get" 
                         + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1); 
                     
                     Object value =  null;
                     if (t instanceof PageData) {
                    	 PageData testData  = (PageData) t;
                    	 if (testData.get(fieldName)!= null) {
                        	 value = testData.get(fieldName).toString(); 
    					 }
					 }else if(t instanceof JSONObject){
						 JSONObject testData  = (JSONObject) t;
                    	 if (testData.get(fieldName)!= null) {
                        	 value = testData.get(fieldName).toString(); 
    					 }
					 }
                    
                     String textValue = null; 
                     if(value == null) { 
                         textValue = ""; 
                     }else if (value instanceof Date) { 
                         Date date = (Date) value; 
                         SimpleDateFormat sdf = new SimpleDateFormat(pattern); 
                          textValue = sdf.format(date); 
                      }  else if (value instanceof byte[]) { 
                         // 有图片时，设置行高为60px; 
                         row.setHeightInPoints(60); 
                         // 设置图片所在列宽度为80px,注意这里单位的一个换算 
                         sheet.setColumnWidth(i, (short) (35.7 * 80)); 
                         byte[] bsValue = (byte[]) value; 
                         HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 
                               1023, 255, (short) 6, index, (short) 6, index); 
                         anchor.setAnchorType(2); 
                         patriarch.createPicture(anchor, workbook.addPicture( 
                               bsValue, HSSFWorkbook.PICTURE_TYPE_JPEG)); 
                      } else{ 
                         //其它数据类型都当作字符串简单处理 
                         textValue = value.toString(); 
                      } 
                      
                     if(textValue!= null){ 
                         Pattern p = Pattern.compile("^-?[0-9]+\\.?[0-9]*(E[0-9]+)?$");   
                         Matcher matcher = p.matcher(textValue); 
                         if(matcher.matches()){ 
                            //是数字当作double处理 
                            cell.setCellValue(Double.parseDouble(textValue)); 
                         }else{ 
                            HSSFRichTextString richString = new HSSFRichTextString(textValue); 
                            cell.setCellValue(richString); 
                         } 
                      } 
                 } 
             }
         }
         
         if (isTp) {
        	 
        	 InputStream in = FileUtil.GenerateImage(img_str);
        	 
        	 ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();  
        	 BufferedImage bufferImg = ImageIO.read(in);
             ImageIO.write(bufferImg, "png", byteArrayOut);  
            //画图的顶级管理器，一个sheet只能获取一个（一定要注意这点）  
             HSSFPatriarch patriarch1 = sheet.createDrawingPatriarch();     
             //anchor主要用于设置图片的属性  
             HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 255, 255,(short) 1, 1, (short) 5, 8);     
             anchor.setAnchorType(3);     
             //插入图片    
             patriarch.createPicture(anchor, workbook.addPicture(byteArrayOut.toByteArray(), HSSFWorkbook.PICTURE_TYPE_JPEG));
		 }
          
         workbook.write(o);
        /* File srcfile[] = new File[fileNames.size()];
 		for (int i = 0, n1 = fileNames.size(); i < n1; i++) {
 			srcfile[i] = new File(fileNames.get(i));
 		}*/
 		/*ZipFiles(srcfile, zip);
 		FileInputStream inStream = new FileInputStream(zip);
 		byte[] buf = new byte[4096];
 		int readLength;
 		while (((readLength = inStream.read(buf)) != -1)) {
 			out.write(buf, 0, readLength);
 		}
 		inStream.close();*/
    	}
     }
	
	public void exportErrDataExcel(PageData data, String file) throws Exception{
		Workbook workbook = new HSSFWorkbook();		// 声明一个工作薄   
		FileOutputStream o = new FileOutputStream(file);
		
		for( int i = 0 ; i < 10 ; i++ ){
			List<PageData> resList = new ArrayList<PageData>();
			List<String> headers = new ArrayList<String>();
			List<String> columns = new ArrayList<String>();
			headers.add("错误信息");
			columns.add("err_msg");
			if(data.get("res"+i) != null){
				resList = (List<PageData>)((PageData)data.get("res"+i)).get("errPdList");
				String cstr = ((PageData)data.get("res"+i)).get("columns").toString();
				List<PageData> list =  JsonUtils.StringToListpd(cstr);
				String hstr = "";
				for (PageData map : list) {
					if(!"id".equals(map.get("title").toString()) && !"tmp_execl_table".equals(map.get("title").toString())){
						headers.add(map.get("title").toString());
						columns.add(map.get("dataIndex").toString());
					}
				}
			}else{
				break;
			}
			HSSFSheet sheet = (HSSFSheet) workbook.createSheet(((PageData)data.get("res"+i)).get("sheet").toString());
			sheet.setDefaultColumnWidth((short)18);
			HSSFCellStyle style = (HSSFCellStyle) workbook.createCellStyle();	// 生成一个样式
			// 设置样式
			style.setFillForegroundColor(HSSFColor.GOLD.index);
			style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
			style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
			style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
			style.setBorderRight(HSSFCellStyle.BORDER_THIN);
			style.setBorderTop(HSSFCellStyle.BORDER_THIN);
			style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
			HSSFFont font = (HSSFFont) workbook.createFont();	// 生成一个字体
			font.setColor(HSSFColor.VIOLET.index);
			font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
			style.setFont(font);		// 把字体应用到当前的样式
			style.setWrapText(true);	// 指定当单元格内容显示不下时自动换行
			HSSFPatriarch patriarch = sheet.createDrawingPatriarch();	// 声明一个画图的顶级管理器
			
			HSSFRow row = sheet.createRow(0);
			for (int j = 0; j < headers.size(); j++) {
				HSSFCell cell = row.createCell((short)j);
				cell.setCellStyle(style);
				HSSFRichTextString text = new HSSFRichTextString(headers.get(j));
				cell.setCellValue(text);
			}
			int index = 1;
			for(PageData res : resList){
				row = sheet.createRow(index);
				index++;
				for(short j = 0; j < columns.size(); j++) {
					HSSFCell cell = row.createCell(j);
					String fieldName = columns.get(j);
					String getMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
					String value =  "";
					if (res.get(fieldName)!= null) {
						value = res.get(fieldName).toString();
					}
					Pattern p = Pattern.compile("^//d+(//.//d+)?$");
					Matcher matcher = p.matcher(value);
					if(matcher.matches()){
						cell.setCellValue(Double.parseDouble(value));	//是数字当作double处理
					}else{
						HSSFRichTextString richString = new HSSFRichTextString(value); 
						cell.setCellValue(richString); 
					} 
				} 
			}
		}
			
		workbook.write(o);
	}

    //获取文件名字
    public static String getFileName(){
    	// 文件名获取
		Date date = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		String f = fileName + format.format(date);
		return f;
    }
    //压缩文件
    public static void ZipFiles(java.io.File[] srcfile, java.io.File zipfile) {
		byte[] buf = new byte[1024];
		try {
			ZipOutputStream out = new ZipOutputStream(new FileOutputStream(
					zipfile));
			for (int i = 0; i < srcfile.length; i++) {
				FileInputStream in = new FileInputStream(srcfile[i]);
				out.putNextEntry(new ZipEntry(srcfile[i].getName()));
				int len;
				while ((len = in.read(buf)) > 0) {
					out.write(buf, 0, len);
				}
				out.closeEntry();
				in.close();
			}
			out.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
    
    /** 设置响应头 */
	public void setResponseHeader(HttpServletResponse response,String fileName) {
		try {
			this.fileName=fileName;
			response.reset();// 清空输出流
			response.setContentType("application/octet-stream;charset=UTF-8");
			response.setHeader("Content-Disposition", "attachment;filename="
					+new String(this.title.getBytes("GB2312"), "8859_1")
					+ ".zip");
			response.addHeader("Pargam", "no-cache");
			response.addHeader("Cache-Control", "no-cache");
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
 }   