<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<script type="text/javascript" src="static/plugin/jquery-easyui-1.5.3/datagrid-cellediting.js"></script>
</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div class="receivable_div">
			<div class="receivable_divtow">
			    <div class="bgdivtit" style="border: 0;border-bottom: 1px #98a7c6 solid;">
			    	<input type="hidden" id="id" name="id">
				    <span class="invo_title" style="line-height: 45px;">记账日期</span>
				    <input type='text' id="voucherdate" name="voucherdate" class="invo_title_year" style="margin: 9px 4px 0 0;" value="${voucherdate }" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M-%d',dateFmt:'yyyy-MM-dd',onpicked:datePickedFunc})" />
				</div>
				<div id="gridlist" data-options="region:'center'" style="border: 1px solid rgb(206, 218, 228);height:241px;width:756px;background: #fff;"></div>
			</div>
		    <a href="javascript:void(0)" class="easyui-linkbutton zhc_btn zhch_btn" onclick="ButtonFun.insertRow({type:2,renderid:'#gridlist'})">增行</a>
		    <a href="javascript:void(0)" class="easyui-linkbutton shc_btn shch_btn" onclick="ButtonFun.removeRow({type:2,renderid:'#gridlist'})">删行</a>	
		</div>

		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;     border-radius: 0 0 5px 5px;">
			<!-- 底部功能栏 -->
			
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="ButtonFun.cancelFun('recpayoff')">关闭</a>
			<a class="easyui-linkbutton sure-dialog" href="javascript:void(0)" onclick="submitForm_receivable({code:'recpayoff',type:2,renderid:'#gridlist'})">确定</a>
		</div>
		</div>
		
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:661px;height:300px;padding:10px;">
		<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px #cedae4 solid;">
			<input type="hidden" id="id" name="id">
			<input type="hidden" id="recpayoffmx" name="recpayoffmx">
			<input type="hidden" id="accountperiod" name="accountperiod" value="${accountperiod }">
			 <input type='hidden' id="voucherdate1" name="voucherdate1"  value="${voucherdate }">
		</form>
		</div>
	</div>

<script type="text/javascript">
	$(function(){
		var gridObj = new Object();
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'recpayoff/list';
		gridObj["columns"] = [[
								{field:'id',title:'ID',hidden:true},
								{field:'zt',title:'zt',hidden:true},
								{field:'rowid',title:'rowid',hidden:true},
								{field:'customerid',title:'客户id',hidden:true},
								{field:'customer_name',title:'客户',align: 'left', halign: 'center',width:200,formatter:formatOper_customer},
								{field:'cus_balance',title:'当前余额',align: 'right', halign: 'center',width:109,
									formatter:function(value,row,index){
										return formatMoney(value);
									}
								},
								{field:'supplierid',title:'供应商id',hidden:true},
								{field:'supplier_name',title:'供应商',align: 'left', halign: 'center',width:200,formatter:formatOper_supplier},
								{field:'sup_balance',title:'当前余额',align: 'right', halign: 'center',width:109,
									formatter:function(value,row,index){
										return formatMoney(value);
									}
								},
								{field:'outmoney',title:'本次冲销',align: 'right', halign: 'center',width:109,
									editor:{type:'numberbox',options:{min:0,precision:2}},
									formatter:function(value,row,index){
										return '<p style="width:100%;height:100%;margin:0 0 0 -17px">'+formatMoney(value)+'</p>';
									}
								},
								
					         ]];
		gridObj["idField"] = 'id';
		Grid.edit_cell_grid_url(gridObj);
		$('#gridlist').datagrid({
			onLoadSuccess: function(data){
				
			}
		});
	});

	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	
	function afterEditFun(node){
		
	}
	function datePickedFunc(){
		var accountperiod=$('#accountperiod').val();
		var voucherdate=$('#voucherdate').val();
		if(accountperiod != voucherdate.substring(0,7)){
			$('#voucherdate').val($('#voucherdate1').val());
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>选择的日期必须在当前会计期间内！','warning');
			return false;
		}
		$('#voucherdate1').val(voucherdate);
	}
	//显示可编辑表格
	function afterAddFun(){
		
	}
	function doSupplierSearch(value){
		$('#supplier_gridlist').datagrid('load',{
			q:value
		})
	}	
	function doCustomerSearch(value){
		$('#customer_gridlist').datagrid('load',{
			q:value
		})
	}
	function formatOper_customer(val,row,index){
		if(checkEm(val)){val = ''}
	    return '<div style="width: 73px;height: 28px;text-align:left;">'+val+'<div class="choose_btnimg"  onclick="javascript:Dialog.archives_customer(\'2;pageForm;customerid:id,customer_name:name,cus_balance:balance;gridlist;'+index+'\')"></div></div>';  
	}
	function formatOper_supplier(val,row,index){
		if(checkEm(val)){
			val = ''
		}
	    return '<div style="width: 73px;height: 28px;text-align:left;">'+val+'<div class="choose_btnimg"  onclick="javascript:Dialog.archives_supplier(\'2;pageForm;supplierid:id,supplier_name:name,sup_balance:balance;gridlist;'+index+'\')"></div></div>';  
	}
	
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){
		var data = $('#gridlist').datagrid('getData');
		var rows=data.rows;
		for(var i=0;i<rows.length;i++){
			//删除空行
			if(checkEm(rows[i].outmoney)){
				$('#gridlist').datagrid('deleteRow',i);
				i--;
				continue;
			}	
			if(!checkEm(rows[i].outmoney) && (checkEm(rows[i].supplierid) || checkEm(checkEm(rows[i].customeridid)))){
				$.messager.alert('提示', '<span class="hintsp_w">提示</span>有核销金额时,供应商与客户不能为空！','warning');
				return false;
			}
		}
		if(rows.length==0){
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>没有符合条件的数据！','warning');
			return false;
		}
		$('#recpayoffmx').val(JSON.stringify(rows));
		return true;
	}

</script>

</body>
</html>