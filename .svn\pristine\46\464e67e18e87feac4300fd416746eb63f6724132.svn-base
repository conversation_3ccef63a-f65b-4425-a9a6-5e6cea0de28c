package org.newstanding.service.invoicing;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.systemset.AccountInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service("costaccountingService")
public class CostaccountingService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private AccountInfoService accountInfoService;
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			dao.delete("CostaccountingMapper.deleteCostaccountingmxByCostaccountingid", pd);
			int count = dao.delete("CostaccountingMapper.delete", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CostaccountingMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	/**
	 * 计算增值税
	 * @param pd
	 * @return
	 */
	public PageData calucationcost(PageData pd){
		
		/*按商品汇总显示
		     期初：略
		     本期入库：数量，合计
		     入库金额：金额，合计
		     本期出库：出库数量，合计
		     出库金额：要有个进销存的表格，刚开始的时候是没有的，点击计算成本后，按规则计算数据再填写进去
		                     全月加权平均的计算：（期初金额+入库金额）/（期初数量+本期入库数量）*本期出库数量
		     期末数量：=期初数量+本期入库-本期出库
		     期末金额：=期初金额+入库金额-出库金额*/
		PageData resPd=new PageData();
		try {
			PageData accInfo = accountInfoService.findAccInfo(pd);
			pd.put("newaccountperiod", DateUtil.substractMonth(pd.get("accountperiod").toString(), 1));
			//根据商品查询出商品类别，商品名称，期初数量，期初金额，本期入库数量，本期入库金额，本期出库数量
			List<PageData> mxList=(List<PageData>) dao.findForList("CostaccountingMapper.findMxListByPeriod", pd);
			Double qcsl=0d;
			Double qcje=0d;
			Double bcrk=0d;
			Double rkje=0d;
			Double bcck=0d;
			Double ckje=0d;
			Double qmsl=0d;
			Double qmje=0d;
			Double ckdj=0d;
			
			for(int i=0;i<mxList.size();i++){
				PageData mxPd = mxList.get(i);
				qcsl = Double.parseDouble(mxPd.get("qcsl").toString());
				qcje = Double.parseDouble(mxPd.get("qcje").toString());
				
				bcrk = Double.parseDouble(mxPd.get("bcrk").toString());
				rkje = Double.parseDouble(mxPd.get("rkje").toString());
				bcck = Double.parseDouble(mxPd.get("bcck").toString());
				if("0".equals(accInfo.get("calmethod").toString())) {
					//全月平均加权法
					//（期初金额+入库金额）/（期初数量+本期入库数量）*本期出库数量
					//全月平均加权法
					//（期初金额+入库金额）/（期初数量+本期入库数量）*本期出库数量
					if(CalculateUtil.add(qcsl,bcrk) == 0) {
						ckje = 0d;
						if(bcck ==0) {
							mxList.remove(i);
							i--;
							continue;
						}
					}else {
						ckje = CalculateUtil.multiply(CalculateUtil.divide(CalculateUtil.add(qcje,rkje), CalculateUtil.add(qcsl,bcrk)), bcck);
					}
					
					if(bcck !=0) {
						ckdj = CalculateUtil.divide(ckje, bcck);
					}else {
						ckdj = 0d;
					}
					
					mxPd.put("ckje", ckje);
					mxPd.put("ckdj", ckdj);
				}
				qmsl = CalculateUtil.subtract(CalculateUtil.add(qcsl, bcrk), bcck);
				qmje = CalculateUtil.subtract(CalculateUtil.add(qcje, rkje), ckje);
				mxPd.put("qmsl", qmsl);
				mxPd.put("qmje", qmje);
				
			}
			//保存到t_costaccounting库
			pd.put("addList", mxList);
			dao.save("CostaccountingMapper.savemx", pd);
			//更新库存余额中的  出库金额  ckdj*sl
			dao.update("CostaccountingMapper.editkcye", pd);
			
			//
			//合计行  数据构建
			List<PageData> footerList=new ArrayList<PageData>();
			PageData footerPd=new PageData();
			footerPd.put("goodstype_name", "合计");
			for(Map<String,String> empPd :mxList){
				for (String key : empPd.keySet()) {  
					  if(key.equals("qcsl") || key.equals("qcje")
							  || key.equals("bcrk") || key.equals("rkje")
							  || key.equals("bcck") || key.equals("ckje")
							  || key.equals("qmsl") || key.equals("qmje")){
						if(empPd.get(key) !=null){
							 if(footerPd.get(key) !=null){
								 String footer=footerPd.get(key).toString();
								 String em=String.valueOf(empPd.get(key));
								 footerPd.put(key, 
										 CalculateUtil.add(footer
												 , em));
							 }else{
								 footerPd.put(key, empPd.get(key));
							 }
						}else{
							footerPd.put(key, 0);
						}
						 
					  }
				  
				} 
			}
			footerList.add(footerPd);
			resPd.put("footer", footerList);
			resPd.put("rows", mxList);
			resPd.put("total", mxList.size());
			resPd.put("state","success");
			resPd.put("message","计算成功");
		} catch (Exception e) {
			e.printStackTrace();
			resPd.put("state","error");
			resPd.put("message","计算存在错误,请联系管理员");
		}
		
		return resPd;
	}
	
	
	public PageData findcalucationcost(PageData pd){
		PageData resPd=new PageData();
		try {
			List<PageData> mxList=(List<PageData>) dao.findForList("CostaccountingMapper.findListByPeriod", pd);
			//合计行  数据构建
			List<PageData> footerList=new ArrayList<PageData>();
			PageData footerPd=new PageData();
			footerPd.put("goodstype_name", "合计");
			for(Map<String,String> empPd :mxList){
				for (String key : empPd.keySet()) {  
					  if(key.equals("qcsl") || key.equals("qcje")
							  || key.equals("bcrk") || key.equals("rkje")
							  || key.equals("bcck") || key.equals("ckje")
							  || key.equals("qmsl") || key.equals("qmje")){
						if(empPd.get(key) !=null){
							 if(footerPd.get(key) !=null){
								 String footer=footerPd.get(key).toString();
								 String em=String.valueOf(empPd.get(key));
								 footerPd.put(key, 
										 CalculateUtil.add(footer
												 , em));
							 }else{
								 footerPd.put(key, empPd.get(key));
							 }
						}else{
							footerPd.put(key, 0);
						}
						 
					  }
				  
				} 
			}
			footerList.add(footerPd);
			resPd.put("footer", footerList);
			resPd.put("rows", mxList);
			resPd.put("total", mxList.size());
			resPd.put("state","success");
			resPd.put("message","查询成功");
		} catch (Exception e) {
			e.printStackTrace();
			resPd.put("state","error");
			resPd.put("message","查询存在错误,请联系管理员");
		}
		
		return resPd;
	}
	
	/**
	 * 获取附加税基数
	 * @return
	 */
	public String getCaltaxbase(PageData pd) {
		String caltaxbase = "";
		try {
			pd.put("database", getDatabase(pd));
			pd.put("accountperiod",getAccountperiod(pd));
			PageData accountinfo = (PageData)dao.findForObject("CostaccountingMapper.getCaltaxbase", pd);
			caltaxbase = accountinfo.get("creditmoneytotal").toString();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return caltaxbase;
	}
	
	
	public PageData findTaxratesetById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CostaccountingMapper.findTaxratesetById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
}
