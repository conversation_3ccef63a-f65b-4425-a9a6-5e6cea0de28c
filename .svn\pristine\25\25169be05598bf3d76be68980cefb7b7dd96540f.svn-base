<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div id="eastToolDiv" data-options="region:'east'" style="width:100px;border-right: 1px #cedae4 solid;">
		
			<!-- 左侧功能栏 -->
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({isCheckInitBalance:false,width:425,height:280})"><i></i>新增</a>
			<a class="easyui-linkbutton compile-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:425,height:280})"><i></i>编辑</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.findFun()"><i></i>查找</a>
			<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')"><i></i>删除</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('关闭')"><i></i>禁用</a>
			<a class="easyui-linkbutton recover-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.recoveryFun()"><i></i>恢复</a>
		</div>
		<div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);"></div>
		<div id="southToolDiv" data-options="region:'south'" style="height:50px;text-align:right;">
		
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('menu')">关闭</a>
		</div>
		
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:300px;height:230px;padding:10px;">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true">
				<input type="hidden" id="id" name="id">
		    	<table cellpadding="5">
		    															<tr>
			    			<td>菜单编号</td>
			    			<td><input  name="code" id="code" type= "text" class= "easyui-textbox" required ="required"></input> </td>
			    		</tr>
																											<tr>
			    			<td>菜单名称</td>
			    			<td><input  name="name" id="name" type= "text" class= "easyui-textbox" required ="required"></input> </td>
			    		</tr>
																											<tr>
			    			<td>菜单图标</td>
			    			<td><input  name="ioc" id="ioc" type= "text" class= "easyui-textbox" required ="required"></input> </td>
			    		</tr>
																											<tr>
			    			<td>链接地址</td>
			    			<td><input  name="url" id="url" type= "text" class= "easyui-textbox" required ="required"></input> </td>
			    		</tr>
																								             <tr>
			    			<td>序号</td>
			    			<td><input  name="order" id="order" type= "hidden" class= "easyui-textbox" ></input> </td>
			    		</tr>
														    		
		    	</table>
		    </form>
		    <div style="text-align:center;padding:5px">
		    	<a href="javascript:void(0)" class="easyui-linkbutton sureNew_btn" onclick="submitForm({code:'menu',type:2,renderid:'#gridlist'})">确定</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton qxNew_btn" onclick="clearForm()">取消</a>
		    </div>
		</div>
		<!-- 查找弹窗 -->
		<div id="find_panel" class="easyui-window" title="查找" data-options="iconCls:'icon-search',closed:true,collapsible:false,minimizable:false,maximizable:false">
			<input id="find_input" name="find_input" class="easyui-textbox" style="width:200px" data-options="iconCls:'icon-search'"> 
			<div style="text-align:center;padding:5px">
		    	<a href="javascript:void(0)" class="easyui-linkbutton sureNew_btn" onclick="$('#gridlist').datagrid('load',{q:$('#find_input').val()})">确定</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton qxNew_btn" onclick="clearFindPanel()">取消</a>
		    </div>
		</div>
	</div>

<script type="text/javascript">
	$(function(){
		var gridObj = new Object();
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'menu/list';
		gridObj["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'code',title:'菜单编号',align: 'left', halign: 'center',width:100},
								{field:'name',title:'菜单名称',align: 'left', halign: 'center',width:100},
								{field:'ioc',title:'菜单图标',align: 'left', halign: 'center',width:100},
								{field:'url',title:'链接地址',align: 'left', halign: 'center',width:100},
								{field:'order',title:'序号',align: 'left', halign: 'center',width:100},
                       ]];
		gridObj["idField"] = 'id';
		gridObj["treeField"] = 'name';
		Grid.list_grid(gridObj);
	});
	
	//新增按钮点击后通用触发方法
	function afterAddFun(){}
	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	//编辑通用赋值后触发方法
	function afterEditFun(node){}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){return true;}
	//通用保存后触发方法
	function afterSaveFun(id){}
	
	//恢复按钮初始化参数
	function initRecovery(){
		var obj= {};
		obj["title"] = "恢复";
		obj["code"] = "menu";
		obj["position"] = "#grid_selectrlist";
		obj["columns"] = [[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'code',title:'菜单编号',align: 'left', halign: 'center',width:100},
							{field:'name',title:'菜单名称',align: 'left', halign: 'center',width:100},
							{field:'ioc',title:'菜单图标',align: 'left', halign: 'center',width:100},
							{field:'url',title:'链接地址',align: 'left', halign: 'center',width:100},
							{field:'order',title:'序号',align: 'left', halign: 'center',width:100},
                            {field:'operation',title:'操作',width:100,formatter:formatOper_renew,no_select:true}
	                 ]];
		return obj;
	}
	
	//渲染恢复按钮
	function formatOper_renew(val,row,index){
		var url = "menu/closeAll.do?DATA_IDS="+row["id"]+"&closestatus=0&table_name=c_menu";
	    return '<a href="javascript:void(0)" class="easyui-linkbutton" onclick="ButtonFun.renew(\''+url+'\')">恢复</a>';  
	}
	//刷新视图
	function reshView(){
		$('#grid_selectrlist').datagrid('reload');
		$('#gridlist').datagrid('reload');
	}
	
	//侧栏关闭按钮方法 operType：str  关闭   删除
	function closeOrDelete(operType){
		var node = $('#gridlist').datagrid('getSelected');
		if(!checkEm(node)){
			var url='';
			if(operType=='关闭'){
				url='menu/closeAll?closestatus=1';
			}else{
				url='menu/deleteAll';
			}
			ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
		
	}
	
</script>

</body>
</html>