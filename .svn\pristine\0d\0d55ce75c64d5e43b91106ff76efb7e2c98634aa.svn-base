package org.newstanding.plugin;

import java.io.IOException;
import java.sql.SQLException;

import javax.script.ScriptException;

import org.apache.poi.openxml4j.exceptions.OLE2NotOfficeXmlFileException;
import org.mybatis.spring.MyBatisSystemException;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.ServiceHelper;
import org.newstanding.service.systemset.UserService;
import org.springframework.dao.CannotAcquireLockException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

public class ExceptionHandler {

	private Exception exception;
	private PageData errpd;
	private String default_message="未知错误请联系管理员";
	
	public ExceptionHandler(Exception e,PageData errpd) {
		this.exception = e;
		this.errpd = errpd;
	}
	
	public PageData getThrowMessage() {
		String message = "";
		if (exception != null) {
			/*如果是运行时异常
			 * 有用户异常信息直接返回 否则返回默认异常*/
			if (exception instanceof BadSqlGrammarException) {
				SQLException sqlException = ((BadSqlGrammarException) exception).getSQLException();
				message = sqlException.getMessage();
				errpd.put("state", "error");
				errpd.put("message", message);
				
			}else if (exception instanceof NullPointerException) {
				NullPointerException nullPointerException = (NullPointerException)exception;
				message = nullPointerException.getMessage();
				errpd.put("state", "error");
				errpd.put("msmessageg", message);
			}else if (exception instanceof IOException) {
				IOException ioException = (IOException) exception;
				message = ioException.getMessage();
				errpd.put("state", "error");
				errpd.put("message", message);
			}else if (exception instanceof MyBatisSystemException) {
				MyBatisSystemException myBatisSystemException = (MyBatisSystemException) exception;
				message = myBatisSystemException.getMessage();
				errpd.put("state", "error");
				errpd.put("message", message);
			}else if(exception instanceof ScriptException){
				errpd.put("state", "error");
				errpd.put("message", "计算公式错误");
			}else if(exception instanceof OLE2NotOfficeXmlFileException){
				errpd.put("state", "error");
				errpd.put("message", "请使用后缀为xlsx的Excel文件");
			}
			//杀死锁表进程
			else if (exception instanceof CannotAcquireLockException){
				UserService userService = (UserService) ServiceHelper.getService("userService");
				userService.killThread();
			}else if (exception instanceof RuntimeException || exception instanceof Exception) {
				if (errpd != null && errpd.size()>0) {
					return errpd;
				}else {
					errpd.put("state", "error");
					errpd.put("message", default_message);
				}
			}
			
			if (TransactionAspectSupport.currentTransactionStatus() != null) {
				TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			}
			exception.printStackTrace();
		}
		return errpd;
	}
	
}
