<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>ComboBox with Extra Icons- jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>ComboBox with Extra Icons</h2>
	<p>The user can attach extra icons to the ComboBox.</p>
	<div style="margin:20px 0"></div>
	<input class="easyui-combobox" 
			name="language"
			data-options="
					url:'combobox_data1.json',
					method:'get',
					valueField:'id',
					textField:'text',
					panelHeight:'auto',
					icons:[{
						iconCls:'icon-add'
					},{
						iconCls:'icon-cut'
					}]
			">

</body>
</html>