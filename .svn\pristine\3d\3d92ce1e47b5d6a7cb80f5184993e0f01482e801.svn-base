package org.newstanding.controller.systemset;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.systemset.TaxRateSetService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
/**
 * deptdoc
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "/taxrateset")
public class TaxRateSetController extends BaseController {
	@Resource(name = "taxRateSetService")
	private TaxRateSetService taxRateSetService;

	/**
	 * 新增
	 * 
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/save")
	@ResponseBody
	public PageData save() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return taxRateSetService.save(pd);
	}

	/**
	 * 删除
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/delete")
	@ResponseBody
	public Object delete() throws Exception {
		PageData pd = this.getPageData();
		return taxRateSetService.delete(pd);
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit")
	@ResponseBody
	public Object edit() throws Exception {
		PageData pd = this.getPageData();
		return taxRateSetService.edit(pd);
	}

	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		String[] atcode=getAccountCodeArray();
		pd.put("codeArray", atcode);
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		page.setPd(pd);
		resultPageData = taxRateSetService.list(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return resultMap;
	}

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		mv.setViewName("system/systemset/taxrateset");
		return mv;
	}

	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "c_taxrateset");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = taxRateSetService.deleteAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 验证重复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkRepeatByParam")
	public boolean checkRepeatByParam() throws Exception {
		PageData pd = this.getPageData();
		if( taxRateSetService.checkRepeatByParam(pd) ){
			return false;
		}
		return true;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "c_taxrateset");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = taxRateSetService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	/**
	 * 检查客户档案是否被引用
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value="/checkIsUsed")
	public Object checkSubAndChild() throws Exception {
		PageData pd = this.getPageData();
		List<PageData> list = taxRateSetService.checkIsUsed(pd);
		if(list.size()>0){
			pd.put("state", "error");
		}else{
			pd.put("state", "success");
		}
		return pd;
	}
	
	/**
	 * 搜索 导出 查询 部门列表
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = taxRateSetService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
}
