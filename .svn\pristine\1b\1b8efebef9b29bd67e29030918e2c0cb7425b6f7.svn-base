<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Pagination Links - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Pagination Links</h2>
	<div class="demo-info">
		<div class="demo-tip icon-tip"></div>
		<div>The example shows how to customize numbered pagination links.</div>
	</div>
	<div style="margin:10px 0;"></div>
	<div class="easyui-pagination" style="border:1px solid #ddd;" data-options="
				total:114,
				layout:['list','sep','first','prev','links','next','last','sep','refresh']
			"></div>
</body>
</html>