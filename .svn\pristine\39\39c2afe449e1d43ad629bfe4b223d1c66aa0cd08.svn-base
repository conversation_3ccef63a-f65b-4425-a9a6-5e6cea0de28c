package org.newstanding.controller.fixedassets;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.fixedassets.FixedAssetsDepreciationService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
/**
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "/fixedassetsdepreciation")
public class FixedAssetsDepreciationController extends BaseController {
	@Resource(name = "FixedAssetsDepreciationService")
	private FixedAssetsDepreciationService fixedAssetsDepreciationService;

	/**
	 * 新增
	 * 
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/save")
	@ResponseBody
	public PageData save() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return fixedAssetsDepreciationService.save(pd);
	}
	
	/**
	 * 删除
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/delete")
	@ResponseBody
	public Object delete() throws Exception {
		PageData pd = this.getPageData();
		return fixedAssetsDepreciationService.delete(pd);
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit")
	@ResponseBody
	public Object edit() throws Exception {
		PageData pd = this.getPageData();
		return fixedAssetsDepreciationService.edit(pd);
	}
	
	/**
	 * 通过ID获取数据
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/findById")
	public Object findById() throws Exception {
		PageData pd = this.getPageData();
		PageData tmp = fixedAssetsDepreciationService.findById(pd);
		pd.putAll(tmp);
		return pd;
	}

	/**
	 * 列表
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		if( pd.get("accountperiod") == null || "".equals(pd.get("accountperiod").toString()) ){
			pd.put("accountperiod",fixedAssetsDepreciationService.getAccountperiod(pd));
		}
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		page.setPd(pd);
		resultPageData = fixedAssetsDepreciationService.list(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	/**
	 * 明细列表
	 * @throws Exception 
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "/mxList")
	public Map<String, Object> referList() throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		if( pd.get("accountperiod") == null || "".equals(pd.get("accountperiod").toString()) ){
			pd.put("accountperiod",fixedAssetsDepreciationService.getAccountperiod(pd));
		}
		if( pd.get("id") == null || "".equals(pd.get("id").toString()) ){
			resultPageData = fixedAssetsDepreciationService.findMxData(pd);
		}else{
			resultPageData = fixedAssetsDepreciationService.findFixedAssetsDepreciationmxByFixedAssetsDepreciationid(pd);
		}
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", resultPageData.size());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		PageData pd=this.getPageData();
		mv.setViewName("system/fixedassets/fixedassetsdepreciation");
		String accountperiod = fixedAssetsDepreciationService.getAccountperiod(pd);
		String[] pfs = accountperiod.split("-");
		mv.addObject("year", pfs[0]);
		mv.addObject("month", pfs[1]);
		return mv;
	}
	
	/**
	 * 新增前判断当前会计期间是否已产生过累计折旧
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/checkAdd")
	public Object checkAdd() throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		if( pd.get("accountperiod") == null || "".equals(pd.get("accountperiod").toString()) ){
			pd.put("accountperiod",fixedAssetsDepreciationService.getAccountperiod(pd));
		}
		resultPageData = fixedAssetsDepreciationService.checkAdd(pd);
		return resultPageData;
	}
	
	/**
	 * 新增
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/goAdd")
	public Object goAdd() throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		if( pd.get("accountperiod") == null || "".equals(pd.get("accountperiod").toString()) ){
			pd.put("accountperiod",fixedAssetsDepreciationService.getAccountperiod(pd));
		}
		resultPageData = fixedAssetsDepreciationService.getAddObj(pd);
		return resultPageData;
	}
	
	/**
	 * 编辑
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/goEdit")
	public Object goEdit() throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		resultPageData = fixedAssetsDepreciationService.goEditObj(pd);
		return resultPageData;
	}

	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "t_fixedassetsdepreciation");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = fixedAssetsDepreciationService.deleteAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 验证重复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkRepeatByParam")
	public boolean checkRepeatByParam() throws Exception {
		PageData pd = this.getPageData();
		if( fixedAssetsDepreciationService.checkRepeatByParam(pd) ){
			return false;
		}
		return true;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "t_fixedassetsdepreciation");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = fixedAssetsDepreciationService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 检查客户档案是否被引用
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value="/checkIsUsed")
	public Object checkSubAndChild() throws Exception {
		PageData pd = this.getPageData();
		List<PageData> list = fixedAssetsDepreciationService.checkIsUsed(pd);
		if(list.size()>0){
			pd.put("state", "error");
		}else{
			pd.put("state", "success");
		}
		return pd;
	}
	
	/**
	 * 搜索 导出 查询 
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = fixedAssetsDepreciationService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	/**
	 * 生成凭证
	 * @return
	 */
	@RequestMapping(value="/voucherCreate")
	@ResponseBody
	public Object voucherCreate() {
		PageData pd = this.getPageData();
		try {
			pd = fixedAssetsDepreciationService.voucherCreate(pd);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return pd;
	}
	
	/**
	 * 撤销凭证
	 * @return
	 */
	@RequestMapping(value="/voucherRevoke")
	@ResponseBody
	public Object voucherRevoke() {
		PageData pd = this.getPageData();
		try {
			pd = fixedAssetsDepreciationService.voucherRevoke(pd);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return pd;
	}
	
}
