<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background: #fff;">
		
			<!-- 左侧功能栏 -->
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({isCheckInitBalance:false,width:425,height:260})">新增</a>
			<a class="easyui-linkbutton compile-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:425,height:260})">编辑</a>
			<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')">删除</a>
		</div>
		<div class="parceldivs" style="height: 326px; width: 605px;border-top:0">
			<div class="parceldiv_tow" style="height: 326px; width: 605px;">
				<div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);height: 326px;"></div>
			</div>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('housefundrate')">关闭</a>
		</div>
		
		<!-- 编辑弹窗 -->
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:300px;height:150px;">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true">
				<input type="hidden" id="id" name="id">
				<div class="bgdivtitfixed" style="height: 186px;width: 100%; border: 0;border-bottom: 1px solid rgb(206, 218, 228);">
					<ul class="pageForm_ul" style="height:158px;float: left;margin: 35px 0 0 0;">
					   <li>
					       <span style="width:100px">项目名称：</span>
					   		<input  type="hidden" id="insuranceitemsid" name="insuranceitemsid"/>
					       <input class="easyui-textbox"style="width:220px; padding:0 24px 0 2px;" type="text" id="insuranceitems_name" name="insuranceitemsid_name" data-options="required:true,validType:['throughtNameFindIdRepeat[\'housefundrate\',\'c_housefundrate\',\'insuranceitemsid\',\'c_insuranceitems\',\'pageForm\']']" />
			    		   <div class="kjkm_btn"  style="right: 10%;top: 54%;" onclick="archives_insuranceitems('1;pageForm;insuranceitemsid:id,insuranceitems_name:name')"></div>
					   </li>
					   <li>
					       <span style="width:100px">公司部分：</span>
					       <input class="easyui-numberbox"style="width:220px" id="comppart" name="comppart" data-options="required:true,min:0,precision:2" />
					       <p style="float: right;margin: 15px 23px 0 0;">%</p>
					   </li>
					  	   <li>
					       <span style="width:100px">个人部分：</span>
					       <input class="easyui-numberbox" style="width:220px" id="personpart" name="personpart" data-options="required:true,min:0,precision:2" />
					       <p style="float: right;margin: 15px 23px 0 0;">%</p>
					   </li>
					</ul>
				</div>	
		    </form>
		    <div style="height:43px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;padding:0px 0 0 0">
		    	<a href="javascript:void(0)" class="easyui-linkbutton  cancel-btn qxNew_btn" style="margin: -1px 13px 0 0;"onclick="clearForm()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton  sure-btn sureNew_btn" style="margin: -1px 9px 0 0;"onclick="submitForm({code:'housefundrate',type:2,renderid:'#gridlist'})">确定</a>
		    </div>
		</div>
		
		<!-- 查找弹窗 -->
		<div id="find_panel" class="easyui-window" style="width: 300px;height:180px" title="查找" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="search_pel"><input id="find_input" name="find_input"  class="easyui-textbox" style="width:200px" data-options="iconCls:'icon-search'"></div>
			<div class="windowbottom">
		    	<a href="javascript:void(0)" class="easyui-linkbutton  cancel-btn qxNew_btn" onclick="clearFindPanel()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" onclick="$('#gridlist').datagrid('load',{q:$('#find_input').val()})">确定</a>
		    </div>
		</div>
		
	</div>

<script type="text/javascript">
	$(function(){
		var gridObj = new Object();
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'housefundrate/list';
		gridObj["columns"] = [[
								{field:'id',title:'id',width:100,hidden:true},
								{field:'insuranceitemsid',title:'insuranceitemsid',hidden:true},
								{field:'insuranceitems_name',title:'项目名称',align: 'left', halign: 'center',width:300},
								{field:'comppart',title:'公司部分（%）',align: 'right', halign: 'center',width:136,formatter:percentFormat},
								{field:'personpart',title:'个人部分（%）',align: 'right', halign: 'center',width:138,formatter:percentFormat},
		                     ]];
		gridObj["idField"] = 'id';
		gridObj["pagination"] = false;
		gridObj["listDbClickFun"] = listDbClickFun;
		Grid.list_grid(gridObj);
	});
	function percentFormat(value,row,index){
		if(!checkEm(value)){
			return '<p style="width:100%;height:100%;margin:0 0 0 -17px">'+value+'%</p>';
		}else{
			return "";
		}
	}
	//双击行的触发事件
	function listDbClickFun(row){
		/* $('#pageForm').form('load', row);
		$('#edit_pageId').window({title:'编辑',width:425, height:260});
		$('#edit_pageId').window('open'); */
		var obj=new Object();
		obj["renderid"]='#gridlist';
		obj["type"]=2;
		obj["width"]='425px';
		obj["height"]='260px';
		ButtonFun.editFun(obj);
	}
	
	//新增按钮点击后通用触发方法
	function afterAddFun(){}
	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	//编辑通用赋值后触发方法
	function afterEditFun(node){
		$("#insuranceitems_name").textbox('setValue',node.insuranceitems_name);
	}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){return true;}
	//通用保存后触发方法
	function afterSaveFun(id){}
	//通用撤账后触发方法
	function afterVoucherRevokeFun(){}
	//通用生成凭证后触发方法
	function afterVoucherCreateFun(){}
	
	//刷新视图
	function reshView(){
		$('#grid_selectrlist').datagrid('reload');
		$('#gridlist').datagrid('reload');
	}
	/**
	 * 客户档案选择 
	 */
	function archives_insuranceitems(str){
		//会计科目选择弹出框
		$('body').append('<div id="insurance_dialog"></div>');
		$('#insurance_dialog').dialog({
			width: 537,
			height: 420,
			modal: true,
			title: '保险项目选择',
			onClose : function(){$(this).dialog('destroy');},
			content: '<div id="panelDiv" class="easyui-layout" data-options="fit:true">'+
					
					'<div class="parceldivs" style="width:535px;border:0">'+
						'<div class="parceldiv_tow" style=" width: 100%;height: 338px;">'+
							'<div id="insurance_gridlist" data-options="region:\'center\'" style="border-left: 1px solid rgb(206, 218, 228);height: 338px;"></div>'+
						'</div>'+
					'</div>'
					+
					'<div id="southToolDiv" data-options="region:\'south\'" style="border: 0;height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">'+
					
						'<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="$(\'#insurance_dialog\').dialog(\'destroy\')">关闭</a>'+
						'<a class="easyui-linkbutton sure-dialog" href="javascript:void(0)" onclick="insurance_ok(\''+ str +'\')">确定</a>' + 
					'</div>'+
				'</div>'
		});
		var gridObj = new Object();
		gridObj["position"] = "#insurance_gridlist";
		gridObj["url"] = 'insuranceitems/list';
		gridObj["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'name',title:'项目名称',align: 'left', halign: 'center',width:502}
		                     ]];
		gridObj["idField"] = 'id';
		//gridObj["listDbClickFun"] = listDbClickFun;
		Grid.list_grid(gridObj);
	}
	function insurance_ok(str){
			var node = $('#insurance_gridlist').datagrid('getSelected');
			var strs = str.split(";");
			var type = strs[0];
			var formid = strs[1];
			var arr = strs[2].split(",");
			switch(type){
				case '1':
					for(var i in arr){
						var kvs = arr[i].split(":");
						if($('#'+kvs[0]).hasClass('easyui-textbox')){
							$('#'+kvs[0]).textbox('setValue',node[kvs[1]]);
						}else{
							$('#'+kvs[0]).val(node[kvs[1]]);
						}
					}
					break;
				case '2':	//datagrid中选择会计科目
					var grid_id = strs[3];
					var index = Number(strs[4]);
					for(var i in arr){
						var kvs = arr[i].split(":");
						var row = $('#'+grid_id).datagrid('getRows')[index];
						row[kvs[0]] = node[kvs[1]];
						$('#'+grid_id).datagrid('refreshRow', index);
					}
					break;
			}
			$('#insurance_dialog').dialog('destroy');
		}
	//侧栏关闭按钮方法 operType：str  关闭   删除
	function closeOrDelete(operType){
		var nodes=$('#gridlist').datagrid('getSelected');
		var url='';
		if(operType=='关闭'){
			url='housefundrate/closeAll?closestatus=1';
		}else{
			url='housefundrate/deleteAll';
		}
		if(!checkEm(nodes)){
			$.messager.confirm(operType+'提示', '<span class="hintsp_w">提示</span>确定是否对该档案执行'+operType+'，'+operType+'后不再被引用？', function(r){
				if (r){
					if(operType=='删除'){
						$.ajax({
						    url:getUrl('housefundrate/checkIsUsed.do'),
						    type:'post',
						    async:false,
						    data: {'id':nodes.id},
						    dataType:'json',
						    success:function(data){
						    	if(!checkEm(data)){
						    		if(data.state == 'success'){
						    			ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
									}else if(data.state == 'error'){
										$.messager.alert('操作失败','<span class="hintsp_e">提示</span>该档案已经被使用，无法'+operType+'！您可对该客户进行关闭操作','error');
									}
						    	}
						    }
						    
					    });
					}else{
						ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
					}
					
				}
			})
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}
	
</script>

</body>
</html>