<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background: #fff;">
		
			<!-- 左侧功能栏 -->
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({isCheckInitBalance:false,width:425,height:443})">新增</a>
			<a class="easyui-linkbutton compile-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:425,height:440})">编辑</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.findFun()">查找</a>
			<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')">删除</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('关闭')">禁用</a>
			<a class="easyui-linkbutton recover-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.recoveryFun()">恢复</a>
		</div>
		<div class="parceldivs" style="width: 1175px;border:0">
			<div class="parceldiv_tow" style=" width: 1175px;height: 338px;">
				<div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);height: 358px;width: 1230px;"></div>
			</div>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('fixedinsurance')">关闭</a>
		</div>
		
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:600px;height:380px;">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px solid rgb(206, 218, 228);">
				<input type="hidden" id="id" name="id">
				<input type="hidden" id="itemNames" name="itemNames">
				<div class="bgdivtitfixed" style="height: 360px;width: 100%; border: 0;    margin: -12px 0 0px 0;">
					<ul class="empdoc_ul" style="height: 350px;">
					   <li style="margin:25px 0 0 3px;">
					      <span>职员编码：</span>
					      <input class="easyui-textbox" type="text" name="code" data-options="required:true,validType:['repeat[\'empdoc\',\'c_empdoc\',\'code\',\'pageForm\']']" />
					   </li>
					   <li style="margin:25px 0 0 3px;">
					      <span>职员姓名：</span>
					      <input class="easyui-textbox" type="text" name="name" data-options="required:true,validType:['repeat[\'empdoc\',\'c_empdoc\',\'name\',\'pageForm\']']" />
					   </li>
					   <li>
					      <span>性别：</span>
						  <select id="sex" class="easyui-combobox" name="sex" data-options="required:true,editable:false,panelHeight:'auto'" style="width:105px" title="款项种类">			
							  <option value="0">男</option>
						      <option value="1">女</option>
						  </select>
					   </li>
					   <li>
					      <span>所属部门：</span>
					      <input class="easyui-textbox" type="text" id="dept_name" name="dept_name" data-options="required:true,validType:['exist[\'deptdoc\',\'c_deptdoc\',\'dept_name\',\'pageForm\']']" />
					   </li>
					   <li>
					      <span>保险类型：</span>
						  <select id="insurancetype" class="easyui-combobox" name="insurancetype" data-options="required:true,editable:false,panelHeight:'auto'" style="width:105px">			
							  <option value="0"></option>
						      <option value="1">正常</option>
						      <option value="2">外包</option>
						  </select>
					   </li>
					   <li  style="width: 100%;">
					      <span>工资科目：</span>
					      <input type="hidden" id="salarysubid"  name="salarysubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="salarysub_name"  name="salarysub_name" data-options="required:true" />
					      <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;salarysubid:id,salarysub_name:name')"></div>
					   </li>
					   <li style="width: 100%;">
					      <span>基本社保科目：</span>
					      <input type="hidden" id="insurancesubid"  name="insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="insurancesub_name" name="insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;insurancesubid:id,insurancesub_name:name')"></div>
					   </li>
					   <li style="width: 100%;">
					      <span>公积金科目：</span>
					      <input type="hidden" id="fund_insurancesubid"  name="fund_insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="fund_insurancesub_name" name="fund_insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;fund_insurancesubid:id,fund_insurancesub_name:name')"></div>
					   </li>
					   <li style="width: 100%;">
					      <span>补充养老科目：</span>
					      <input type="hidden" id="oldage_insurancesubid"  name="oldage_insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="oldage_insurancesub_name" name="oldage_insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;oldage_insurancesubid:id,oldage_insurancesub_name:name')"></div>
					   </li>
					    <li style="width: 100%;">
					      <span>补充医疗科目：</span>
					      <input type="hidden" id="medical_insurancesubid"  name="medical_insurancesubid"  />
					      <input class="easyui-textbox" type="text" style="width: 287px; padding:0 24px 0 2px;" id="medical_insurancesub_name" name="medical_insurancesub_name" data-options="required:true" />
					  	   <div class="kjkm_btn" style="right: 8%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;medical_insurancesubid:id,medical_insurancesub_name:name')"></div>
					   </li>
					    <li style="width: 100%;">
					       <span>专项扣除：</span>
					       <input class="easyui-numberbox" style="width: 287px; padding:0 24px 0 2px;" id="specialdeducate" name="specialdeducate" data-options="required:true,min:0,precision:2" />
					   </li>
					</ul>
				</div>	
		    </form>
		    <div style="height:37px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;padding:4px 0 0 0">
		    	<a href="javascript:void(0)" class="easyui-linkbutton  cancel-btn qxNew_btn" style="margin: -1px 13px 0 0;"onclick="clearForm()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton  sure-btn  sureNew_btn" style="margin: -1px 9px 0 0;"onclick="submitForm({code:'empdoc',type:2,renderid:'#gridlist'})">确定</a>
		    </div>
		</div>
		
		<!-- 查找弹窗 -->
		<div id="find_panel" class="easyui-window" style="width: 300px;height:182px" title="查找" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="parcelsearch">
				<input id="find_input" name="find_input" class="easyui-textbox" style="width:200px" data-options="iconCls:'icon-search'">
			</div> 
			<div style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;padding:0px 0 0 0">
		    	<a href="javascript:void(0)" class="easyui-linkbutton  cancel-btn qxNew_btn" onclick="clearFindPanel()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" onclick="$('#gridlist').datagrid('load',{q:$('#find_input').val()})">确定</a>
		    </div>
		</div>
		
	</div>

<script type="text/javascript">
	$(function(){  
    		$.ajax({
    		    url:getUrl('fixedinsurance/list.do'),
    		    type:'post',
    		    async:false,
    		    data: {},
    		    dataType:'json',
    		    success:function(data){
    	    			if(data.state == 'success'){
    	    				var columns=new Array();
    	    				columns.push({field:'id',title:'id',hidden:true});
    	    				columns.push({field:'empdocid',title:'empdocid',hidden:true});
    	    				columns.push({field:'empdoc_name',title:'职员名称',align: 'left', halign: 'center',width:70});
    	    				columns.push({field:'deptid',title:'deptid',hidden:true});
    	    				columns.push({field:'dept_name',title:'部门名称',align: 'left', halign: 'center',width:76});
    	    				columns.push({field:'basemoney',title:'社保基数',align: 'right', halign: 'center',width:70,editor:{type:"numberbox",options:{precision:2}},formatter:formatBaseMoney});
    	    				columns.push({field:'housefund_basemoney',title:'公积金基数',align: 'right', halign: 'center',width:70,editor:{type:"numberbox",options:{precision:2}},formatter:formatBaseMoney});
			              
    	    				var itemNames=data.itemNames;
    	    				$("#itemNames").val(JSON.stringify(itemNames));
    	    				var editor=new Object();
    	    				editor["type"]="numberbox";
    	    				editor["options"]={precision:2};
    	    				
    	    				
    	    				var editor2=new Object();
    	    				editor2["type"]="numberbox";
    	    				editor2["options"]={precision:0};
    	    				
    	    				for(var i=0 ; i<itemNames.length ; i++){
    	    					var column=new Object();
    	    					var column2=new Object();
    	    					column["field"]=itemNames[i].insuranceitemscode;
    	    					column["title"]=itemNames[i].insuranceitems_name;
    	    					column["width"]=97;
    	    					
    	    					column["align"]='right';
    	    					column["halign"]='center';
    	    					if(itemNames[i].insuranceitems_name.split('-')[0].indexOf('公积金') !=-1){//公积金四舍五入
    	    						column["formatter"]=cloformatMoney;
    	    						column["editor"]=editor2;
    	    					}else{//社保保留一位小数
    	    						column["formatter"]=cloformatMoney1;
    	    						column["editor"]=editor;
    	    					}
    	    					
    	    					column2["field"]=itemNames[i].insuranceitemscoderate;
    	    					column2["title"]=itemNames[i].insuranceitemscoderate;
    	    					//column2["width"]=30;
    	    					column2["hidden"]=true;
    	    					columns.push(column);
    	    					columns.push(column2);
    	    					//columns.push({field : itemNames[i].code ,title:itemNames[i].name,width:80,type:'numberbox'});
    	    				};
    	    				columns.push({field:'action',title:'操作',width:80,align:'center',formatter:operFormatFun});
    		                $('#gridlist').datagrid({  
    		                    title:'',  
    		                    height:338,  
    		                    singleSelect:true,  
    		                    url:getUrl('fixedinsurance/list.do'),
    		                   /*  data:data.rows, */
    		                    onBeforeEdit:function(index,row){
    		    					row.editing = true;
    		    					$('#gridlist').datagrid('refreshRow',index);
    		    				},
    		    				onAfterEdit:function(index,row){
    		    					row.editing = false;
    		    					$('#gridlist').datagrid('refreshRow',index);
    		    				},
    		    				onCancelEdit:function(index,row){
    		    					row.editing = false;
    		    					$('#gridlist').datagrid('refreshRow',index);
    		    				},
    		    				onEndEdit :function(index,row,changes){
    		    					debugger;
    		    					if(!checkEm(changes)){
    		    						$.each(changes, function(key1, val1) {
    		    							if(key1 == "basemoney"){
    		    								var itemNames=JSON.parse($('#itemNames').val());
    		    								$.each(row, function(key, val) {
    	   		    								for(var i=0;i<itemNames.length;i++){
    	   		    									if(key==itemNames[i].insuranceitemscode && itemNames[i].insuranceitems_name.split('-')[0].indexOf('公积金') ==-1){
    	   		    										var rate=parseFloat(row[key+'rate']);
    	   		    										var aa=(parseFloat(val1) * (rate/100)).toFixed(2)
    	   		    										row[key]=aa;
    	   		    									}
    	   		    								}
    	    		    						});
    		    							}
    		    							
    		    							if(key1 == "housefund_basemoney"){
    		    								var itemNames=JSON.parse($('#itemNames').val());
    		    								$.each(row, function(key, val) {
    	   		    								for(var i=0;i<itemNames.length;i++){
    	   		    									if(itemNames[i].insuranceitems_name.split('-')[0].indexOf('公积金') !=-1 && key==itemNames[i].insuranceitemscode){
    	   		    										var rate=parseFloat(row[key+'rate']);
    	   		    										var aa=(parseFloat(val1) * (rate/100)).toFixed(0)
    	   		    										row[key]=aa;
    	   		    									}
    	   		    								}
    	    		    						});
    		    							}
    		    						});
    		    					}
    		    				},
    		                   	columns : [  
    		                        columns  
    		                    ]});  
    					}else if(data.state == 'error'){
    						$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
    					}
    		    },
    		    error : function(data){
    		    	$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败，请联系管理员','error');
    		    }
    		    
    	    });
        });  
	function calOthers(obj){
		alert(obj)
	}
	function formatBaseMoney(value,row,index){
		return formatMoney_1(value);
	}
	function cloformatMoney(value,row,index){
		return formatMoney_1(value,0);
	}
	function cloformatMoney1(value,row,index){
		return formatMoney_1(value,2);
	}
	function operFormatFun(value,row,index){
			if (row.editing){
				var s = '<div style="width: 73px;height: 28px;margin: 0 auto;"><a href="javascript:void(0)" class="mxqd_btn" style="margin:4px 0 0 0" onclick="saverow(this)">确定</a> ';
				var c = '<a href="javascript:void(0)" class="mxqx_btn" style="margin:4px 0 0 0" onclick="cancelrow(this)">取消</a></div>';
				return s+c;
			} else {
				var e = '<a href="javascript:void(0)" class="mxxg_btn" style="margin:0" onclick="editrow(this)">修改</a> ';
				return e;
			}
	}
	//获得标记行下标
	function getRowIndex(target){
	    var tr = $(target).closest('tr.datagrid-row');
	    return parseInt(tr.attr('datagrid-row-index'));
	}
	//启动编辑行
	function editrow(target){
	    $('#gridlist').datagrid('beginEdit', getRowIndex(target));
	}

	//编辑行确定
	function saverow(target){
		debugger;
		var index = getRowIndex(target);
		if(!$('#gridlist').datagrid('validateRow', index)){
			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>未验证成功！',showType:'slide',timeout:1000,style:{
				right : '', bottom : '',
				top : document.body.scrollTop + document.documentElement.scrollTop
			}}); */
			promptbox('success','未验证成功！');
			return;
		}
		$('#gridlist').datagrid('endEdit', index);
		var row = $('#gridlist').datagrid('getRows')[index];
		var array=new Array();
		var tObj=new Object();
		var itemNames=JSON.parse($('#itemNames').val());
		$.each(row, function(key, val) {
			for(var i=0;i<itemNames.length;i++){
				if(key==itemNames[i].insuranceitemscode){
					var obj=new Object();
					debugger;
					obj["insuranceitemscode"]=key;
					if(row[key]=='NaN' || row[key]=='undefined'){
					}else{
						obj["money"]=row[key];
					}
					obj["empdocid"]=row["empdocid"];
					obj["basemoney"]=row["basemoney"];
					obj["housefund_basemoney"]=row["housefund_basemoney"];
					obj["insuranceitemsname"]=itemNames[i].insuranceitems_name;
					array.push(obj);
				}
			}
			});
		tObj["empdocid"]=row.empdocid;
		tObj["fixedinsurancemx"]=JSON.stringify(array);
		$.ajax({
		    url: getUrl('fixedinsurance/edit'),
		    type: 'post', async: false, data: tObj, dataType:'json',
		    success:function(data){
	   			if(data.state == 'success'){
	    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>保存成功！',showType:'slide',timeout:1000,style:{
	    				right : '', bottom : '',
	    				top : document.body.scrollTop + document.documentElement.scrollTop
	    			}}); */
	    			promptbox('success','保存成功！');
	    			$('#gridlist').datagrid('reload');
				}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
			}
	    });
	}

	//编辑行取消
	function cancelrow(target){
	    $('#gridlist').datagrid('cancelEdit', getRowIndex(target));
	}
	
	//职员操作js
	
	//部门选择框
	var obj = new Object();
	obj["idColName"] = 'name';
	obj["url"] = 'deptdoc/list';
	obj["cgId"] = 'dept_name';
	obj["cgColumns"] = 	[[	{field:'code',title:'部门编码',align: 'left', halign: 'center',width:100},
		             		{field:'name',title:'部门名称',align: 'left', halign: 'center',width:100}
						]];
	Dialog.archives_selector_list(obj);
	
	//侧栏关闭按钮方法 operType：str  关闭   删除
	function closeOrDelete(operType){
		var nodes=$('#gridlist').datagrid('getSelected');
		var url='';
		if(operType=='关闭'){
			url='empdoc/closeAll?closestatus=1';
		}else{
			url='empdoc/deleteAll';
		}
		if(!checkEm(nodes)){
			$.messager.confirm(operType+'提示', '<span class="hintsp_w">提示</span>确定是否对该职员执行'+operType+'，'+operType+'后不再被引用？', function(r){
				if (r){
						closeAndRemoveFun1(2,'#gridlist',url);
					}
			})
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}
	
	 function closeAndRemoveFun1 (type,renderid,url){
			var obj=new Object();
			var nodes = getSelectedData(renderid,type);
			if(!checkEm(nodes)){
				
				obj["ids"]=nodes.empdocid;
				delAndCloseAjax(url,obj,function(data){
					if(data != null && data["state"] == 'success'){
						/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>操作成功！',showType:'slide',timeout:1000,style:{
		    				right : '', bottom : '',
		    				top : document.body.scrollTop + document.documentElement.scrollTop
		    			}}); */
						promptbox('success','操作成功！');
						$('#gridlist').datagrid('reload');
						/* var index=$('#gridlist').datagrid('getRowIndex',nodes);
						$('#gridlist').datagrid('deleteRow',index); */
					}else if(data != null && data.state =='error'){
						$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
				}
				});
			}
		}
	//恢复按钮初始化参数
		function initRecovery(){
			var obj= {};
			obj["title"] = "恢复";
			obj["code"] = "empdoc";
			obj["position"] = "#grid_selectrlist";
			obj["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'code',title:'职员编码',align: 'left', halign: 'center',width:172},
								{field:'name',title:'职员名称',align: 'left', halign: 'center',width:172},
			                    {field:'operation',title:'操作',width:123,formatter:formatOper_renew,no_select:true}
		                     ]];
			return obj;
		}
		
		//渲染恢复按钮
		function formatOper_renew(val,row,index){
			var url = "empdoc/closeAll.do?DATA_IDS="+row["id"]+"&closestatus=0&table_name=c_custom";
		    return '<a href="javascript:void(0)" class="easyui-linkbutton  recover-cssbtn" onclick="ButtonFun.renew(\''+url+'\')">恢复</a>';  
		}
		
		//刷新视图
		function reshView(){
			$('#grid_selectrlist').datagrid('reload');
			$('#gridlist').datagrid('reload');
		}
		
		//新增按钮点击后通用触发方法
		function afterAddFun(){}
		//编辑通用赋值前触发方法
		function beforeEditFun(node){
		}
		
		//编辑通用赋值后触发方法
		function afterEditFun(node){
			debugger
			//$('#pageForm').form('load',getUrl('empdoc/findById?id='+node.empdocid));
			$("#id").val(node.empdocid);
			$("#sex").combobox("setValue",node.sexid);
			$("#insurancetype").combobox("setValue",node.insurancetypeid);
		}
		
		//通用表单提交前触发方法
		function beforeSubmitFormFun(){return true;}
		//通用保存后触发方法
		function afterSaveFun(id){}
		//通用撤账后触发方法
		function afterVoucherRevokeFun(){}
		//通用生成凭证后触发方法
		function afterVoucherCreateFun(){}
		//通用删除前触发方法
		function beforeDeleteFun(rows){return true;}
</script>
class="scedutablebox"   custom-class="calendarfirst"
</body>
</html>