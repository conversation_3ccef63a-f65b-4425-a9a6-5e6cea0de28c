package org.newstanding.common.utils.bussiness;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.newstanding.common.entity.User;
@SuppressWarnings({ "unchecked", "rawtypes" })
public class BussinessUtils {
	
	public static List<String> getArrayByUser(Map pageDataSingleton) {
		List<String> list = new ArrayList<String>();
		Set<String> sets = pageDataSingleton.keySet();
		for (String key : sets) {
			User user = (User) pageDataSingleton.get(key);
			list.add(user.getDatabase_());
		}
		return list;
	}
	
	/**
     * 返回 val 相等的 key 值
     * @param val
     * @param pd
     * @return
     */
    public static String findPdByVal(String val,Map pd) {
		Set<String> sets = pd.keySet();
		for (String key : sets) {
			User user = (User) pd.get(key);
			if (val.equals(user.getDatabase_())) {
				return key;
			}
		}
		return "";
	}
}
