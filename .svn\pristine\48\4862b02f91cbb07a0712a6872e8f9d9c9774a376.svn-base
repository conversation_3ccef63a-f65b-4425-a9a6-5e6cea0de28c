<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Remote JSONP - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Remote JSONP</h2>
	<div class="demo-info" style="margin-bottom:10px">
		<div class="demo-tip icon-tip"></div>
		<div>This sample shows how to use JSONP to retrieve data from a remote site.</div>
	</div>
	<input class="easyui-combobox" style="width:250px" data-options="
				loader: myloader,
				mode: 'remote',
				valueField: 'id',
				textField: 'name'
			">
	<script>
		var myloader = function(param,success,error){
			var q = param.q || '';
			if (q.length <= 1){return false}
			$.ajax({
				url: 'http://ws.geonames.org/searchJSON',
				dataType: 'jsonp',
                data: {
                    featureClass: "P",
                    style: "full",
                    maxRows: 20,
                    name_startsWith: q
                },
				success: function(data){
					var items = $.map(data.geonames, function(item){
						return {
							id: item.geonameId,
							name: item.name + (item.adminName1 ? ', ' + item.adminName1 : '') + ', ' + item.countryName
						};
					});
					success(items);
				},
                error: function(){
					error.apply(this, arguments);
				}
			});
		}
	</script>
</body>
</html>