<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Aligning Columns in DataGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Aligning Columns in DataGrid</h2>
	<div class="demo-info">
		<div class="demo-tip icon-tip"></div>
		<div>Use align and halign properties to set the alignment of the columns and their header.</div>
	</div>
	<div style="margin:10px 0;"></div>
	
	<table class="easyui-datagrid" title="Aligning Columns in DataGrid" style="width:700px;height:250px"
			data-options="singleSelect:true,collapsible:true,url:'datagrid_data1.json',method:'get'">
		<thead>
			<tr>
				<th data-options="field:'itemid',width:80,halign:'center'">Item ID</th>
				<th data-options="field:'productid',width:100,halign:'center'">Product</th>
				<th data-options="field:'listprice',width:80,align:'right',halign:'center'">List Price</th>
				<th data-options="field:'unitcost',width:80,align:'right',halign:'center'">Unit Cost</th>
				<th data-options="field:'attr1',width:250,halign:'center'">Attribute</th>
				<th data-options="field:'status',width:60,align:'center',halign:'center'">Status</th>
			</tr>
		</thead>
	</table>

</body>
</html>