<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
</head>
<body>

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background: #fff;">
		
			<!-- 左侧功能栏 -->
			<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({isCheckInitBalance:false,width:325,height:160})">新增</a>
			<a class="easyui-linkbutton compile-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:325,height:160})">编辑</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.findFun({width:325,height:140})">查找</a>
			<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('删除')">删除</a>
			<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="closeOrDelete('关闭')">禁用</a>
			<a class="easyui-linkbutton recover-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.recoveryFun()">恢复</a>
		</div>
		<div class="parceldivs" style="height: 331px; width: 630px;border:0">
			<div class="parceldiv_tow" style="height: 331px; width: 630px;">
				<div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);width:505px;height: 331px;"></div>
			</div>
		</div>
		<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		
			<!-- 底部功能栏 -->
			<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="javascript:ButtonFun.cancelFun('user')">关闭</a>
		</div>
		
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="width:300px;height:150px;">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="float: left;width: 100%;">
				<input type="hidden" id="id" name="id">
				<div class="bgdivtitfixed" style="border-bottom: 1px solid rgb(206, 218, 228);margin: 0px 0 0 0;height: 79px;width: 100%">
			    	<table cellpadding="5" style="margin: 7px 0 0px -13px;float: left;">
						<tr>
			    			<td style="width: 84px;text-align: right;">用户名</td>
			    			<td><input name="username" id="username" type="text" class="easyui-textbox" data-options="required:true,validType:['repeat[\'user\',\'sys_user\',\'username\',\'pageForm\']']"></input> </td>
			    		</tr>
			    	</table>
			    </div>	
		    </form>
		    <div style="height:40px;text-align:right;background:#ecf5fa;float: left;width: 100%;border-radius: 0 0 5px 5px;padding:0px 0 0 0">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="clearForm()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" onclick="submitForm({code:'user',type:2,renderid:'#gridlist'})">确定</a>
		    </div>
		</div>
		<!-- 查找弹窗 -->
		<div id="find_panel" class="easyui-window" style="width: 300px;height:184px" title="查找" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="search_pel"><input id="find_input" name="find_input" class="easyui-textbox" data-options="iconCls:'icon-search'" style="width:200px"> </div>
			<div class="windowbottom" style="height:42px">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="clearFindPanel()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" onclick="$('#gridlist').datagrid('load',{q:$('#find_input').val()})">确定</a>
		    </div>
		</div>
		
		<!-- 授权弹框 -->
		<div id="accredit_panel" class="easyui-window" style="width: 500px;height:480px" title="用户授权" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="search_pel" style="height: 216px;overflow: auto;">
				<div id="treeList" style="height: 184px;"></div>
				<input type="hidden" id="userid"/>
			</div>
			<div class="windowbottom" style="height:42px">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="clearFindPanels()">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" onclick="accredit_ok()">确定</a>
		    </div>
		</div>
	</div>

<script type="text/javascript">
	$(function(){
		var gridObj = new Object();
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'user/list.do?cxlx=1&cs=closestatus=0';
		gridObj["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'username',title:'用户名',align: 'left', halign: 'center',width:236},
								{field:'operation',title:'操作',width:237,formatter:formatOper_list,no_select:true}
							 ]];
		gridObj["idField"] = 'id';
		Grid.list_grid(gridObj);
	});
	function clearFindPanels(){//关闭
		$('#accredit_panel').window('close');
	}
	//渲染操作按钮
	function formatOper_list(val,row,index){
		return '<a href="javascript:void(0)" class="easyui-linkbutton fq-cssbtn" onclick="accredit('+row.id+')">赋权</a>'+
	    	   '<a href="javascript:void(0)" class="easyui-linkbutton cz-cssbtn" onclick="resetPasswrod(\''+row.username+'\')">重置密码</a>';  
	}
	
	
	//新增按钮点击后通用触发方法
	function afterAddFun(){}
	//编辑通用赋值前触发方法
	function beforeEditFun(node){}
	//编辑通用赋值后触发方法
	function afterEditFun(node){}
	//通用表单提交前触发方法
	function beforeSubmitFormFun(){return true;}
	//通用保存后触发方法
	function afterSaveFun(id){}
	
	//恢复按钮初始化参数
	function initRecovery(){
		var obj= {};
		obj["title"] = "恢复";
		obj["code"] = "user";
		obj["position"] = "#grid_selectrlist";
		obj["columns"] = [[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'username',title:'用户名',align: 'left', halign: 'center',width:300},
	                    	{field:'operation',title:'操作',width:167,formatter:formatOper_renew,no_select:true}
	                     ]];
		return obj;
	}
	
	//渲染恢复按钮
	function formatOper_renew(val,row,index){
		var url = "user/closeAll.do?DATA_IDS="+row["id"]+"&closestatus=0&table_name=sys_user";
	    return '<a href="javascript:void(0)" class="easyui-linkbutton recover-cssbtn" onclick="ButtonFun.renew(\''+url+'\')">恢复</a>';  
	}
	
	//刷新视图
	function reshView(){
		$('#grid_selectrlist').datagrid('reload');
		$('#gridlist').datagrid('reload');
	}
	
	//侧栏关闭按钮方法 operType：str  关闭   删除
	function closeOrDelete(operType){
		var node = $('#gridlist').datagrid('getSelected');
		if(!checkEm(node)){
			var url='';
			if(operType=='关闭'){
				url='user/closeAll?closestatus=1';
			}else{
				url='user/deleteAll';
			}
			ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}
	
	//用户授权
	function accredit(userid){
		/* var gridObj = new Object();
		gridObj["position"] = "#treeList";
		gridObj["url"] = 'menu/treeList.do?userid='+userid;
		gridObj["idField"] = 'id';
		gridObj["treeField"] = 'name';
		gridObj["checkbox"] = true;
		gridObj["cascadeCheck"] = false;
		gridObj["onlyLeafCheck"] = false;
		gridObj["onCheck"] = onCheckFun;
		Tree.treeCheck(gridObj); */
		var autoParent = false;
    	var eventNode = false;
		$('#treeList').tree({
            url:getUrl('menu/treeList.do?userid='+userid),
            checkbox:true,
            cascadeCheck: false,
            loadFilter: function(rows){
				return convertTreeTable(rows);
			},
			onLoadSuccess: function () {
				$('#treeList').tree('collapseAll');
			},
            onBeforeLoad: function (node, param) {
                var node = $('#treeList').tree('getSelected');
                if (node)
                    param.ResID = node.ID;
            },
            onCheck: function (node, checked) {
            	debugger
            	 var tree = $('#treeList');
            	 if (!eventNode) {//鼠标勾选事件初始化标致，标识鼠标勾选事件第一次遍历开始
                     eventNode = node;//将鼠标勾选时的节点保存起来，待向下 遍历时，将从此节点开始
                     treeup = true;//向上遍历标志，由鼠标勾选的节点eventNode开始向上遍历
                 }
                if (treeup) {//向上遍历                  
                    var ParentNode = tree.tree("getParent", node.target);
                    if (ParentNode) {//存在上级节点
                        if (checked)//如果是选择
                            tree.tree("check", ParentNode.target);//选中上级节点。注意：如果父节点之前未选中，执行此行代码后，会再次触发onCheck事件，下面的代码暂时不会被执行；如果之前兄弟节点已被选中，那么上级节点也被选中，此行代码执行后不会触发onCheck事件，而直接执行下面的代码
                        else {//如果是取消选择
                            var isCheck = false;
                            var childNode = tree.tree("getLeafChildren", ParentNode.target);
                            for (var i = 0; i < childNode.length; i++) {//循环当前节点的父节点的所有子节点，及包含当前节点的所有兄弟节点
                                if (childNode[i].checked) {
                                    isCheck = true;//只要有兄弟节点被选中，则退出循环
                                    break;
                                }
                            }
                            if (!isCheck)//如果所有兄弟节点及当前节点都未勾选，则取消父节点的勾选
                                tree.tree("uncheck", ParentNode.target);
                        }
                    }
                    treeup = false;//向上遍历结束
                   
                }
                //到达根部节点或向上遍历结束之后再回到原始节点开始向下遍历
                if (!treeup && eventNode) {
                    var childNode = tree.tree("getChildren", eventNode.target);//获取原始节点eventNode的所有子孙节点
                    if (checked) {//如果是选择
                        for (var i = 0; i < childNode.length; i++) {//循环所有子孙节点，全部选中
                            tree.tree("check", childNode[i].target);
                        }
                    }
                    else {//如果是取消
                        for (var i = 0; i < childNode.length; i++) {//循环所有子孙节点，全部取消勾选
                            tree.tree("uncheck", childNode[i].target);
                        }
                    }
                }
                eventNode = null;//标志本次鼠标勾选事件遍历结束
            }
        });
		
		$('#accredit_panel').dialog({
			title: '用户授权',    
		    width: 400,    
		    height: 300,    
		    closed: false,    
		    cache: false,
		    modal: true 
		});
		
		$('#userid').val(userid)
	}
	
	//用户授权-OK
	function accredit_ok(){
		debugger
		var userid = $('#userid').val()
		var jurisdiction;
		var nodeList = $('#treeList').tree('getChecked');
		if(nodeList == null){
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要授权的菜单！','warning');
		}else{
			jurisdiction = getSelectionIds(nodeList)
		}
		
		if(jurisdiction != null){
			sendAjax('user/accredit',{id:userid,jurisdiction:jurisdiction},accredit_callback);
		}
	}
	
	//用户授权-callback
	function accredit_callback(data){
		if(data.state='success'){
			/* $.messager.show({
				title:'操作成功',
				msg:data.message,
				showType:'show',
				timeout:1000,
			}); */
			promptbox('success','操作成功！');
			//关闭弹框
			$('#accredit_panel').dialog('close');
			
		}else if(data.state='error'){
			$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
		}
	}
	
	//用户 重置密码
	function resetPasswrod(username){
		sendAjax('user/initPwd',{username:username},accredit_callback);
	}
	
	
	
</script>

</body>
</html>