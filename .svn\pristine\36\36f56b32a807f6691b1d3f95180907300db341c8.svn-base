package org.newstanding.service.salary;

import java.io.File;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.fileupload.disk.DiskFileItem;
import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.common.utils.excel.XLSXCovertCSVReader;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.financialhandle.FillvouchersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;


@Service("calnorinsuranceService")
public class CalnorinsuranceService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	@Autowired
	private FillvouchersService fillvouchersService;
	
	
	/**
	 * 导入
	 */
	public PageData importExc(PageData pd, MultipartFile file)throws Exception{
		PageData resPd = new PageData();
		List<PageData> allList=new ArrayList<PageData>();
		List<PageData> itemList=new ArrayList<PageData>();
		try {
			//将接收到的文件保存成一个tmp临时文件存于项目根目录下
			CommonsMultipartFile cf = (CommonsMultipartFile)file; 
	        DiskFileItem fi = (DiskFileItem)cf.getFileItem(); 
	        File f = fi.getStoreLocation();
	        
	        //根据单据类型获取导入excel模板设定参数
	        List<PageData> params = (List<PageData>)dao.findForList("CommonMapper.findImportExcelParams", pd);
	        
	        //遍历每个sheet
	        boolean forflag=false;
	        outer:for (PageData param : params) {
	        	String tmpid = param.get("id").toString();				//模板主表ID
	      		//表层级
	        	String sheet = param.get("sheet").toString();			//excel工作表名称
	        	pd.put("tmpid", tmpid);
	        	
	        	//解析工作表
	        	List<String[]> dataList = XLSXCovertCSVReader.readerExcel(f.getAbsolutePath(), sheet, 30);
	        	
	        	if(dataList == null || dataList.size() == 0){
	        		resPd.put("state", "error");
	        		resPd.put("message", "无导入数据，请检查导入文件工作表名称是否正确");
	        		return resPd;
	        	}
	        	//dataList.remove(0);	//去除标题栏
	        	//将数据封装为：List<PageData>类型数据
	        	
	        	//先验证  薪资项是否存在  存在的话将id 和公司  或者个人 拼接起来
	        	if(dataList.size() != 0 ){
	        		
	        		String [] title=removeArrayEmptyTextBackNewArray(dataList.get(0));
	        		for(int k=0;k<title.length;k++){
	        			if(title[k] !=null){
	        				if(title[k].indexOf("-") !=-1){
	        					PageData insuranceitemPd=new PageData();
		        				String itemName=title[k].split("-")[0];
		        				String flag=title[k].split("-")[1];
		        				if(flag.equals("个人")){
		        					flag="personpart";
		        				}else{
		        					flag="comppart";
		        				}
		        				insuranceitemPd.put("insuranceitemsname", title[k]);
		        				
		        				pd.put("itemName", itemName);
		        				PageData itemPd=new PageData();
		        				if(pd.get("type") !=null && "housefund".equals(pd.get("type"))){
		        					pd.put("fromTable", "c_housefundrate");
		        					//根据name 查询  是否存在公積金比率设定中，如果不存在，则报错，存在找到  code
			        				itemPd=(PageData) dao.findForObject("CalnorinsuranceMapper.findItemsByName", pd);
		        				}else if(pd.get("type") !=null && "outsource".equals(pd.get("type"))){
		        					//根据name 查询  是否存在社保比率设定中 或公積金比率設定，如果不存在，则报错，存在找到  code
			        				itemPd=(PageData) dao.findForObject("CalnorinsuranceMapper.findAllItemsByName", pd);
		        				}else{
		        					pd.put("fromTable", "c_insurancerate");
		        					//根据name 查询  是否存在社保比率设定中，如果不存在，则报错，存在找到  code
			        				itemPd=(PageData) dao.findForObject("CalnorinsuranceMapper.findItemsByName", pd);
		        				}
		        				
		        				if(itemPd ==null){
		        					forflag=true;
		        					resPd.put("state", "error");
		        	        		resPd.put("message", "存在无档案保险项，"+itemName +" 请修改后重新导入");
		        	        		break outer;
		        				}
		        				title[k]=itemPd.get("insuranceitemsid").toString()+"-"+flag;
		        				insuranceitemPd.put("insuranceitemscode", title[k]);
		        				itemList.add(insuranceitemPd);
		        			}
	        			}
	        		}
	        		
	        		//循环数据  封装list
	        		for(int i=1;i<dataList.size();i++){
	        			PageData rowDataPd=new PageData();
	        			//通过职员名称判断是否有职员  
	        			PageData empPd =new PageData();
	        			for(int j=0;j<title.length;j++){
	        				if((title[j].indexOf("职员") !=-1 
	        						|| title[j].indexOf("员工") !=-1 
	        						|| title[j].indexOf("姓名") !=-1)
	        						&& title[j].indexOf("-") ==-1){
	        					if(dataList.get(i)[j] ==null || "".equals(dataList.get(i)[j])){
	        						forflag=true;
	        						resPd.put("state", "error");
		        	        		resPd.put("message", "职员名称不能为空， 请修改后重新导入！");
		        	        		break outer;
	        					}
	        					pd.put("empName", dataList.get(i)[j].trim());
	        					empPd=(PageData) dao.findForObject("CalnorinsuranceMapper.findEmpByName", pd);
	        					if(empPd ==null){
	        						forflag=true;
	        						resPd.put("state", "error");
		        	        		resPd.put("message", "存在无档案人员，"+dataList.get(i)[j]+" 请修改后重新导入！");
		        	        		break outer;
	        					}
	        				}
	        				if(title[j] !=null &&  !"".equals(title[j])){
	        					rowDataPd.put(title[j], dataList.get(i)[j].replace( ",", "") );
	        				}
	        			}
	        			rowDataPd.putAll(empPd);
	        			allList.add(rowDataPd);
	        		}
	        	}else{
	        		resPd.put("state", "error");
	        		resPd.put("message", "无有效数据");
	        		return resPd;
	        	}
			}
	        if(forflag){
        		return resPd;
        	}
	      //合计行  数据构建
			List<PageData> footerList=new ArrayList<PageData>();
			PageData footerPd=new PageData();
			footerPd.put("empdoc_name", "合计");
			for(Map<String,String> empPd :allList){
				for (String key : empPd.keySet()) {  
					  if(key.contains("-") || key.equals("basemoney")){
						 if(footerPd.get(key) !=null){
							 footerPd.put(key, 
									 CalculateUtil.add(footerPd.get(key).toString().replace( ",", "")
											 , String.valueOf(empPd.get(key)).replace( ",", "") ));

						 }else{
							 footerPd.put(key, String.valueOf(empPd.get(key)).replace( ",", "") );
						 }
					  }
				  
				} 
			}
			footerList.add(footerPd);
			
			resPd.put("itemNames", itemList);
			resPd.put("footer", footerList);
			resPd.put("rows", allList);
			resPd.put("total", allList.size());
			resPd.put("state","success");
			resPd.put("message","计算成功");
		} catch (Exception e) {
			e.printStackTrace();
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	private String[] removeArrayEmptyTextBackNewArray(String[] strArray) {
        List<String> strList= Arrays.asList(strArray);
        List<String> strListNew=new ArrayList<>();
        for (int i = 0; i <strList.size(); i++) {
            if (strList.get(i)!=null&&!strList.get(i).equals("")){
                strListNew.add(strList.get(i));
            }
        }
        String[] strNewArray = strListNew.toArray(new String[strListNew.size()]);
        return   strNewArray;
    }
	
	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.save("CalnorinsuranceMapper.save", pd);
			String id = pd.getString("id");
			
			String mxdata = pd.get("calnorinsurancemxdata").toString();
			List<PageData> mxdatalist = JsonUtils.strToListPd(mxdata);
			
			String data = pd.get("calnorinsurancemx").toString();
			Map<String, Object> map = JsonUtils.strToMap(data);
			
			List<PageData> itemlist = JsonUtils.strToListPd(map.get("itemNames").toString());
			
			List<PageData> mxlist = new ArrayList<PageData>();
			for (PageData mx : mxdatalist) {
				for (PageData item : itemlist) {
					String code = item.get("insuranceitemscode").toString();
					PageData tmp = new PageData();
					tmp.put("calnorinsuranceid", id);
					tmp.put("empdocid", mx.get("empdocid").toString());
					tmp.put("item", code);
					if(mx.get(code) != null){
						tmp.put("money", mx.get(code).toString());
					}else{
						tmp.put("money", 0);
					}
					mxlist.add(tmp);
				}
			}
			
			pd.put("mxList", mxlist);
			dao.save("CalnorinsuranceMapper.saveCalnorinsurancemx", pd);
			
			if (count > 0) {
				resPd.put("id", id);
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.delete("CalnorinsuranceMapper.delete", pd);
			dao.delete("CalnorinsuranceMapper.deleteCalnorinsurancemx", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			String id = pd.getString("id");
			int count = dao.update("CalnorinsuranceMapper.edit", pd);
			
			pd.put("DATA_IDS", id);
			dao.delete("CalnorinsuranceMapper.deleteCalnorinsurancemx", pd);
			
			String mxdata = pd.get("calnorinsurancemxdata").toString();
			List<PageData> mxdatalist = JsonUtils.strToListPd(mxdata);
			
			String data = pd.get("calnorinsurancemx").toString();
			Map<String, Object> map = JsonUtils.strToMap(data);
			
			List<PageData> itemlist = JsonUtils.strToListPd(map.get("itemNames").toString());
			
			List<PageData> mxlist = new ArrayList<PageData>();
			for (PageData mx : mxdatalist) {
				for (PageData item : itemlist) {
					String code = item.get("insuranceitemscode").toString();
					PageData tmp = new PageData();
					tmp.put("calnorinsuranceid", id);
					tmp.put("empdocid", mx.get("empdocid").toString());
					tmp.put("item", code);
					if(mx.get(code) != null){
						tmp.put("money", mx.get(code).toString());
					}else{
						tmp.put("money", 0);
					}
					mxlist.add(tmp);
				}
			}
			
			pd.put("mxList", mxlist);
			dao.save("CalnorinsuranceMapper.saveCalnorinsurancemx", pd);
			
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CalnorinsuranceMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CalnorinsuranceMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}

	/**
	 * 查找最大月份
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findMaxMonth(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("CalnorinsuranceMapper.findMaxMonth", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	/**
	 * 计算社保
	 * @param pd
	 * @return
	 */
	public PageData calinsurance(PageData pd){
		PageData resPd=new PageData();
		try {
			//获取社保维护中的  职员 中社保类型为  正常的职员 
			List<PageData> emplist =  (List<PageData>)dao.findForList("CalnorinsuranceMapper.fixedinsuempdoclist", pd);
			//获取 社保信息维护  中所有的 项目
			List<PageData> itemsList = findItems(pd);
			//在单个职员pd中插入此职员的所有  社保项目
			if(emplist.size()>0){
				for (PageData empd : emplist) {
					//获取职员所有保险项的值
					empd.put("database", getDatabase(pd));
					//List<PageData> levellist = findEmpdocSalaryItems(empd);
					
					PageData pageData = findEmpdocSalaryItems(empd);
					if(pageData !=null){
					/*	for (PageData pageData : levellist) {*/
						for (int j = 0; j < itemsList.size(); j++) {
						/*	if(itemsList.get(j).get("insuranceitemscode") ==null || "".equals(itemsList.get(j).get("insuranceitemscode"))) {
								resPd.put("state","error");
								resPd.put("message","计算存在错误,保险项："+itemsList.get(j).get("insuranceitemsname")+"，不存在保险信息维护数据！,请检查！");
								return resPd;
							}*/
							
						/*	if(itemsList.get(j).get("insuranceitemscode").toString().equals(pageData.get("insuranceitemscode"))){*/
								
								Double rate = Double.parseDouble(itemsList.get(j).get("rate").toString()) /100;
								empd.put("basemoney", pageData.get("basemoney").toString());
								//empd.put(itemsList.get(j).get("insuranceitemscode").toString(), pageData.get("money").toString());
								if("personpart".equals(itemsList.get(j).get("insuranceitemscode").toString().split("-")[1])) {
									empd.put(itemsList.get(j).get("insuranceitemscode").toString(), String.valueOf(CalculateUtil.formatMoneySpecial(CalculateUtil.multiply(Double.parseDouble(pageData.get("basemoney").toString()), rate,4))));
								}else {
									empd.put(itemsList.get(j).get("insuranceitemscode").toString(), String.valueOf(CalculateUtil.multiply(Double.parseDouble(pageData.get("basemoney").toString()), rate,1)));
								}
							}
						/*}*/
						/*}*/
					}else{
						resPd.put("state","error");
						resPd.put("message","计算存在错误,没有保险信息维护数据,请检查！");
						return resPd;
					}
				}
			}else{
				resPd.put("state","error");
				resPd.put("message","计算存在错误,没有保险信息维护数据,请检查！");
				return resPd;
			}
			//合计行  数据构建
			List<PageData> footerList=new ArrayList<PageData>();
			PageData footerPd=new PageData();
			footerPd.put("empdoc_name", "合计");
			for(Map<String,String> empPd :emplist){
				for (String key : empPd.keySet()) {  
					  if(key.contains("-") || key.equals("basemoney")){
						 if(footerPd.get(key) !=null){
							 footerPd.put(key, 
									 CalculateUtil.add(footerPd.get(key).toString()
											 , empPd.get(key)));

						 }else{
							 footerPd.put(key, empPd.get(key));
						 }
					  }
				  
				} 
			}
			footerList.add(footerPd);
			resPd.put("footer", footerList);
			resPd.put("rows", emplist);
			resPd.put("total", emplist.size());
			resPd.put("state","success");
			resPd.put("message","计算成功");
		} catch (Exception e) {
			e.printStackTrace();
			resPd.put("state","error");
			resPd.put("message","计算存在错误,请联系管理员");
		}
		
		return resPd;
	}
	public List<PageData> findItems(PageData pd)throws Exception{
		List<PageData> list = (List<PageData>)dao.findForList("CalnorinsuranceMapper.findItems", pd);
		return list;
	}
	/*public List<PageData> findEmpdocSalaryItems(PageData pd)throws Exception{
		return (List<PageData>)dao.findForList("CalnorinsuranceMapper.findEmpdocSalaryItems", pd);
	}*/
	
	public PageData findEmpdocSalaryItems(PageData pd)throws Exception{
		return (PageData)dao.findForObject("CalnorinsuranceMapper.findEmpdocSalaryItems", pd);
	}
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findCalnorinsurancemxByCalnorinsuranceid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CalnorinsuranceMapper.findCalnorinsurancemxByCalnorinsuranceid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CalnorinsuranceMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 查询是否被其他单据引用
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public List<PageData> checkIsUsed(PageData pd) {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("CalnorinsuranceMapper.checkIsUsed", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	public boolean checkRepeatByParam2(PageData pd) throws Exception {
		String check_param = pd.get("check_param").toString();
		if (StringUtils.getEncoding(check_param).equals("GB2312")) {
			check_param = URLDecoder.decode(URLDecoder.decode(check_param,"UTF-8"),"UTF-8");
		}
		String[] check_params = {check_param}; 
		PageData param = new PageData();
		param.put("database", pd.get("database").toString());
		param.put("table_name", pd.get("table_name").toString());
		param.put("id", pd.get("id").toString());
		PageData p = new PageData();
		for (String str : check_params) {
			String[] strs = str.split(";");
			param.put("str", strs[0]);
			param.put("value", strs[1]);
			p = (PageData)dao.findForObject("CommonMapper.checkRepeatByParam", param);
			if(p != null && Integer.parseInt(p.get("num").toString()) != 0){
				return true;
			}
		}
		return false;
	}
	
	public List<PageData> getExcelData(PageData pd) throws Exception {
		List<PageData> list = (List<PageData>) dao.findForList("CalnorinsuranceMapper.listAll", pd);
		PageData p = list.get(0);
		String data = p.get("calnorinsurancemxdata").toString();
		List<PageData> datalist = JsonUtils.strToListPd(data);
		return datalist;
	}
	
	/**
	 * 生成凭证
	 * @param pd
	 * @return
	 */
	public PageData createVoucher(PageData pd) {
		PageData resPd = new PageData();
		try {
			//按科目汇总后List
			List<PageData> subTotalList = new ArrayList<PageData>();
			//取凭证模板
			List<PageData> vmlist = getVoucherteMplate(pd.getString("code"),pd);
			//将mx转换成List
			List<PageData> addList = new ArrayList<PageData>();
			if (!"[]".equals(pd.getString("calnorinsurancemx"))) {
				com.alibaba.fastjson.JSONArray jay = com.alibaba.fastjson.JSONArray.parseArray(pd.getString("calnorinsurancemx"));
				for (Object o : jay) {
					PageData calnorinsurancemx = new PageData(JsonUtils.strToMap(o.toString()));
					addList.add(calnorinsurancemx);
				}
			}else{
				resPd.put("state", "error");
				resPd.put("message", "没有获取到社保数据，请检查");
				return resPd;
			}
			//构造凭证数据
			PageData mainPd = new PageData();
			List<PageData> mx_list = new ArrayList<PageData>();
			mainPd.put("accountperiod", pd.get("accountperiod"));
			mainPd.put("createby", getCurrentUserByCatch(pd).getUsername());
			mainPd .put("source", "calnorinsurance");
			mainPd .put("attachcount", "0");
			mainPd.put("database", getDatabase(pd));
			mainPd.put("voucherdate", pd.get("createdate"));
			PageData codePd=fillvouchersService.findMaxCodeAndEtc(mainPd);
			if(codePd != null && codePd.get("code")!=null){
				mainPd.put("code", codePd.get("code"));
			}
			for (PageData vm : vmlist) {
				PageData mxPd = new PageData();
				switch (vm.getString("businessmatters")) {
				case "公司部分": 	
					//查询所有的人员及公司部分薪资项。然后按照所属累别对应的科目合计金额
					List<PageData> comppartlist = (List<PageData>) dao.findForList("CalnorinsuranceMapper.findComppart", pd);
					for(PageData ywPd : comppartlist){
						String subid = "";
						if("基本社保".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("insurancesubid").toString();
						}else if("公积金".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("fund_insurancesubid").toString();
						}else if("补充养老".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("oldage_insurancesubid").toString();
						}else if("补充医疗".equals(ywPd.get("belongsto").toString())) {
							subid = ywPd.get("medical_insurancesubid").toString();
						}else {
							resPd.put("state", "error");
							resPd.put("message",  "保险项设置中的归属项未设置完整，请检查！。"); 
							return resPd;
						}
							
						boolean state=false;
						for(PageData subPd : subTotalList){
							if(subid.equals(subPd.get("insurancesubid").toString())){
							 	double money = Double.parseDouble(subPd.get("compparttotal").toString());    
			                    money = CalculateUtil.add(money, Double.parseDouble(ywPd.get("money").toString()),1); 
			                    subPd.put("compparttotal", money);
			                    state = true; 
							}
						}
						if(!state){    
							PageData dpd = new PageData();
							dpd.put("insurancesubid", subid);
							dpd.put("compparttotal", ywPd.get("money"));
							subTotalList.add(dpd);    
			            }    
					}
					
					
					for( PageData afterSubPd: subTotalList){
						if(afterSubPd.get("compparttotal") != null && Double.parseDouble(afterSubPd.get("compparttotal").toString()) != 0){
							PageData comppartPd=new PageData();
							comppartPd.put("debitmoney", afterSubPd.get("compparttotal"));
							comppartPd.put("rowid",(int)(Math.random()*100000));
							comppartPd.put("subjectid",afterSubPd.get("insurancesubid"));
							comppartPd.put("abstracta", vm.get("defaulttranmemo"));
							mx_list.add(comppartPd);
						}
					}
					break;
				case "个人部分":	
					if(pd.get("personparttotal") != null && Double.parseDouble(pd.get("personparttotal").toString()) != 0){
						mxPd.put("debitmoney", pd.get("personparttotal"));
						mxPd.put("rowid",(int)(Math.random()*100000));
						mxPd.put("subjectid",vm.get("accsubjectsid"));
						mainPd.put("perssubjectid",vm.get("accsubjectsid"));//將个人部分的  会计科目更新到业务表中，工资单生成凭证时使用
						mxPd.put("abstracta", vm.get("defaulttranmemo"));
						mx_list.add(mxPd);
					}
					break;
				case "应付社保":	
					if(pd.get("totalmoney") != null && Double.parseDouble(pd.get("totalmoney").toString()) != 0){
						mxPd.put("creditmoney", pd.get("totalmoney"));
						mxPd.put("rowid",(int)(Math.random()*100000));
						mxPd.put("subjectid",vm.get("accsubjectsid"));
						mxPd.put("abstracta", vm.get("defaulttranmemo"));
						mx_list.add(mxPd);
					}
					break;
				default:
					break;
				}
			}
			mainPd.put("fillvouchersmx", JsonUtils.PageDataToJSONArray(mx_list).toString());
			//生成凭证
			resPd=fillvouchersService.save(mainPd);
			if(resPd .get("state") !=null && "success".equals(resPd .get("state").toString())){
				resPd.put("message",  "已生成凭证,凭证号码："+mainPd.get("code").toString());
				mainPd.put("id", pd.get("id"));
				mainPd.put("fillvouchersid", resPd.get("id"));
				dao.update("CalnorinsuranceMapper.updateVouchercodeAndIsAccount", mainPd);
			}else{
				resPd.put("message",  "生成凭证失败，请联系管理员！");
			}
				
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
}
