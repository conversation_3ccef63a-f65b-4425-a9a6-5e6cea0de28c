<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Row Border in DataGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Row Border in DataGrid</h2>
	<div class="demo-info">
		<div class="demo-tip icon-tip"></div>
		<div>This sample shows how to change the row border style of datagrid.</div>
	</div>
	<div style="margin:10px 0;">
		<span>Border:</span>
		<select onchange="changeBorder(this.value)">
			<option value="lines-both">Both</option>
			<option value="lines-no">No Border</option>
			<option value="lines-right">Right Border</option>
			<option value="lines-bottom">Bottom Border</option>
		</select>
		<span>Striped:</span>
		<input type="checkbox" onclick="$('#dg').datagrid({striped:$(this).is(':checked')})">
	</div>
	<table id="dg" class="easyui-datagrid" title="Row Border in DataGrid" style="width:700px;height:250px"
			data-options="singleSelect:true,fitColumns:true,url:'datagrid_data1.json',method:'get'">
		<thead>
			<tr>
				<th data-options="field:'itemid',width:80">Item ID</th>
				<th data-options="field:'productid',width:100">Product</th>
				<th data-options="field:'listprice',width:80,align:'right'">List Price</th>
				<th data-options="field:'unitcost',width:80,align:'right'">Unit Cost</th>
				<th data-options="field:'attr1',width:250">Attribute</th>
				<th data-options="field:'status',width:60,align:'center'">Status</th>
			</tr>
		</thead>
	</table>
	<script type="text/javascript">
		function changeBorder(cls){
			$('#dg').datagrid('getPanel').removeClass('lines-both lines-no lines-right lines-bottom').addClass(cls);
		}
	</script>
	<style type="text/css">
		.lines-both .datagrid-body td{
		}
		.lines-no .datagrid-body td{
			border-right:1px dotted transparent;
			border-bottom:1px dotted transparent;
		}
		.lines-right .datagrid-body td{
			border-bottom:1px dotted transparent;
		}
		.lines-bottom .datagrid-body td{
			border-right:1px dotted transparent;
		}
	</style>
	
</body>
</html>