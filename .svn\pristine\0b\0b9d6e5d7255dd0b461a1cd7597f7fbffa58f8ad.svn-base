<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
</head>
<body style="background: #ebedf3;">

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
	
		<!-- 顶部功能栏 -->
		<div id="northToolDiv" data-options="region:'north'" style="background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4); background: -moz-linear-gradient(top,#fff,#f4f4f4);background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<a class="easyui-linkbutton gl_btns" style="float: left;" href="javascript:void(0)" onclick="$('#filter_pageId').window('open')"></a>
			<a class="easyui-linkbutton gs_btns" id="formulaBtn0" style="float: left;" href="javascript:void(0)" onclick="formulaBtn(0)"></a>
			<a class="easyui-linkbutton gs_btns" id="formulaBtn1" style="float: left;display: none;" href="javascript:void(0)" onclick="formulaBtn(1)"></a>
			<p class="refreshbtns" id="refreshbtn" onclick="refresh()" style="display:none"></p>
			<div style="float: right;width: 435px;margin: 0 0 0 100px;">
				<a class="easyui-linkbutton dc_btns" href="javascript:void(0)" onclick="ExportExcel()"></a>
				<a class="easyui-linkbutton dy_btns" href="javascript:void(0)" onclick="printAndPreview('打印')"></a>
				<a class="easyui-linkbutton yl_btns" href="javascript:void(0)" onclick="printAndPreview('预览')"></a>
				<a class="easyui-linkbutton dypz_btns" href="javascript:void(0)" onclick="printallocation({width:320,height:490,menucode:'CashFlow'})"></a>
				<a class="easyui-linkbutton gb_btns" href="javascript:void(0)" onclick="closeIndexTabs({title:'现金流量表'})"></a>
			</div>
		</div>
		
		<!-- 主面板 -->
		<div style="margin: 54px 0 0 0;border:1px #6f9ec2 solid;border-bottom:0" id="parcel_southToolDiv">
		    <div class="gridlist_top">
		        <p class="gridlist_top_le">
		        	<span>单位：</span>
		        	<input id="accountname" type="text" name="" class="" readonly="readonly" />
		        </p>
		        <p class="gridlist_top_cent">现金流量表</p>
		        <p class="gridlist_top_ri" style="margin:0">
		        	<span>会计期间：</span>
		        	<span class="towsp" id="period"></span>
		        </p>
		    </div>
		    <!-- 数据面板 -->
			<div id="gridlist" data-options="region:'center'" style="width: 100%"></div>
		</div>
		
		<!--报表过滤弹窗 -->
		<div id="filter_pageId" class="easyui-window" title="过滤" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
	    	<div class="bgdivtitfixed" style="border: 0;margin: 0 0 0 0;height: 132px;width: 100%">
		    	<ul class="report_ul report_ults">
		    		<li style="margin: 31px 0 0 0;">
		    			<p >期间单位：</p>
	    				<select class="easyui-combobox" id="unitSelect" data-options="onChange:unitSelect,editable:false,panelHeight:'auto'" style="width:100px;height:27px;">
							<option value="mon">月</option>
							<option value="quarter">季度</option>
						</select>
		    		</li>
		    		<li>
		    			<p >会计期间：</p>
		    			<input type='text' id="year" name="year" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" readonly="readonly" value="${year}" onclick="WdatePicker({skin:'whyGreen',startDate:'%y',dateFmt:'yyyy'})" />
		    			<div id="unit_mon">
							<input type="text" id="month" name="month" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" readonly="readonly" value="${month}" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM'})" /> 
							<span style="float: left;margin: 6px 3px 0 0;">月
		    			</div>
		    			<div id="unit_quarter" hidden="hidden">
		    				<select class="easyui-combobox" id="quarter" data-options="editable:false,panelHeight:'auto'" style="width:100px">
								<option value="1">第一季度</option>
								<option value="2">第二季度</option>
								<option value="3">第三季度</option>
								<option value="4">第四季度</option>
							</select>
		    			</div>
		    		</li>
		    	</ul>
		    </div>	
		    <div style="height:40px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="$('#filter_pageId').window('close');">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn"  onclick="queryData()">确定</a>
		    </div>
		</div>
	<!-- 打印配置 -->
			<div id="printedit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div style="width:316px;border: 1px #cedae4 solid;">
		        <div class="bgdivtit" style="border: 0;height:47px">
			        <span style="margin:0 10px;line-height: 46px;display: inline-block;float: left;">模板名称</span>
					<select id="temp_name"  name="temp_name"  class="easyui-combobox"  data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="width:120px;margin: 0;height: 27px;">
					    
					</select>
					<a class="delelte_fillvouchers" href="javascript:void(0)" onclick="del_temp()">删除</a>
					<a class="add_fillvouchers" href="javascript:void(0)" onclick="add_temp()">新增</a>
				</div>	
		    	<div class="centerpagediv">
		    	     <div class="tablepage">
			    	     <p class="paper_btn paper_btns" onclick="switchoverF(1)">纸张</p>
			    	     <p class="page_btn page_btns" onclick="switchoverF(2)">页面</p>
		    	     </div>
		    	     <div class="paper_div">
		    	     <div id="divMMHeight" style="height: 1mm; width: 1mm; display: none;"></div>
		    	     <input type="hidden" id="menucode" name="menucode"> 
		    	     <input type="hidden" id="tempid" name="tempid">  
		    	      <input type="hidden" id="name" name="name">    
		    	      <input type="hidden" id="content" name="content">
		    	      <input type="hidden" id="html" name="html">  
		    	          <ul>
		    	             <li style="margin: 22px 0 0 0;">
		    	                 <span>纸张类型：</span>
		    	                 <div style="background:#fff;width: 149px;height: 25px;margin: 1px 0 0 62px; border-radius: 3px;">
			    	                 <div id="selectStyle" class="selectStyle">
							             <select class="select" id="pagelx" name="pagelx">
											<option value="A5(横向)">A5(横向)</option>
											<option value="A4">A4</option>
											<option value="自定义">自定义</option>
										</select>
									</div>
									<div class="input-group-addon"><p class="glyphicon actips"></p></div>
		    	                 </div>
		    	             </li>
		    	             <li>
		    	                 <span>页面属性</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>高度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="height" type="text" id="height" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>宽度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="width" type="text" id="width" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>页边距</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>上边距：</span>
		    	                 <input name="page_top" type="text" id="page_top" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>下边距：</span>
		    	                 <input name="page_bottom" type="text" id="page_bottom"onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>左边距：</span>
		    	                 <input name="page_left" type="text" id="page_left" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>右边距：</span>
		    	                 <input name="page_right" type="text" id="page_right" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	          </ul>
		    	     </div> 
		    	     <div class="pape_div">
		    	         <ul class="page_t_ul page_t_ulspec">
		    	             <li>
		    	                <span>列项目</span>
		    	                <span>列宽（毫米）</span>
		    	                <span>是否打印</span>
		    	             </li>
		    	               <li>
		    	                <span>资产</span>
		    	                <span>
		    	                    <input  type="text" id="itemwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                <span>
		    	                    <select id="itemSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             
		    	               <li>
		    	                <span>行次</span>
		    	                <span>
		    	                    <input  type="text" id="numberwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                <span>
		    	                    <select id="numberSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             
		    	                <li>
		    	                <span>本月(季)金额</span>
		    	                <span>
		    	                    <input  type="text" id="datasources_width" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                <span>
		    	                    <select id="datasources_Sel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>本年度累计金额</span>
		    	                <span>
		    	               		<input   type="text" id="datasources_yearwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="datasources_yearSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	         </ul>
		    	         <ul class="page_b_ul" style="margin: 17px 0 109px 19px;height: 63px;">
		    	             <li style="margin: 1px 0 0 0;">
		    	                 <span>行高：</span>
		    	                 <input name="rowheight" type="text" id="rowheight" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>每页循环行数：</span>
		    	                 <input name="hs" type="text" id="hs" onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')" class=""/>
		    	                 <p>（单位：行）</p>
		    	             </li>
		    	         </ul>
		    	     </div>
		    	</div>
		    </div>
		    <div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
				<!-- 底部功能栏 -->
				<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="closeclearF()">取消</a>
				<a class="easyui-linkbutton sure-dialog" href="javascript:void(0)" onclick="templateSure()">确定</a>
			</div>
		</div>
		
		
		<div id="edit_pageIdm" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="overflow: hidden;display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的模板</p>
		    	<select class="easyui-combobox" id="template_name" name="template_name" data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="overflow: hidden;width: 125px;height:30px">
   	       			
				</select>
				<input type="hidden" id="tempurl" />
				<input type="hidden" id="opertype">
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdm')">取消</a>
			    	<a href="javascript:void(0)" id="next_btn" class="easyui-linkbutton cancel-btn sureNew_btn" onclick="nextprint()">下一步</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn sureNew_btn print_btns" style="display:none" onclick="printTemp('edit')">打印</a>
		    	</div>
		    </div>
		</div>
		
		<div id="edit_pageIdprintb" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的操作</p>
		    	<input type="hidden" id="selecttempurl" />
		    	<input type="hidden" id="tempname" />
		    	<!-- <a href="javascript:void(0)" class="print_btn" onclick="exportTemp()">导出</a> -->
		    	<a href="javascript:void(0)" class="print_btn" style="margin:-5px 0 0 28px" onclick="printTemp('edit')">打印</a>
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdprintb')">取消</a>
		    	</div>
		    </div>
		</div>
	

<script type="text/javascript">

	var accinfo = findAccInfo();
	$('#accountname').val(accinfo.compname);

	var width = $(window).width()-76;
	var w1 = width/30;
	var heights = $(window).height();
	$("#parcel_southToolDiv").height(heights-100);
	$("#gridlist").height(heights-100);
	//$("#gridlist").width(w1*15+50);
	var data = JSON.parse('${data}');
	var code = data.code;
	
	var obj = new Object();
	obj["position"] = '#gridlist';
	obj["url"] = 'report/getGridData_CashFlow';
	obj["columns"] = [[
						{field:'id',title:'id',hidden:true},
						{field:'item',title:'资产',align: 'left', halign: 'center',width:w1*6,hidden:true,
							editor:{
								type: 'textbox',
							},
							formatter:function(value,row,index){
								if(row.rank == 1){
									return '&nbsp;&nbsp;' + value;
								}
								if(row.rank == 2){
									return '&nbsp;&nbsp;&nbsp;&nbsp;' + value;
								}
								if(row.rank == 3){
									return '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + value;
								}
								return value;
							}
						},
						
						{field:'item0',title:'资产',align: 'left', halign: 'center',width:w1*6,
							formatter:function(value,row,index){
								if(row.rank == 1){
									return '&nbsp;&nbsp;' + value;
								}
								if(row.rank == 2){
									return '&nbsp;&nbsp;&nbsp;&nbsp;' + value;
								}
								if(row.rank == 3){
									return '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' + value;
								}
								return value;
							}
						},
						{field:'number',title:'行次',align: 'left', halign: 'center',width:w1-5},
						{field:'datasources_',title:'本月(季)金额',align: 'right', halign: 'center',width:w1*4-3.6,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'datasources_year',title:'本年度累计金额',align: 'right', halign: 'center',width:w1*4-3.5,
							formatter:function(value,row,index){
								return '<p style="width:100%;height:100%;font-size: 12px;margin:0 0 0 -17px;">'+formatMoney(value)+'</p>';
							}
						},
						{field:'datasources',title:'数据来源',align: 'left', halign: 'center',width:w1*19,hidden:true,
							editor:{
								type: 'textbox',
							}
						},
					]];
	obj["pagination"] = false;
	obj["listDbClickFun"] = function (){};
	obj["fitColumns"] = false;
	//生成报表表格
	Grid.list_report(obj);
	
	$('#gridlist').datagrid({
		onLoadSuccess: function (data){
			debugger
			var val = data.rows[24].datasources_;
			if(!checkEm(val) && flag == 1){
				var result;
				$.ajax({
					url: getUrl('report/check_CashFlow'),
					type: 'post',
					async: false,
					data: {code:code, unit:unit, year:year, quarter:quarter, month:month,begin_month:begin_month,end_month:end_month},
					dataType: 'json',
					success: function(d){
						debugger
						result = d.final_balance;
					}
				});
				debugger;
				if(result.toFixed(2) != val.toFixed(2)){
					$.messager.alert('提示','<span class="hintsp_e">提示</span>科目中现金流量属性设置或数据来源不对，请检查科目及凭证。','error');
				};
			}
		},
		onClickCell: function(index, field){
			if( !checkEm($(obj.position).datagrid('getColumnOption',field).editor) ){
				if ($(obj.position).datagrid('options').editIndex == undefined){
					$(obj.position).datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
					$(obj.position).datagrid('options').editIndex = index;
					var ed = $(obj.position).datagrid('getEditor', {index:index,field:field});  
					if($(ed.target).next().find(".textbox-text").length>0){
						$(ed.target).next().find(".textbox-text").focus();
					}else{
						$(ed.target).focus();
					}
				}else if ($(obj.position).datagrid('validateRow', $(obj.position).datagrid('options').editIndex)){
					$(obj.position).datagrid('endEdit', $(obj.position).datagrid('options').editIndex);
					$(obj.position).datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
					$(obj.position).datagrid('options').editIndex = index;
					var ed = $(obj.position).datagrid('getEditor', {index:index,field:field});
					if($(ed.target).next().find(".textbox-text").length>0){
						$(ed.target).next().find(".textbox-text").focus();
					}else{
						$(ed.target).focus();
					}
				}
			}
		},
		onEndEdit: function (index, row, changes){
			if(!$('#gridlist').datagrid('validateRow', index)){
				/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>未验证成功！',showType:'slide',timeout:1000,style:{
					right : '', bottom : '',
					top : document.body.scrollTop + document.documentElement.scrollTop
				}}); */
				promptbox('success','未验证成功！');
			}else{
				changes["id"] = row.id;
				$.ajax({
					url: getUrl('report/edit_cashFlow'),
					type: 'post',
					async: false,
					data: changes,
					dataType: 'json',
					success: function(data){
						data = JSON.parse(data);
						if(data.state != 'success'){
							$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
						}
					}
				});
			}
		}
	});
	
	
	//定义变量
	var flag = 0;
	var unit, year, quarter, month, accountperiod,begin_month,end_month;
	
	//初始化变量 并赋值
	function initData(){
		debugger
		unit = $('#unitSelect').combobox('getValue');
		
		year = $("#year").val();
		quarter = $("#quarter").combobox('getValue');
		month = $("#month").val();
		begin_month = $("#month").val();
		end_month = $("#month").val();
		var mon_str = month;
		if(unit == 'quarter'){
			if(quarter == 1){
				mon_str = '1~3';
				begin_month=1;
				end_month = 3;
			}else if(quarter == 2){
				mon_str = '4~6';
				begin_month=4;
				end_month = 6;
			}else if(quarter == 3){
				mon_str = '7~9';
				begin_month=7;
				end_month = 9;
			}else if(quarter == 4){
				mon_str = '10~12';
				begin_month=10;
				end_month = 12;
			}
		}
		$('#period').text(year + '年' + mon_str + '月');
	}
	
	//过滤弹窗 确定
	function filterOk(){
		queryData();
	}
	var searchFlag=0;
	//查询
	function queryData(){
		searchFlag=1;
		initData();
		flag = 1;
		$('#gridlist').datagrid('load',{code:code, unit:unit, year:year, quarter:quarter, month:month,begin_month:begin_month});
		$('#filter_pageId').window('close');
	}
	//公式
	function formulaBtn(i){
		var width = $(window).width()-76;
		var w1 = width/30;
		if(i == 0){
			$('#formulaBtn0').css('display','none');
			$('#formulaBtn1').css('display','inline');
			$('#gridlist').datagrid('hideColumn', 'datasources_');
			$('#gridlist').datagrid('hideColumn', 'datasources_year');
			$('#gridlist').datagrid('showColumn', 'datasources');
			
			$('#gridlist').datagrid('hideColumn', 'item0');
			$('#gridlist').datagrid('showColumn', 'item');
			
		}else{
			$('#formulaBtn1').css('display','none');
			$('#formulaBtn0').css('display','inline');
			$('#gridlist').datagrid('hideColumn', 'datasources');
			$('#gridlist').datagrid('showColumn', 'datasources_');
			$('#gridlist').datagrid('showColumn', 'datasources_year');
			
			$('#gridlist').datagrid('hideColumn', 'item');
			$('#gridlist').datagrid('showColumn', 'item0');
		}
	}
	//期间单位切换
	function unitSelect(){
		var unit = $('#unitSelect').combobox('getValue');
		if(unit == 'mon'){
			$('#unit_quarter').hide();
			$('#unit_mon').show();
		}else{
			$('#unit_mon').hide();
			$('#unit_quarter').show();
		}
	}
	
	//导出
	function ExportExcel(){
		var obj=new Object;
		obj['renderid']='#gridlist';
		obj['title'] = '现金流量表';
		obj['controllername'] = 'report';
		obj['run_method'] = 'getGridData_CashFlow';
		obj['method_type'] = '1';
		obj['cs'] = '';
		obj['pms'] = {code:code, unit:unit, year:year, quarter:quarter, month:month};
		toExcel(obj);
	}
	//打印、预览
	function printAndPreview(opertype){
		if(searchFlag==0){
			return;
		}
		var obj=new Object;
		obj['opertype']=opertype;
		obj['operpage']='report';
		obj['menucode']='CashFlow';
		obj['controllername'] = 'report';
		obj['run_method'] = 'getGridData_CashFlow';
		obj['method_type'] = '1';
		obj['cs'] = '';
		obj['pms'] = {code:code, unit:unit, year:year, quarter:quarter, month:month, accountperiod:year+'-'+month};
		previewClick_report(obj);
	}

	function closeKmye(id){
		$('#'+id).dialog('close');
	}
	//关闭弹出框
	function closeclearF(){
		$('#printedit_pageId').window('close');
	}
	//temp_name 事件
	$("#temp_name").combobox({
		onSelect:function(record){
			$.ajax({
			    url:getUrl('printSet/findByCode.do'),
			    type:'post',
			    async:false,
			    data: {"menucode":"CashFlow","name" : record.text},
			    dataType:'json',
			    success:function(data){
	  				$("#menucode").val(data.menucode);
	  				$('#tempid').val(data.id);
	  				$("#height").val(data.height);
	  				$("#width").val(data.width);
	  				$("#hs").val(data.hs);
	  				$("#page_bottom").val(data.page_bottom);
	  				$("#page_left").val(data.page_left);
	  				$("#page_right").val(data.page_right);
	  				$("#page_top").val(data.page_top);
	  				$("#pagelx").val(data.pagelx);
	  				$("#rowheight").val(data.rowheight);
	  				$("#title").val(data.title);
	  				
	  				$("#content").val(data.content);
	  				$("#html").val(data.html);
	  				var columnstr=data.columnstr;
	  				var cols=columnstr.split(',');
	  				for(var i=0;i<cols.length;i++){
	  					var str=cols[i].split(':');
	  					$("#"+str[0]).val(str[1]);
	  				}
			    }
			})
		}
	})
	//模板设置确定
	function templateSure(){
		//将毫米转化成px  
		debugger
		var height=$("#height").val();
		var width=$("#width").val();
		var hs=$("#hs").val();
		var page_bottom=$("#page_bottom").val();
		var page_left=$("#page_left").val();
		var page_right=$("#page_right").val();
		var page_top=$("#page_top").val();
		var pagelx=$("#pagelx").val();
		var rowheight=$("#rowheight").val();
		var title='现金流量表';
		
		
		var pheight = $('#divMMHeight').height(), pwidth = $('#divMMHeight').width();
		var rowheight=$("#rowheight").val();
		
		var itemwidth = $("#itemwidth").val() || 0;
		var numberwidth = $("#numberwidth").val() || 0;
		var datasources_width = $("#datasources_width").val() || 0;
		var datasources_yearwidth = $("#datasources_yearwidth").val() || 0;
		
		var itemSel = $("#itemSel").val();
		var numberSel = $("#numberSel").val();
		var datasources_Sel = $("#datasources_Sel").val();
		var datasources_yearSel = $("#datasources_yearSel").val();
		var menucode="CashFlow";
		var name=/* $('#name').val() */$("#temp_name").combobox("getValue");
		var tdf=1;
		var widthp = ((parseInt(itemwidth)+parseInt(numberwidth)+parseInt(datasources_width)+parseInt(datasources_yearwidth)) * pwidth)+parseInt(17)
		
		var columnstr="itemwidth:"+itemwidth+",numberwidth:"+numberwidth
		+",datasources_width:"+datasources_width+",datasources_yearwidth:"+datasources_yearwidth
		+",itemSel:"+itemSel+",numberSel:"+numberSel
		+",datasources_Sel:"+datasources_Sel+",datasources_yearSel:"+datasources_yearSel;
		
		var contentstr='<style type="text/css"></style>'
			+'<p style="text-align: center;">'
			+'    <span style="font-size: 20px;">现金流量表</span>'
			+'</p>'
			+'<p style="width:'+widthp+'px;margin: 0 auto;">'
			+'    <span style="font-size: px;"></span><span style="font-size: 12px;width: 100%;display: inline-block;">'
			+'<span style="font-size: px;display: inline-block;float:left">'
			+'    单位：<textarea class="laowang" readonly="readonly" id="companyname" '
			+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 190px; height: 19px; border: 1px dashed rgb(0, 0, 0); font-size: px; font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 2px 0 0 0;" '
			+'    title="~{main.companyname}">公司名称</textarea> '
			+'</span><span style="font-size: px;display: inline-block;float:right">'
			+'    会计期间：<textarea class="laowang" readonly="readonly" id="accountperioda" '
			+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 150px; height: 18px; border: 1px dashed rgb(0, 0, 0); font-size: px;'
			+'font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 2px 0 0 0;" title="~{main.accountperioda}">会计期间</textarea></span></span>'
			+'</p>'
			+'<hr/>'
			+'<table>'
			+'    <tbody>'
			+'        <tr class="firstRow">';
			
			var firstTr='';
			var secondtr='';
			var hjtr='';
			
			if(itemSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+itemwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center" >记账日期</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+itemwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="item" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+itemwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.item}">记账日期</textarea>'
				+'            </td>';
			}
			if(numberSel=='是'){
				
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+numberwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center" >行次</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+numberwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="number" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+numberwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.number}">行次</textarea>'
				+'            </td>';
			}
			if(datasources_Sel=='是'){
				
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+datasources_width * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center" >本月(季)金额</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+datasources_width * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="datasources_" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+datasources_width * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.datasources_}">本月(季)金额</textarea>'
				+'            </td>';
			}
			
			if(datasources_yearSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+datasources_yearwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">本年度累计金额</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+datasources_yearwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="datasources_year" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+datasources_yearwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.datasources_year}">本年度累计金额</textarea>'
				+'            </td>';
				firstTr +='</tr><tr>';
				secondtr +='</tr>'
					+'</tbody>'
					+'</table>'
					+'<hr/>'
					+'<p>'
					+'<span style="font-size: 12px;"></span><br/>'
					+'</p>';
			}else{
				firstTr +='</tr><tr>';
				secondtr +='</tr>'
					+'</tbody>'
					+'</table>'
					+'<hr/>'
					+'<p>'
					+'<span style="font-size: 12px;"></span><br/>'
					+'</p>';
			}
			var content=contentstr + firstTr +secondtr;
		//保存
		if(name == ''){
			var name = prompt('请输入文件名', '');
	    	if(name != null && name != ''){
	    		//保存模板
	    		$.post(getUrl('printSet/save'),{menucode:menucode,name:name,title:title,content:content,
	    									   pagelx:pagelx,width:width,height:height,
	    									   page_top:page_top,page_bottom:page_bottom,
	    									   page_left:page_left,page_right:page_right,rowheight:rowheight,pagegs:7,
	    									   hs:hs,columnstr:columnstr},function(data){
											    if(data.state == 'success'){
											    	$('#printcode').val(data.code);
								    				promptbox('success','操作成功！')
								    			}else{
								    				alert(data.msg);
								    				promptbox('error',data.msg)
								    			}
	    		})
	    	}
		}else{
			$.post(getUrl('printSet/edit'),{menucode:menucode,name:name,title:title,content:content,
				   pagelx:pagelx,width:width,height:height,
				   page_top:page_top,page_bottom:page_bottom,
				   page_left:page_left,page_right:page_right,rowheight:rowheight,pagegs:7,
				   hs:hs,columnstr:columnstr},function(data){
				    if(data.state == 'success'){
				    	$('#printcode').val(data.code);
	    				promptbox('success','操作成功！')
	    			}else{
	    				alert(data.msg);
	    				promptbox('error',data.msg)
	    			}
	})
		}
		$('#printedit_pageId').window('close');
	}
	$("#pagelx").change(function(e){
		var lx= $("#pagelx").val();
		if(lx=="A5(横向)"){
			$("#height").val(148);
			$("#width").val(210);
			$("#height").attr("readOnly",true);
			$("#width").attr("readOnly",true);
		}else{
			$("#height").val(297);
			$("#width").val(210);
			if(lx=="A4"){
				$("#height").attr("readOnly",true);
				$("#width").attr("readOnly",true);
			}else{
				$("#height").attr("readOnly",false);
				$("#width").attr("readOnly",false);
			}
		}
		
	})
</script>

</body>
</html>