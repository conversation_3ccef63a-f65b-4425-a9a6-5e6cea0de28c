package org.newstanding.common.utils.excel;


import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import org.apache.commons.lang.ArrayUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;


public class ImportExcel {

   /* *//**
     * 读取excel表格内容返回List<Map>
     * @param inputStream  excel文件流
     * @param head         表头数组
     * @param headerAlias  表头别名数组
     * @return
     *//*
    public static List<Map<String,Object>> importExcel(InputStream inputStream, String[] head, String[] headerAlias){
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        List<Object> header=reader.readRow(0);
        //替换表头关键字
        if(ArrayUtils.isEmpty(head)||ArrayUtils.isEmpty(headerAlias)||head.length!=headerAlias.length){
            return null;
        }else{
            for(int i=0;i<head.length;i++){
                if(head[i].equals(header.get(i))){
                    reader.addHeaderAlias(head[i],headerAlias[i]);
                }else{
                    return null;
                }

            }
        }
        //读取指点行开始的表数据（以下介绍的三个参数也可以使用动态传入，根据个人业务情况修改）
        //1：表头所在行数  2：数据开始读取位置   Integer.MAX_VALUE:数据读取结束行位置   
        List<Map<String,Object>> read = reader.read(0,1,Integer.MAX_VALUE);
        return read;
    }
    *//**
     * 读取excel表格内容返回List<Bean>
     * @param inputStream  excel文件流
     * @param head         表头数组
     * @param headerAlias  表头别名数组
     * @return
     *//*
    public static <T>List<T> importExcel(InputStream inputStream, String[] head, String[] headerAlias,Class<T> bean){
        ExcelReader reader = ExcelUtil.getReader(inputStream);
        List<Object> header=reader.readRow(1);
        //替换表头关键字
        if(ArrayUtils.isEmpty(head)||ArrayUtils.isEmpty(headerAlias)||head.length!=headerAlias.length){
            return null;
        }else{
            for(int i=0;i<head.length;i++){
                if(head[i].equals(header.get(i))){
                    reader.addHeaderAlias(head[i],headerAlias[i]);
                }else{
                    return null;
                }

            }
        }
        //读取指点行开始的表数据（从0开始）
        List<T> read = reader.read(1,2,bean);
        return read;
    }
*/
/*	InputStream inputStream = null;
	try{
	     inputStream = file.getInputStream();
	}catch (Exception e){
	    return ResponseData.fail(ResponseCodeEnum.ERROR_PARAM_INVALID);
	}
	// 2.应用HUtool ExcelUtil获取ExcelReader指定输入流和sheet
	ExcelReader excelReader = ExcelUtil.getReader(inputStream, "导入材料清单");
	// 可以加上表头验证
	// 3.读取第二行到最后一行数据
	List<List<Object>> read = excelReader.read(2, excelReader.getRowCount());
	for (List<Object> objects : read) {
	// objects.get(0),读取某行第一列数据
	// objects.get(1),读取某行第二列数据
	}*/
    
public static void main(String[] args) throws FileNotFoundException {

    //InputStream inputStream=new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\仓湾文化2020年8月进项发票明细.xls导入.xls"));
	ExcelReader reader = ExcelUtil.getReader(new File("C:\\Users\\<USER>\\Desktop\\仓湾文化2020年8月进项发票明细.xls"),"采购发票");
	
	reader = ExcelUtil.getReader(FileUtil.file("test.xlsx"), "sheet1");
	List<List<Object>> read = reader.read();
	System.out.println(read);


}
}