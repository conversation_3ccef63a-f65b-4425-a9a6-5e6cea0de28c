<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="FixedAssetsDisposeMapper">

	<!-- 删除处置数据 -->
	<update id="deleteDispose" parameterType="pd">
		update ${ database }.t_fixedassetscard
		set 
			dispose_type = 0,
			dispose_date = null,
			dispose_profit = null,
			profit_accsubjects_id = null,
			dispose_net = null,
			loss_accsubjects_id = null
		where 
			id = #{ id }
	</update>
	
	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.t_fixedassetscard
		set 
			dispose_type = 1,
			dispose_date = #{dispose_date},
			dispose_profit = #{dispose_profit},
			<if test="profit_accsubjects_id != null and profit_accsubjects_id !=''">profit_accsubjects_id = #{profit_accsubjects_id},</if>
			dispose_net = #{dispose_net},
			<if test="loss_accsubjects_id != null and loss_accsubjects_id !=''">loss_accsubjects_id = #{loss_accsubjects_id},</if>
			disposeby_id = #{disposeby_id},
			modifyby = #{modifyby},
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
			a.code,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.mon_dep_amount mon_dep_amount2,
			a.dispose_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			a.net_value,
			a.dispose_date,
			a.dispose_profit,
			a.profit_accsubjects_id,
			c4.aliasname profit_accsubjects_name,
			a.dispose_net,
			a.loss_accsubjects_id,
			c5.aliasname loss_accsubjects_name,
			ifnull(a.voucher_id,'') voucher_id,
			t.code voucher_number,
			a.disposeby_id,
			s.username disposeby_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_fixedassetscard a
			left join ${ database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ database }.c_accsubjects c4 on a.profit_accsubjects_id = c4.id
			left join ${ database }.c_accsubjects c5 on a.loss_accsubjects_id = c5.id
			left join ${ database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ database }.sys_user s on a.disposeby_id = s.id
		where
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.code,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.mon_dep_amount mon_dep_amount2,
			a.dispose_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			a.net_value,
			a.dispose_date,
			a.dispose_profit,
			a.profit_accsubjects_id,
			c4.aliasname profit_accsubjects_name,
			a.dispose_net,
			a.loss_accsubjects_id,
			c5.aliasname loss_accsubjects_name,
			a.voucher_id,
			t.code voucher_number,
			a.disposeby_id,
			s.username disposeby_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.t_fixedassetscard a
			left join ${ pd.database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ pd.database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ pd.database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ pd.database }.c_accsubjects c4 on a.profit_accsubjects_id = c4.id
			left join ${ pd.database }.c_accsubjects c5 on a.loss_accsubjects_id = c5.id
			left join ${ pd.database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ pd.database }.sys_user s on a.disposeby_id = s.id
		where
			a.closestatus = 0
			<if test="pd.dispose_type != null and pd.dispose_type !=''">
				and a.dispose_type in ( ${pd.dispose_type} )
			</if>
	</select>
	
	<select id="listAll" parameterType="pd" resultType="pd">
		select
			a.code,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.mon_dep_amount mon_dep_amount2,
			a.dispose_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			a.net_value,
			a.dispose_date,
			a.dispose_profit,
			a.profit_accsubjects_id,
			c4.aliasname profit_accsubjects_name,
			a.dispose_net,
			a.loss_accsubjects_id,
			c5.aliasname loss_accsubjects_name,
			a.voucher_id,
			t.code voucher_number,
			a.disposeby_id,
			s.username disposeby_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.t_fixedassetscard a
			left join ${ database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ database }.c_accsubjects c4 on a.profit_accsubjects_id = c4.id
			left join ${ database }.c_accsubjects c5 on a.loss_accsubjects_id = c5.id
			left join ${ database }.t_fillvouchers t on a.voucher_id = t.id
			left join ${ database }.sys_user s on a.disposeby_id = s.id
		where
			a.closestatus = 0
			<if test="dispose_type != null and dispose_type !=''">
				and a.dispose_type in ( ${dispose_type} )
			</if>
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.code,
			a.name,
			a.entrytime,
			a.ori_value,
			a.periods,
			a.dep_method,
			a.salvage,
			a.mon_dep_amount,
			a.dispose_type,
			a.fixed_id,
			c1.aliasname fixed_name,
			a.dep_cum_id,
			c2.aliasname dep_cum_name,
			a.dep_cost_id,
			c3.aliasname dep_cost_name,
			a.dep_ed_periods,
			a.dep_ed,
			a.net_value,
			a.dispose_date,
			a.dispose_profit,
			a.profit_accsubjects_id,
			c4.aliasname profit_accsubjects_name,
			a.dispose_net,
			a.loss_accsubjects_id,
			c5.aliasname loss_accsubjects_name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.t_fixedassetscard a
			left join ${ pd.database }.c_accsubjects c1 on a.fixed_id = c1.id
			left join ${ pd.database }.c_accsubjects c2 on a.dep_cum_id = c2.id
			left join ${ pd.database }.c_accsubjects c3 on a.dep_cost_id = c3.id
			left join ${ pd.database }.c_accsubjects c4 on a.profit_accsubjects_id = c4.id
			left join ${ pd.database }.c_accsubjects c5 on a.loss_accsubjects_id = c5.id
	</select>
	
	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	</select>
	
	<!-- 验证是否存在单据已审核 -->
	<select id="checkVoucherCreate" parameterType="pd" resultType="pd">
		select count(*) num from ${ database }.t_fixedassetscard a where a.id in ( ${ids} ) and ifnull(a.voucher_id,'') != '' 
	</select>
	
	<!-- 生成凭证  主表 -->
	<select id="voucherCreate1" parameterType="pd" resultType="pd">
		select 
				left(a.dispose_date,7) accountperiod,
				a.dispose_date voucherdate,
				1 attachcount,
				#{createby} createby,
				'fixedassetscard' source,
				a.id source_ids
		from
				${ database }.t_fixedassetscard a
		where 
				a.id in ( ${ids} )
	</select>
	
	<!-- 生成凭证  明细表  累计折旧  -->
	<select id="voucherCreateMx1" parameterType="pd" resultType="pd">
		select 
				concat('固定资产处置-（', a.name, '）') abstracta, 
				a.dep_cum_id subjectid, 
				c.name subject_name,
				c.auxbus,
				a.dep_ed debitmoney, 
				'fixedassetscard' source,
				a.id source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_fixedassetscard a,
				${ database }.c_accsubjects c
		where
				a.dep_cum_id = c.id
				and a.id in ( ${ids} ) and a.dep_ed !=0
	</select>
	
	<!-- 生成凭证  明细表  处置收益  -->
	<select id="voucherCreateMx2" parameterType="pd" resultType="pd">
		select 
				concat('固定资产处置-（', a.name, '）') abstracta, 
				a.profit_accsubjects_id subjectid, 
				c.name subject_name,
				c.auxbus,
				a.dispose_profit debitmoney, 
				'fixedassetscard' source,
				a.id source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_fixedassetscard a,
				${ database }.c_accsubjects c
		where
				a.profit_accsubjects_id = c.id
				and a.id in ( ${ids} ) and a.dispose_profit !=0
	</select>
	
	<!-- 生成凭证  明细表  固定资产  -->
	<select id="voucherCreateMx3" parameterType="pd" resultType="pd">
		select 
				concat('固定资产处置-（', a.name, '）') abstracta, 
				a.fixed_id subjectid, 
				c.name subject_name,
				c.auxbus,
				a.ori_value creditmoney, 
				'fixedassetscard' source,
				a.id source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_fixedassetscard a,
				${ database }.c_accsubjects c
		where
				a.fixed_id = c.id
				and a.id in ( ${ids} ) and a.ori_value !=0
	</select>
	
	<!-- 生成凭证  明细表  处置净损益  -->
	<select id="voucherCreateMx4" parameterType="pd" resultType="pd">
		select 
				concat('固定资产处置-（', a.name, '）') abstracta, 
				a.loss_accsubjects_id subjectid,
				c.name subject_name,
				c.auxbus,
				( ifnull(a.dispose_profit,0) - ( ifnull(a.ori_value,0) - ifnull(a.dep_ed,0)) ) creditmoney, 
				'fixedassetscard' source,
				a.id source_ids,
				floor((rand() * 100000)) rowid
		from
				${ database }.t_fixedassetscard a,
				${ database }.c_accsubjects c
		where
				a.loss_accsubjects_id = c.id
				and a.id in ( ${ids} )
	</select>
	
	<!-- 生成凭证后回写id -->
	<update id="voucherCreateAfter" parameterType="pd">
		update 
				${ database }.t_fixedassetscard a,
				${ database }.t_fillvouchers t
		set 
				a.voucher_id = t.id
		where
				locate( concat(',', a.id, ','), concat(',', t.source_ids, ',')) != 0
				and t.source = 'fixedassetscard'
				and a.id in ( ${ids} ) and t.estatus = 2
	</update>
	
	<!-- 将生成的凭证借方金额为负数的转为正数记入贷方金额 -->
	<update id="voucherMoneyConversion" parameterType="pd">
		update 
				${ database }.t_fillvouchersmx a
		set 
				a.debitmoney = -a.creditmoney,
				a.creditmoney = null
		where 
				a.creditmoney &lt; 0 and a.estatus = 2
	</update>
	
	<!-- 查询单据凭证ID -->
	<select id="findVoucherId" parameterType="pd" resultType="pd">
		select 
				group_concat(a.voucher_id) ids
		from
				${ database }.t_fixedassetscard a
		where
				a.id in ( ${ids} )
	</select>
	
	<!-- 撤销凭证后清除单据凭证ID -->
	<update id="voucherRevokeAfter" parameterType="pd">
		update 
				${ database }.t_fixedassetscard a
		set 
				a.voucher_id = null
		where
				a.id in ( ${ids} )
	</update>
	
</mapper>