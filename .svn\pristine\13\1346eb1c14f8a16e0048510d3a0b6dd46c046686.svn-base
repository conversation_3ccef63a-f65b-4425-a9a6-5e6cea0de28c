package org.newstanding.service.invoicing;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.ws.rs.PUT;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.entity.User;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.newstanding.service.financialhandle.FillvouchersService;
import org.newstanding.service.systemset.SupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("RecostService")
public class RecostService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;
	
	@Resource(name = "fillvouchersService")
	private FillvouchersService fillvouchersService;
	@Resource(name = "supplierService")
	private SupplierService supplierService;

	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.save("RecostMapper.save", pd);
			if (count > 0) {
				resPd.put("id", pd.get("id").toString());
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.delete("RecostMapper.delete", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			int count = dao.update("RecostMapper.edit", pd);
			
			resPd.put("id", pd.get("id").toString());
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 明细供应商增加
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData addRowSupplier(PageData pd) {
		PageData resPd = new PageData();
		try {
			//验证供应商档案是否存在
			PageData check = new PageData();
			check.put("database", pd.get("database").toString());
			check.put("name", pd.get("name").toString());
			
			if(!supplierService.checkName(check)){		//若不存在，则增加
				check.put("createby", getCurrentUserByCatch(pd).getUsername());
				supplierService.save(check);
			}
			
			//匹配供应商档案
			dao.update("RecostMapper.rowSupplier", pd);

			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 明细供应商替换
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData changeRowSupplier(PageData pd) {
		PageData resPd = new PageData();
		try {
			//匹配供应商档案
			dao.update("RecostMapper.rowSupplier", pd);
			
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list = new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("RecostMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询 参考数据 列表
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData referList(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list = new ArrayList<PageData>();
		try {
			PageData p = new PageData();	//存放每个月的参考数据容器
			p.put("database", pd.get("database").toString());
			//列出所有月份
			String accountperiod = pd.get("accountperiod").toString();
			String month = accountperiod.substring(0, 7) + "," + getMonths(accountperiod);
			String[] months = month.split(",");
			//遍历列计算每个月的参考数据
			for (String m : months) {
				PageData mp = new PageData();	//存放此月的参考数据容器
				p.put("mon", m);
				//取这些科目发生额 accrual
				PageData accrual = (PageData) dao.findForObject("RecostMapper.getAccrual", p);
				/*主营业务收入（revenue）：取5001科目的贷方发生额-借方发生额+ 未记账凭证的贷方发生额-未记账凭证的借方发生额   （除结转损益凭证产生的以外）*/
				Double revenue = ((BigDecimal)accrual.get("accrual_revenue")).doubleValue();
				p.put("revenue_" + m , revenue);			//存放此月份的主营业务收入
				/*主营业务成本（cost）：取5401科目的借方发生额-贷方发生额+ 未记账凭证的借方发生额-未记账凭证的贷方发生额   （除结转损益凭证产生的以外）*/
				Double cost = ((BigDecimal)accrual.get("accrual_cost")).doubleValue();
				p.put("cost_" + m , cost);					//存放此月份的主营业务成本
				/*毛利：主营业务收入-主营业务成本*/
				Double gross_profit = revenue - cost;
				p.put("gross_profit_" + m , gross_profit);	//存放此月份的毛利
				/*毛利率：毛利/主营业务收入  %*/
				if(gross_profit == 0){
					p.put("rate_" + m , "0%");				//存放此月份的毛利率
				}else{
					String rate = formatDouble((gross_profit / revenue * 100)) + "%";
					p.put("rate_" + m , rate);				//存放此月份的毛利率
				}
				/*费用（expenses）：  5403 5601 5602 5603 科目的借方发生额 - 贷方发生额 + 未记账凭证的借方发生额 - 未记账凭证的贷方发生额   （除结转损益凭证产生的以外）*/
				Double expenses = ((BigDecimal)accrual.get("accrual_expenses")).doubleValue();;
				p.put("expenses_" + m , expenses);			//存放此月份的费用
				/*其他收支净额（other_1,2）：5111 5051 5301 （科目的贷方发生额-借方发生额 + 未记账凭证的贷方发生额 - 未记账凭证的借方发生额 ）
					- 5402 5711 （科目的借方发生额-贷方发生额 + 未记账凭证的借方发生额 - 未记账凭证的贷方发生额）  （除结转损益凭证产生的以外）*/
				Double other = ((BigDecimal)accrual.get("accrual_other")).doubleValue();
				p.put("other_" + m , other);				//存放此月份的其他收支净额
				/*净利润：计算值 = 毛利-费用+其他收支净额*/
				Double profit = gross_profit - expenses + other;
				p.put("profit_" + m , profit);				//存放此月份的净利润
			}
			Map<String, String> map = new LinkedHashMap<String, String>();
			map.put("revenue", "主营业务收入");
			map.put("cost", "主营业务成本");
			map.put("gross_profit", "毛利");
			map.put("rate", "毛利率");
			map.put("expenses", "费用");
			map.put("other", "其他收支净额");
			map.put("profit", "净利润");
			//遍历每个项目
			for (Map.Entry<String, String> entry : map.entrySet()) {
				PageData item_p = new PageData();		//存放此项的参考数据容器
				item_p.put("item", entry.getValue());	//存放项目
				Double average = 0d;	//用于存放每项的平均值
				//遍历月份，取出每个月份对应项目的值
				for (int i = 0 ; i < months.length ; i++ ) {
					//除毛利率以外的计算
					if(entry.getKey() != "rate"){
						item_p.put("col_" + i , formatDouble( (Double)p.get(entry.getKey() + "_" + months[i])) );		//存放每个月对应项目的值
						average += (Double)p.get(entry.getKey() + "_" + months[i]);
					}else{
						item_p.put("col_" + i , p.get(entry.getKey() + "_" + months[i]) );		//存放每个月对应毛利率的值
						if((Double)p.get("average_gross_profit") == 0){
							average = 0d;
						}else{
							average = ( (Double)p.get("average_gross_profit") / (Double)p.get("average_revenue") * 100);
						}
					}
				}
				if(entry.getKey() != "rate"){
					p.put("average_" + entry.getKey(), average / 6);	//存放计算后的平均值
					item_p.put("average", formatDouble(average / 6));
				}else{
					item_p.put("average", formatDouble(average) + "%");		//存放毛利率平均值
				}
				list.add(item_p);
			}
			resPd.put("state", "success");
			resPd.put("list", list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("RecostMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 获取新增数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData getAddObj(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			//accountperiod		会计期间：默认=当前会计期间	
			//begin_inventory	期初库存：当前会计期间“库存商品”(1405)会计科目的期初余额
			//now_enter			本期入库：当前会计期间“库存商品”(1405)会计科目的借方发生额
			resPd = (PageData) dao.findForObject("RecostMapper.getAddObj", pd);
			
			resPd.put("fiscal_period", DateUtil.getLastDay(pd.get("accountperiod").toString() + "-01") );			//记账日期
			User user = getCurrentUserByCatch(pd);
			resPd.put("createby_id", user.getId());
			resPd.put("createby_name", user.getUsername());
			resPd.put("cols", getMonths(pd.getString("accountperiod")));
			
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 获取编辑数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData goEditObj(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd = findById(pd);
			resPd.put("cols", getMonths(pd.getString("accountperiod")));
			
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 传递一个期间，返回此期间前的五个月份，“,”分割
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public String getMonths(String fiscal_period) throws Exception {
		String date_str = "";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
		Date date = sdf.parse(fiscal_period);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, -1);
		date_str += sdf.format(calendar.getTime());
		calendar.add(Calendar.MONTH, -1);
		date_str += "," + sdf.format(calendar.getTime());
		calendar.add(Calendar.MONTH, -1);
		date_str += "," + sdf.format(calendar.getTime());
		calendar.add(Calendar.MONTH, -1);
		date_str += "," + sdf.format(calendar.getTime());
		calendar.add(Calendar.MONTH, -1);
		date_str += "," + sdf.format(calendar.getTime());
		return date_str;
	}
	
	/**
	 * 查询是否被其他单据引用
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public List<PageData> checkIsUsed(PageData pd) {
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("RecostMapper.checkIsUsed", pd);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return list;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("RecostMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	/**
	 * 生成凭证
	 * @param pd
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData voucherCreate(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			//验证所选数据是否可审核
			PageData check_before = checkVoucherCreateBefore(pd);
			if( !"success".equals(check_before.get("state").toString()) ){
				return check_before;
			}
			
			PageData vcPd = new PageData();		//存放构造出的凭证数据的容器
			//取凭证模板
			List<PageData> vmlist = getVoucherteMplate(pd.getString("code"),pd);
			//构造凭证数据
			List<PageData> main_list = new ArrayList<PageData>();
			List<PageData> mx_list = new ArrayList<PageData>();
			
			pd.put("createby", getCurrentUserByCatch(pd).getUsername());
			
			main_list = (List<PageData>) dao.findForList("RecostMapper.voucherCreate1", pd);
			for (PageData vm : vmlist) {
				vm.put("database", pd.getString("database"));
				vm.put("ids", pd.getString("ids"));
				switch (vm.getString("businessmatters")) {
				case "主营业务成本": 	//逐笔生成  模板  主营业务成本  数据
					mx_list.addAll( (List<PageData>) dao.findForList("RecostMapper.voucherCreateMx1_1", vm) );
					break;
				case "库存商品":		//逐笔生成  模板  库存商品  数据
					mx_list.addAll( (List<PageData>) dao.findForList("RecostMapper.voucherCreateMx1_2", vm) );
					break;
				default:
					break;
				}
			}
			
			vcPd.put("dataList", main_list);
			vcPd.put("datamxList", mx_list);
			vcPd.put("database", pd.getString("database"));
			vcPd.put("accountperiod", pd.getString("accountperiod"));
			
			//验证 生成凭证数据
			PageData check_data = checkVoucherCreateData(vcPd);
			if( !"success".equals(check_data.get("state").toString()) ){
				return check_data;
			}
			
			fillvouchersService.saveMore(vcPd);
			dao.update("RecostMapper.voucherCreateAfter", pd);
			fillvouchersService.saveMoreFinsh(vcPd);
			
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 生成凭证 验证是否可生成
	 * 		1. 验证是否存在单据已审核
	 * 
	 * @param pd
	 * @throws Exception
	 */
	public PageData checkVoucherCreateBefore(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			//1.验证是否存在单据已审核
			PageData check = (PageData) dao.findForObject("RecostMapper.checkVoucherCreateBefore", pd);
			if( !"0".equals(check.get("num").toString()) ){
				resPd.put("state", "err");
				resPd.put("message", "所选单据中存在单据已生成凭证！");
				return resPd;
			}
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 撤账
	 * @param pd
	 * @throws Exception
	 */
	public PageData voucherRevoke(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			pd.put("accountperiod", getAccountperiod(pd));	//获取当前记账日期
			
			//查询ids对应的凭证id
			PageData vs = (PageData) dao.findForObject("RecostMapper.findVoucherId", pd);
			
			pd.put("DATA_IDS", vs.getString("ids"));
			
			//验证所选数据的凭证是否可撤账
			PageData check = checkVoucherRevoke(pd);
			if( !"success".equals(check.get("state").toString()) ){
				return check;
			}
			
			//撤销凭证
			fillvouchersService.revokeMore(pd);
			//撤销凭证后清除单据凭证ID
			dao.update("RecostMapper.voucherRevokeAfter", pd);
			
			resPd.put("state", "success");
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
}

