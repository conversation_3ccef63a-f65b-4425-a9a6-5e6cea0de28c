package org.newstanding.service.report;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.CalculateUtil;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.service.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
@SuppressWarnings("unchecked")
@Service("report_SummaryInvoiceService")
public class Report_SummaryInvoiceService extends BaseServiceImpl{

	@Autowired
	private DaoSupport dao;
	
	
	/**
	 * 报表进销存汇总表 数据
	 */
	public List<PageData> getGridData_SummaryInvoice(PageData pd) throws Exception {
		List<PageData> hbList=new ArrayList<PageData>();
		try {
			//根据开始日期和结束日期  查询哪些月份没有计算过成本
			String[] months = DateUtil.getAllMonths(pd.get("month_begin").toString(), pd.get("month_finish").toString());
			List<String> notCostMonths = new ArrayList<>();
			for(int i = 0;i<months.length;i++) {
				pd.put("month", months[i]);
				List<PageData> list = findNotCostAccountMonth(pd);
				if(list.size() == 0) {
					notCostMonths.add(months[i]);
				}
			}
			
			//查询有计算过的月份
		List<PageData> hasCost = findSummaryInvoice(pd);
		
		//查询未计算过的月份
		if(notCostMonths.size()>0) {
			for(int i = 0;i<notCostMonths.size();i++) {
				pd.put("noCostMonth", notCostMonths.get(i));
				List<PageData> unCost = findUnCost(pd);
				hasCost.addAll(unCost);
			}
		}
		for(PageData empPd :hasCost){
			boolean flag = false;
			for (PageData hbPd : hbList) {  
				 if(empPd.get("goodsid").equals(hbPd.get("goodsid"))) {
					 hbPd.put("bcrk", CalculateUtil.add(empPd.get("bcrk").toString(),hbPd.get("bcrk").toString()));
					 hbPd.put("rkje", CalculateUtil.add(empPd.get("rkje").toString(),hbPd.get("rkje").toString()));
					 hbPd.put("bcck", CalculateUtil.add(empPd.get("bcck").toString(),hbPd.get("bcck").toString()));
					 hbPd.put("ckje", CalculateUtil.add(empPd.get("ckje").toString(),hbPd.get("ckje").toString()));
					 flag= true;
				 }
			} 
			
			if(!flag) {
				hbList.add(empPd);
			}
		}
		
		//查询期初
		List<PageData> qcyeList = findCostAccountByMonthBegin(pd);
		//将期初放入hbList中，并计算期末
		for(PageData hbPd:hbList) {
			for(PageData qcPd:qcyeList) {
				if(hbPd.get("goodsid").equals(qcPd.get("goodsid"))) {
					hbPd.put("qcsl", qcPd.get("qcsl"));
					hbPd.put("qcje", qcPd.get("qcje"));
					
					 hbPd.put("qmsl", CalculateUtil.subtract(CalculateUtil.add(qcPd.get("qcsl").toString(),hbPd.get("bcrk").toString())
							 , hbPd.get("bcck").toString()));
					 hbPd.put("qmje", CalculateUtil.subtract(CalculateUtil.add(qcPd.get("qcje").toString(),hbPd.get("rkje").toString())
							 , hbPd.get("ckje").toString()));
				}
			}
		}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return hbList;
	}
	
	private List<PageData> findCostAccountByMonthBegin(PageData pd) throws Exception {
		return (List<PageData>)dao.findForList("Report_SummaryInvoiceMapper.findCostAccountByMonthBegin", pd);
	}
	private List<PageData> findNotCostAccountMonth(PageData pd) throws Exception {
		return (List<PageData>)dao.findForList("Report_SummaryInvoiceMapper.findNotCostAccountMonth", pd);
	}
	/**
	 *商品汇总表 根据 选择的  开始期间  和结束期间  查询所有商品
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	private List<PageData> findGoodsByDate(PageData pd) throws Exception{
		return (List<PageData>)dao.findForList("Report_SummaryInvoiceMapper.findGoodsByDate", pd);	
	}
	private List<PageData> findSummaryInvoice(PageData pd) throws Exception{
		return (List<PageData>)dao.findForList("Report_SummaryInvoiceMapper.findSummaryInvoice", pd);	
	}
	private List<PageData> findUnCost(PageData pd) throws Exception{
		return (List<PageData>)dao.findForList("Report_SummaryInvoiceMapper.findUnCost", pd);	
	}
}
