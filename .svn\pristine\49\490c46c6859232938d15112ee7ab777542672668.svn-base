<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://java.sun.com/xml/ns/javaee" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd" version="2.5">
   <filter> 
    <filter-name>shiroFilter</filter-name> 
    <filter-class>
        org.springframework.web.filter.DelegatingFilterProxy
    </filter-class> 
</filter> 
<filter-mapping> 
    <filter-name>shiroFilter</filter-name> 
    <url-pattern>/*</url-pattern> 
</filter-mapping>
 
  <context-param>
    <param-name>contextConfigLocation</param-name>
    <param-value>classpath:ApplicationContext.xml</param-value>
  </context-param>
  <context-param>
    <param-name>log4jConfigLocation</param-name>
    <param-value>classpath:log4j.properties</param-value>
  </context-param>
  <filter>
    <filter-name>encodingFilter</filter-name>
    <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>utf-8</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>encodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  
  <filter>
    <filter-name>DruidWebStatFilter</filter-name>
    <filter-class>com.alibaba.druid.support.http.WebStatFilter</filter-class>
    <init-param>
      <param-name>exclusions</param-name>
      <param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*</param-value>
    </init-param>
  </filter>
  <filter-mapping>
    <filter-name>DruidWebStatFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>
  <servlet>
    <servlet-name>DruidStatView</servlet-name>
    <servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
  </servlet>
  <servlet-mapping>
    <servlet-name>DruidStatView</servlet-name>
    <url-pattern>/druid/*</url-pattern>
  </servlet-mapping>
  <filter>
    <filter-name>loginFilter</filter-name>
    <filter-class>org.newstanding.filter.LoginFilter</filter-class>
  </filter>
  
  <listener>
    <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
  </listener>
  <context-param>
      <param-name>webAppRootKey</param-name>
      <param-value>app.fintaxsystem</param-value>
  </context-param> 
  
  <listener>
    <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
  </listener>
  <servlet>
    <servlet-name>springMvc</servlet-name>
    <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
    <init-param>
      <param-name>contextConfigLocation</param-name>
      <param-value>classpath:ApplicationContext-mvc.xml</param-value>
    </init-param>
    <load-on-startup>1</load-on-startup>
  </servlet>
  <servlet-mapping>
    <servlet-name>springMvc</servlet-name>
    <url-pattern>/</url-pattern>
    <url-pattern>/service/*</url-pattern>
  </servlet-mapping>
 
  <!-- webService CXF 配置 START-->
  <servlet>
        <servlet-name>CXFService</servlet-name>
        <servlet-class>org.apache.cxf.transport.servlet.CXFServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>CXFService</servlet-name>
        <url-pattern>/webservice/*</url-pattern>
        <http-method>GET</http-method>
        <http-method>POST</http-method>
    </servlet-mapping>
  <!--webService 配置  END-->
 
  <!-- <filter>
    <filter-name>startFilter</filter-name>
    <filter-class>org.newstanding.filter.startFilter</filter-class>
  </filter> -->
 
   
  <error-page>
    <error-code>404</error-code>
    <location>/404.jsp</location>
  </error-page>
  <session-config>
    <session-timeout>600</session-timeout>
  </session-config>
  <listener>
  	<listener-class>org.newstanding.listener.WebAppContextListener</listener-class>
  	 <load-on-startup>3</load-on-startup>
  </listener>
  <jsp-config>
  	<!-- <taglib>
  		<taglib-uri>/wtmis</taglib-uri>
		<taglib-location>/WEB-INF/tld/wtmis.tld</taglib-location>
  	</taglib> -->
  </jsp-config>
</web-app>