<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="FixedsalaryMapper">
	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		
		insert into ${ database }.t_fixedsalary(
			empdocid,
			salaryitemscode,
			salaryitemsname,
			money
		) values
		<foreach item="item" index="index" collection="addList" separator=",">
		 (
		 	#{item.empdocid},
			#{item.salaryitemscode},
			#{item.salaryitemsname},
			<if test="item.money !=null and item.money !='' ">
			#{item.money}
			</if>
			<if test="item.money ==null or item.money =='' ">
			null
			</if>
		)
		</foreach>
	</insert>
	
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.t_fixedsalary where id = #{ id } and estatus = 1 and closestatus = 0 and ifnull(isdefault,0)=0
	</delete>

	<delete id="deleteByEmpdocid" parameterType="pd">
		delete from ${ database }.t_fixedsalary where empdocid = #{ empdocid } 
	</delete>
	<!-- 修改 -->
	<update id="edit" parameterType="pd">

	</update>
	
	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select
			a.code,
			a.name,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.t_fixedsalary a
		where 
			a.id = #{ id }
	</select>
	
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		
	</select>
	<!-- 查询所有的职员 -->
	<select id="empdoclistPage" parameterType="page" resultType="pd">
		select
			a.code,
			a.name as empdoc_name,
			a.name,
			a.sex as sexid,
			case a.sex when 0 then '男' when 1 then '女' else '' end as sex,
			a.deptid,
			t.name as dept_name,
			a.insurancetype as insurancetypeid,
			case a.insurancetype when 0 then '' when 1 then '正常' when 2 then '外包' else '' end as insurancetype,
			a.salarysubid,
			b.aliasname as salarysub_name,
			a.insurancesubid,
			c.aliasname as insurancesub_name,
			
			a.fund_insurancesubid,
			a.oldage_insurancesubid,
			a.medical_insurancesubid,
			d.aliasname as fund_insurancesub_name,
			e.aliasname as oldage_insurancesub_name,
			f.aliasname as medical_insurancesub_name,
			a.specialdeducate,
			a.id,
			a.id as empdocid
		from 
			${ pd.database }.c_empdoc a
			left join ${ pd.database }.c_deptdoc t on a.deptid = t.id
			left join ${ pd.database }.c_accsubjects b on a.salarysubid=b.id
			left join ${ pd.database }.c_accsubjects c on a.insurancesubid=c.id
			
			left join ${ pd.database }.c_accsubjects d on a.fund_insurancesubid=d.id
			left join ${ pd.database }.c_accsubjects e on a.oldage_insurancesubid=e.id
			left join ${ pd.database }.c_accsubjects f on a.medical_insurancesubid=f.id
		where 
			a.closestatus=0
			<if test="pd.q != null and pd.q !=''">
				and a.name like '%${ pd.q }%'
			</if>
	</select>
	<!-- 查询所有的薪资项 -->
	<select id="findItems" parameterType="pd" resultType="pd">
		select
			a.code,
			a.name,
			case a.datasource when 0 then '' when 1 then '固定薪资' when 2 then '手工录入' 
			when 3 then '保险费扣除' when 4 then '个税计算' when 5 then '计算公式' else '' end as datasource,
			a.calformula,
			a.subjectid,
			case a.sub_direction when 0 then '' when 1 then '借' when 2 then '贷' else '' end as sub_direction,
			a.isdefault,
			a.id
		from 
			${ database }.t_salaryitems a
		where
			a.datasource=1 and 
			a.closestatus=0
	</select>
	
	<select id="findEmpdocSalaryItems" parameterType="pd" resultType="pd">
			select 
				a.empdocid,
				b.name as empdoc_name,	
				ifnull(a.money,0) as money,
				a.salaryitemscode
			from 
				${ database }.t_fixedsalary as a
				left join ${ database }.c_empdoc as b on a.empdocid=b.id
			where 
				a.empdocid = #{empdocid}  and b.closestatus=0
	</select>

	<!-- 查看是否被其他单据引用   TODO -->
	<select id="checkIsUsed" parameterType="pd" resultType="pd">
	
	</select>
	
</mapper>