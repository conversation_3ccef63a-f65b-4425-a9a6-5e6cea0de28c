<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="UserMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.sys_user(
			 username,
			 password,
			 alias,
			 ip,
			 email,
			 phone,
			 createby,createtime
		) values (
			 #{username},
			 #{password},
			 #{alias},
			 #{ip},
			 #{email},
			 #{phone},
			#{createby},now()
	)
	</insert>
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.sys_user
		where
		id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.sys_user
		set 
			username = #{username},
			password = #{password},
			alias = #{alias},
			ip = #{ip},
			email = #{email},
			phone = #{phone},
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.username,	
			a.password,	
			a.alias,	
			a.ip,	
			a.email,	
			a.phone,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.jurisdiction,
			a.id
		from 
			${ database }.sys_user as a
		<where> 
			<choose>
				<when test="id != null and id !=''">a.id = #{ id }</when>
				<when test="username != null and username !='' and password != null and password != ''">a.username = #{ username } and a.password = #{password}</when>
				<when test="username != null and username !='' and (password == null or password == '')">a.username = #{ username }</when>
			</choose>
		</where>
	</select>
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.username,	
			a.password,	
			a.alias,	
			a.ip,	
			a.email,	
			a.phone,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.sys_user a
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.username,	
			a.password,	
			a.alias,	
			a.ip,	
			a.email,	
			a.phone,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ pd.database }.sys_user a
	</select>
	
	<!-- 授权 -->
	<update id="updateJurisdictionByuserid" parameterType="pd">
		update ${ database }.sys_user
		set 
			jurisdiction = #{jurisdiction}
		where 
			id = #{ id }
	</update>
	
	<!-- 重置密码按钮 -->
	<update id="initPwd" parameterType="pd">
		update  ${ database }.sys_user
			set 
				password=#{password}
			where 
				username = #{username}
	</update>

</mapper>