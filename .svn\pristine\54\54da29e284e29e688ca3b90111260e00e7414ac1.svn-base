<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/summaryAccount.css">
</head>
<body style="background: #ebedf3;">

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
	
		<!-- 顶部功能栏 -->
		<div id="northToolDiv" data-options="region:'north'" style="background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4); background: -moz-linear-gradient(top,#fff,#f4f4f4);background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<a class="easyui-linkbutton gl_btns" style="float: left;" href="javascript:void(0)" onclick="$('#filter_pageId').window('open')"></a>
			<div style="float: left;width: 400px;margin: 0 0 0 100px;">
				<a class="easyui-linkbutton dyy_btns" href="javascript:void(0)" onclick="cross('min')"></a>
				<a class="easyui-linkbutton prevy_btns" href="javascript:void(0)" onclick="cross('b')"></a>
				<a class="easyui-linkbutton nexty_btns" href="javascript:void(0)" onclick="cross('n')"></a>
				<a class="easyui-linkbutton my_btns" href="javascript:void(0)" onclick="cross('max')"></a>
			</div>
			<p class="refreshbtns" id="refreshbtn" onclick="refresh()" style="display:none"></p>
			<div style="float: right;width: 435px;margin: 0 0 0 100px;">
				<a class="easyui-linkbutton dc_btns" href="javascript:void(0)" onclick="ExportExcel()"></a>
				<a class="easyui-linkbutton dy_btns" href="javascript:void(0)" onclick="printAndPreview('打印')"></a>
				<a class="easyui-linkbutton yl_btns" href="javascript:void(0)" onclick="printAndPreview('预览')"></a>
				<a class="easyui-linkbutton dypz_btns" href="javascript:void(0)" onclick="printallocation({width:320,height:490,menucode:'CashierDaily'})"></a>
				<a class="easyui-linkbutton gb_btns" href="javascript:void(0)" onclick="closeIndexTabs({title:'现金日记账'})"></a>
			</div>
			
		</div>
		
		<!-- 主面板 -->
		<div style="margin: 54px 0 0 0;border:1px #6f9ec2 solid;border-bottom:0" id="parcel_southToolDiv">
		    <div class="gridlist_top">
		        <p class="gridlist_top_le">
		        	<span>单位：</span>
		        	<input type="text" name="accountname"  id="accountname" class="" readonly="readonly" />
		        </p>
		        <p class="gridlist_top_cent">现金日记账</p>
		        <p class="gridlist_top_cents">
		        	<span>会计科目：</span>
		        	<span class="towsp" id="accsubject_now"></span>
		        	<span class="towsp" id="accsubject_now_name"></span>
		        	<input type="hidden" id="accsubject_now_id" name="accsubject_now_id" />
		        	<input type="hidden" id="accsubject_now_sub" name="accsubject_now_sub" />
		        </p>
		        <p class="gridlist_top_ri" style="margin:-21px 0 0 0">
		        	<span>会计期间：</span>
		        	<span class="towsp" id="period"></span>
		        </p>
		    </div>
		    
		    <!-- 数据面板 -->
			<div id="gridlist" data-options="region:'center'" style="width: 100%"></div>
		</div>
		
		<!--报表过滤弹窗 -->
		<div id="filter_pageId" class="easyui-window" title="过滤" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
	    	<div class="bgdivtitsch" style="height: 132px;    border: 0;">
		    	<ul class="report_ul" style="width:502px">
		    		<li style="margin: 15px 0 0 0;">
		    			<p >会计期间：</p>
	    				<input type='text' id="year" id="year" readonly="readonly" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" value="${year}" onclick="WdatePicker({skin:'whyGreen',startDate:'%y',dateFmt:'yyyy'})" />
						<input type="text" id="month_begin" class="invo_title_year" name="month_begin" style="float: left;margin: 0 3px 0 0;" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM'})" /> 
						<span style="float: left;margin: 6px 3px 0 0;">月&nbsp;&nbsp; 至</span>
						<input type="text" id="month_finish" name="month_finish" class="invo_title_year" style="float: left;margin: 0 3px 0 0;" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%M',dateFmt:'MM'})" />
						 <span style="float: left;margin: 6px 0 0 0;">月</span>
		    		</li>
		    		<li>	
		    			<p style="margin: 0 2px 0 14px;">会计科目：</p>
		    			<input type="hidden" id="accsubject_begin_id" name="accsubject_begin_id"/>
		    			<input type="hidden" id="accsubject_begin_name" name="accsubject_begin_name"/>
		    			<input type="hidden" id="accsubject_begin_sub" name="accsubject_begin_sub" />
	    				<input class="easyui-textbox" id="accsubject_begin" style="width:173px; padding:0 24px 0 2px;height:27px" name="accsubject_begin" />
	    				<div class="kjkm_btn" style="right: 52%;top: 29%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;accsubject_begin:code,accsubject_begin_id:id,accsubject_begin_name:name,accsubject_begin_sub:sub_direction')"></div>
	    				<span>至</span>
	    				<input type="hidden" id="accsubject_finish_id" name="accsubject_finish_id"/>
		    			<input type="hidden" id="accsubject_finish_name" name="accsubject_finish_name"/>
		    			<input type="hidden" id="accsubject_finish_sub" name="accsubject_finish_sub" />
	    				<input class="easyui-textbox" id="accsubject_finish" style="width:173px; padding:0 24px 0 2px;height:27px" name="accsubject_finish" />
	    				<div class="kjkm_btn" style="right: 14%;top: 29%;" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;accsubject_finish:code,accsubject_finish_id:id,accsubject_finish_name:name,accsubject_finish_sub:sub_direction')"></div>
		    		</li>
		    		<li>
		    			<p>是否包含未过账凭证：</p>
	    				<input type="checkbox" id="isinclude" name="isinclude" class="checkboxbgimg" style="margin: 6px 0 0 0;" />
		    		</li>
		    	</ul>
		    </div>	
		    <div style="text-align:center;height: 37px;background:#ecf5fa; padding: 4px 0 0 0;border-radius: 0 0 5px 5px;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" style="margin: -1px 13px 0 0;" onclick="$('#filter_pageId').window('close');">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn"style="margin: -1px 9px 0 0;" onclick="filterOk()">确定</a>
		    </div>
		</div>
	<!-- 打印配置 -->
			<div id="printedit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div style="width:316px;border: 1px #cedae4 solid;">
		        <div class="bgdivtit" style="border: 0;height:47px">
			        <span style="margin:0 10px;line-height: 46px;display: inline-block;float: left;">模板名称</span>
					<select id="temp_name"  name="temp_name"  class="easyui-combobox"  data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="width:120px;margin: 0;height: 27px;">
					    
					</select>
					<a class="delelte_fillvouchers" href="javascript:void(0)" onclick="del_temp()">删除</a>
					<a class="add_fillvouchers" href="javascript:void(0)" onclick="add_temp()">新增</a>
				</div>	
		    	<div class="centerpagediv">
		    	     <div class="tablepage">
			    	     <p class="paper_btn paper_btns" onclick="switchoverF(1)">纸张</p>
			    	     <p class="page_btn page_btns" onclick="switchoverF(2)">页面</p>
		    	     </div>
		    	     <div class="paper_div">
		    	     <div id="divMMHeight" style="height: 1mm; width: 1mm; display: none;"></div>
		    	     <input type="hidden" id="menucode" name="menucode"> 
		    	     <input type="hidden" id="tempid" name="tempid">  
		    	      <input type="hidden" id="name" name="name">    
		    	      <input type="hidden" id="content" name="content">
		    	      <input type="hidden" id="html" name="html">  
		    	          <ul>
		    	             <li style="margin: 22px 0 0 0;">
		    	                 <span>纸张类型：</span>
		    	                 <div style="background:#fff;width: 149px;height: 25px;margin: 1px 0 0 62px; border-radius: 3px;">
			    	                 <div id="selectStyle" class="selectStyle">
							             <select class="select" id="pagelx" name="pagelx">
											<option value="A5(横向)">A5(横向)</option>
											<option value="A4">A4</option>
											<option value="自定义">自定义</option>
										</select>
									</div>
									<div class="input-group-addon"><p class="glyphicon actips"></p></div>
		    	                 </div>
		    	             </li>
		    	             <li>
		    	                 <span>页面属性</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>高度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="height" type="text" id="height" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>宽度：</span>
		    	                 <p>（单位：毫米）</p>
		    	                 <input name="width" type="text" id="width" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	             </li>
		    	             <li>
		    	                 <span>页边距</span>
		    	                 
		    	             </li>
		    	             <li>
		    	                 <span>上边距：</span>
		    	                 <input name="page_top" type="text" id="page_top" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>下边距：</span>
		    	                 <input name="page_bottom" type="text" id="page_bottom" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>左边距：</span>
		    	                 <input name="page_left" type="text" id="page_left" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>右边距：</span>
		    	                 <input name="page_right" type="text" id="page_right" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	          </ul>
		    	     </div> 
		    	     <div class="pape_div">
		    	         <ul class="page_t_ul page_t_ulspec">
		    	             <li>
		    	                <span>列项目</span>
		    	                <span>列宽（毫米）</span>
		    	                <span>是否打印</span>
		    	             </li>
		    	             <li>
		    	                <span>日期</span>
		    	                <span>
		    	                    <input  type="text" id="voucherdatewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                <span>
		    	                    <select id="voucherdateSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>凭证字号</span>
		    	                <span>
		    	               		<input   type="text" id="vouchercodewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="vouchercodeSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>摘要</span>
		    	                <span>
		    	                	<input  name="abstractawidth" type="text" id="abstractawidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="abstractaSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	              <li>
		    	                <span>对方科目</span>
		    	                <span>
		    	                	<input  name="subjectnamewidth" type="text" id="subjectnamewidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="subjectnameSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	                 <li>
		    	                <span>往来单位</span>
		    	                <span>
		    	                	<input  name="auxbuswidth" type="text" id="auxbuswidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	               <span>
		    	                    <select id="auxbusSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>借方金额</span>
		    	                <span>
		    	                     <input  name="debitmoneywidth" type="text" id="debitmoneywidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                <span>
		    	                    <select id="debitmoneySel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	              <li>
		    	                <span>贷方金额</span>
		    	                <span>
		    	                    <input  name="creditmoneywidth" type="text" id="creditmoneywidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="creditmoneySel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>余额方向</span>
		    	                <span>
		    	                    <input  name="directionwidth" type="text" id="directionwidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="directionSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	             <li>
		    	                <span>余额</span>
		    	                <span>
		    	                    <input  name="surpluswidth" type="text" id="surpluswidth" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                </span>
		    	                 <span>
		    	                    <select id="surplusSel" style="width: 100%;height: 100%;border: 0;">
		    	                    <option value="是">是</option>
		    	                     <option value="否">否</option>
		    	                    </select>
		    	                </span>
		    	             </li>
		    	         </ul>
		    	         <ul class="page_b_ul" style="margin:0px 0 1px 19px;height: 63px;">
		    	             <li style="margin: 1px 0 0 0;">
		    	                 <span>行高：</span>
		    	                 <input name="rowheight" type="text" id="rowheight" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/[^\d\.]/g,'')}"class=""/>
		    	                 <p>（单位：毫米）</p>
		    	             </li>
		    	             <li>
		    	                 <span>每页循环行数：</span>
		    	                 <input name="hs" type="text" id="hs" onkeyup="this.value=this.value.replace(/[^0-9]/g,'')" onafterpaste="this.value=this.value.replace(/[^0-9]/g,'')" class=""/>
		    	                 <p>（单位：行）</p>
		    	             </li>
		    	         </ul>
		    	     </div>
		    	</div>
		    </div>
		    <div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
				<!-- 底部功能栏 -->
				<a class="easyui-linkbutton close-dialog" href="javascript:void(0)" onclick="closeclearF()">取消</a>
				<a class="easyui-linkbutton sure-dialog" href="javascript:void(0)" onclick="templateSure()">确定</a>
			</div>
		</div>
		
		
		<div id="edit_pageIdm" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="overflow: hidden;display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的模板</p>
		    	<select class="easyui-combobox" id="template_name" name="template_name" data-options="valueField:'id', textField:'text',editable:false,panelHeight:'auto'" style="overflow: hidden;width: 125px;height:30px">
   	       			
				</select>
				<input type="hidden" id="tempurl" />
				<input type="hidden" id="opertype">
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdm')">取消</a>
			    	<a href="javascript:void(0)" id="next_btn" class="easyui-linkbutton cancel-btn sureNew_btn" onclick="nextprint()">下一步</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn sureNew_btn print_btns" style="display:none" onclick="printTemp('edit')">打印</a>
		    	</div>
		    </div>
		</div>
		
		<div id="edit_pageIdprintb" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
		    <div class="printdiv" style="width: 308px;height: 124px;border-bottom: 1px #6f9ec2 solid;background: #fff;">
		    	<p class="ptilte">请选择需要的操作</p>
		    	<input type="hidden" id="selecttempurl" />
		    	<input type="hidden" id="tempname" />
		    	<!-- <a href="javascript:void(0)" class="print_btn" onclick="exportTemp()">导出</a> -->
		    	<a href="javascript:void(0)" class="print_btn" style="margin:-5px 0 0 28px" onclick="printTemp('edit')">打印</a>
		    </div>
		    <div style="text-align:center;padding:2px;background:#ecf5fa;height: 41px;">
		        <div style="float: right;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="closeKmye('edit_pageIdprintb')">取消</a>
		    	</div>
		    </div>
		</div>
	</div>

<script type="text/javascript">
	$(function(){
		$("#month_begin").val('01');
		$('#month_finish').val('12');
		var accinfo = findAccInfo();
		$('#accountname').val(accinfo.compname);
	});

	var width = $(window).width()-76;
	var w1 = width/30;
	var heights = $(window).height();
	$("#parcel_southToolDiv").height(heights-80);
	$("#gridlist").height(heights-100);
	//$("#gridlist").width(1302);
	
	var data = JSON.parse('${data}');
	var code = data.code;
	var columns = data.columns;
	//格式化columns
	columnsFormat(columns);
	
	var obj = new Object();
	obj["position"] = '#gridlist';
	obj["url"] = 'report/getGridData_CashierDaily';
	obj["columns"] = [columns];
	obj["pagination"] = false;
	obj["fitColumns"] = false;
	//obj["queryParams"] = {code:'DetailedAccount',params:'month_begin:2017-01,month_finish:2017-12,subjectid:72',sub_direction:'0'}
	obj["listDbClickFun"] = function (){};
	//生成报表表格
	Grid.list_report(obj);
	
	//定义变量
	var year, month_begin, month_finish, isinclude, subjectid, sub_direction, params;
	
	//初始化变量 并赋值
	function initData(){
		year = $("#year").val();
		month_begin = year + '-' + $("#month_begin").val();
		month_finish = year + '-' + $('#month_finish').val();
		
		var accinfo = findAccInfo();
		var startusedate = accinfo.startusedate;
		var startusedates = startusedate.split('-');
		var periodofaccount = accinfo.periodofaccount;
		var periodofaccounts = periodofaccount.split('-');
		if(month_begin < startusedate){
			$("#year").val(startusedates[0]);
			$("#month_begin").val(startusedates[1]);
			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>最小月份已自动取账套开始使用月份！',showType:'slide',timeout:1000,style:{
				right : '', bottom : '',
				top : document.body.scrollTop + document.documentElement.scrollTop
			}}); */
			promptbox('success','最小月份已自动取账套开始使用月份！');
		}
		if(month_begin > month_finish){
			$("#month_finish").val($("#month_begin").val());
		}
		
		year = $("#year").val();
		month_begin = year + '-' + $("#month_begin").val();
		month_finish = year + '-' + $('#month_finish').val();
		subjectid = $('#accsubject_now_id').val();
		sub_direction = $('#accsubject_now_sub').val();
		
		if($("#isinclude").is(':checked')){
			isinclude = "2";
		}else{
			isinclude = "1";
		}
		$('#period').text(year + '年' + $("#month_begin").val() + '月至' + $('#month_finish').val() + '月');
		
		if(checkEm(subjectid)){
			var pd = {
					accsubject_begin :$("#accsubject_begin").textbox('getValue'), 
					accsubject_finish : $("#accsubject_finish").textbox('getValue'), 
					accsubject_now : $("#accsubject_begin").textbox('getValue'),
					sub_type_cash:1,
					flag : "nb"
				};
			findAccSubject(pd);
		}
		
		params = 'month_begin:'+month_begin+',month_finish:'+month_finish
		   		+',!isinclude:'+isinclude+',subjectid:'+ $('#accsubject_now_id').val();
	}
	function cloformatMoney(value,row,index){
		return formatMoney_1(value);
	}
	//过滤弹窗 确定
	function filterOk(){
		var pd = {
				accsubject_begin :$("#accsubject_begin").textbox('getValue'), 
				accsubject_finish : $("#accsubject_finish").textbox('getValue'), 
				accsubject_now : $("#accsubject_begin").textbox('getValue'),
				sub_type_cash:1
			};
		findAccSubject(pd);
		queryData();
	}
	var searchFlag=0;
	//查询
	function queryData(){
		searchFlag=1;
		initData();
		$('#gridlist').datagrid('load',{code:code, params:params, sub_direction:sub_direction});
		$('#filter_pageId').window('close');
	}

	//翻页
	function cross(flag){
		var accsubject_begin = $('#accsubject_begin').val();
		var accsubject_finish = $('#accsubject_finish').val();
		var accsubject_now = $('#accsubject_now').text();
		switch(flag){
		case 'n':	//下一页
			var pd = {
				accsubject_begin : accsubject_begin, 
				accsubject_finish : accsubject_finish, 
				accsubject_now : accsubject_now,
				sub_type_cash:1,
				flag : "n"
			};
			findAccSubject(pd);
			queryData();
			break;
		case 'b':	//上一页
			var pd = {
				accsubject_begin : accsubject_begin, 
				accsubject_finish : accsubject_finish, 
				accsubject_now : accsubject_now,
				sub_type_cash:1,
				flag : "b"
			};
			findAccSubject(pd);
			queryData();
			break;
		case 'min':	//第一页
			var pd = {
				accsubject_begin : accsubject_begin, 
				accsubject_finish : accsubject_finish, 
				accsubject_now : accsubject_now,
				sub_type_cash:1,
				flag : "firstb"
			};
			findAccSubject(pd);
			queryData();
			break;
		case 'max':	//最末页
			var pd = {
				accsubject_begin : accsubject_begin, 
				accsubject_finish : accsubject_finish, 
				accsubject_now : accsubject_now,
				sub_type_cash:1,
				flag : "lastb"
			};
			findAccSubject(pd);
			queryData();
			break;
		}
	}
	
	//查询会计科目
	function findAccSubject(pd){
		$.ajax({
			url: getUrl('report/findAccSubject'),
			type: 'post',
			async: false,
			data: pd,
			dataType: 'json',
			success: function(data){
				$('#accsubject_now').text(data.code);
				$('#accsubject_now_id').val(data.id);
				$('#accsubject_now_name').text(data.name);
				$('#accsubject_now_sub').val(data.sub_direction);
				sub_direction=data.sub_direction;
			}
		});
	}
	
	//导出
	function ExportExcel(){
		var obj=new Object;
		obj['renderid']='#gridlist';
		obj['title'] = '现金日记账';
		obj['controllername'] = 'report';
		obj['run_method'] = 'getGridData_CashierDaily';
		obj['method_type'] = '1';
		obj['cs'] = '';
		obj['pms'] = {code:code, params:params, sub_direction:sub_direction};
		toExcel(obj);
	}
	//打印、预览
	function printAndPreview(opertype){
		if(searchFlag==0){
			return;
		}
		var obj=new Object;
		obj['opertype']=opertype;
		obj['operpage']='report';
		obj['menucode']='CashierDaily';
		obj['controllername'] = 'report';
		obj['run_method'] = 'getGridData_CashierDaily';
		obj['method_type'] = '1';
		obj['cs'] = '';
		obj['pms'] = {code:code, params:params, sub_direction:sub_direction};
		previewClick_report(obj);
	}

	function closeKmye(id){
		$('#'+id).dialog('close');
	}
	//关闭弹出框
	function closeclearF(){
		$('#printedit_pageId').window('close');
	}
	//temp_name 事件
	$("#temp_name").combobox({
		onSelect:function(record){
			$.ajax({
			    url:getUrl('printSet/findByCode.do'),
			    type:'post',
			    async:false,
			    data: {"menucode":"CashierDaily","name" : record.text},
			    dataType:'json',
			    success:function(data){
	  				$("#menucode").val(data.menucode);
	  				$('#tempid').val(data.id);
	  				$("#height").val(data.height);
	  				$("#width").val(data.width);
	  				$("#hs").val(data.hs);
	  				$("#page_bottom").val(data.page_bottom);
	  				$("#page_left").val(data.page_left);
	  				$("#page_right").val(data.page_right);
	  				$("#page_top").val(data.page_top);
	  				$("#pagelx").val(data.pagelx);
	  				$("#rowheight").val(data.rowheight);
	  				$("#title").val(data.title);
	  				
	  				$("#content").val(data.content);
	  				$("#html").val(data.html);
	  				var columnstr=data.columnstr;
	  				var cols=columnstr.split(',');
	  				for(var i=0;i<cols.length;i++){
	  					var str=cols[i].split(':');
	  					$("#"+str[0]).val(str[1]);
	  				}
			    }
			})
		}
	})
	//模板设置确定
	function templateSure(){
		//将毫米转化成px  
		debugger
		var height=$("#height").val();
		var width=$("#width").val();
		var hs=$("#hs").val();
		var page_bottom=$("#page_bottom").val();
		var page_left=$("#page_left").val();
		var page_right=$("#page_right").val();
		var page_top=$("#page_top").val();
		var pagelx=$("#pagelx").val();
		var rowheight=$("#rowheight").val();
		var title='现金日记账';
		
		
		var pheight = $('#divMMHeight').height(), pwidth = $('#divMMHeight').width();
		var rowheight=$("#rowheight").val();
		
		var voucherdatewidth = $("#voucherdatewidth").val() || 0;
		var vouchercodewidth = $("#vouchercodewidth").val() || 0;
		var abstractawidth=$("#abstractawidth").val() || 0;
		var subjectnamewidth=$("#subjectnamewidth").val() || 0;
		var auxbuswidth=$("#auxbuswidth").val() || 0;
		var debitmoneywidth=$("#debitmoneywidth").val() || 0;
		var creditmoneywidth=$("#creditmoneywidth").val() || 0;
		var directionwidth=$("#directionwidth").val() || 0;
		var surpluswidth=$("#surpluswidth").val() || 0;
		
		var voucherdateSel=$("#voucherdateSel").val();
		var vouchercodeSel=$("#vouchercodeSel").val();
		var abstractaSel=$("#abstractaSel").val();
		var subjectnameSel=$("#subjectnameSel").val();
		var auxbusSel=$("#auxbusSel").val();
		var debitmoneySel=$("#debitmoneySel").val();
		var creditmoneySel=$("#creditmoneySel").val();
		var directionSel=$("#directionSel").val();
		var surplusSel=$("#surplusSel").val();
		var menucode="CashierDaily";
		var name=/* $('#name').val() */$("#temp_name").combobox("getValue");
		var tdf=1;
		var widthp = ((parseInt(debitmoneywidth)+parseInt(creditmoneywidth)+parseInt(directionwidth)+parseInt(surpluswidth)+parseInt(voucherdatewidth)+parseInt(vouchercodewidth)+parseInt(abstractawidth)+parseInt(subjectnamewidth)+parseInt(auxbuswidth)) * pwidth)+parseInt(30)
		
		var columnstr="voucherdatewidth:"+voucherdatewidth+",vouchercodewidth:"+vouchercodewidth
		+",abstractawidth:"+abstractawidth+",debitmoneywidth:"+debitmoneywidth+",creditmoneywidth:"+creditmoneywidth
		+",directionwidth:"+directionwidth+",surpluswidth:"+surpluswidth
		+",voucherdateSel:"+voucherdateSel+",subjectnamewidth:"+subjectnamewidth
		+",auxbuswidth:"+auxbuswidth
		+",vouchercodeSel:"+vouchercodeSel+",abstractaSel:"+abstractaSel
		+",debitmoneySel:"+debitmoneySel+",creditmoneySel:"+creditmoneySel
		+",directionSel:"+directionSel+",surplusSel:"+surplusSel
		+",subjectnameSel:"+subjectnameSel+",auxbusSel:"+auxbusSel;
		
		var contentstr='<style type="text/css"></style>'
			+'<p style="text-align: center;">'
			+'    <span style="font-size: 20px;">现金日记账</span>'
			+'</p>'
			+'<p style="width:'+widthp+'px;margin: 0 auto;">'
			+'    <span style="font-size: 12px;"></span><span style="font-size: 12px;width: 100%;display: inline-block;">'
			+'<span style="font-size: 12px;display: inline-block;float:left">'
			+'    单位：<textarea class="laowang" readonly="readonly" id="companyname" '
			+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 190px; height: 15px; border: 1px dashed rgb(0, 0, 0); font-size: 12px; font-family: &quot;Microsoft YaHei&quot;outline: none; margin:  2px 0 0 0;" '
			+'    title="~{main.companyname}">公司名称</textarea>'
			+'</span><span style="font-size: 12px;display: inline-block;float:right">'
			+'    会计期间：<textarea class="laowang" readonly="readonly" id="accountperioda" '
			+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 200px; height: 18px; border: 1px dashed rgb(0, 0, 0); font-size: 12px;'
			+'font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 2px 0 0 0;" title="~{main.accountperioda}">会计期间</textarea>'
			+'</span><span style="font-size: 12px;display: inline-block;float:right">'
			+'    会计科目：<textarea class="laowang" readonly="readonly" id="accsubject_now_name" '
			+'    style="vertical-align: top; overflow: hidden; padding: 0px; width: 153px; height: 16px; border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
			+'    font-family: &quot;Microsoft YaHei&quot;outline: none; margin:  2px 0 0 0;" title="~{main.accsubject_now_name}">会计科目</textarea> '
			+'</span></span>'
			+'</p>'
			+'<hr/>'
			+'<table>'
			+'    <tbody>'
			+'        <tr class="firstRow">';
			
			var firstTr='';
			var secondtr='';
			var hjtr='';
			
			if(voucherdateSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">日期</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="voucherdate" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.voucherdate}">日期</textarea>'
				+'            </td>';
				hjtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+voucherdatewidth * pwidth+'px;height:'+rowheight * pwidth +'px; word-break: break-all;" valign="top">'
				+'               &nbsp; &nbsp; &nbsp;合计'
				+'            </td>';
			}
			if(vouchercodeSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">凭证字号</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="vouchercode" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.vouchercode}">凭证字号</textarea>'
				+'            </td>';
				hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+vouchercodewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
			}
			if(abstractaSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">摘要</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="abstracta" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.abstracta}">摘要</textarea>'
				+'            </td>';
				hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+abstractawidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
			}
			if(subjectnameSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+subjectnamewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">对方科目</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+subjectnamewidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="subjectname" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+subjectnamewidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.subjectname}">对方科目</textarea>'
				+'            </td>';
				hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+subjectnamewidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
			}
			if(auxbusSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+auxbuswidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">往来单位</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+auxbuswidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="auxbus" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+auxbuswidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.auxbus}">往来单位</textarea>'
				+'            </td>';
				hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+auxbuswidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>'
			}
			if(debitmoneySel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">借方金额</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="debitmoney" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.debitmoney}">借方金额</textarea>'
				+'            </td>';
				hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
					+'                <textarea class="laowang" readonly="readonly" id="debitmoney" '
					+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+debitmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
					+'		title="~{mx.debitmoney}">借方金额</textarea>'
					+'            </td>'
			}
			if(creditmoneySel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">贷方金额</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="creditmoney" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px;text-align:right; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.creditmoney}">贷方金额</textarea>'
				+'            </td>';
				hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
					+'                <textarea class="laowang" readonly="readonly" id="creditmoney" '
					+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+creditmoneywidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
					+'		title="~{mx.creditmoney}">贷方金额</textarea>'
					+'            </td>'
			}
			
			if(directionSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">余额方向</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="direction" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; '
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.direction}">余额方向</textarea>'
				+'            </td>';
				hjtr += ' <td style="width:100%;height:100%;font-size: 12px;width:'+directionwidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="top"></td>';
			}
			if(surplusSel=='是'){
				firstTr +='<td style="width:100%;height:100%;font-size: 12px; word-break: break-all;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px" valign="middle" align="center">余额</td>';
				secondtr += '<td style="width:100%;height:100%;font-size: 12px;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
				+'                <textarea class="laowang" readonly="readonly" id="surplus" '
				+'		style="vertical-align: top; overflow: hidden; padding: 0px;border: 1px dashed rgb(0, 0, 0); font-size: 12px; text-align:right;'
				+'		font-family: &quot;Microsoft YaHei&quot;outline: none; margin: 0px;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px" title="~{mx.surplus}">余额</textarea>'
				+'            </td>';
				hjtr += '  <td style="width:100%;height:100%;font-size: 12px;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px"  valign="top">'
					+'                <textarea class="laowang" readonly="readonly" id="surplus" '
					+'		style="vertical-align: top;overflow:hidden;padding: 0;border:1px dashed #000;font-size:12px;text-align:right;font-family: Microsoft YaHei;width:'+surpluswidth * pwidth+'px;height:'+rowheight * pwidth +'px" '
					+'		title="~{mx.surplus}">余额</textarea>'
					+'            </td>'
					firstTr +='</tr><tr>';
				secondtr +='</tr><tr>';
				hjtr +='</tr>'
					+'</tbody>'
					+'</table>'
					+'<hr/>'
					+'<p>'
					+'<span style="font-size: 12px;"></span><br/>'
					+'</p>';
			}else{
				firstTr +='</tr><tr>';
				secondtr +='</tr><tr>';
				hjtr +='</tr>'
					+'</tbody>'
					+'</table>'
					+'<hr/>'
					+'<p>'
					+'<span style="font-size: 12px;"></span><br/>'
					+'</p>';
			};
			var content=contentstr + firstTr +secondtr + hjtr;
		//保存
		if(name == ''){
			var name = prompt('请输入文件名', '');
	    	if(name != null && name != ''){
	    		//保存模板
	    		$.post(getUrl('printSet/save'),{menucode:menucode,name:name,title:title,content:content,
	    									   pagelx:pagelx,width:width,height:height,
	    									   page_top:page_top,page_bottom:page_bottom,
	    									   page_left:page_left,page_right:page_right,rowheight:rowheight,pagegs:7,
	    									   hs:hs,columnstr:columnstr},function(data){
											    if(data.state == 'success'){
											    	$('#printcode').val(data.code);
								    				promptbox('success','操作成功！')
								    			}else{
								    				alert(data.msg);
								    				promptbox('error',data.msg)
								    			}
	    		})
	    	}
		}else{
			$.post(getUrl('printSet/edit'),{menucode:menucode,name:name,title:title,content:content,
				   pagelx:pagelx,width:width,height:height,
				   page_top:page_top,page_bottom:page_bottom,
				   page_left:page_left,page_right:page_right,rowheight:rowheight,pagegs:7,
				   hs:hs,columnstr:columnstr},function(data){
				    if(data.state == 'success'){
				    	$('#printcode').val(data.code);
	    				promptbox('success','操作成功！')
	    			}else{
	    				alert(data.msg);
	    				promptbox('error',data.msg)
	    			}
	})
		}
		$('#printedit_pageId').window('close');
	}
	$("#pagelx").change(function(e){
		var lx= $("#pagelx").val();
		if(lx=="A5(横向)"){
			$("#height").val(148);
			$("#width").val(210);
			$("#height").attr("readOnly",true);
			$("#width").attr("readOnly",true);
		}else{
			$("#height").val(297);
			$("#width").val(210);
			if(lx=="A4"){
				$("#height").attr("readOnly",true);
				$("#width").attr("readOnly",true);
			}else{
				$("#height").attr("readOnly",false);
				$("#width").attr("readOnly",false);
			}
		}
		
	})
</script>

</body>
</html>