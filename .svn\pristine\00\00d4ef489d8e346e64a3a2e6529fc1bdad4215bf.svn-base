package org.newstanding.common.utils;

import org.springframework.context.ApplicationContext;
/**
 * 项目名称：
 * @author:fh
 * 
*/
public class Const {
	public static final String SESSION_SECURITY_CODE = "sessionSecCode";
	public static final String SESSION_USER = "sessionUser";
	public static final String SESSION_ROLE_RIGHTS = "sessionRoleRights";
	public static final String SESSION_menuList = "menuList";			//当前菜单
	public static final String SESSION_allmenuList = "allmenuList";		//全部菜单
	public static final String SESSION_QX = "QX";
	public static final String SESSION_userpds = "userpds";			
	public static final String SESSION_USERROL = "USERROL";				//用户对象
	public static final String SESSION_USERNAME = "USERNAME";	
	public static final String SESSION_NAME = "NAME";//用户名
	public static final String TRUE = "T";
	public static final String FALSE = "F";
	public static final String LOGIN = "/login_toLogin.do";				//登录地址
	public static final String SYSNAME = "admin/config/SYSNAME.txt";	//系统名称路径
	public static final String PAGE	= "admin/config/PAGE.txt";			//分页条数配置路径
	public static final String EMAIL = "admin/config/EMAIL.txt";		//邮箱服务器配置路径
	public static final String SMS1 = "admin/config/SMS1.txt";			//短信账户配置路径1
	public static final String SMS2 = "admin/config/SMS2.txt";			//短信账户配置路径2
	public static final String FWATERM = "admin/config/FWATERM.txt";	//文字水印配置路径
	public static final String IWATERM = "admin/config/IWATERM.txt";	//图片水印配置路径
	public static final String WEIXIN	= "admin/config/WEIXIN.txt";	//微信配置路径
	public static final String FILEPATHIMG = "uploadFiles/uploadImgs/";	//图片上传路径
	public static final String FILEPATHFILE = "uploadFiles/file/";		//文件上传路径
	public static final String FILEPATHTWODIMENSIONCODE = "uploadFiles/twoDimensionCode/"; //二维码存放路径
	public static final String NO_INTERCEPTOR_PATH = ".*/((login)|(logout)|(code)|(app)|(weixin)|(static)|(main)|(head)|(webService2)|(formproperty)).*";	//不对匹配该值的访问路径拦截（正则）
	
	public static final String CurentJzrq = "Closing_date";             //当前结账日期
	public static final String CurentUser_fzRole = "CurentUser_fzRole"; //当前用户辅助角色
	public static final String Ischeck_code = "Ischeck_code"; //是否验证表单单号 重复
	
	public static ApplicationContext WEB_APP_CONTEXT = null; //该值会在web容器启动时由WebAppContextListener初始化
	public static String DICT_TYPE_ALL="dictTypeAll";//系统中所有字典类型key
	public static String DICT_MAP="dictMap";//系统所有字典项<字典类型,字典项列表>
	
	/**
	 * APP Constants
	 */
	//app注册接口_请求协议参数)
	public static final String[] APP_REGISTERED_PARAM_ARRAY = new String[]{"countries","uname","passwd","title","full_name","company_name","countries_code","area_code","telephone","mobile"};
	public static final String[] APP_REGISTERED_VALUE_ARRAY = new String[]{"国籍","邮箱帐号","密码","称谓","名称","公司名称","国家编号","区号","电话","手机号"};
	
	//app根据用户名获取会员信息接口_请求协议中的参数
	public static final String[] APP_GETAPPUSER_PARAM_ARRAY = new String[]{"USERNAME"};
	public static final String[] APP_GETAPPUSER_VALUE_ARRAY = new String[]{"用户名"};
	
	//用户权限
	public static final String SESSION_ROLE_ADDQX = "sessionRoleAddQx";
	public static final String SESSION_ROLE_DELQX = "sessionRoleDelQx";
	public static final String SESSION_ROLE_MODQX = "sessionRoleModQx";
	public static final String SESSION_ROLE_CHECKQX = "sessionRoleCheckQx";
	public static final String SESSION_ROLE_UNCHECKQX = "sessionRoleUncheckQx";
	public static final String SESSION_ROLE_QyQX = "sessionRoleQyQx";
	public static final String SESSION_ROLE_GBQX = "sessionRoleGbQx";
	public static final String SESSION_ROLE_SPQX = "sessionRoleSpQx";
	
	public static final String CODE_FILE = "uploadFiles/code/";
	
}
