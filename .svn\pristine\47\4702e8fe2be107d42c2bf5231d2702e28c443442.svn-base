<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Customize Columns of PropertyGrid - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Customize Columns of PropertyGrid</h2>
	<div class="demo-info">
		<div class="demo-tip icon-tip"></div>
		<div>The columns of PropertyGrid can be changed.</div>
	</div>
	<div style="margin:10px 0;"></div>
	<table class="easyui-propertygrid" style="width:300px" data-options="
				url: 'propertygrid_data1.json',
				method: 'get',
				showGroup: true,
				scrollbarSize: 0,
				columns: mycolumns
			">
	</table>
	<script>
		var mycolumns = [[
    		{field:'name',title:'MyName',width:100,sortable:true},
   		    {field:'value',title:'MyValue',width:100,resizable:false}
        ]];
	</script>
</body>
</html>