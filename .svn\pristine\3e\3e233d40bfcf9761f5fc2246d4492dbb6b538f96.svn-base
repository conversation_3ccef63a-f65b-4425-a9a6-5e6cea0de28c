<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ReportMapper">
	
	<select id="querysql" parameterType="string" resultType="pd" >
  		${value}
  	</select>
  	
	<!-- 根据报表名称得到报表信息 -->
	<select id="findSysReprotByCode" parameterType="pd" resultType="pd">
		select * from ${ database }.sys_report where code = #{code}
	</select>
	
	<!-- 按code顺序返回下一个/上一个code的会计科目信息 -->
	<select id="findAccSubject1" parameterType="pd" resultType="pd">
		select * from ${ database }.c_accsubjects 
		where 
			code 
				<if test='flag == "n"'> &gt; </if> 
				<if test='flag == "b"'> &lt; </if> 
			#{accsubject_now} 
			and code &gt;= #{accsubject_begin}
			and code &lt;= #{accsubject_finish}
		order by code 
			<if test='flag == "n"'> asc </if> 
			<if test='flag == "b"'> desc </if> 
		limit 0,1
	</select>
	
	<select id="findAccSubject" parameterType="pd" resultType="pd">
		select
			a.code,
			a.aliasname as name,
			a.sub_class,
			a.sub_direction,
			a.isdayacc,
				ifnull(a.sub_type_cash,0) as sub_type_cash,
			ifnull(a.sub_type_bank,0) as sub_type_bank,
			a.auxbus,
			 a.cashflowatt,
			 a.parentid,
			 a.isfixed,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		 from ${ database }.c_accsubjects a
		where 
				1=1 
				<if test='flag == "n"'>and a.code &gt; </if> 
				<if test='flag == "b"'> and a.code &lt;</if> 
				<if test='flag == "nb"'> and a.code &gt;=</if> 
				<if test='flag == "lastb"'>and a.code &gt;</if> 
			<choose>
				<when test="accsubject_now !=null and accsubject_now !='' ">#{accsubject_now}</when>
				<when test='flag == "n" '>0</when>
				<when test='flag == "b" '>'9'</when>
			</choose>
			<if test="accsubject_begin !=null and accsubject_begin !='' ">
				and a.code &gt;= #{accsubject_begin}
			</if>
			<if test="accsubject_finish !=null and accsubject_finish !='' ">
				and a.code &lt;= #{accsubject_finish}
			</if>
			<if test="sub_type_cash !=null and sub_type_cash !='' and sub_type_cash==1 ">
				and ifnull(a.sub_type_cash,0) = 1
			</if>
			<if test="sub_type_bank !=null and sub_type_bank !='' and sub_type_bank==1 ">
				and ifnull(a.sub_type_bank,0) = 1
			</if>
			and not exists(select 1 from ${ database }.c_accsubjects s where a.id = s.parentid )
			<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join ${ database }.t_fillvouchers as b on t.fillvouchersid = b.id
									where t.subjectid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) 
									and  str_to_date( b.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( b.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m') )
							or exists(	select 1 from ${ database }.i_kmye i 
										where i.subjectid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)
										and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m')  )
							or (select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
								from ${ database }.i_kmye kmye 
								where kmye.subjectid = a.id 
								and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
							
						)
					</when>
					<otherwise>
						and 
						(
						(select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
						from ${ database }.i_kmye kmye 
						where kmye.subjectid = a.id 
						and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
						
						or exists(	select 1 from ${ database }.i_kmye i
									where i.subjectid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) 
									and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
									and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m') )
									)
					</otherwise>
				</choose>
			</if>
		order by a.code 
			<if test='flag == "n" or flag == "firstb" or flag=="nb"' > asc </if> 
			<if test='flag == "b" or flag=="lastb" or flag == "last"'> desc </if> 
		limit 0,1
	</select>
	
	
	
	<select id="findAccSubject2" parameterType="pd" resultType="pd">
		select a.* from ${ database }.c_accsubjects a
		where 
			 	a.code
				<if test='flag == "n"'> &gt; </if> 
				<if test='flag == "b"'> &lt;</if> 
				<if test='flag == "nb"'> &gt;=</if> 
				<if test='flag == "lastb"'> &gt;</if> 
			<choose>
				<when test="accsubject_now !=null and accsubject_now !='' ">#{accsubject_now}</when>
				<when test='flag == "n" '>0</when>
				<when test='flag == "b" '>'9'</when>
			</choose>
			<if test="accsubject_begin !=null and accsubject_begin !='' ">
				and a.code &gt;= #{accsubject_begin}
			</if>
			<if test="accsubject_finish !=null and accsubject_finish !='' ">
				and a.code &lt;= #{accsubject_finish}
			</if>
			<if test="sub_type_cash !=null and sub_type_cash !='' and sub_type_cash==1 ">
				and ifnull(a.sub_type_cash,0) = 1
			</if>
			<if test="sub_type_bank !=null and sub_type_bank !='' and sub_type_bank==1 ">
				and ifnull(a.sub_type_bank,0) = 1
			</if>
			
			<if test="isParent !=null and  isParent !='' and isParent==1">
			and ifnull(a.parentid,'') =''
			</if>
			
			<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t 
										left join ${ database }.t_fillvouchers as a on t.fillvouchersid = a.id
										left join ${ database }.c_accsubjects as b on t.subjectid = b.id 
									where left(b.code,4) = a.code and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) 
									and  str_to_date( a.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( a.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m'))
							or exists(	select 1 from ${ database }.i_kmye i left join ${ database }.c_accsubjects as b on i.subjectid = b.id 
										where left(b.code,4) = a.code and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)
										and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
										and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m')  )
										
							or (select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
								from ${ database }.i_kmye kmye 
								where kmye.subjectid = a.id 
								and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
						)
					</when>
					<otherwise>
						and (
						(select ifnull(sum(kmye.debitmoney),'')-ifnull(sum(kmye.creditmoney),'') 
						from ${ database }.i_kmye kmye left join ${ database }.c_accsubjects as b on kmye.subjectid = b.id 
						where left(b.code,4) = a.code
						and  str_to_date( kmye.accountperiod, '%Y-%m') &lt; str_to_date( #{month_begin}, '%Y-%m'))!=0
						
						or exists(	select 1 from ${ database }.i_kmye i left join ${ database }.c_accsubjects as b on i.subjectid = b.id 
									where left(b.code,4) = a.code and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) 
									and  str_to_date( i.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
									and  str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m') )
									)
					</otherwise>
				</choose>
			</if>
		order by a.code 
			<if test='flag == "n" or flag == "firstb" or flag=="nb"' > asc </if> 
			<if test='flag == "b" or flag=="lastb" '> desc </if> 
		limit 0,1
	</select>
	<!-- 按code顺序返回下一个/上一个code的会计科目信息 -->
	<select id="findAux" parameterType="pd" resultType="pd">
	<if test="auxtype==1 or auxtype=='1' or auxtype=='客户' ">
		select a.* from ${ database }.c_customer a
		where 
			a.id 
				<if test='flag == "n"'> &gt; #{assist_now_id}</if> 
				<if test='flag == "b"'> &lt;#{assist_now_id} </if> 
			and a.id &gt;= #{assist_begin_id}
			and a.id &lt;= #{assist_finish_id}
			
			<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join  ${ database }.c_accsubjects as acc on t.subjectid = acc.id 
									left join ${ database }.t_fillvouchers fill on t.fillvouchersid = fill.id 
									where t.assistaccountid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) and acc.auxbus = 1 
									and fill.accountperiod &gt;= #{month_begin} and fill.accountperiod &lt;= #{month_finish} and t.subjectid = #{subjectid})
							or exists(	select 1 from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) and (i.assisttype = '客户' or i.assisttype =1)
										and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid} )
										
							or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '客户' or i.assisttype =1) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
						)
					</when>
					<otherwise><!-- 期初  或者发生额不为0 -->
						and (exists(	select 1 from ${ database }.i_gzdxye i
									where i.assistid = a.id and  
									(i.assisttype = '客户' or i.assisttype =1) and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)
									and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid}
									)
									
								or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
										from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (i.assisttype = '客户' or i.assisttype =1) and i.accountperiod &lt; #{month_begin}
										and i.subjectid = #{subjectid})!=0
									)
					</otherwise>
				</choose>
			</if>
		order by a.id 
			<if test='flag == "n"  or flag == "firstb" or flag=="nb"'> asc </if> 
			<if test='flag == "b" or flag=="lastb" '> desc </if> 
		limit 0,1
	</if>
	
	<if test="auxtype==2 or auxtype=='2'  or auxtype=='供应商' ">
		select a.* from ${ database }.c_supplier a
		where 
			a.id 
				<if test='flag == "n"'> &gt;#{assist_now_id}  </if> 
				<if test='flag == "b"'> &lt;#{assist_now_id}  </if> 
			and a.id &gt;= #{assist_begin_id}
			and a.id &lt;= #{assist_finish_id}
		<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join  ${ database }.c_accsubjects as acc on t.subjectid = acc.id 
									left join ${ database }.t_fillvouchers fill on t.fillvouchersid = fill.id 
									where t.assistaccountid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) and acc.auxbus = 2  
									and fill.accountperiod &gt;= #{month_begin} and fill.accountperiod &lt;= #{month_finish} and t.subjectid = #{subjectid})
							or exists(	select 1 from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)  and (i.assisttype = '供应商' or i.assisttype =2)
										and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid})
										
							or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '供应商' or i.assisttype =2) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
						)
					</when>
					<otherwise><!-- 期初  或者发生额不为0 -->
						and (
							exists(	select 1 from ${ database }.i_gzdxye i
									where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) 
									and (i.assisttype = '供应商' or i.assisttype =2) 
									and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid}
									)
									
							or 
							(select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '供应商' or i.assisttype =2) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
								)
					</otherwise>
				</choose>
			</if>
		order by a.id 
			<if test='flag == "n"  or flag == "firstb" or flag=="nb"'> asc </if> 
			<if test='flag == "b" or flag=="lastb" '> desc </if> 
		limit 0,1
	</if>
	</select>
	
	
<select id="findAllAux" parameterType="pd" resultType="pd">
	<if test="auxtype==1 or auxtype=='1' or auxtype=='客户' ">
		select a.* from ${ database }.c_customer a
		where 
			and a.id &gt;= #{assist_begin_id}
			and a.id &lt;= #{assist_finish_id}
			
			<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join  ${ database }.c_accsubjects as acc on t.subjectid = acc.id 
									left join ${ database }.t_fillvouchers fill on t.fillvouchersid = fill.id 
									where t.assistaccountid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) and acc.auxbus = 1 
									and fill.accountperiod &gt;= #{month_begin} and fill.accountperiod &lt;= #{month_finish} and t.subjectid = #{subjectid})
							or exists(	select 1 from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) and (i.assisttype = '客户' or i.assisttype =1)
										and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid} )
										
							or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '客户' or i.assisttype =1) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
						)
					</when>
					<otherwise><!-- 期初  或者发生额不为0 -->
						and (exists(	select 1 from ${ database }.i_gzdxye i
									where i.assistid = a.id and  
									(i.assisttype = '客户' or i.assisttype =1) and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)
									and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid}
									)
									
								or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
										from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (i.assisttype = '客户' or i.assisttype =1) and i.accountperiod &lt; #{month_begin}
										and i.subjectid = #{subjectid})!=0
									)
					</otherwise>
				</choose>
			</if>
		order by a.id 
	</if>
	
	<if test="auxtype==2 or auxtype=='2'  or auxtype=='供应商' ">
		select a.* from ${ database }.c_supplier a
		where 
			a.id 
			and a.id &gt;= #{assist_begin_id}
			and a.id &lt;= #{assist_finish_id}
		<if test="isshowvoidvalue !=null and isshowvoidvalue !='' and isshowvoidvalue==1 ">
				<choose>
					<when test="isinclude !=null and isinclude !='' and isinclude==2">
						and (
							exists(	select 1 from ${ database }.t_fillvouchersmx t left join  ${ database }.c_accsubjects as acc on t.subjectid = acc.id 
									left join ${ database }.t_fillvouchers fill on t.fillvouchersid = fill.id 
									where t.assistaccountid = a.id and (ifnull(t.debitmoney,0) != 0 or ifnull(t.creditmoney,0) != 0) and acc.auxbus = 2  
									and fill.accountperiod &gt;= #{month_begin} and fill.accountperiod &lt;= #{month_finish} and t.subjectid = #{subjectid})
							or exists(	select 1 from ${ database }.i_gzdxye i 
										where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0)  and (i.assisttype = '供应商' or i.assisttype =2)
										and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid})
										
							or (select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '供应商' or i.assisttype =2) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
						)
					</when>
					<otherwise><!-- 期初  或者发生额不为0 -->
						and (
							exists(	select 1 from ${ database }.i_gzdxye i
									where i.assistid = a.id and (ifnull(i.debitmoney,0) != 0 or ifnull(i.creditmoney,0) != 0) 
									and (i.assisttype = '供应商' or i.assisttype =2) 
									and i.accountperiod &gt;= #{month_begin} and i.accountperiod &lt;= #{month_finish} and i.subjectid = #{subjectid}
									)
									
							or 
							(select ifnull(sum(i.debitmoney),'')-ifnull(sum(i.creditmoney),'') 
								from ${ database }.i_gzdxye i 
								where i.assistid = a.id and (i.assisttype = '供应商' or i.assisttype =2) and i.accountperiod &lt; #{month_begin}
								and i.subjectid = #{subjectid})!=0
								)
					</otherwise>
				</choose>
			</if>
		order by a.id 
	</if>
	</select>
	
	
	
	<select id="findGoods" parameterType="pd" resultType="pd">
	select
			a.code,
			a.name,
			a.model,
			a.typeid,
			a.typeid _parentId,
			t.name as type_name,
			a.unit,
			a.taxrate,
			a.estatus,
			a.closestatus,
			a.version,
			0 as state,
			a.id 
		from 
			${ database }.c_goods a
			left join ${ database }.c_goodstype t on a.typeid = t.id
		where 
			 	a.code
				<if test='flag == "n"'> &gt; </if> 
				<if test='flag == "b"'> &lt;</if> 
				<if test='flag == "nb"'> &gt;=</if> 
				<if test='flag == "lastb"'> &gt;</if> 
			<choose>
				<when test="goods_now !=null and goods_now !='' ">#{goods_now}</when>
				<when test='flag == "n" '>0</when>
				<when test='flag == "b" '>'9'</when>
			</choose>
			<if test="goods_begin !=null and goods_begin !='' ">
				and a.code &gt;= #{goods_begin}
			</if>
			<if test="goods_finish !=null and goods_finish !='' ">
				and a.code &lt;= #{goods_finish}
			</if>
		order by a.code 
			<if test='flag == "n" or flag == "firstb" or flag=="nb"' > asc </if> 
			<if test='flag == "b" or flag=="lastb" '> desc </if> 
		limit 0,1
	</select>
	<!-- 根据会计科目code得到会计科目信息 -->
	<select id="findAccSubjectByCode" parameterType="pd" resultType="pd">
		select * from ${ database }.c_accsubjects where code = #{code}
	</select>
	
	<!--  报表 资产负债表 主表数据 -->
	<select id="findBalanceSheet" parameterType="pd" resultType="pd">
		select 
				id,
				assets,
				assets assets0,
				rank1,
				number1,
				datasources1,
				datasources1 datasources1_final,
				datasources1 datasources1_beginyear,
				debt,
				debt debt0,
				rank2,
				number2,
				datasources2,
				datasources2 datasources2_final,
				datasources2 datasources2_beginyear
		from ${ database }.sys_balancesheet
	</select>
	
	<!--  报表 利润表 主表数据 -->
	<select id="findIncomeStatement" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources,
				datasources datasources_,
				datasources datasources_year
		from ${ database }.sys_incomestatement
	</select>
	<select id="findGeneralIncome" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources,
				datasources datasources_
		from ${ database }.sys_generalincome
	</select>
	
	<select id="findGeneralPay" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources,
				datasources datasources_
		from ${ database }.sys_generalpay
	</select>
	
	<select id="findAdvertisePay" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources,
				datasources datasources_
		from ${ database }.sys_advertisepay
	</select>
	
	<select id="findStaffSalary" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources,
				datasources datasources_
		from ${ database }.sys_staffsalary
	</select>
	
		<select id="findPeriodCost" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources1,
				datasources2,
				datasources3,
				datasources4,
				datasources5,
				datasources6,
				datasources1 datasources1_,
				datasources2 datasources2_,
				datasources3 datasources3_,
				datasources4 datasources4_,
				datasources5 datasources5_,
				datasources6 datasources6_
		from ${ database }.sys_periodcost
	</select>
	
	<select id="findAssetsInfo" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources1,
				datasources2,
				datasources3,
				datasources1 datasources1_,
				datasources2 datasources2_,
				datasources3 datasources3_
				
		from ${ database }.sys_assetsinfo
	</select>
	<!--  报表 现金流量表 主表数据 -->
	<select id="findCashFlow" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				item item0,
				rank,
				number,
				datasources,
				datasources datasources_,
				datasources datasources_year
		from ${ database }.sys_cashflow
	</select>
	
	<!--  报表 简易增值税申报 主表数据 -->
	<select id="findSimVaTax" parameterType="pd" resultType="pd">
		select 
				id,
				item,
				rank,
				number
		from ${ database }.sys_simvatax
	</select>
	
	<!-- 期末科目数据 -->
	<select id="findAccsubjectsData_final" parameterType="pd" resultType="pd">
		select 
			concat('&lt;',acc.code,'&gt;') code,
			ifnull(t.dy,0) dy,
			ifnull(t.cy,0) cy,
			ifnull(t.ye,0) ye
		from 
			${ database }.c_accsubjects acc
			left join (
				select 
					a.id,
					ifnull(sum( ifnull(i.debitmoney,0) ),0) dy, 
					ifnull(sum( ifnull(i.creditmoney,0) ),0) cy,
					case a.sub_direction when 0 then (ifnull(sum( ifnull(i.debitmoney,0) ),0) - ifnull(sum( ifnull(i.creditmoney,0) ),0))
							else (ifnull(sum( ifnull(i.creditmoney,0) ),0) - ifnull(sum( ifnull(i.debitmoney,0) ),0)) end ye
				from
					${ database }.c_accsubjects a
					left join ${ database }.i_kmye i on a.id = i.subjectid 
						and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{accountperiod}, '%Y-%m')	
				where 
					ifnull(a.auxbus,0) = 0 
					and not exists(select 1 from ${ database }.c_accsubjects acc where a.id = acc.parentid)
				group by a.id
				
				union all
				
				select 
						s.id,
						sum(case when s.sub_direction = 0 and s.ye &gt; 0 then s.ye 
								 when s.sub_direction = 1 and s.ye &lt; 0 then abs(s.ye)
								 else 0 end) dy,
						sum(case when s.sub_direction = 1 and s.ye &gt; 0 then s.ye 
								 when s.sub_direction = 0 and s.ye &lt; 0 then abs(s.ye)
								 else 0 end) cy,
						sum(s.ye) ye
				from 
						(
						select 
							a.id, a.code, a.sub_direction,
							case t.sub_direction when 0 then (ifnull(sum( ifnull(i.debitmoney,0) ),0) - ifnull(sum( ifnull(i.creditmoney,0) ),0))
									else (ifnull(sum( ifnull(i.creditmoney,0) ),0) - ifnull(sum( ifnull(i.debitmoney,0) ),0)) end ye
						from
							${ database }.c_accsubjects a,
							${ database }.c_accsubjects t
							left join ${ database }.i_kmye i on t.id = i.subjectid 
								and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{accountperiod}, '%Y-%m')	
						where 
							ifnull(a.auxbus,0) = 0 
							and exists(select 1 from ${ database }.c_accsubjects acc where a.id = acc.parentid)
							and <!-- find_in_set(a.code,t.code)>0 -->  locate( concat(',', a.code), concat(',', t.code)) != 0 
						group by a.id, t.id
						) s 
				group by s.id
				
				union all
				
				select 
						c.id,
						sum(t.dy) dy,
						sum(t.cy) cy,
						s.ye
				from 
						${ database }.c_accsubjects c,
						(
							select 
								a.id,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
										then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
										then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
							from
								${ database }.c_accsubjects a
								left join ${ database }.i_gzdxye i on a.id = i.subjectid 
									and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{accountperiod}, '%Y-%m')
							where 
								ifnull(a.auxbus,0) = 1 and a.sub_direction = 0
							group by i.assistid,i.subjectid
					
							union all 
							
							select 
								a.id,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
										then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
										then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
							from
								${ database }.c_accsubjects a
								left join ${ database }.i_gzdxye i on a.id = i.subjectid 
									and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{accountperiod}, '%Y-%m')
							where 
								ifnull(a.auxbus,0) = 2 and a.sub_direction = 0
							group by i.assistid,i.subjectid
					
							union all 
				
							select 
								a.id,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
										then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
										then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
							from
								${ database }.c_accsubjects a
								left join ${ database }.i_gzdxye i on a.id = i.subjectid 
									and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{accountperiod}, '%Y-%m')
							where 
								ifnull(a.auxbus,0) = 1 and a.sub_direction = 1
							group by i.assistid,i.subjectid
							
							union all 
				
							select 
								a.id,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
										then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
								case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
										then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
							from
								${ database }.c_accsubjects a
								left join ${ database }.i_gzdxye i on a.id = i.subjectid 
									and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{accountperiod}, '%Y-%m')
							where 
								ifnull(a.auxbus,0) = 2 and a.sub_direction = 1
							group by i.assistid,i.subjectid
						) t,
						(
							select 
								a.id,
								case a.sub_direction when 0 then (ifnull(sum( ifnull(i.debitmoney,0) ),0) - ifnull(sum( ifnull(i.creditmoney,0) ),0))
										else (ifnull(sum( ifnull(i.creditmoney,0) ),0) - ifnull(sum( ifnull(i.debitmoney,0) ),0)) end ye
							from
								${ database }.c_accsubjects a
								left join ${ database }.i_kmye i on a.id = i.subjectid 
									and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{accountperiod}, '%Y-%m')	
							where 
								ifnull(a.auxbus,0) != 0 
							group by a.id
						) s
				where
						c.id = t.id and c.id = s.id
				group by c.id
			)t on t.id = acc.id
	</select>
	
	<!-- 年初科目数据 -->
	<select id="findAccsubjectsData_beginyear" parameterType="pd" resultType="pd">
		select 
			concat('&lt;',acc.code,'&gt;') code,
			ifnull(t.dy,0) dy,
			ifnull(t.cy,0) cy,
			ifnull(t.ye,0) ye
		from 
			${ database }.c_accsubjects acc
			left join (
					select 
						a.id,
						ifnull(sum( ifnull(i.debitmoney,0) ),0) dy, 
						ifnull(sum( ifnull(i.creditmoney,0) ),0) cy,
						case a.sub_direction when 0 then (ifnull(sum( ifnull(i.debitmoney,0) ),0) - ifnull(sum( ifnull(i.creditmoney,0) ),0))
								else (ifnull(sum( ifnull(i.creditmoney,0) ),0) - ifnull(sum( ifnull(i.debitmoney,0) ),0)) end ye
					from
						${ database }.c_accsubjects a
						left join ${ database }.i_kmye i on a.id = i.subjectid 
							and str_to_date( i.accountperiod, '%Y') &lt; str_to_date( #{accountperiod}, '%Y')	
					where 
						ifnull(a.auxbus,0) = 0 
						and not exists(select 1 from ${ database }.c_accsubjects acc where a.id = acc.parentid)
					group by a.id
					
					union all
					
					select 
							s.id,
							sum(case when s.sub_direction = 0 and s.ye &gt; 0 then s.ye 
									 when s.sub_direction = 1 and s.ye &lt; 0 then abs(s.ye)
									 else 0 end) dy,
							sum(case when s.sub_direction = 1 and s.ye &gt; 0 then s.ye 
									 when s.sub_direction = 0 and s.ye &lt; 0 then abs(s.ye)
									 else 0 end) cy,
							sum(s.ye) ye
					from 
							(
							select 
								a.id, a.code, a.sub_direction,
								case t.sub_direction when 0 then (ifnull(sum( ifnull(i.debitmoney,0) ),0) - ifnull(sum( ifnull(i.creditmoney,0) ),0))
										else (ifnull(sum( ifnull(i.creditmoney,0) ),0) - ifnull(sum( ifnull(i.debitmoney,0) ),0)) end ye
							from
								${ database }.c_accsubjects a,
								${ database }.c_accsubjects t
								left join ${ database }.i_kmye i on t.id = i.subjectid 
									and str_to_date( i.accountperiod, '%Y') &lt; str_to_date( #{accountperiod}, '%Y')	
							where 
								ifnull(a.auxbus,0) = 0 
								and exists(select 1 from ${ database }.c_accsubjects acc where a.id = acc.parentid)
								and  locate( concat(',', a.code), concat(',', t.code)) != 0 
							group by a.id, t.id
							) s 
					group by s.id
					
					union all
					
					select 
							c.id,
							sum(t.dy) dy,
							sum(t.cy) cy,
							s.ye
					from 
							${ database }.c_accsubjects c,
							(
								select 
									a.id,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
												then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
											then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
								from
									${ database }.c_accsubjects a
									left join ${ database }.i_gzdxye i on a.id = i.subjectid 
										and str_to_date( i.accountperiod, '%Y') &lt; str_to_date( #{accountperiod}, '%Y')
								where 
									ifnull(a.auxbus,0) = 1 and a.sub_direction = 0
								group by i.assistid,i.subjectid
								
								union all 
								
								select 
									a.id,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
												then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
											then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
								from
									${ database }.c_accsubjects a
									left join ${ database }.i_gzdxye i on a.id = i.subjectid 
										and str_to_date( i.accountperiod, '%Y') &lt; str_to_date( #{accountperiod}, '%Y')
								where 
									ifnull(a.auxbus,0) = 2 and a.sub_direction = 0
								group by i.assistid,i.subjectid
					
								union all 
					
								select 
									a.id,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
											then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
											then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
								from
									${ database }.c_accsubjects a
									left join ${ database }.i_gzdxye i on a.id = i.subjectid 
										and str_to_date( i.accountperiod, '%Y') &lt; str_to_date( #{accountperiod}, '%Y')
								where 
									ifnull(a.auxbus,0) = 1 and a.sub_direction = 1
								group by i.assistid,i.subjectid
									
								union all 
					
								select 
									a.id,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &lt; 0 
											then abs(sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) )) else 0 end dy,
									case when sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) &gt; 0 
											then sum( ifnull(i.creditmoney,0) - ifnull(i.debitmoney,0) ) else 0 end cy
								from
									${ database }.c_accsubjects a
									left join ${ database }.i_gzdxye i on a.id = i.subjectid 
										and str_to_date( i.accountperiod, '%Y') &lt; str_to_date( #{accountperiod}, '%Y')
								where 
									ifnull(a.auxbus,0) = 2 and a.sub_direction = 1
								group by i.assistid,i.subjectid
							) t,
							(
								select 
									a.id,
									case a.sub_direction when 0 then (ifnull(sum( ifnull(i.debitmoney,0) ),0) - ifnull(sum( ifnull(i.creditmoney,0) ),0))
											else (ifnull(sum( ifnull(i.creditmoney,0) ),0) - ifnull(sum( ifnull(i.debitmoney,0) ),0)) end ye
								from
									${ database }.c_accsubjects a
									left join ${ database }.i_kmye i on a.id = i.subjectid 
										and str_to_date( i.accountperiod, '%Y') &lt; str_to_date( #{accountperiod}, '%Y')	
								where 
									ifnull(a.auxbus,0) != 0 
								group by a.id
							) s
					where
							c.id = t.id and c.id = s.id
					group by c.id
			) t on t.id = acc.id
	</select>
	
	<!-- 根据期间单位取科目发生额 -->
	<select id="findAccsubjectsData" parameterType="pd" resultType="pd">
		select
			concat('&lt;',a.code,'&gt;') code,
			round(ifnull(sum(b.money),0),2) money
		from 
			${ database }.c_accsubjects a
			left join (
				select 
						s.id,
						t.accountperiod,
						case s.sub_direction 
							when 0 then ifnull(sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ),0)
								else ifnull(sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ),0)
						end money
				from 
						${ database }.c_accsubjects s,
						${ database }.c_accsubjects c,
						${ database }.t_fillvouchers t,
						${ database }.t_fillvouchersmx mx
				where 
						ifnull(t.source,'') != 'carryover' and t.isaccount = 1
						and t.id = mx.fillvouchersid and mx.subjectid = c.id 
						and locate( concat(',', s.code), concat(',', c.code)) != 0 
				group by t.accountperiod,s.id
			) b on a.id = b.id and year(concat(b.accountperiod,'-01')) = #{year}
				<if test="unit == 'mon'">
					and month(concat(b.accountperiod,'-01')) = #{month}
				</if>
				<if test="unit == 'quarter'">
					and quarter(concat(b.accountperiod,'-01')) = #{quarter}
				</if>
				<if test="unit == 'year_mon'">
					and month(concat(b.accountperiod,'-01')) &lt;= #{month}
				</if>
				<if test="unit == 'year_quarter'">
					and quarter(concat(b.accountperiod,'-01')) &lt;= #{quarter}
				</if>
		group by a.id
	</select>
	<select id="findAccsubjectsDataByPeriod" parameterType="pd" resultType="pd">
		select
			concat('&lt;',a.code,'&gt;') code,
			round(ifnull(sum(b.money),0),2) money
		from 
			${ database }.c_accsubjects a
			left join (
				select 
						s.id,
						t.accountperiod,
						case s.sub_direction 
							when 0 then ifnull(sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ),0)
								else ifnull(sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ),0)
						end money
				from 
						${ database }.c_accsubjects s,
						${ database }.c_accsubjects c,
						${ database }.t_fillvouchers t,
						${ database }.t_fillvouchersmx mx
				where 
						ifnull(t.source,'') != 'carryover' and t.isaccount = 1
						and t.id = mx.fillvouchersid and mx.subjectid = c.id 
						and locate( concat(',', s.code), concat(',', c.code)) != 0 
						
						and  str_to_date( t.accountperiod, '%Y-%m') &gt;= str_to_date( #{month_begin}, '%Y-%m')
						and  str_to_date( t.accountperiod, '%Y-%m') &lt;= str_to_date( #{month_finish}, '%Y-%m')
				group by t.accountperiod,s.id
			) b on a.id = b.id 
		group by a.id
	</select>
	<!-- 根据期间单位取科目现金流量的发生额 -->
	<select id="findAccsubjectsData_CashFlow" parameterType="pd" resultType="pd">
		select 
				concat('&lt;',ca.cashflowatt,'&gt;') code,
				ifnull(sum(ifnull(tfmx.debitmoney,0)),0) dy,
				ifnull(sum(ifnull(tfmx.creditmoney,0)),0) cy
		from 
				${ database }.c_accsubjects ca,
				${ database }.t_fillvouchers tf,
				${ database }.t_fillvouchersmx tfmx,
				(
					select 
					    distinct t.id
					from 
							${ database }.c_accsubjects s,
							${ database }.c_accsubjects c,
							${ database }.t_fillvouchers t,
							${ database }.t_fillvouchersmx mx
					where 
							(s.sub_type_bank = 1 or s.sub_type_cash = 1)
							and (c.sub_type_bank = 1 or c.sub_type_cash = 1)
							and <!-- find_in_set(s.code,c.code)>0  -->  locate( concat(',', s.code), concat(',', c.code)) != 0
							and ifnull(t.source,'') != 'carryover' and t.isaccount = 1
							and t.id = mx.fillvouchersid and mx.subjectid = c.id 
							and year(concat(t.accountperiod,'-01')) = #{year}
							<if test="unit == 'mon'">
								and month(concat(t.accountperiod,'-01')) = #{month}
							</if>
							<if test="unit == 'quarter'">
								and quarter(concat(t.accountperiod,'-01')) = #{quarter}
							</if>
							<if test="unit == 'year_mon'">
								and month(concat(t.accountperiod,'-01')) &lt;= #{month}
							</if>
							<if test="unit == 'year_quarter'">
								and quarter(concat(t.accountperiod,'-01')) &lt;= #{quarter}
							</if>
				) v
		where 
				ifnull(ca.cashflowatt,'') != '' and ifnull(ca.cashflowatt,'') != '' 
				and tf.id = tfmx.fillvouchersid and tfmx.subjectid = ca.id 
				and tf.id = v.id
		group by ca.cashflowatt
	</select>
	
	<!-- 根据期间单位取科目属性为现金类、银行类科目的期初余额合计 -->
	<select id="findBeginBalance_CashFlow" parameterType="pd" resultType="pd">
		select 
			case c.sub_direction when 0 then ifnull(sum( ifnull(km.debitmoney,0) - ifnull(km.creditmoney,0)),0) 
				else ifnull(sum( ifnull(km.creditmoney,0) - ifnull(km.debitmoney,0)),0) end ye
		from 
				${ database }.c_accsubjects s,
				${ database }.c_accsubjects c,
				${ database }.i_kmye km
		where 
				(s.sub_type_bank = 1 or s.sub_type_cash = 1)
				and (c.sub_type_bank = 1 or c.sub_type_cash = 1)
				and find_in_set(s.code,c.code)>0 <!-- and locate( concat(',', s.code), concat(',', c.code) ) != 0 -->
				and km.subjectid = s.id 
				<if test="unit == 'mon'">
					and str_to_date( concat(year(concat(km.accountperiod,'-01')),'-',month(concat(km.accountperiod,'-01'))), '%Y-%m' ) 
						&lt; str_to_date( concat(year(concat(#{year},'-',#{month},'-01')),'-',month(concat(#{year},'-',#{month},'-01'))), '%Y-%m' )
				</if>
				<if test="unit == 'quarter'">
					and concat(year(concat(km.accountperiod,'-01')),'-',quarter(concat(km.accountperiod,'-01'))) 
						&lt; concat(year(concat(#{year},'-',#{begin_month},'-01')),'-',quarter(concat(#{year},'-',#{begin_month},'-01')))
				</if>
				<if test="unit == 'year'">
					and concat(year(concat(km.accountperiod,'-01'))) &lt; concat(year(concat(#{year},'-',#{month},'-01')))
				</if>
	</select>
	
	<!-- 现金流量表 验证期末余额合计 -->
	<select id="check_CashFlow" parameterType="pd" resultType="pd">
		select 
				case c.sub_direction when 0 then ifnull(sum( km.debitmoney - km.creditmoney),0) 
											else ifnull(sum( km.creditmoney - km.debitmoney),0) end final_balance
		from
				${ database }.c_accsubjects c
				left join ${ database }.i_kmye km on km.subjectid = c.id 
		where
				(c.sub_type_cash = 1 or c.sub_type_bank = 1)
				
				
				and str_to_date( km.accountperiod, '%Y-%m') &lt;= str_to_date( concat(#{year},'-',#{end_month}), '%Y-%m')
	</select>
	
	<!-- 修改  资产负债表——数据来源 -->
	<update id="edit_BalanceSheet" parameterType="pd">
		update ${ database }.sys_balancesheet
		set 
			<if test="datasources1 != null">datasources1 = #{datasources1},</if>
			<if test="datasources2 != null">datasources2 = #{datasources2},</if>
			<if test="assets != null">assets = #{assets},</if>
			<if test="debt != null">debt = #{debt},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
	<!-- 修改  利润表——数据来源 -->
	<update id="edit_IncomeStatement" parameterType="pd">
		update ${ database }.sys_incomestatement
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources != null">datasources = #{datasources},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
		<!-- 修改  利润表——数据来源 -->
	<update id="edit_GeneralIncome" parameterType="pd">
		update ${ database }.sys_generalincome
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources != null">datasources = #{datasources},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
	<update id="edit_GeneralPay" parameterType="pd">
		update ${ database }.sys_generalpay
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources != null">datasources = #{datasources},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
	
	<update id="edit_AdvertisePay" parameterType="pd">
		update ${ database }.sys_advertisepay
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources != null">datasources = #{datasources},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
	<update id="edit_StaffSalary" parameterType="pd">
		update ${ database }.sys_staffsalary
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources != null">datasources = #{datasources},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
	<update id="edit_PeriodCost" parameterType="pd">
		update ${ database }.sys_periodcost
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources1 != null">datasources1 = #{datasources1},</if>
			<if test="datasources2 != null">datasources2 = #{datasources2},</if>
			<if test="datasources3 != null">datasources3 = #{datasources3},</if>
			<if test="datasources4 != null">datasources4 = #{datasources4},</if>
			<if test="datasources5 != null">datasources5 = #{datasources5},</if>
			<if test="datasources6 != null">datasources6 = #{datasources6},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
	<update id="edit_AssetsInfo" parameterType="pd">
		update ${ database }.sys_assetsinfo
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources1 != null">datasources1 = #{datasources1},</if>
			<if test="datasources2 != null">datasources2 = #{datasources2},</if>
			<if test="datasources3 != null">datasources3 = #{datasources3},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	<update id="edit_cashFlow" parameterType="pd">
		update ${ database }.sys_cashflow
		set 
			<if test="item != null">item = #{item},</if>
			<if test="datasources != null">datasources = #{datasources},</if>
			id = #{id}
		where 
			id = #{id}
	</update>
	
	<!-- 获取 税负分析表 指定科目发生额 -->
	<select id="getAccrual_TaxBurden" parameterType="pd" resultType="pd">
		select 
				ifnull(s_revenue.money,0) accrual_revenue,
				ifnull(s_addedtax.money,0) accrual_addedtax,
				ifnull(s_surtax.money,0) accrual_surtax,
				ifnull(s_incometax.money,0) accrual_incometax,
				ifnull(s_cost.money,0) accrual_cost,
				ifnull(s_profit.money,0) accrual_profit
		from
			(
				select 
					sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) money
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'revenue' and <!-- find_in_set(s.code,c.code)>0   -->locate( concat(',', s.code), concat(',', c.code)) != 0 
			) s_revenue,
			(
				select 
					sum( ifnull(mx.creditmoney,0) ) money
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'addedtax_2' and <!-- find_in_set(s.code,c.code)>0  --> locate( concat(',', s.code), concat(',', c.code)) != 0 
			) s_addedtax,
			(
				select 
					sum( ifnull(mx.creditmoney,0) ) money
				from 
					${ database }.c_taxrateset ct, ${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					ct.subject_credit_id = s.id
					and t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and <!-- find_in_set(s.code,c.code)>0 -->  locate( concat(',', s.code), concat(',', c.code)) != 0
			) s_surtax,
			(
				select 
					sum( ifnull(mx.debitmoney,0) ) money
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'incometax' and <!-- find_in_set(s.code,c.code)>0  --> locate( concat(',', s.code), concat(',', c.code)) != 0
			) s_incometax,
			(
				select 
					sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ) money
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'cost' and <!-- find_in_set(s.code,c.code)>0 --> locate( concat(',', s.code), concat(',', c.code)) != 0
			) s_cost,
			(
				select
					sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) money
				from
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') = 'carryover'
					and s.flag = 'profit' and <!-- find_in_set(s.code,c.code)>0 --> locate( concat(',', s.code), concat(',', c.code)) != 0
			)s_profit
	</select>
	
	<!-- 获取 经营分析表 指定科目发生额 -->
	<select id="getAccrual_OperationAnalysis" parameterType="pd" resultType="pd">
		select 
				ifnull(s_revenue.money,0) accrual_revenue,			<!-- 主营业务收入 -->
				ifnull(s_cost.money,0) accrual_cost,				<!-- 主营业务成本 -->
				ifnull(s_1_begin.money,0) accrual_1_begin,			<!-- 总资产：资产类会计科目的期初余额 -->
				ifnull(s_1_finish.money,0) accrual_1_finish,		<!-- 总资产：资产类会计科目的期末余额 -->
				ifnull(s_2_begin.money,0) accrual_2_begin,			<!-- 应收账款：应收账款的期初余额 -->
				ifnull(s_2_finish.money,0) accrual_2_finish,		<!-- 应收账款：应收账款的期末余额 -->
				ifnull(s_3_begin.money,0) accrual_3_begin,			<!-- 库存商品：库存商品的期初余额 -->
				ifnull(s_3_finish.money,0) accrual_3_finish,		<!-- 库存商品：库存商品的期末余额 -->
				ifnull(s_4_begin.money,0) accrual_4_begin,			<!-- 负债总额：负债类会计科目的期初余额 -->
				ifnull(s_4_finish.money,0) accrual_4_finish,		<!-- 负债总额：负债类会计科目的期末余额 -->
				ifnull(s_5_begin.money,0) accrual_5_begin,			<!-- 应付账款：应付账款的期初余额 -->
				ifnull(s_5_finish.money,0) accrual_5_finish,		<!-- 应付账款：应付账款的期末余额 -->
				ifnull(s_6.money,0) accrual_6,						<!-- 销售费用：销售费用的发生额 -->
				ifnull(s_7.money,0) accrual_7,						<!-- 管理费用：管理费用的发生额 -->
				ifnull(s_8.money,0) accrual_8,						<!-- 财务费用：财务费用的发生额 -->
				ifnull(s_9.money,0) accrual_9,						<!-- 净利润：月末结转凭证中的本年利润，贷为正数，借为负数 -->
				ifnull(s_10.money,0) accrual_10,					<!-- 应缴增值税：未交增值税的贷方发生额 -->
				ifnull(s_11.money,0) accrual_11,					<!-- 应缴附加税：附加税率设置中的贷方科目的发生额 -->
				ifnull(s_12.money,0) accrual_12,					<!-- 应缴所得税：应交所得税的贷方发生额 -->
				ifnull((30/ ( ifnull(s_revenue.money,0)/( ifnull(s_1_begin.money,0) + ifnull(s_1_finish.money,0) )/2)),0) accrual_1_days,	<!--总资产周转天数-->
				ifnull((30/ ( ifnull(s_revenue.money,0)/( ifnull(s_2_begin.money,0) + ifnull(s_2_finish.money,0) )/2)),0) accrual_2_days,	<!--应收账款周转天数-->
				ifnull((30/ ( ifnull(s_cost.money,0)/( ifnull(s_3_begin.money,0) + ifnull(s_3_finish.money,0) )/2)),0) accrual_3_days,		<!--库存商品周转天数-->
				concat(round(ifnull(s_4_finish.money,0) / ifnull(s_1_finish.money,0) * 100,2), '%') ratio,		<!-- 资产负债率 -->
				ifnull((ifnull(s_revenue.money,0) - ifnull(s_cost.money,0)),0) gross_profit,													<!-- 毛利 -->
				concat(round(ifnull((ifnull(s_revenue.money,0) - ifnull(s_cost.money,0))/ifnull(s_revenue.money,0)*100,0),2), '%') gross_rate,	<!-- 毛利率 -->
				concat(round(ifnull(ifnull(s_9.money,0)/ifnull(s_revenue.money,0)*100,0),2), '%') net_rate,		<!-- 净利率 -->
				ifnull((ifnull(s_10.money,0) + ifnull(s_11.money,0) + ifnull(s_12.money,0)),0) sum_tax			<!-- 合计缴税 -->
		from
			(
				select 
					sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) money
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'revenue' and <!-- find_in_set(s.code,c.code)>0  -->  locate( concat(',', s.code), concat(',', c.code)) != 0
			) s_revenue,
			(
				select 
						sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ) money
				from 
						${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
						${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
						t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
						and s.flag = 'cost' and <!-- find_in_set(s.code,c.code)>0  --> locate( concat(',', s.code), concat(',', c.code)) != 0
			) s_cost,
			(
				select sum(t.begin_balance) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end begin_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.sub_class = 0
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt; str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_1_begin,
			(
				select sum(ifnull(t.finish_balance,0)) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end finish_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.sub_class = 0
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_1_finish,
			(
				select sum(t.begin_balance) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end begin_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.flag = '1122'
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt; str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_2_begin,
			(
				select sum(t.finish_balance) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end finish_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.flag = '1122'
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_2_finish,
			(
				select sum(t.begin_balance) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end begin_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.flag = '1405'
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt; str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_3_begin,
			(
				select sum(t.finish_balance) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end finish_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.flag = '1405'
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_3_finish,
			(
				select sum(ifnull(t.begin_balance,0)) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end begin_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.sub_class = 1
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt; str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_4_begin,
			(
				select sum(ifnull(t.finish_balance,0)) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end finish_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.sub_class = 1
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_4_finish,
			(
				select sum(t.begin_balance) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end begin_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.flag = '2202'
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt; str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_5_begin,
			(
				select sum(t.finish_balance) money
				from 
				(
					select 
						case a.sub_direction when 0 then ifnull(sum( i.debitmoney - i.creditmoney),0) 
											 else ifnull(sum( i.creditmoney - i.debitmoney),0) end finish_balance
					from 
						${ database }.c_accsubjects a,
						${ database }.i_kmye i
					where 
						a.flag = '2202'
						and i.subjectid = a.id
						and str_to_date( i.accountperiod, '%Y-%m') &lt;= str_to_date( #{mon}, '%Y-%m')
					group by a.id
				) t 
			) s_5_finish,
			(
				select 
						sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ) money
				from 
						${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
						${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
						t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
						and s.flag = 'expenses_1' and <!-- find_in_set(s.code,c.code)>0 -->    locate( concat(',', s.code), concat(',', c.code)) != 0 
			) s_6,
			(
				select 
						sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ) money
				from 
						${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
						${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
						t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
						and s.flag = 'expenses_2' and <!-- find_in_set(s.code,c.code)>0 -->  locate( concat(',', s.code), concat(',', c.code)) != 0
			) s_7,
			(
				select 
						sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ) money
				from 
						${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
						${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
						t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
						and s.flag = 'expenses_3' and <!-- find_in_set(s.code,c.code)>0 -->  locate( concat(',', s.code), concat(',', c.code)) != 0
			) s_8,
			(
				select 
						sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) money 
				from 
						${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, ${ database }.c_accsubjects c
				where 
						t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') = 'carryover'
						and c.flag = 'profit'
			) s_9,
			(
				select 
					sum( ifnull(mx.creditmoney,0) ) money
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'addedtax_2' and <!-- find_in_set(s.code,c.code)>0 --> locate( concat(',', s.code), concat(',', c.code)) != 0 
			) s_10,
			(
				select 
					sum( ifnull(mx.creditmoney,0) ) money
				from 
					${ database }.c_taxrateset ct, 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					ct.subject_credit_id = s.id
					and t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and <!-- find_in_set(s.code,c.code)>0  --> locate( concat(',', s.code), concat(',', c.code)) != 0 
			) s_11,
			(
				select 
					sum( ifnull(mx.creditmoney,0) ) money
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{mon} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'IncomeTaxPayable' and <!-- find_in_set(s.code,c.code)>0 -->   locate( concat(',', s.code), concat(',', c.code)) != 0 
			) s_12
	</select>
	
	<select id="findSujectByName" parameterType="pd" resultType="pd">
		select
			a.code,
			a.code as code1,
			a.name,
			cast(CONCAT(a.code,' ',a.name) as char) as text,
			a.sub_class,
			a.sub_direction,
			a.isdayacc,
			ifnull(a.sub_type_cash,0) as sub_type_cash,
			ifnull(a.sub_type_bank,0) as sub_type_bank,
			a.auxbus,
			a.auxbus as auxbus1,
			<!-- case --> ifnull(a.cashflowatt,13) cashflowatt, 
			a.parentid parentId,
			a.isfixed,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_accsubjects a
		where 
			a.name = #{ subjectname }
	</select>
	
	
		<select id="findSujectListByAuxtype" parameterType="pd" resultType="pd">
		select
			a.code,
			a.code as code1,
			a.name,
			cast(CONCAT(a.code,' ',a.name) as char) as text,
			a.sub_class,
			a.sub_direction,
			a.isdayacc,
			ifnull(a.sub_type_cash,0) as sub_type_cash,
			ifnull(a.sub_type_bank,0) as sub_type_bank,
			a.auxbus,
			a.auxbus as auxbus1,
			<!-- case --> ifnull(a.cashflowatt,13) cashflowatt, 
			a.parentid parentId,
			a.isfixed,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_accsubjects a
		where 
			a.auxbus = #{ auxbus }
	</select>
	
	
	
	<!-- 获取 简易增值税申报 指定发生额 -->
	<select id="findAccData_SimVaTax" parameterType="pd" resultType="pd">
		select 
				ifnull(s_0.num,0) accrual_0,
				ifnull(s_1.num,0) accrual_1,
				ifnull(s_2.num,0) accrual_2,
				ifnull(s_3.num,0) accrual_3,
				ifnull(s_4.num,0) accrual_4,
				ifnull(s_5.num,0) accrual_5,
				ifnull(s_6.num,0) accrual_6,
				ifnull(s_7.num,0) accrual_7,
				ifnull(s_8.num,0) accrual_8,
				ifnull(s_9.num,0) accrual_9,
				(ifnull(s_7.num,0) - ifnull(s_9.num,0)) accrual_10,
				ifnull(s_11.num,0) accrual_11,
				ifnull(s_12.num,0) accrual_12,
				(ifnull(s_2.num,0) - ifnull(s_7.num,0) - ifnull(s_11.num,0) + ifnull(s_12.num,0) ) accrual_14
		from
			(
				select 
					sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) num
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{month} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'revenue' and find_in_set(s.code,c.code)>0  <!-- locate( concat(',', s.code), concat(',', c.code)) != 0 -->
			) s_0,
			(
				select count(*) num from ${ database }.t_saleinvoice t 
				where str_to_date( t.accountperiod, '%Y-%m') = str_to_date( #{month}, '%Y-%m')
			) s_1,
			(
				select 
					sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) num
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{month} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'output_tax' and find_in_set(s.code,c.code)>0  <!-- locate( concat(',', s.code), concat(',', c.code)) != 0 -->
			) s_2,
			(
				select count(*) num from ${ database }.t_saleinvoice t 
				where str_to_date( t.accountperiod, '%Y-%m') = str_to_date( #{month}, '%Y-%m') and t.invoice_type = 1
			) s_3,
			(
				select sum(t.tax) num from ${ database }.t_saleinvoice t 
				where str_to_date( t.accountperiod, '%Y-%m') = str_to_date( #{month}, '%Y-%m') and t.invoice_type = 1
			) s_4,
			(
				select count(*) num from ${ database }.t_saleinvoice t 
				where str_to_date( t.accountperiod, '%Y-%m') = str_to_date( #{month}, '%Y-%m') and t.invoice_type = 2
			) s_5,
			(
				select sum(t.tax) num from ${ database }.t_saleinvoice t 
				where str_to_date( t.accountperiod, '%Y-%m') = str_to_date( #{month}, '%Y-%m') and t.invoice_type = 2
			) s_6,
			(
				select 
					sum( ifnull(mx.debitmoney,0) - ifnull(mx.creditmoney,0) ) num
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{month} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'input_tax' and find_in_set(s.code,c.code)>0  <!-- locate( concat(',', s.code), concat(',', c.code)) != 0 -->
			) s_7,
			(
				select count(*) num from ${ database }.t_purchaseinvoice t 
				where str_to_date( t.accountperiod, '%Y-%m') = str_to_date( #{month}, '%Y-%m')
			) s_8,
			(
				select sum(t.tax) num from ${ database }.t_purchaseinvoice t 
				where str_to_date( t.accountperiod, '%Y-%m') = str_to_date( #{month}, '%Y-%m')
			) s_9,
			(
				select 
					case when st.num &gt; 0 then 0 else abs(st.num) end num
				from
				(
					select 
						sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) num
					from 
						${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
						${ database }.c_accsubjects c, ${ database }.c_accsubjects s
					where 
						t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod &lt; #{month} and ifnull(t.source,'') != 'carryover'
						and s.flag = 'addedtax_1' and find_in_set(s.code,c.code)>0  <!-- locate( concat(',', s.code), concat(',', c.code)) != 0 -->
				) st
			) s_11,
			(
				select 
					sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) num
				from 
					${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
					${ database }.c_accsubjects c, ${ database }.c_accsubjects s
				where 
					t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{month} and ifnull(t.source,'') != 'carryover'
					and s.flag = 'input_tax_out' and find_in_set(s.code,c.code)>0  <!-- locate( concat(',', s.code), concat(',', c.code)) != 0 -->
			) s_12
	</select>
	
	<!-- 获取 若“主营业务收入”下有明细科目，取明细科目的发生额 -->
	<select id="findAccData_SimVaTaxMx" parameterType="pd" resultType="pd">
		select 
			c.aliasname,
			sum( ifnull(mx.creditmoney,0) - ifnull(mx.debitmoney,0) ) num
		from 
			${ database }.t_fillvouchers t, ${ database }.t_fillvouchersmx mx, 
			${ database }.c_accsubjects c, ${ database }.c_accsubjects s
		where 
			t.id = mx.fillvouchersid and mx.subjectid = c.id and t.accountperiod = #{month} and ifnull(t.source,'') != 'carryover'
			and s.flag = 'revenue' and find_in_set(s.code,c.code)>0  <!-- locate( concat(',', s.code), concat(',', c.code)) != 0 -->
		group by c.id
	</select>
	
	<!-- 查询所有的薪资项目 -->
	<select id="findPayrollItems" parameterType="pd" resultType="pd">
		select 
			case a.code when '99' then 'code_99' else a.code end code, a.name, a.id 
		from ${ database }.t_salaryitems a 
		where a.closestatus=0 order by a.code+0 asc
	</select>
	
	<!-- 查询所有的社保项目 -->
	<select id="findNorinsuranceItems" parameterType="pd" resultType="pd">
		select 
			distinct
			a.insuranceitemscode code,
			t.insuranceitemsname name
		from 
			${ database }.t_fixedinsurance as a,
			(
				select
					cast(CONCAT(a.insuranceitemsid,'-','comppart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','公司') as char) as insuranceitemsname
				from 
					${ database }.c_insurancerate a 
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.comppart >0 and b.closestatus=0
					
				union all
			
				select
					cast(CONCAT(a.insuranceitemsid,'-','personpart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','个人') as char) as insuranceitemsname
				from 
					${ database }.c_insurancerate a
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.personpart >0 and b.closestatus=0
			) as t
		where 
			a.insuranceitemscode = t.insuranceitemscode
		order by a.insuranceitemscode asc
	</select>
	
	<!-- 查询所有的公积金项目 -->
	<select id="findHousefundItems" parameterType="pd" resultType="pd">
		select 
			distinct
			a.insuranceitemscode code,
			t.insuranceitemsname name
		from 
			${ database }.t_fixedinsurance as a,
			(
				select
					cast(CONCAT(a.insuranceitemsid,'-','comppart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','公司') as char) as insuranceitemsname
				from 
					${ database }.c_housefundrate a
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.comppart >0 and b.closestatus=0
					
				union all
				 
				select
					cast(CONCAT(a.insuranceitemsid,'-','personpart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','个人') as char) as insuranceitemsname
				from 
					${ database }.c_housefundrate a
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.personpart >0 and b.closestatus=0
			) as t
		where 
			a.insuranceitemscode = t.insuranceitemscode
		order by a.insuranceitemscode asc
	</select>
	
	<!-- 查询所有的外包保险项目 -->
	<select id="findOutsourceItems" parameterType="pd" resultType="pd">
		select 
			distinct
			a.insuranceitemscode code,
			t.insuranceitemsname name
		from 
			${ database }.t_fixedinsurance as a,
			(
				select
					cast(CONCAT(a.insuranceitemsid,'-','comppart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','公司') as char) as insuranceitemsname
				from 
					${ database }.c_insurancerate a 
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.comppart >0 and b.closestatus=0
					
				union all
				
				select
					cast(CONCAT(a.insuranceitemsid,'-','personpart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','个人') as char) as insuranceitemsname
				from 
					${ database }.c_insurancerate a
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.personpart >0 and b.closestatus=0
				
				union all
			
				select
					cast(CONCAT(a.insuranceitemsid,'-','comppart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','公司') as char) as insuranceitemsname
				from 
					${ database }.c_housefundrate a 
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.comppart >0 and b.closestatus=0
					
				union all 
				
				select
					cast(CONCAT(a.insuranceitemsid,'-','personpart') as char) as insuranceitemscode,
					cast(CONCAT(b.name,'-','个人') as char) as insuranceitemsname
				from 
					${ database }.c_housefundrate a
					left join ${ database }.c_insuranceitems as b on a.insuranceitemsid=b.id
				where
					a.closestatus=0 and a.personpart >0 and b.closestatus=0
			) as t 
		where 
			a.insuranceitemscode = t.insuranceitemscode
		order by a.insuranceitemscode asc
	</select>
	
	<select id="findVoucherByMonthAndSubjectcode" parameterType="pd" resultType="pd">
		select 
			a.code from ${ database }.t_fillvouchers as a 
	   			left join ${ database }.t_fillvouchersmx as b on a.id = b.fillvouchersid
	   			left join ${ database }.c_accsubjects as c on b.subjectid = c.id
	 		where 
	 			a.accountperiod = #{voucherdate} and left(c.code,4) = #{subjectcode}
	 			order by substring_index(a.code,'-', -1)+0 asc
	</select>
</mapper>