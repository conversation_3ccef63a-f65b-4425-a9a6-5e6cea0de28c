package org.newstanding.test;

import java.text.DecimalFormat;

public class TestAuoIncCode {
	    private static int totalCount = 0;  
	    private int customerID;  
	    public TestAuoIncCode(){  
	        ++totalCount;  
	        customerID = totalCount;  
	        System.out.println("增加一个");  
	    }  
	    public String getCustomerID() {  
	    DecimalFormat decimalFormat = new DecimalFormat("00000000");  
	    return decimalFormat.format(customerID);  
	    }
	  public static void main(String[] args) {
		  TestAuoIncCode c1 = new TestAuoIncCode();  
	    System.out.println(c1.getCustomerID());  
	}
}
