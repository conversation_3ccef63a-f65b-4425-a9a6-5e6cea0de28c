package org.newstanding.service.systemset;

import java.util.ArrayList;
import java.util.List;

import org.newstanding.common.dao.DaoSupport;
import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.StringUtils;
import org.newstanding.plugin.ExceptionHandler;
import org.newstanding.service.base.BaseServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("vouchertemplateService")
public class VouchertemplateService  extends BaseServiceImpl{
	@Autowired
	private DaoSupport dao;

	/*
	 * 新增
	 */
	public PageData save(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.save("VouchertemplateMapper.save", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	/**
	 * 通过id删除数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData delete(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			int count = dao.delete("VouchertemplateMapper.delete", pd);
			if (count > 0) {
				resPd.put("state", "success");
			} else {
				resPd.put("state", "error");
			}
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 修改数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData edit(PageData pd) {
		PageData resPd = new PageData();
		try {
			if (StringUtils.isUpdate(pd.getString("vouchertemplatemx"))) {
				String vouchertemplatemx = pd.getString("vouchertemplatemx");
				List<PageData> modList = JsonUtils.strToListPd(JsonUtils.strToMap(vouchertemplatemx).get("rows").toString());
				pd.remove("vouchertemplatemx");
				pd.put("modList", modList);
				pd.put("isversion", 0);
				count = dao.update("VouchertemplateMapper.edit", pd);
			}
			
			resPd.put("state", "success");
			resPd.put("count", count);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData list(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("VouchertemplateMapper.datalistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	/**
	 * 根据id查询一条数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findById(PageData pd) throws Exception {
		PageData resPd = new PageData();
		try {
			resPd=(PageData) dao.findForObject("VouchertemplateMapper.findById", pd);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
		return resPd;
	}
	
	
	/**
	 * 通过模板ID获取明细数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	public PageData findVouchertemplatemxByVouchertemplateid(PageData pd) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("VouchertemplateMapper.findVouchertemplatemxByVouchertemplateid", pd);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
	
	/**
	 * 查询列表数据
	 * @param pd
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public PageData allList(Page page) throws Exception {
		PageData resPd = new PageData();
		List<PageData> list=new ArrayList<PageData>();
		try {
			list=(List<PageData>) dao.findForList("VouchertemplateMapper.alllistPage", page);
			resPd.put("state","success");
			resPd.put("list",list);
		} catch (Exception e) {
			resPd = new ExceptionHandler(e,errpd).getThrowMessage();
		}
	
		return resPd;
	}
	
}
