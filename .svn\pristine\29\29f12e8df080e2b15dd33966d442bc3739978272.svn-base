<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
	<head>
		<base href="<%=basePath%>">
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>新屹--财税系统</title>
		<link rel="shortcut icon" href="static/login/img/top.ico" type="image/x-icon" />
		<%@ include file="../../public/common_css_js.jspf"%>
		<link rel="stylesheet" type="text/css" href="static/css/index.css">
		<style>
			*{margin: 0;padding: 0;}
			html,body{height: 100%;}
			body{border: 0px 10px 0px 0px solid;}
		</style>
	</head>
<body>
<!-- 修改密码弹出框 -->
<div class="shadediv">
   <div class="bannermian_r">
	       <form id="formid" class="formclas" name= "myform" method = 'post'>
	           <h2>修改密码</h2>
	           <p class="closebtn" id="closebtn"></p>
	           <p class="formp"><span></span></p>
	          <div class="reightdiv">密码由6-20字符构成，请输入正确格式</div>
	           <div class="controlsdiv">
                   <input type="password" name="oldpassword" id="oldpassword" value="" placeholder="请输入原密码" />
	           </div>
	           <div class="controlsdiv">
	               <input type="password" name="newpassword" id="newpassword" value="" placeholder="请输入新密码" />
	           </div>
	           <div class="controlsdiv">
	               <input type="password" name="confirmpassword" id="confirmpassword" placeholder="请确认密码" value="" />
	           </div>
	           <div class="controlsdiv controlsdivs">
	               <a onclick="severCheck();" class="flip-linkbtn" id="to-recover">确定</a>
	           </div>
	       </form>
	 </div>
</div>
<div  style="width:100%;height:100%;">
    <div id="index_div" class="easyui-tabs" style="width:100%;height:100%;">   
	    <div title="首页">   
	        <div id="main" class="easyui-layout" style="width:100%;height:100%;">		
			    <div data-options="region:'north',split:true" style="height:54px;border: 0;    background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4);
    background: -moz-linear-gradient(top,#fff,#f4f4f4);
    background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);
    background-repeat: repeat-x;">
				    <span class="companyName" id="companyName"></span>
				    <div class="header_right">
				         <p class="userbtn"  id="userbtn"></p>
				         <p class="username">${ sessionScope.sessionUser.username}</p>
				         <p class="userimg"></p>
				         <div class="headPortrait" id="headPortrait" style="display:none">
				             <p class="headP"></p>
				             <ul>
				                 <li id="logout">退出</li>
				                 <li id="password_pop">修改密码</li>
				             </ul>
				         </div>
				    </div>
			    </div>     
			    <div id="center_div" class="left_menuUL" data-options="region:'center'" style="box-shadow: rgba(11,3,6,0.15) 0px 2px 2px inset;background:#ebedf3;border: 0;">
		    		<div id="topmenu_div" class="easyui-tabs" data-options="tabPosition:'left',onSelect:reloadSelect" style="width:100%;height:100%;">
		    			<div title="<span class='menuicon menuiconsy'></span><p>首页</p>">
			    			 <iframe id="iframe_systemset" src="common/topMenuPage?code=homepage" style="height:99.7%;width:100%" frameborder="0" scrolling="no" border="0" ></iframe>
		    			</div>
		    			<c:forEach items="${pd.menuList}" var="mx">
		    				<c:if test="${mx.type == 1}">
		    					<div title="<span class='menuicon ${mx.ioc}'></span><p>${mx.name}</p>">
					    			<iframe id="iframe_${mx.code}" src="${mx.url}" style="height:99.7%;width:100%" frameborder="0" scrolling="no" border="0" ></iframe>
				    			</div>
		    				</c:if>
		    			</c:forEach>	
		    				
		    		</div>
			    </div>   
			</div>  
		</div>   
	</div>     
</div>
<input type="hidden" id="version_" value="${pd.version_}"> 

<script>

$(function(){
	
	var accinfo = findAccInfo();
	$('#companyName').html(accinfo.compname);
	
	$("#main").layout("remove", "east");
	$(".left_menuUL").find("#topmenu_div").find(".tabs-inner").css({"height":"35px","line-height":"35px"});
	$(".userbtn").click(function(){
		if($(".headPortrait").css("display")=="none"){
			$(".headPortrait").fadeIn();
		}else{
			$(".headPortrait").fadeOut();
		}
	});
	$("#closebtn").click(function(){//关闭修改密码弹出框
		$(".shadediv").fadeOut();
	});
	$("#password_pop").click(function(){//弹出修改密码弹出框
		$(".headPortrait").fadeOut();
		$(".shadediv").fadeIn();
	});
	setTimeout(function(){
		 $(document).bind('click', function(e){
		        e = e || event;
		        if(window.event != undefined){
			        var btn = document.getElementById("userbtn");
			        var msg = document.getElementById("headPortrait");
			        if(!msg.contains(window.event.srcElement) && !btn.contains(window.event.srcElement)){
				        var target = e.target || e.srcElement;
				        if (target !== btn && target !== msg) {
								$('#headPortrait').fadeOut();		        		
				        }
			        };
		        }
	   })
	},500);
	
	$("#logout").click(function(){
		checkBrower('logout');
	});
	debugger;
	jsonUtils.version_ = $('#version_').val();
	
});

/**
 *	新打开页签用
 */
function openTabs(obj){
	if($('#index_div').tabs('exists', obj.title)){
		//$('#index_div').tabs('select', obj.title);
		$('#index_div').tabs('close', obj.title);
		$('#index_div').tabs('add', {
			title: obj.title,
			closable: true,
			content: '<iframe id="iframe_'+ obj.code +'" src="'+ getUrl(obj.url) +'" style="height:100%;width:100%" frameborder="0" scrolling="no" border="0" ></iframe>',
		});
	}else{
		$('#index_div').tabs('add', {
			title: obj.title,
			closable: true,
			content: '<iframe id="iframe_'+ obj.code +'" src="'+ getUrl(obj.url) +'" style="height:100%;width:100%" frameborder="0" scrolling="no" border="0" ></iframe>',
		});
	}
}

/**
 *	关闭页签用
 */
function closeTabs(obj){
	if($('#index_div').tabs('exists', obj.title)){
		$('#index_div').tabs('close', obj.title);
	}
}

//重新加载
function reloadSelect(title){
	debugger
	debugger
	//点击出纳页刷新
/* 	var code = '';
    if(title == "<span class='menuicon menuiconc'></span><p>出纳</p>"){
    	code = 'iframe_cashier'
    }else if(title == "<span class='menuicon menuiconj'></span><p>进销存</p>"){
    	code = 'iframe_invoicing'
    }else if(title == "<span class='menuicon menuiconcw'></span><p>财务处理</p>"){
    	code = 'iframe_financeManage'
    }else if(title == "<span class='menuicon menuicong'></span><p>固定资产</p>"){
    	code = 'iframe_fixedassets'
    }else if(title == "<span class='menuicon menuiconx'></span><p>薪资</p>"){
    	code = 'iframe_pay'
    }else if(title == "<span class='menuicon menuicons'></span><p>申报</p>"){
    	code = 'iframe_report'
    }else if(title == "<span class='menuicon menuiconxt'></span><p>系统设置</p>"){
    	code = 'iframe_systemset'
    }
	
	var cashierSrc=parent.document.getElementById(code).contentWindow.location.href 
	$('#'+code).attr('src',getUrl(cashierSrc));
	document.getElementById(code).contentWindow.location.reload(true); */
	
	//点击出纳页刷新
    if(title == "<span class='menuicon menuiconc'></span><p>出纳</p>"){
    	var cashierSrc=parent.document.getElementById("iframe_cashier").contentWindow.location.href 
    	$('#iframe_cashier').attr('src',getUrl(cashierSrc));
    	document.getElementById('iframe_cashier').contentWindow.location.reload(true);
    }
}
function selectTab(obj){
	 $('#topmenu_div').tabs('select',Number(obj.index));
}

function severCheck(){
	var oldpwd = $('#oldpassword').val(),
	    newpwd = $('#newpassword').val(),
	    conpwd = $('#confirmpassword').val(),
	    username = '${ sessionScope.sessionUser.username}';
	
	sendAjax('user/resetPwd',{username:username,oldpassword:oldpwd,newpassword:newpwd,confirmpassword:conpwd},function(data){
		if(data.state == 'success'){
			/* $.messager.show({
				title:'操作成功',
				msg:data.message,
				showType:'show',
				timeout:1000,
			}); */
			promptbox('success',data.message);
			checkBrower('logout');
			
		}else if(data.state == 'error'){
			$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+data.message,'error');
		}
	});
}
</script>
</body>
</html>