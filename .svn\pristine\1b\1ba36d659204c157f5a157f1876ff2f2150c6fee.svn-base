<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html>
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<script type="text/javascript" src="static/plugin/jquery-easyui-1.5.3/datagrid-cellediting.js"></script>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/fillvouchers.css">
<style>
   .pageForm_topul li{position:relative}
   .pageForm_topul li .textbox{width:150px !important}
   #make_date{width:145px !important}
   .textbox #_easyui_textbox_input1{width:100% !important}
   .kjkm_btn{position:absolute;top:18px;right:14px;}
</style>
</head>
<body>
	<div id="panelDiv" class="easyui-layout" data-options="fit:true"style="">
	    <!-- 顶部功能栏 -->
	<div id="eastToolDiv" data-options="region:'north'" style="height:50px;background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4);
    background: -moz-linear-gradient(top,#fff,#f4f4f4);
    background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);
    background-repeat: repeat-x;border-top: 0;">
			<div id="toolbar" class="fillvouch_poptop">
			    <div class="fillvouch_poptopleft fillvouch_poptoplefts">
				    <span style="margin: 0 6px 0 0px;float: left;">会计期间</span>
				    <input type='text' id="year" class="invo_title_years" readonly="readonly" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M-%d',dateFmt:'yyyy',onpicked:yearPickedFunc})" />
				    <input type="text" name="month" class="invo_title_years"  id="month" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M',dateFmt:'MM',onpicked:monthPickedFunc})" />
				    <span>期</span>
				   	
				</div>
			    <div class="fillvouch_poptopright">
			        <a class="easyui-linkbutton xzadd_btns" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({width:1000,height:392,title:' '})"></a>
			        <a class="easyui-menubutton scpz_btns" href="javascript:void(0)" style="margin: 8px 0 0 0;" data-options="menu:'#createvoucherDiv'"></a>
					<div id="createvoucherDiv" style="width:150px;display:none">
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:1,code:'purchaselist'})">逐笔生成</div>
						<div>
							<span>按时间汇总</span>
							<div>
								<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaselist',day:1})">1天</div>
								<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaselist',day:3})">3天</div>
								<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaselist',day:5})">5天</div>
								<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaselist',day:7})">7天</div>
								<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaselist',day:10})">10天</div>
								<div onclick="beforeVoucherCreate({position:'#gridlist',type:2,code:'purchaselist',day:15})">15天</div>
							</div>
						</div>
						
						<div onclick="beforeVoucherCreate({position:'#gridlist',type:4,code:'purchaselist'})">全月汇总</div>
					</div>
					<a class="easyui-linkbutton cz_btns" style="margin: 8px 0 0 0;color: #6d62af;" href="javascript:void(0)" onclick="beforeVoucherRevoke({position:'#gridlist',code:'purchaselist'})"><i></i></a>
			        <a class="easyui-linkbutton dr_btns" href="javascript:void(0)" style="margin: 8px 0 0 40px;" onclick="$('#import_panel').window('open')"><i></i></a>
			        <a class="easyui-linkbutton dc_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="ExportExcel()"><i></i></a>
			        <a class="easyui-linkbutton gb_btns" href="javascript:void(0) " onclick="closeIndexTabs({title:'采购入库单'})"></a>
			    </div>
			    <p class="refreshbtns" id="refreshbtn" onclick="refresh()"></p>
			</div>
		</div>
		
		<!-- 列表div start -->
		<div style="margin: 50px 0 0 0;border:1px #6f9ec2 solid;" id="parcel_southToolDiv">
		    <div id="gridlist" data-options="region:'center'" style="border-left: 1px solid rgb(206, 218, 228);"></div>
		</div>    
		<!-- 列表div end -->
		<!-- 编辑页面弹框 start -->
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
			<!-- 单据表单 -->
			<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px #cedae4 solid;">
				<input type="hidden" id="id" name="id">
				<input type="hidden" id="supplierid" name="supplierid">
				<input type="hidden" id="total_money" name="total_money">
				<input type="hidden" id="accountperiod" name="accountperiod">
				<input type="hidden" id="purchaselistmx" name="purchaselistmx">
			    <span style=" text-align: center;position: absolute;top: 9px;font-weight: 600; font-size: 14px;color: #fff;left: 470px;">采购入库单<span id="name" name="name"></span></span>
			    <ul class="pageForm_topul fillvoucher_topul pageForm_topults" style="border-bottom: 1px #6f9ec2 solid;">
	    		    <li >
	    		       <p>供应商：</p>
	    		       <input id="supplier_name" class="easyui-textbox" name="supplier_name" style="height: 27px;margin:8px 0 0 0" data-options="readonly:true,required:true" />
					   <div class="kjkm_btn" id="supplier_btn" onclick="javascript:Dialog.archives_supplierdoc('1;pageForm;supplierid:id,supplier_name:name')"></div>
	    		    </li>
		    		<li>
	    		       <p>入库日期：</p>
	    		       <input  id="make_date"  type="text" name="make_date" onclick="WdatePicker({skin:'whyGreen',startDate:'%y-%M-%d',dateFmt:'yyyy-MM-dd',onpicked:voucherPickedFunc})"/>
	    		    </li>
		    		<li>
	    		       <p >入库单号：</p>
	    		       <input id="code" class="easyui-textbox" name="code" style="height: 27px;margin:8px 0 0 0" data-options="required:true,validType:['repeatMultiParameter[\'purchaselist\',\'t_purchaselist\',\'code\',\'pageForm\',\'make_date\']']" />
	    		    </li>
	    		</ul>
	    		<div style="border: 0px #cedae4 solid;border-left:0;border-right:0;"><div id="purchaselistmx_gridlist" style="width:996px;height:218px"></div></div>
		   		  	<a href="javascript:void(0)" class="easyui-linkbutton zhc_btn zhch_btn" onclick="ButtonFun.insertRow({type:2,renderid:'#purchaselistmx_gridlist'})">增行</a>
		    		<a href="javascript:void(0)" class="easyui-linkbutton shc_btn shch_btn" onclick="ButtonFun.removeRow({type:2,renderid:'#purchaselistmx_gridlist'})">删行</a>	
		    </form>
		    <div style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
		        <div style="float: left;margin: 2px 0 0 5px;">
			        <a href="javascript:void(0)" class="easyui-linkbutton xz_btn" onclick="ButtonFun.addFun({width:1000,height:392})">新增</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton bc_btn" onclick="submitForm({code:'purchaselist',type:2,renderid:'#gridlist',noClose:true})">保存</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton xg_btn" onclick="editVoucher()">修改</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton sc_btn" onclick="closeOrDelete('删除')">删除</a>
		    	</div>
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="clearForm()" style="margin: 4px 7px 0 0;">关闭</a>
		    </div>
		</div>
		<!-- 编辑页面弹框 end -->
		
		
			<!-- 核销页面弹框 start -->
		<div id="writeoff_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
			<!-- 单据表单 -->
			<form id="writeoff_pageForm" class="easyui-form" method="post" data-options="novalidate:true" style="border: 1px #cedae4 solid;">
				<input type="hidden" id="id1" name="id1">
				<input type="hidden" id="supplierid1" name="supplierid1">
				
				<input type="hidden" id="invoice_listmx" name="invoice_listmx">
				<input type="hidden" id="purchase_listmx" name="purchase_listmx">
				<input type="hidden" id="purchase_dellistmx" name="purchase_dellistmx">
			    <span style=" text-align: center;position: absolute;top: 9px;font-weight: 600; font-size: 14px;color: #fff;left: 470px;">入库发票核销<span id="name" name="name"></span></span>
			    <ul class="pageForm_topul fillvoucher_topul pageForm_topults" style="border-bottom: 1px #6f9ec2 solid;">
	    		    <li >
	    		       <p>供应商：</p>
	    		       <input id="supplier_name1" class="easyui-textbox" name="supplier_name1" style="height: 27px;margin:8px 0 0 0" data-options="readonly:true,required:true" />
	    		    </li>
	    		</ul>
	    		<div style="border: 0px #cedae4 solid;border-left:0;border-right:0;"><div id="purchaselist_gridlist" style="width:996px;height:88px"></div></div>
	    		<div style="border: 0px #cedae4 solid;border-left:0;border-right:0;"><span style="">发票明细</span><div id="invoicelist_gridlist" style="width:996px;height:218px"></div></div>
		   		  <a href="javascript:void(0)" class="easyui-linkbutton zhc_btn zhch_btn" onclick="insertRow1({type:2,renderid:'#invoicelist_gridlist'})">增行</a>
		    <a href="javascript:void(0)" class="easyui-linkbutton shc_btn shch_btn" onclick="removeRow1({type:2,renderid:'#invoicelist_gridlist'})">删行</a>	
		    </form>
		    <div style="height:42px;text-align:right;background:#ecf5fa;border-radius: 0 0 5px 5px;">
		        <div style="float: left;margin: 2px 0 0 5px;">
			    	<a href="javascript:void(0)" class="easyui-linkbutton bc_btn" onclick="saveWriteOff({code:'purchaselist',type:2,renderid:'#gridlist',noClose:false})">保存</a>
			    	<!-- <a href="javascript:void(0)" class="easyui-linkbutton xg_btn" onclick="editWriteOff()">修改</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton sc_btn" onclick="DeleteWriteOff('删除')">删除</a> -->
		    	</div>
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn closeNew_btn" onclick="clearWriteOffForm()" style="margin: 4px 7px 0 0;">关闭</a>
		    </div>
		</div>
		<!-- 核销页面弹框 end -->
	</div>
<script type="text/javascript">
	$(function(){
		var width = $(window).width()-24;
		var w1 = width/30;
		var heights = $(window).height();
		$("#parcel_southToolDiv").height(heights-50);
		$("#gridlist").height(heights-50);
		//列表columns
		var gridObj = new Object();
		debugger
		gridObj["position"] = "#gridlist";
		gridObj["url"] = 'purchaselist/list';
		//gridObj["rownumbers"] = 'false';
		gridObj["singleSelect"] = false;
		gridObj["columns"] = [[
								{field:'',checkbox:true},
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'accountperiod',title:'会计期间',align: 'left', halign: 'center',width:w1*2,hidden:true},
								{field:'code',title:'入库单号',align: 'left', halign: 'center',width:w1*2.6},
								{field:'make_date',title:'入库日期',align: 'left', halign: 'center',width:w1*2},
								{field:'supplier_name',title:'供应商',align: 'left', halign: 'center',width:w1*5},
								{field:'total_money',title:'金额',align: 'left', halign: 'center',width:w1*2},
								{field:'this_invoice',title:'当期发票',align: 'left', halign: 'center',width:w1*2},
								{field:'pre_invoice',title:'前期发票',align: 'left', halign: 'center',width:w1*2},
								{field:'un_invoice',title:'未收票',align: 'left', halign: 'center',width:w1*2},
								{field:'invoicecode',title:'发票号码',align: 'left', halign: 'center',width:w1*3.5},
								{field:'vouchercode',title:'凭证号码',align: 'left', halign: 'center',width:w1*3.5,
									formatter:function(value,row,index){
										if(!checkEm(row.voucher_id)){
											return '<a href="javascript:void(0)" onclick="Dialog.linkVoucher('+ row.voucher_id +')">'+ row.vouchercode +'</a>';
										}
										return '';
									}},
								{field:'action',title:'操作',width:w1*5,align:'center',
									formatter:function(value,row,index){
										var e = '<div style="width: 73px;height: 28px;margin: 0 auto;text-align: center;"><a href="javascript:void(0)" class="mxxg_btn" style="margin:3px 0 0 0" onclick="editrow(this,'+index+',\'gridlist\')">查看</a>';
										var f = '<a href="javascript:void(0)" class="mxxg_btn" style="margin:3px 0 0 2px" onclick="writeoffrow(this,'+index+')">核销</a>';
										var d = '<a href="javascript:void(0)"  class="mxsc_btn" style="margin:3px 0 0 4px" onclick="deleterow(this,'+index+')">删除</a></div>';
										return e+f+d;
										
									}
								}
					         ]];
		gridObj["idField"] = 'id';
		gridObj["listDbClickFun"] = listDbClickFun;
		Grid.list_grid(gridObj);
		$('#gridlist').datagrid({
			onLoadSuccess: function(data){
				if(!checkEm(data.accountperiod)){
					$('#accountperiod').val(data.accountperiod)
					var accountperiod=data.accountperiod.split('-');
					$('#year').val(accountperiod[0]);
					$('#month').val(accountperiod[1]);
				}
			}
		});
		
		
		
		//编辑弹框columns
		//明细grid
		var gridObj1 = new Object();
		gridObj1["position"] = "#purchaselistmx_gridlist";
		gridObj1["url"] = 'purchaselist/listPurchaselistmx';
		gridObj1["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'zt',title:'zt',hidden:true},
								{field:'rowid',title:'rowid',hidden:true},
								{field:'purchaselistid',title:'purchaselistid',hidden:true},
								{field:'type_name',title:'商品类别',width:220},
								{field:'goodsid',title:'商品id',hidden:true},
								{field:'goods_name',title:'商品名称',width:220,formatter:formatOper_goods},
								{field:'unit',title:'单位' ,width:100},
								{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:100},
								{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:100},
								{field:'money',title:'金额',editor:{type:'numberbox',options:{min:0,precision:2}},width:100,
								formatter:function(value,row,index){
									return formatMoney(value);
								}},
					         ]];
		//gridObj1["onEndEdit"]= onEndFunc;
		gridObj1["idField"] = 'id';
		gridObj1["showFooter"] = true;
		gridObj1["onAfterEdit"] = onAfterEdit_1;
		Grid.edit_cell_grid_url(gridObj1);
		
		
		
		
		//核销弹框columns
		//采购入库grid
		var gridObj2 = new Object();
		gridObj2["position"] = "#purchaselist_gridlist";
		gridObj2["url"] = 'purchaselist/listPurchaselist';
		gridObj2["columns"] = [[
								{field:'id',title:'ID',width:100,hidden:true},
								{field:'code',title:'入库单号',align: 'left', halign: 'center',width:w1*4},
								{field:'make_date',title:'入库日期',align: 'left', halign: 'center',width:w1*2},
								{field:'total_money',title:'金额',align: 'left', halign: 'center',width:w1*2},
								{field:'has_invoice',title:'已收票金额',align: 'left', halign: 'center',width:w1*2},
								{field:'now_invoice',title:'本次收票',align: 'left', halign: 'center',width:w1*2},
								{field:'un_invoice',title:'收票余额',align: 'left', halign: 'center',width:w1*2},
								{field:'action',title:'操作',width:w1*3,align:'center',
									formatter:function(value,row,index){
										var e = '<div style="width: 73px;height: 28px;margin: 0 auto;text-align: center;"><a href="javascript:void(0)" class="mxxg_btn" style="margin:3px 0 0 0" onclick="editrow(this,'+index+',\'purchaselist_gridlist\')">查看</a>';
										return e;
										
									}
								}
					         ]];
		gridObj2["idField"] = 'id';
		gridObj2["showFooter"] = true;
		Grid.edit_cell_grid_url(gridObj2);
		
		//采购发票grid
		var gridObj3 = new Object();
		gridObj3["position"] = "#invoicelist_gridlist";
		gridObj3["url"] = 'purchaselist/listPurchaseInvoicemx';//purchaselist/listPurchaselistInvoicemx
		gridObj3["columns"] = [[
								{field:'invoiceid',title:'ID',width:100,hidden:true},
								{field:'invoice_number',title:'发票号码',align: 'left', halign: 'center',width:w1*3, formatter:formatInvoice_number},
								{field:'accountperiod',title:'记账日期',align: 'left', halign: 'center',width:w1*2},
								{field:'invoice_date',title:'发票日期',align: 'left', halign: 'center',width:w1*2},
								{field:'money',title:'金额',align: 'left', halign: 'center',width:w1*2},
								{field:'tax',title:'税额',align: 'left', halign: 'center',width:w1*2},
								{field:'advalorem',title:'价税合计',align: 'left', halign: 'center',width:w1*2},
								{field:'has_instock',title:'已入库金额',align: 'left', halign: 'center',width:w1*2},
								{field:'now_instock',title:'本次入库金额',align: 'left', halign: 'center',width:w1*2,editor:{type:'numberbox',options:{min:0,precision:2}}},
								{field:'un_instock',title:'未入库余额',align: 'left', halign: 'center',width:w1*2},
					         ]];
		//gridObj1["onEndEdit"]= onEndFunc;
		gridObj3["idField"] = 'id';
		gridObj3["showFooter"] = true;
		gridObj3["onAfterEdit"] = onAfterEdit_3;
		Grid.edit_cell_grid_url(gridObj3);
	});
	function insertRow1(obj){
		//验证所选期间是否是当前期间，不是当前期间不可以曾行  删行
		debugger;
		var info = findAccInfo();
		var now_period = info.periodofaccount;
		
		var select_period = $('#year').val()+'-'+$('#month').val();
		
		if(now_period != select_period){
			 return;
		 }
		debugger
		ButtonFun.insertRow(obj);

	}
	
	/**
	 * 删除选中的行,将删除的数据传到后台
	 */
	 var delList = [];
	 function removeRow1(obj){
		var node = getSelectedData(obj.renderid,obj.type);
		if(checkEm(node)){
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要删除的行！','warning');
			return;
		}
		//验证所选期间是否是当前期间，不是当前期间不可以曾行  删行
		debugger;
		var info = findAccInfo();
		var now_period = info.periodofaccount;
		
		var select_period = $('#year').val()+'-'+$('#month').val();
		
		if(now_period != select_period){
			 return;
		 }
		//放入全局数组中，传到后台
		if(parseFloat(node.ismodify) ==1){
			delList.push(node);
		}
		
		var index = $(obj.renderid).datagrid('getRowIndex',node);
		$(obj.renderid).datagrid('deleteRow',index);
		
		//删除后重新计算  本次入库金额
		var rows = $("#purchaselist_gridlist").datagrid('getRows');
	       	    var list_row = rows[0];
		var total_instock = 0;
			var invoice_rows = $("#invoicelist_gridlist").datagrid('getRows');
		for(var i =0;i<invoice_rows.length;i++){
			if(!checkEm(invoice_rows[i].now_instock)){
				if(parseFloat(invoice_rows[i].ismodify) ==0){
					total_instock = (parseFloat(total_instock) + parseFloat(invoice_rows[i].now_instock)).toFixed(2);
				}
				
			}
		}
		var has_invoice = list_row.has_invoice;
		if(parseFloat(node.ismodify)==1){
			has_invoice = parseFloat(list_row.has_invoice) - parseFloat(node.now_instock);
		}
		var uninvoice_money = (parseFloat(list_row.total_money) - parseFloat(has_invoice) - parseFloat(total_instock)).toFixed(2);
		$('#purchaselist_gridlist').datagrid('updateRow',{
			index: 0,
			row: {
				has_invoice:has_invoice,
				now_invoice: total_instock,
				un_invoice:uninvoice_money
			}
		});
		
	}
	/**
	 * 生成凭证前验证
	 */
	function beforeVoucherCreate(obj){
		//1、所选择的明细的记账日期是否为记账月份前的日期，如果是则提示：所选明细的记账日期已结账，不可生成凭证
		var year = $('#year').val();
		var month = $('#month').val();
		var accinfo = findAccInfo();
		if( (year + '-' + month) < accinfo.periodofaccount ){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>当前会计期间（'+year + '-' + month+'）已结账，不可生成凭证！','error');
			return;
		}
		var lastday=getDaysInMonth(year,month);
		if(Number(obj.type)==3 && Number(obj.day)==31){//如果是按类型，并且是全部的时候，重新查找当月最后一天的天数。
			obj["day"]=lastday;
		}
		var rows = $(obj.position).datagrid('getSelections');	//所选数据
		if(rows.length == 0){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>请先选择数据！','error');
			return;
		}
		voucherCreate(obj);		//生成凭证
	}

	/**
	 * 撤账前验证
	 */
	function beforeVoucherRevoke(obj){
		var rows = $(obj.position).datagrid('getSelections');	//所选数据
		if(rows.length == 0){
			$.messager.alert('提示','<span class="hintsp_e">提示</span>请先选择数据！','error');
			return;
		}
		voucherRevoke(obj);		//撤账
	}

	//通用撤账后触发方法
	function afterVoucherRevokeFun(){}
	//通用生成凭证后触发方法
	function afterVoucherCreateFun(){}

	
	
	
	//选择发票
 	function formatInvoice_number(val,row,index){
		if(checkEm(val)){val = ''}
		debugger
		var supplierid = $("#supplierid1").val();
	    return '<div style="width: 73px;height: 28px;text-align:left;">'+val+'<div class="choose_btnimg"  style="margin: 0px 0 0 0;"'
	    +' onclick="javascript:Dialog.archives_invoice(\'2;pageForm;invoiceid:invoiceid,ismodify:ismodify,invoice_number:invoice_number,accountperiod:accountperiod,invoice_date:invoice_date,money:money,tax:tax,advalorem:advalorem,has_instock:has_instock,now_instock:now_instock,un_instock:un_instock;invoicelist_gridlist;'+index+';'+supplierid+'\')"></div></div>';  
	} 
	
	

	// 核销页面方法 start-->
	//通用表单提交前触发方法
	function beforeSubmitFormFuna(){
		//再次验证编号不能重复
		debugger;
		var obj=new Object();
		var code=$('#code').textbox("getValue");
		obj["code"]='purchaselist';
		obj["table"]='t_purchaselist';
		obj["id"]= $("#id").val();
		
		debugger
		//保存时如果有未关闭的编辑框   将编辑框关闭，否则取不到值
		var indexs=$('#invoicelist_gridlist').datagrid('getEditingRowIndexs');
		if(indexs.length>0){
			for(var i=0;i<indexs.length;i++){
				 $('#invoicelist_gridlist').datagrid('refreshRow', indexs[i]);
				$('#invoicelist_gridlist').datagrid('endEdit',indexs[i]);
			}
		}
		var data = $('#invoicelist_gridlist').datagrid('getData');
		var rows=data.rows;
		for(var i=0;i<rows.length;i++){
			//删除空行
			if(checkEm(rows[i].now_instock) || rows[i].now_instock ==0){
				$('#invoicelist_gridlist').datagrid('deleteRow',i);
				i--;
				continue;
			}
		
		}
		
		/* if(rows.length==0){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>发票明细不能为空！','warning');  
			return false;
		} */
		$('#invoice_listmx').val(JSON.stringify(rows));
		
		if(delList.length>0){
			$('#purchase_dellistmx').val(JSON.stringify(delList));
		}
		
		var data1 = $('#purchaselist_gridlist').datagrid('getData');
		$('#purchase_listmx').val(JSON.stringify(data1.rows));
		return true;
	}
	
	//通用保存后触发方法
	function afterSaveFuna(id){
		 delList = [];
	}
	
	
	function saveWriteOff(obj){
		debugger
		if(checkEm(obj)){return;}
		var id = $('#id').val(),method;
		if(checkEm(id)){
			method = 'saveWriteOff'
		}else{
			method = 'saveWriteOff.do?id='+id
		}
		//有checkbox时，统一处理checkbox的赋值
		$('input:checkbox').each(function() {
			if ($(this)[0].checked) {
				$(this).val(1);
			}else{
				$(this).val(0);
			}
		});
		debugger;
		//20180709 wang 修改获取Url
		var url = getUrl(obj.code + "/" + method);
		
		$.messager.progress();	// 显示进度条
		$('#writeoff_pageForm').form("submit", {"url": url,
					onSubmit: function(param){
						var isValid = $(this).form('enableValidation').form('validate');
						if (!isValid){
							$.messager.progress('close');// 如果表单是无效的则隐藏进度条
							return false;
						}
						//需在页面定义此方法
						isValid=beforeSubmitFormFuna();
						
						if (!isValid){
							$.messager.progress('close');// 如果表单是无效的则隐藏进度条
							return false;
						}
						return isValid;	// 返回false终止表单提交
					},
					success: function(data){
						//后台返回数据处理逻辑
						if(!checkEm(data)){
							data = JSON.parse(data);
							if(data.state == 'success'){
								promptbox(data.state,'操作成功！');
								switch(obj.type){
								case 3:
									$(obj.renderid).tabs('select', Number(data.sub_class));
									$('#accTab_'+data.sub_class).tree('reload');
									break;
								default:
									reloadData(obj.renderid,obj.type);
									break; 
								} 
								/* if( checkEm(obj.noClose) && true ){ */
									$('#writeoff_pageForm').form('clear');
									$('#writeoff_pageId').window('close');
							/* 	} */
								
								if(checkEm(id)){id = data.id;}	//新增成功返回id
								
								//需在页面定义afterSaveFun
								afterSaveFuna(id);
							}else if(data.state == 'error'){
								var msg='操作失败,请联系管理员!'
								if(!checkEm(data.message)){
									msg=data.message;
								}
								$.messager.alert('操作失败','<span class="hintsp_e">提示</span>'+msg,'error');
							}
						}
						$.messager.progress('close');// 如果提交成功则隐藏进度条
					},
					error:function(data){
						$.messager.alert('操作失败','<span class="hintsp_e">提示</span>操作失败,请联系管理员！','error');
						$.messager.progress('close');
					}
					
			});
	}

		//弹框关闭方法
		function clearWriteOffForm(){
			$('#writeoff_pageForm').form('clear');
			$('#writeoff_pageId').window('close');
		}
		
		//onAfterEdit方法，计算
		
			function onAfterEdit_3(index,row,changes){
				debugger;
				var now_instock=row.now_instock || 0;
				var has_instock=row.has_instock || 0;
				var advalorem=row.money || 0;
				var un_instock = row.un_instock || 0;
				if(!checkEm(changes.now_instock)){
					un_instock=(parseFloat(advalorem) - parseFloat(has_instock) - parseFloat(now_instock)).toFixed(2);
				}
				
				//获取采购入库明细数据。将本次入库金额  合计到本次入库金额中
				var rows = $("#purchaselist_gridlist").datagrid('getRows');
           	    var list_row = rows[0];
           	    //发票总额不能超出未收票金额
           	 	var uninvoice_money = (parseFloat(list_row.total_money) - parseFloat(list_row.has_invoice)).toFixed(2);
           	    var total_instock = 0;
           		var invoice_rows = $("#invoicelist_gridlist").datagrid('getRows');
				for(var i =0;i<invoice_rows.length;i++){
					if(!checkEm(invoice_rows[i].now_instock)){
						if(parseFloat(invoice_rows[i].ismodify) ==0){
							total_instock = (parseFloat(total_instock) + parseFloat(invoice_rows[i].now_instock)).toFixed(2);
						}
						
					}
				}
				if(parseFloat(total_instock) > parseFloat(uninvoice_money) ){
					$.messager.alert("提示","<span class='hintsp_w'>提示</span>本次入库金额合计不能超出未收票余额！","warn");
					total_instock = (parseFloat(total_instock) - parseFloat(now_instock)).toFixed(2);
					uninvoice_money = (parseFloat(list_row.total_money) - parseFloat(list_row.has_invoice) - parseFloat(total_instock)).toFixed(2);
					$('#purchaselist_gridlist').datagrid('updateRow',{
						index: 0,
						row: {
							now_invoice: total_instock,
							un_invoice:uninvoice_money
						}
					});
					
					un_instock=(parseFloat(advalorem) - parseFloat(has_instock)).toFixed(2);
					$('#invoicelist_gridlist').datagrid('updateRow',{
						index: index,
						row: {
							now_instock: 0,
							un_instock:un_instock
						}
					}); 
					return;
				}
				
				uninvoice_money = (parseFloat(list_row.total_money) - parseFloat(list_row.has_invoice) - parseFloat(total_instock)).toFixed(2);
				$('#invoicelist_gridlist').datagrid('updateRow',{
					index: index,
					row: {
						un_instock: un_instock
					}
				}); 
				
				
				$('#purchaselist_gridlist').datagrid('updateRow',{
					index: 0,
					row: {
						now_invoice: total_instock,
						un_invoice:uninvoice_money
					}
				});
				
			}
		
	// 核销页面方法 end-->
	
	
	
// 编辑页面方法 start-->
	function formatOper_goods(val,row,index){
		if(checkEm(val)){val = ''}
	    return '<div style="width: 73px;height: 28px;text-align:left;">'+val+'<div class="choose_btnimg"  style="margin: 0px 0 0 0;"  onclick="javascript:Dialog.archives_goods(\'2;pageForm;goodsid:id,goods_name:name,type_name:type_name,unit:unit,quantity:quantity,price:price,money:price;purchaselistmx_gridlist;'+index+'\')"></div></div>';  
	}
	function doGoodsSearch(value){
		$('#customer_gridlist').datagrid('load',{
			q:value
		})
	}
	
	function doInvoiceSearch(value){
		debugger
		$('#customer_gridlist').datagrid('load',{
			q:value
		})
	}
	//会计期间年  change事件
	function yearPickedFunc(){
		var period=$('#year').val()+'-'+$('#month').val();
		$('#gridlist').datagrid('load',{
			accountperiod: period
		});
	}
	//会计期间月  change事件
	function monthPickedFunc(){
		var period=$('#year').val()+'-'+$('#month').val();
		$('#gridlist').datagrid('load',{
			accountperiod: period
		});
	}
	//入库日期选择，只能选择当前期间内的日期
	function voucherPickedFunc(){
		debugger
		var make_date=$('#make_date').val().substring(0,7);
		var accountperiod=$('#accountperiod').val();
		if(accountperiod!=make_date){
			$.messager.alert("提示","<span class='hintsp_w'>提示</span>只能选择当前期间内的日期","warn");
			$('#make_date').val('');
		}
	}
	//编辑后事件，计算
	function onAfterEdit_1(index,row,changes){
		debugger;
		var quantity=row.quantity || 0;
		var price=row.price || 0;
		var money=row.money || 0;
		if(!checkEm(changes.quantity || changes.price)){
			money=(parseFloat(quantity) * parseFloat(price)).toFixed(2);
		}
		if(!checkEm(changes.money)){
			if(quantity != 0){
				price=(parseFloat(money) / parseFloat(quantity)).toFixed(4);
			}
		}
		
		$('#purchaselistmx_gridlist').datagrid('updateRow',{
			index: index,
			row: {
				money: money,
				price: price,
				quantity : quantity
			}
		});
	}
	
	
	//编辑通用赋值前触发方法
	function beforeEditFun(node){
		
	}
	
	function afterEditFun(node){
		debugger
		$('#make_date1').val(node.make_date);
		$('#code1').val(node.code);
		$('#supplier_name').textbox("setValue",node.supplier_name);
		$('#purchaselistmx_gridlist').datagrid('options').editIndex = undefined;
		
		$("#code").textbox({"editable":false});
		$("#make_date").attr("disabled",true);
		$("#code").attr("disabled",true);
		
		var id=node.id;
		if(checkEm(id)){
			id=$("#id").val();
		}
		 $('#purchaselistmx_gridlist').datagrid('load',{
			 id:id
		 })
		 $("#purchaselistmx_gridlist").datagrid({
				columns:[[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'zt',title:'zt',hidden:true},
							{field:'rowid',title:'rowid',hidden:true},
							{field:'purchaselistid',title:'purchaselistid',hidden:true},
							{field:'type_name',title:'商品类别',width:220},
							{field:'goodsid',title:'商品id',hidden:true},
							{field:'goods_name',title:'商品名称',width:220,formatter:formatOper_goods},
							{field:'unit',title:'单位' ,width:100},
							{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:100},
							{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:100},
							{field:'money',title:'金额',editor:{type:'numberbox',options:{min:0,precision:2}},width:100,formatter:function(value,row,index){
								return formatMoney(value);
							}},
				         ]],
				         onClickCell: function(index, field){
							 
						 }
			 
		});
	}
	function onEndFunc (index,row,changes){
    	var editors = $('#purchaselistmx_gridlist').datagrid('getEditors', index);  
    	if(editors.length>0){
    		var footer=$('#purchaselistmx_gridlist').datagrid('getFooterRows'); 
    		var field=editors[0].field;
          	if(editors[0].field=='quantity' || editors[0].field=='price'){
        		footer[0][field] = calTotalByCol("#purchaselistmx_gridlist",field);
        		$('#purchaselistmx_gridlist').datagrid('reloadFooter',footer);
        	}
    	}

    }
	function editVoucher(){
		debugger
		var make_date=$('#make_date').val().substring(0,7);
		var accountperiod=$('#accountperiod').val();
		if(CompareDate(accountperiod,make_date)){
			$.messager.alert("提示","<span class='hintsp_w'>提示</span>凭证所属月份已经结账,不可修改","info");
			return;
		}
		
		$("#code").textbox({"editable":true});
		$("#make_date").attr("disabled",false);
		
			$("#purchaselistmx_gridlist").datagrid({
				columns:[[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'zt',title:'zt',hidden:true},
							{field:'rowid',title:'rowid',hidden:true},
							{field:'purchaselistid',title:'purchaselistid',hidden:true},
							{field:'type_name',title:'商品类别',width:220},
							{field:'goodsid',title:'商品id',hidden:true},
							{field:'goods_name',title:'商品名称',width:220,formatter:formatOper_goods},
							{field:'unit',title:'单位' ,width:100},
							{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:100},
							{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:100},
							{field:'money',title:'金额',editor:{type:'numberbox',options:{min:0,precision:2}},width:100,formatter:function(value,row,index){
								return formatMoney(value);
							}},
				         ]],
				         onAfterEdit:onAfterEdit_1 || function(){},
						 onClickCell: function(index, field){
						    	debugger;
								if ($("#purchaselistmx_gridlist").datagrid('options').editIndex == undefined){
									$("#purchaselistmx_gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
									$("#purchaselistmx_gridlist").datagrid('options').editIndex = index;
									var ed = $("#purchaselistmx_gridlist").datagrid('getEditor', {index:index,field:field});  
									if($(ed.target).next().find(".textbox-text").length>0){
										$(ed.target).next().find(".textbox-text").focus();
									}else{
										$(ed.target).focus();
									}
								}else if ($("#purchaselistmx_gridlist").datagrid('validateRow', $("#purchaselistmx_gridlist").datagrid('options').editIndex)){
									$("#purchaselistmx_gridlist").datagrid('endEdit', $("#purchaselistmx_gridlist").datagrid('options').editIndex);
									$("#purchaselistmx_gridlist").datagrid('selectRow', index).datagrid('editCell', {index:index,field:field});
									$("#purchaselistmx_gridlist").datagrid('options').editIndex = index;
									var ed = $("#purchaselistmx_gridlist").datagrid('getEditor', {index:index,field:field});
									if($(ed.target).next().find(".textbox-text").length>0){
										$(ed.target).next().find(".textbox-text").focus();
									}else{
										$(ed.target).focus();
									}
								}
							}
			})
	}
	//显示可编辑表格
	function afterAddFun(){
		
		$('#purchaselistmx_gridlist').datagrid('options').editIndex = undefined;
		
		$("#code").textbox({"editable":true});
		$("#make_date").attr("disabled",false);
		$("#attachcount").attr("disabled",false);
		$.ajax({
			url:getUrl('purchaselist/listPurchaselistmx?id='),
			async:false,
			success:function(data){
				if(!checkEm(data)){
					debugger
					$('#make_date').val(data.make_date);
					$("#accountperiod").val(data.accountperiod);
					if(!checkEm(data.code)){
						$('#code').textbox("setValue",data.code);
					}
					$('#purchaselistmx_gridlist').datagrid('loadData',{total:0,rows:[],footer:[]})
				}
			}
			
		})
	}

	//通用表单提交前触发方法
	function beforeSubmitFormFun(){
		//再次验证编号不能重复
		if($("#make_date").prop("disabled")){
			return false;
		}
		var obj=new Object();
		var code=$('#code').textbox("getValue");
		obj["code"]='purchaselist';
		obj["table"]='t_purchaselist';
		obj["id"]= $("#id").val();
		//验证凭证日期是否已经结账
		var make_date=$('#make_date').val().substring(0,7);
		var accountperiod=$('#accountperiod').val();
		if(CompareDate(accountperiod,make_date)){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>凭证所属月份已经结账！','warning'); 
			return false;
		}
		debugger
		//保存时如果有未关闭的编辑框   将编辑框关闭，否则取不到值
		var indexs=$('#purchaselistmx_gridlist').datagrid('getEditingRowIndexs');
		if(indexs.length>0){
			for(var i=0;i<indexs.length;i++){
				 $('#purchaselistmx_gridlist').datagrid('refreshRow', indexs[i]);
				$('#purchaselistmx_gridlist').datagrid('endEdit',indexs[i]);
			}
		}
		var data = $('#purchaselistmx_gridlist').datagrid('getData');
		var rows=data.rows;
		for(var i=0;i<rows.length;i++){
			//删除空行
			if(checkEm(rows[i].goodsid)){
				$('#purchaselistmx_gridlist').datagrid('deleteRow',i);
				i--;
				continue;
			}
		
		}
		
		if(rows.length==0){
			$.messager.alert('提示','<span class="hintsp_w">提示</span>入库明细不能为空！','warning');  
			return false;
		}

		var money_total=calTotalByCol('#purchaselistmx_gridlist','money');
		$("#total_money").val(money_total);
		$('#purchaselistmx').val(JSON.stringify(rows));
		return true;
	}
	
	//通用保存后触发方法
	function afterSaveFun(id){
		$('#id').val(id);
		$('#make_date1').val($("#make_date").val());
		$('#code1').val($("#code").textbox('getValue'));
		$("#code").textbox({"editable":false});
		$("#make_date").attr("disabled",true);
		$("#attachcount").attr("disabled",true);
		 $("#purchaselistmx_gridlist").datagrid('load',{id:id})  
		 $("#purchaselistmx_gridlist").datagrid({
				columns:[[
							{field:'id',title:'ID',width:100,hidden:true},
							{field:'zt',title:'zt',hidden:true},
							{field:'rowid',title:'rowid',hidden:true},
							{field:'purchaselistid',title:'purchaselistid',hidden:true},
							{field:'type_name',title:'商品类别',width:220},
							{field:'goodsid',title:'商品id',hidden:true},
							{field:'goods_name',title:'商品名称',width:220,formatter:formatOper_goods},
							{field:'unit',title:'单位' ,width:100},
							{field:'quantity',title:'数量',editor:{type:'numberbox',options:{min:0,precision:2}},width:100},
							{field:'price',title:'单价',editor:{type:'numberbox',options:{min:0,precision:4}},width:100},
							{field:'money',title:'金额',width:100,formatter:function(value,row,index){
								return formatMoney(value);
							}},
				         ]],
				         onClickCell: function(index, field){
							 
						 }
			 
		});
	}
	
	function closeOrDelete(operType){
		debugger
		var make_date=$('#make_date').val().substring(0,7);
		var accountperiod=$('#accountperiod').val();
		if(CompareDate(accountperiod,make_date)){
			$.messager.alert("提示","<span class='hintsp_w'>提示</span>凭证所属月份已经结账,不可修改","info");
			return;
		}
		var nodes=$('#gridlist').datagrid('getSelected');
		//var id= $("#id").val();
		var url='';
		if(operType=='关闭'){
			url='purchaselist/closeAll?closestatus=1';
		}else{
			url='purchaselist/delete';
		}
		if(!checkEm(nodes)){
			$.messager.confirm('提示', '<span class="hintsp_w">提示</span>确定是否删除该单据？', function(r){
				if (r){
					ButtonFun.closeAndRemoveFun(2,'#gridlist',url);
					ButtonFun.addFun({width:1000,height:392})
				}
			})
		}else{
			$.messager.alert('提示', '<span class="hintsp_w">提示</span>请先选择要操作的数据！','warning');
		}
	}
	
	
	
	//关闭弹出框
	function closeclearF(){
		$('#printedit_pageId').window('close');
	}

	function doSupplierSearch(value){
		$('#supplier_gridlist').datagrid('load',{
			q:value
		})
	}

// 编辑页面方法 end-->
	
	
	
			
//列表页面方法 start-->
		//双击列表
		function listDbClickFun(row){
			var obj=new Object();
			obj["renderid"]='#gridlist';
			obj["type"]=2;
			obj["width"]='1000px';
			obj["height"]='392px';
			ButtonFun.editFun(obj);
		}
		//点击查看按钮
		function editrow(row,index,renderid){
			debugger;
			$('#'+renderid).datagrid('selectRow',index);
			var obj=new Object();
			obj["renderid"]='#'+renderid;
			obj["type"]=2;
			obj["width"]='1000px';
			obj["height"]='392px';
			ButtonFun.editFun(obj);
		}
		//点击核销按钮
		function writeoffrow(row,index){
			$('#gridlist').datagrid('clearSelections'); 
			$('#gridlist').datagrid('selectRow',index);
			var obj=new Object();
			obj["renderid"]='#gridlist';
			obj["type"]=2;
			obj["width"]='1000px';
			obj["height"]='492px';
			ButtonFun.writeOffFun(obj);
		}
		function beforeWriteOffFun(node){
			
		}
		
		function afterWriteOffFun(node){
			debugger
			var id=node.id;
			if(checkEm(id)){
				id=$("#id").val();
			}
			$('#id1').val(id);
			var supplierid = node.supplierid;
			if(checkEm(supplierid)){
				supplierid=$("#supplierid").val();
			}
			var accountperiod = $("#accountperiod").val();
			$('#supplierid1').val(node.supplierid);
			$('#supplier_name1').textbox("setValue",node.supplier_name);
			 $('#purchaselist_gridlist').datagrid('load',{
				 id:id
			 })
			// $('#invoicelist_gridlist').datagrid('loadData',{total:0,rows:[],footer:[]})
			$('#invoicelist_gridlist').datagrid('load',{
				 id:id
			 }) 
		}
		//点击删除按钮
		//删除行
		function deleterow(target,index){
			debugger
			var row = $('#gridlist').datagrid('getRows')[index];
			if(checkEm(row.id)){
				$('#gridlist').datagrid('deleteRow', index);
				$('#gridlist').datagrid('clearSelections');
			}else{
				$.messager.confirm('提示','<span class="hintsp_e">提示</span>确定要删除一条数据吗?',function(r){
					if (r){
						$.ajax({
						    url: getUrl('purchaselist/delete'),
						    type: 'post', async: false, data: {id:row.id}, dataType:'json',
						    success:function(data){
					   			if(data.state == 'success'){
					    			promptbox('success','删除成功！');
					    			$('#gridlist').datagrid('deleteRow', index);
					    			$('#gridlist').datagrid('clearSelections');
								}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
							}
					    });
					}
				});
			}
		}
	//列表页面方法end 
	//导出
function ExportExcel(){
	var obj=new Object;
	obj['renderid']='#gridlist';
	obj['title'] = '采购入库单';
	obj['controllername']='purchaselist';
	obj['cs'] = '';
	obj['pms'] = {accountperiod:$('#year').val()+'-'+$('#month').val(), status:$('#auditstatus').combobox('getValue')};
	toExcel(obj);
}
	
	
</script>
</body>
</html>