<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
</head>
<body style="background: #ebedf3;">

	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<!-- 顶部功能栏 -->
		<div id="eastToolDiv" data-options="region:'north'" style="background-color: #f4f4f4;
    background: -webkit-linear-gradient(top,#fff,#f4f4f4);
    background: -moz-linear-gradient(top,#fff,#f4f4f4);
    background: -o-linear-gradient(top,#fff,#f4f4f4);
    background: linear-gradient(to bottom,#fff,#f4f4f4);
    background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<span class="invo_title">资产状态</span>
			<select class="easyui-combobox" id="cardTypeSelect" data-options="editable:false,panelHeight:'auto'" style="width:100px">
				<option value="2">处置</option>
			</select>
			<a class="easyui-linkbutton xzadd_btns" href="javascript:void(0)" onclick="javascript:ButtonFun.addFun({width:720,height:485,title:'固定资产处置'})"></a>
			<a class="easyui-linkbutton ck_btns" href="javascript:void(0)" onclick="javascript:ButtonFun.editFun({type:2,renderid:'#gridlist',width:720,height:485})"><i></i></a>
			<a class="easyui-linkbutton scc_btns" href="javascript:void(0)" onclick="beforeVoucherRevoke({position:'#gridlist',code:'fixedassetsdispose'})"><i></i></a>
			<a class="easyui-linkbutton dc_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="ExportExcel()"><i></i></a>
			<a class="easyui-linkbutton gb_btns" style="margin: 9px 0 0 0;" href="javascript:void(0)" onclick="closeIndexTabs({title:'固定资产处置'})"><i></i></a>
			<p class="refreshbtns" id="refreshbtn" onclick="refresh()"></p>
		</div>
		
		<div style="margin: 54px 0 0 0;border:1px #6f9ec2 solid;" id="parcel_southToolDiv">
			<!-- 主面板 -->
		    <div id="gridlist" data-options="region:'center'" ></div>
		</div>
		
		<div id="edit_pageId" class="easyui-window" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false" style="display:none">
			<!-- 单据表单 -->
			<input type="hidden" id="year" id="year" value="${year}" />
			<input type="hidden" id="month" name="month" value="${month}" />
			<div class="parceldiv" style="height:401px">
				<div class="bgdivtitfixed" style="height:400px">
					<div class="parcelformt">
						<form id="pageForm" class="easyui-form" method="post" data-options="novalidate:true">
							<input type="hidden" id="id" name="id">
							<input type="hidden" id="voucher_id" name="voucher_id">
							<div class="card_top">
								<div class="card_title"><i></i><p>基本信息</p><em></em></div>
								<ul>
									<li>
										<p>资产编号：</p>
										<input class="easyui-textbox" id="code" name="code" data-options="required:true" />
									</li>
									<li>
										<p>资产名称：</p>
										<input class="easyui-textbox" id="name" name="name" data-options="readonly:true" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>入账时间：</p>
										<input class="easyui-textbox" id="entrytime" name="entrytime" data-options="readonly:true" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>资产原值：</p>
										<input class="easyui-numberbox" id="ori_value" name="ori_value" data-options="readonly:true,precision:2" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>使用期数：</p>
										<input class="easyui-numberbox" id="periods" name="periods" data-options="readonly:true,required:true" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>折旧方法：</p>
										<select class="easyui-combobox" id="dep_method" name="dep_method"  data-options="readonly:true,editable:false,panelHeight:'auto'" style="width: 123px;height:30px">
											<option value="0">平均年限法</option>
										</select>
									</li>
									<li>
										<p>预计残值：</p>
										<input class="easyui-numberbox" id="salvage" name="salvage" data-options="readonly:true,required:true,precision:2" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>月折旧额：</p>
										<input class="easyui-textbox" id="mon_dep_amount" name="mon_dep_amount" data-options="readonly:true" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>资产状态：</p>
										<select class="easyui-combobox" id="dispose_type" name="dispose_type" data-options="readonly:true,editable:false,panelHeight:'auto'" style="width: 123px;height:30px">
											<option value="0">在用</option>
											<option value="1">处置</option>
										</select>
									</li>
								</ul>
							</div>
					    	<div class="card_bott">
					    	    <div  class="card_title"><i></i><p>折旧信息</p><em></em></div>
					    	    <ul>
					    	       <li>
										<p>月折旧额：</p>
										<input class="easyui-textbox" id="mon_dep_amount2" name="mon_dep_amount2" data-options="readonly:true" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
					    	           <p>已折旧期数：</p>
					    	           <input class="easyui-numberbox" id="dep_ed_periods" name="dep_ed_periods" data-options="readonly:true,required:true" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
										<p>已折旧：</p>
										<input class="easyui-textbox" id="dep_ed" name="dep_ed" data-options="readonly:true" style="width: 120px;height:30px"/>
					    	       </li>
					    	       <li>
					    	           <p>资产净值：</p>
					    	           <input class="easyui-textbox" id="net_value" name="net_value" data-options="readonly:true" style="width: 120px;height:30px"/>
					    	       </li>
					    	    </ul>
					    	</div>
							<div class="card_bott" >
								<div class="card_title"><i></i><p>处置信息</p><em></em></div>
								<ul>
									<li>
										<p>处置日期：</p>
										<input class="easyui-datebox" id="dispose_date" name="dispose_date" data-options="required:true,validType:['period']" style="width:120px"/>
									</li>
									<li>
										<p>处置收益：</p>
										<input class="easyui-numberbox" id="dispose_profit" name="dispose_profit" data-options="required:true,precision:2,min:0,value:0,onChange:profitCal" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>收益科目：</p>
										<input type="hidden" id="profit_accsubjects_id" name="profit_accsubjects_id"/>
										<input class="easyui-textbox" id="profit_accsubjects_name" name="profit_accsubjects_name" data-options="required:true,readonly:true" style="width: 114px;height:30px; padding:0 24px 0 2px;"/>
										<div class="kjkm_btn" id="profit_accsubjects_btn" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;profit_accsubjects_id:id,profit_accsubjects_name:name')"></div>
									</li>
									<li>
										<p>处置净损益：</p>
										<input class="easyui-textbox" id="dispose_net" name="dispose_net" data-options="readonly:true" style="width: 120px;height:30px"/>
									</li>
									<li>
										<p>损益科目：</p>
										<input type="hidden" id="loss_accsubjects_id" name="loss_accsubjects_id"/>
										<input class="easyui-textbox" id="loss_accsubjects_name" name="loss_accsubjects_name" data-options="required:true,readonly:true" style="width: 114px;height:30px; padding:0 24px 0 2px;"/>
										<div class="kjkm_btn" id="loss_accsubjects_btn" onclick="javascript:Dialog.archives_accsubjects('1;pageForm;loss_accsubjects_id:id,loss_accsubjects_name:name')"></div>
									</li>
								</ul>
							</div>
						</form>
					</div>	
				</div>
			</div>
			<div style="text-align:center;height: 41px;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
				<div style="float: left; padding: 6px 0 0 0;" class="btnsdiv">
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="revokeVoucherBtn" onclick="beforeVoucherRevoke({code:'fixedassetsdispose'})">撤账</a>
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="createVoucherBtn" onclick="beforeVoucherCreate({code:'fixedassetsdispose'})">生成凭证</a>
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="deleteDisposeBtn" onclick="deleteDispose()">删除</a>
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="editBtn" onclick="editData()">修改</a>
					<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn" id="saveBtn" onclick="submitForm({code:'fixedassetsdispose',type:2,renderid:'#gridlist',noClose:false})">保存</a>
				</div>
				<a href="javascript:void(0)" class="easyui-linkbutton sure-btn closeNew_btn" onclick="clearForm()">关闭</a>
		    </div>
		</div>
		
	</div>

<script type="text/javascript">
$(function(){
	
	//资产编号选择框
	var fcObj = new Object();
	fcObj["cgId"] = 'code';
	fcObj["url"] = 'fixedassetscard/list';
	fcObj["idColName"] = 'code';
	fcObj["textColName"] = 'code';
	fcObj["panelWidth"] = 600;
	fcObj["panelHeight"] = 200;
	fcObj["cgColumns"] =[[	
							{field:'id',title:'id',hidden:true},
							{field:'code',title:'固定资产编号',align: 'left', halign: 'center',width:w1*2},
							{field:'name',title:'资产名称',align: 'left', halign: 'center',width:w1*3},
							{field:'entrytime',title:'入账时间',align: 'left', halign: 'center',width:w1*2},
							{field:'ori_value',title:'资产原值',align: 'right', halign: 'center',width:w1*3,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							},
							{field:'periods',title:'使用期数',align: 'right', halign: 'center',width:w1*2},
							{field:'dep_method',title:'折旧方法',align: 'right', halign: 'center',width:w1*2,
								formatter:function(value){
									var arr = [{label: '平均年限法', value: 0}];
									for(var i=0; i<arr.length; i++){
										if (arr[i].value == value){
											return arr[i].label;
										}
									}
									return value;
								}
							},
							{field:'salvage',title:'预计残值',align: 'right', halign: 'center',width:w1*2,sum:true,
								formatter:function(value,row,index){
									return formatMoney(value);
						    	}
							},
							{field:'mon_dep_amount',title:'月折旧额',align: 'right', halign: 'center',width:w1*2,sum:true,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							},
							{field:'dep_ed_periods',title:'已折旧期数',align: 'right', halign: 'center',width:w1*2},
							{field:'dep_ed',title:'已折旧',align: 'right', halign: 'center',width:w1*3,sum:true,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							},
							{field:'net_value',title:'资产净值',align: 'right', halign: 'center',width:w1*3-1,sum:true,
								formatter:function(value,row,index){
									return formatMoney(value);
								}
							}
						]];
	Dialog.archives_selector_list(fcObj);
	
	$('#code').combogrid({onSelect: 
		function (index, row){
			$('#pageForm').form('load', row);
		}
	});
	
	$('#code').combogrid({onChange: 
		function (newValue, oldValue){
			if(!checkEm(newValue)){
				var rows = $($('#code').combogrid('grid')).datagrid('getRows');
				for(var i in rows){
					if(rows[i].code == newValue || rows[i].name == newValue){
						$($('#code').combogrid('grid')).datagrid('selectRow', i);
						return;
					}
				}
			}else{
				$('#pageForm').form('reset');
			}
		}
	});
	
	var obj = new Object();
	var width = $(window).width()-30;
	var w1 = width/30;
	var heights = $(window).height();
	$("#gridlist").height(heights-55);
	obj["position"] = '#gridlist';
	obj["idField"] = 'id';
	obj["url"] = 'fixedassetsdispose/list';
	obj["columns"] = [[
						{field:'id',title:'id',hidden:true},
						{field:'code',title:'固定资产编号',align: 'left', halign: 'center',width:w1*2,total:true},
						{field:'name',title:'资产名称',align: 'left', halign: 'center',width:w1*3},
						{field:'entrytime',title:'入账时间',align: 'left', halign: 'center',width:w1*2},
						{field:'ori_value',title:'资产原值',align: 'right', halign: 'center',width:w1*3,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
						    }
						},
						{field:'periods',title:'使用期数',align: 'right', halign: 'center',width:w1*2},
						{field:'dep_ed_periods',title:'已折旧期数',align: 'right', halign: 'center',width:w1*2},
						{field:'dep_ed',title:'已折旧金额',align: 'right', halign: 'center',width:w1*3,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'net_value',title:'资产净值',align: 'right', halign: 'center',width:w1*3-1,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'dispose_profit',title:'处置收益',align: 'right', halign: 'center',width:w1*2,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'dispose_net',title:'处置净损益',align: 'right', halign: 'center',width:w1*2,sum:true,
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'dispose_date',title:'处置日期',align: 'right', halign: 'center',width:w1*2},
						{field:'voucher_id',title:'voucher_id',hidden:true},
						{field:'voucher_number',title:'凭证号码',align: 'left', halign: 'center',width:w1*2},
						{field:'disposeby_id',title:'disposeby_id',hidden:true},
						{field:'disposeby_name',title:'经手人',align: 'left', halign: 'center',width:w1*2},
					]];
	obj["listDbClickFun"] = listDbClickFun;
	obj["showFooter"] = true;
	Grid.list_grid(obj);
	
	$('#gridlist').datagrid({onLoadSuccess: 
		function (data){
            $('#gridlist').datagrid('statistics'); //合计
		}
	});
	
});

//双击行的触发事件
function listDbClickFun(row){
	$('#pageForm').form('load', row);
	$('#edit_pageId').window({title:'编辑'});
	$('#edit_pageId').window('open');
	
	//双击编辑时控制btn
	disableControl(['#saveBtn']);
	enableControl(['#editBtn','#deleteDisposeBtn']);
	if(checkEm(row.voucher_id)){
		disableControl(['#revokeVoucherBtn']);
		enableControl(['#createVoucherBtn']);
	}else{
		enableControl(['#revokeVoucherBtn']);
		disableControl(['#createVoucherBtn']);
	}
	
	//双击编辑时控制控件
	cannotEditable();
}

//编辑页面 可编辑状态
function canEditable(){
	$('#code').textbox('readonly',false);
	$('#dispose_date').textbox('readonly',false);
	$('#dispose_profit').datebox('readonly',false);
	$('#profit_accsubjects_btn').show();
	$('#loss_accsubjects_btn').show();
	
	//如果处置收益不为0，则收益科目必填
	var dispose_profit = $('#dispose_profit').numberbox("getValue");	//处置收益
	if(!checkEm(dispose_profit) && dispose_profit != 0){
		$('#profit_accsubjects_name').textbox('enable');
		$('#profit_accsubjects_btn').show();
	}else if(dispose_profit == 0){
		$('#profit_accsubjects_name').textbox('disable');
		$('#profit_accsubjects_btn').hide();
	}
	//如果处置净损益不为0，则损益科目必填
	var dispose_net = $('#dispose_net').numberbox("getValue");	//处置收益
	if(!checkEm(dispose_net) && dispose_net != 0){
		$('#loss_accsubjects_name').textbox('enable');
		$('#loss_accsubjects_btn').show();
	}else if(dispose_profit == 0){
		$('#loss_accsubjects_name').textbox('disable');
		$('#loss_accsubjects_btn').hide();
	}
}

//编辑页面 不可编辑状态
function cannotEditable(){
	$('#code').textbox('readonly',true);
	$('#dispose_date').textbox('readonly',true);
	$('#dispose_profit').datebox('readonly',true);
	$('#profit_accsubjects_btn').hide();
	$('#loss_accsubjects_btn').hide();
}

//新增按钮点击后通用触发方法
function afterAddFun(){
	//点击新增时控制btn
	enableControl(['#saveBtn']);
	disableControl(['#editBtn','#deleteDisposeBtn','#createVoucherBtn','#revokeVoucherBtn']);
	
	//点击新增时控制控件
	canEditable();
}

//编辑通用赋值前触发方法
function beforeEditFun(node){
	//点击编辑时控制btn
	disableControl(['#saveBtn']);
	enableControl(['#editBtn','#deleteDisposeBtn']);
	
	if(checkEm(node.voucher_id)){
		disableControl(['#revokeVoucherBtn']);
		enableControl(['#createVoucherBtn']);
	}else{
		enableControl(['#revokeVoucherBtn']);
		disableControl(['#createVoucherBtn']);
	}
	
	//点击编辑时控制控件
	cannotEditable();
}

//编辑通用赋值后触发方法
function afterEditFun(node){}
//通用表单提交前触发方法
function beforeSubmitFormFun(){return true;}

//通用保存后触发方法
function afterSaveFun(id){
	//保存后时控制btn
	disableControl(['#saveBtn','#revokeVoucherBtn']);
	enableControl(['#editBtn','#deleteDisposeBtn','#createVoucherBtn']);
	
	//保存后控制控件
	cannotEditable();
}

//通用撤账后触发方法
function afterVoucherRevokeFun(){
	var id = $('#id').val();
	$('#pageForm').form('load',getUrl('fixedassetsdispose/findById?id='+id));
	disableControl(['#revokeVoucherBtn']);
	enableControl(['#createVoucherBtn']);
}

//通用生成凭证后触发方法
function afterVoucherCreateFun(){
	var id = $('#id').val();
	$('#pageForm').form('load',getUrl('fixedassetsdispose/findById?id='+id));
	disableControl(['#createVoucherBtn']);
	enableControl(['#revokeVoucherBtn']);
}

//计算已折旧、资产净值
function depEdCal(){
	var ori_value = $('#ori_value').numberbox("getValue");		//资产原值
	var periods = $('#periods').numberbox("getValue");			//使用期数
	var salvage = $('#salvage').numberbox("getValue");			//预计残值
	var dep_ed_periods = $('#dep_ed_periods').numberbox("getValue");	//已折旧期数
	if(!checkEm(ori_value) && !checkEm(periods) && !checkEm(salvage) && !checkEm(dep_ed_periods)){
		var dep_ed = ((( ori_value - salvage ) / periods) * dep_ed_periods).toFixed(2);	//已折旧 = 月折旧额 * 已折旧期数
		$('#dep_ed').textbox('setValue', dep_ed.toFixed(2));
		var net_value = ori_value - dep_ed;			//资产净值 = 资产原值 - 已折旧
		$('#net_value').textbox('setValue', net_value.toFixed(2));
	}
}

//计算处置净损益
function profitCal(){
	var ori_value = $('#ori_value').numberbox("getValue");		//资产原值
	var periods = $('#periods').numberbox("getValue");			//使用期数
	var salvage = $('#salvage').numberbox("getValue");			//预计残值
	
	var dep_ed = $('#dep_ed').val();
	var dep_ed_periods = $('#dep_ed_periods').numberbox("getValue");	//已折旧期数
	var dispose_profit = $('#dispose_profit').numberbox("getValue");	//处置收益
	//如果处置收益不为0，则收益科目必填
	if(!checkEm(dispose_profit) && dispose_profit != 0){
		$('#profit_accsubjects_name').textbox('enable');
		$('#profit_accsubjects_btn').show();
	}else if(dispose_profit == 0){
		$('#profit_accsubjects_id').val('');
		$('#profit_accsubjects_name').textbox('setValue', '');
		$('#profit_accsubjects_name').textbox('disable');
		$('#profit_accsubjects_btn').hide();
	}
	debugger
	if(!checkEm(ori_value) && !checkEm(periods) && !checkEm(salvage) && !checkEm(dep_ed_periods) && !checkEm(dispose_profit)){
		//var dep_ed = ((( parseFloat(ori_value) - parseFloat(salvage) ) / parseFloat(periods)) * parseFloat(dep_ed_periods)).toFixed(4);	//已折旧 = 月折旧额 * 已折旧期数
		var dispose_net = (parseFloat(dispose_profit) - (parseFloat(ori_value) - parseFloat(dep_ed))).toFixed(4);			//处置净损益 = 处置收益-（资产原值-已折旧）
		$('#dispose_net').textbox('setValue', parseFloat(dispose_net).toFixed(2));
		//如果处置净损益不为0，则损益科目必填
		if(!checkEm(dispose_net) && dispose_net != 0){
			$('#loss_accsubjects_name').textbox('enable');
			$('#loss_accsubjects_btn').show();
		}else if(dispose_profit == 0){
			$('#loss_accsubjects_id').val('');
			$('#loss_accsubjects_name').textbox('setValue', '');
			$('#loss_accsubjects_name').textbox('disable');
			$('#loss_accsubjects_btn').hide();
		}
	}
	
}

//开启修改
function editData(){
	var voucher_id = $('#voucher_id').val();
	//1、所选择的单据的处置日期是否已生成凭证，如果是则提示：当前处置单据已生成凭证，不可修改
	if( !checkEm(voucher_id) ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前处置单据已生成凭证，不可修改！','error');
		return;
	}
	//2、所选择的单据的处置日期是否为记账月份前的日期，如果是则提示：当前处置单据的处置日期已结账，不可修改
	var dispose_date = $('#dispose_date').datebox('getValue');
	var accinfo = findAccInfo();
	if( dispose_date < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前处置单据的处置日期（'+dispose_date+'）已结账，不可修改！','error');
		return;
	}
	
	//点击修改时控制btn
	enableControl(['#saveBtn','#deleteDisposeBtn']);
	disableControl(['#editBtn']);
	if(checkEm($('#voucher_id').val())){
		disableControl(['#revokeVoucherBtn']);
		enableControl(['#createVoucherBtn']);
	}else{
		enableControl(['#revokeVoucherBtn']);
		disableControl(['#createVoucherBtn']);
	}
	
	//点击修改时控制控件
	canEditable();
}

//处置删除
function deleteDispose(){
	var voucher_id = $('#voucher_id').val();
	//1、所选择的单据的处置日期是否已生成凭证，如果是则提示：当前处置单据已生成凭证，不可删除
	if( !checkEm(voucher_id) ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前处置单据已生成凭证，不可删除！','error');
		return;
	}
	//2、所选择的单据的处置日期是否为记账月份前的日期，如果是则提示：当前处置单据的处置日期已结账，不可删除
	var dispose_date = $('#dispose_date').datebox('getValue');
	var accinfo = findAccInfo();
	if( dispose_date < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>当前处置单据的处置日期（'+dispose_date+'）已结账，不可删除！','error');
		return;
	}
	$.messager.confirm('提示','<span class="hintsp_w">提示</span>确定要删除吗?',function(r){
		if (r){
			$.ajax({
			    url: getUrl('fixedassetsdispose/deleteDispose'),
			    type: 'post', async: false, data: {id:$('#id').val()}, dataType:'json',
			    success:function(data){
		   			if(data.state == 'success'){
		    			/* $.messager.show({title:'提示',msg:'<span class="hintsp_w">提示</span>删除成功！',showType:'slide',timeout:1000,style:{
		    				right : '', bottom : '',
		    				top : document.body.scrollTop + document.documentElement.scrollTop
		    			}}); */
		    			promptbox('success','删除成功！');
		    			$('#gridlist').datagrid('reload');
		    			$('#edit_pageId').window('close');
					}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
				}
		    });
		}
	});
}

/**
 * 生成凭证前验证
 */
function beforeVoucherCreate(obj){
	//1、所选择的明细的记账日期是否为记账月份前的日期，如果是则提示：所选明细的记账日期已结账，不可生成凭证
	var dispose_date = $('#dispose_date').datebox('getValue');
	var accinfo = findAccInfo();
	if( dispose_date < accinfo.periodofaccount ){
		$.messager.alert('提示','<span class="hintsp_e">提示</span>记账日期（'+dispose_date+'）已结账，不可生成凭证！','error');
		return;
	}
	
	voucherCreate(obj);		//生成凭证
}

/**
 * 撤账前验证
 */
function beforeVoucherRevoke(obj){
	voucherRevoke(obj);		//撤账
}

//导出
function ExportExcel(){
	var obj=new Object;
	obj['renderid'] = '#gridlist';
	obj['title'] = '固定资产处置';
	obj['controllername'] ='fixedAssetsDispose';
	obj['cs'] = '';
	toExcel(obj);
}

</script>

</body>
</html>