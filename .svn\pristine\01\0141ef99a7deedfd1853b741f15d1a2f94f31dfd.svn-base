package org.newstanding.common.utils.sql;

import org.newstanding.common.utils.ReflectHelper;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLJoinTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelect;
import com.alibaba.druid.sql.ast.statement.SQLSelectItem;
import com.alibaba.druid.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.ast.statement.SQLSubqueryTableSource;
import com.alibaba.druid.sql.dialect.mysql.parser.MySqlStatementParser;
import com.alibaba.druid.sql.visitor.SQLASTOutputVisitor;
import com.alibaba.druid.util.JdbcUtils;

public class SQLParseUtils {

	public static String testsql(String sql) throws SecurityException, NoSuchFieldException, IllegalArgumentException, IllegalAccessException {
        //使用mysql解析  
		/*	if(sql.indexOf("union") !=-1){
			sql=sql.split("union")[0];
		}*/
        MySqlStatementParser sqlStatementParser = new MySqlStatementParser(sql) ;  
        //解析select查询  
        SQLSelectStatement sqlStatement = (SQLSelectStatement) sqlStatementParser.parseSelect() ;  
        SQLSelect sqlSelect = sqlStatement.getSelect() ;  
        //获取sql查询块  
        SQLSelectQueryBlock sqlSelectQuery = (SQLSelectQueryBlock)sqlSelect.getQuery() ;  
        return querySql(sqlSelectQuery);
        
	}
	
	public static String querySql(SQLSelectQueryBlock sqlSelectQuery) throws SecurityException, NoSuchFieldException, IllegalArgumentException, IllegalAccessException {
		StringBuffer out = new StringBuffer() ;  
        //创建sql解析的标准化输出  
        SQLASTOutputVisitor sqlastOutputVisitor = SQLUtils.createFormatOutputVisitor(out , null , JdbcUtils.MYSQL) ;  
  
        //解析select项  
        out.delete(0, out.length()) ;  
        for (SQLSelectItem sqlSelectItem : sqlSelectQuery.getSelectList()) {  
            if(out.length()>0){  
                out.append("~") ;  
            }  
            sqlSelectItem.accept(sqlastOutputVisitor);  
        }  
        return out.toString().replaceAll("[\\t\\n\\r]", "");
	}
	
	public static Object queryLeftSql(SQLJoinTableSource joinTableSource,String table_name) throws SecurityException, NoSuchFieldException, IllegalArgumentException, IllegalAccessException {

		Object left = null,right;
		
		while (joinTableSource != null) {
			right = joinTableSource.getRight();
			getAlais(right,table_name);
			
			if (joinTableSource.getLeft() instanceof SQLJoinTableSource) {
				joinTableSource = (SQLJoinTableSource) joinTableSource.getLeft();
			}else{
				left = joinTableSource.getLeft();
				joinTableSource = null;
			}
		}
		
		if (!(left instanceof SQLJoinTableSource)) {
			getAlais(left,table_name);
		}
		
		return left;
	}
	
	public static void getAlais(Object right,String table_name) throws SecurityException, NoSuchFieldException, IllegalArgumentException, IllegalAccessException {
		if (right instanceof SQLExprTableSource) {
			SQLExprTableSource rightTableSource = (SQLExprTableSource) right;
			//如果存在当前table 表名
			if (rightTableSource.toString().equals(table_name)) {
				//System.out.println(table_name);
				Object parent = rightTableSource.getParent();
				while(parent instanceof SQLJoinTableSource) {
					SQLJoinTableSource parentTableSource = (SQLJoinTableSource) parent;
					parent = parentTableSource.getParent();
				}
			}
			if (rightTableSource.getAlias()== null) {
				System.err.println(rightTableSource);
			}else{
				System.err.println(rightTableSource.getAlias());
			}
		}else if (right instanceof SQLSubqueryTableSource) {
    		SQLSubqueryTableSource subqueryTableSource = (SQLSubqueryTableSource) right;
    		SQLSelect sqlSelect1 = (SQLSelect) ReflectHelper.getValueByFieldName(subqueryTableSource, "select");
    		SQLSelectQueryBlock queryBlock = (SQLSelectQueryBlock) ReflectHelper.getValueByFieldName(sqlSelect1, "query");
    		querySql(queryBlock);
    		System.err.println(subqueryTableSource.getAlias());
		}
	}
}
