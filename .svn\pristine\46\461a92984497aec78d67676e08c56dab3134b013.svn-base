package org.newstanding.controller.salary;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.newstanding.common.entity.Page;
import org.newstanding.common.entity.PageData;
import org.newstanding.common.utils.JsonUtils;
import org.newstanding.common.utils.date.DateUtil;
import org.newstanding.controller.base.BaseController;
import org.newstanding.service.invoicing.PurchaseInvoiceService;
import org.newstanding.service.salary.CalnorinsuranceService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;
/**
 * deptdoc
 * 
 * <AUTHOR>
 *
 */
@Controller
@RequestMapping(value = "calnorinsurance")
public class CalnorinsuranceController extends BaseController {
	@Resource(name = "calnorinsuranceService")
	private CalnorinsuranceService calnorinsuranceService;
	@Resource(name = "purchaseInvoiceService")
	private PurchaseInvoiceService purchaseInvoiceService;
	/**
	 * 新增
	 * 
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "/save")
	@ResponseBody
	public PageData save() throws Exception {
		PageData pd = new PageData();
		pd = this.getPageData();
		pd.put("createby", getCurrentUserByCatch().getUsername());
		return calnorinsuranceService.save(pd);
	}

	/**
	 * 删除
	 * 
	 * @throws Exception
	 */
	@RequestMapping(value = "/delete")
	@ResponseBody
	public Object delete() throws Exception {
		PageData pd = this.getPageData();
		return calnorinsuranceService.delete(pd);
	}

	/**
	 * 修改
	 */
	@RequestMapping(value = "/edit")
	@ResponseBody
	public Object edit() throws Exception {
		PageData pd = this.getPageData();
		return calnorinsuranceService.edit(pd);
	}
	/**
	 * 通过ID获取数据
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/findById")
	public Object findById() throws Exception {
		PageData pd = this.getPageData();
		PageData tmp = calnorinsuranceService.findById(pd);
		pd.putAll(tmp);
		if(pd.get("itemNames") ==null || "".equals(pd.get("itemNames"))){
			pd.put("itemNames",JsonUtils.PageDataToJSONArray(calnorinsuranceService.findItems(pd)));
		}
		//pd.put("itemNames",JsonUtils.PageDataToJSONArray(calnorinsuranceService.findItems(pd)));
		pd.put("accountperiod",calnorinsuranceService.getAccountperiod(pd));
		return pd;
	}
	/**
	 * 列表
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value = "/list")
	public Map<String, Object> list(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String accountperiod="";
		if(pd.get("accountperiod") == null){
			accountperiod = calnorinsuranceService.getAccountperiod(pd);
			pd.put("accountperiod", accountperiod);
			resultMap.put("year", accountperiod.split("-")[0]);
		}else{
			accountperiod = pd.get("accountperiod").toString();
			resultMap.put("year", accountperiod);
		}
		setPage(pd,page);
	
		page.setPd(pd);
		resultPageData =  calnorinsuranceService.list(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			for(PageData varPd : varList){
				//varPd.put("itemNames",JsonUtils.PageDataToJSONArray(calnorinsuranceService.findItems(pd)));
				if(varPd.get("itemNames") ==null || "".equals(varPd.get("itemNames"))){
					varPd.put("itemNames",JsonUtils.PageDataToJSONArray(calnorinsuranceService.findItems(pd)));
				}
				varPd.put("accountperiod",calnorinsuranceService.getAccountperiod(pd));
			}
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
			resultMap.put("accountperiod", calnorinsuranceService.getAccountperiod(pd));
			resultMap.put("itemNames",calnorinsuranceService.findItems(pd));
		}else{
			resultMap.putAll(resultPageData);
		}
		
		return resultMap;
	}

	/**
	 * 去列表页
	 * 
	 * @throws Exception
	 */
	@RequestMapping("/goList")
	public ModelAndView goList() throws Exception {
		ModelAndView mv = this.getModelAndView();
		mv.setViewName("system/salary/calnorinsurance");
		return mv;
	}

	/**
	 * 批量删除
	 * @throws Exception 
	 */
	@RequestMapping(value="/deleteAll")
	@ResponseBody
	public Object deleteAll() throws Exception {
		PageData pd = this.getPageData();
		String DATA_IDS = pd.getString("DATA_IDS");
		pd.put("table_name", "c_calnorinsurance");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = calnorinsuranceService.deleteAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要删除的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 验证重复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/checkRepeatByParam")
	public boolean checkRepeatByParam() throws Exception {
		PageData pd = this.getPageData();
		if( calnorinsuranceService.checkRepeatByParam2(pd) ){
			return false;
		}
		return true;
	}
	
	/**
	 * 批量关闭、恢复
	 * @throws Exception 
	 */
	@ResponseBody
	@RequestMapping(value="/closeAll")
	public Object closeAll() throws Exception {
		PageData pd = this.getPageData();
		pd.put("closeby", getCurrentUserByCatch().getUsername());
		pd.put("table_name", "c_calnorinsurance");
		String DATA_IDS = pd.getString("DATA_IDS");
		if(DATA_IDS !=null && !"".equals(DATA_IDS)){
			String ArrayDATA_IDS[] = DATA_IDS.split(",");
			pd.put("array", ArrayDATA_IDS);
			pd = calnorinsuranceService.closeAll(pd);
		}else{
			pd.put("state", "error");
			pd.put("message", "没有获取到需要关闭的数据,请检查！");
		}
		return pd;
	}
	
	/**
	 * 搜索 导出 查询 部门列表
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/allList")
	public Object allList(Page page) throws Exception {
		PageData pd = this.getPageData(),resultPageData = null;
		setPage(pd,page);
		Map<String, Object> resultMap = new HashMap<String, Object>();
		resultPageData = calnorinsuranceService.allList(page);
		List<PageData> varList = null;
		if (resultPageData.get("state")!= null && resultPageData.get("state").equals("success")) {
			varList = (List<PageData>) resultPageData.get("list");
			resultMap.put("total", page.getTotalResult());
			resultMap.put("rows", varList);
		}else{
			resultMap.putAll(resultPageData);
		}
		return resultMap;
	}
	
	/**
	 * 新增时获取  保险月份  制单人 等信息
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value="/findMaxCodeAndEtc")
	public Object findMaxCodeAndEtc() throws Exception {
		PageData pd = this.getPageData();
		SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM");
		pd.put("createby", getCurrentUserByCatch().getUsername());
		PageData monthPd = calnorinsuranceService.findMaxMonth(pd);
		String insurmonth="";
		if(monthPd != null){
			String yearmonth=monthPd.get("insurancemonth").toString();
			insurmonth= DateUtil.addMonth(yearmonth, 1);
		}else{
			insurmonth=sdf.format(new Date());
		}
		pd.put("accountperiod", calnorinsuranceService.getAccountperiod(pd));
		pd.put("insurancemonth",insurmonth);
		pd.put("createdate", DateUtil.getMaxDayOfMonth(calnorinsuranceService.getAccountperiod(pd)));
		return pd;
	}
	/**
	 * 计算社保
	 * @param page
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value = "/calinsurance")
	public PageData calucationvatax() throws Exception {
		PageData pd = this.getPageData();
		 PageData  resPd =  calnorinsuranceService.calinsurance(pd);
		 resPd.put("itemNames",calnorinsuranceService.findItems(pd));
		return resPd;
	}

	/**
	 * 检查人员下是否被使用
	 * @return
	 * @throws Exception
	 */
	@ResponseBody
	@RequestMapping(value="/checkIsUsed")
	public Object checkSubAndChild() throws Exception {
		PageData pd = this.getPageData();
		List<PageData> list = calnorinsuranceService.checkIsUsed(pd);
		if(list.size()>0){
			pd.put("state", "error");
		}else{
			pd.put("state", "success");
		}
		return pd;
	}
	/**
	 * 生成凭证
	 */
	@ResponseBody
	@RequestMapping(value = "/createvoucher")
	public PageData createvoucher() throws Exception {
		PageData pd = this.getPageData();
		 PageData  resPd =  calnorinsuranceService.createVoucher(pd);
		return resPd;
	}
	/**
	 * 导入
	 * @param file
	 * @param templet
	 * @return
	 */
	@RequestMapping(value="/importExcel")
	@ResponseBody
	public PageData importExcel(@RequestParam(value="file_name") MultipartFile file) {
		PageData pd = this.getPageData();
		PageData resPd=new PageData();
		try {
			pd.put("code", "calnorinsurance");
			resPd = calnorinsuranceService.importExc(pd,file);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return resPd;
	}
	
}




