<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Load Dynamic ComboBox Data - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Load Dynamic ComboBox Data</h2>
	<p>Click the button below to load data.</p>
	
	<div style="margin:20px 0;">
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#language').combobox('reload', 'combobox_data1.json')">LoadData</a>
	</div>

	<input class="easyui-combobox" id="language" name="language"
			data-options="valueField:'id',textField:'text'">
</body>
</html>