<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<base href="<%=basePath%>">
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<%@ include file="../../public/common_css_js.jspf"%>
<link rel="stylesheet" type="text/css" href="<%=basePath%>static/css/tabcontrol.css">
</head>
<body style="background: #ebedf3;">
	<div id="panelDiv" class="easyui-layout" data-options="fit:true">
		<!-- 顶部功能栏 -->
		<div id="eastToolDiv" data-options="region:'north'" style="background-color: #f4f4f4; background: -webkit-linear-gradient(top,#fff,#f4f4f4);background: -moz-linear-gradient(top,#fff,#f4f4f4);background: -o-linear-gradient(top,#fff,#f4f4f4);
			background: linear-gradient(to bottom,#fff,#f4f4f4); background-repeat: repeat-x;height:54px;border:1px #cedae4 solid;border-bottom: 4px #43d5ca solid;box-shadow: rgba(0,0,0,0.6) 0 -2px 10px; margin: 0 0 7px 0;border-top: 0;">
			<a class="easyui-linkbutton jz_btns" style="margin: 8px 0 0 0;color: #6d62af;" id="tallyBtn" href="javascript:void(0)" onclick="checkInitialbalance({position:'#gridlist',code:'initialbalance',estatus:'1'})"><i></i></a>
			<a class="easyui-linkbutton cz_btns" style="margin: 8px 0 0 0;color: #6d62af;" id="untallyBtn" href="javascript:void(0)" onclick="checkInitialbalance({position:'#gridlist',code:'initialbalance',estatus:'0'})"><i></i></a>
			<a class="easyui-linkbutton dc_btns" style="margin: 8px 0 0 0;color: #6d62af;" href="javascript:void(0)" onclick="beforeVoucherRevoke({position:'#gridlist',code:'initialbalance'})"><i></i></a>
			<a class="easyui-linkbutton gb_btns" style="margin: 8px 0 0 0;" href="javascript:void(0)" onclick="closeIndexTabs({title:'期初余额'})"><i></i></a>
		    <p class="refreshbtns" id="refreshbtn" onclick="refresh()"></p>
		</div>
		
		<!-- 主面板 -->
		<div style="margin: 54px 0 0 0;border:1px #cedae4 solid;" id="parcel_southToolDiv">
			<input type="hidden" id="estatus">
			<input type="hidden" id="isaccount">
		    <div id="gridlist" data-options="region:'center'" ></div>
		</div>
		
		<!-- 编辑弹出框 -->
		<div id="edit_panel" class="easyui-window" style="width: 400px;height:200px;display:none" title="期初余额" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="easyui-layout" data-options="fit:true">
				<div id="eastToolDiv" data-options="region:'east'" style="width:93px;border-right: 1px #cedae4 solid;background: #fff;">
		
					<!-- 左侧功能栏 -->
					<a class="easyui-linkbutton add-btncssbg" href="javascript:void(0)" onclick="javascript:addrow()">新增</a>
					<a class="easyui-linkbutton delete-btncssbg" href="javascript:void(0)" onclick="javascript:removerow()">删除</a>
					<a class="easyui-linkbutton  dr-btncssbg" href="javascript:void(0)" onclick="$('#import_panel').window('open')">导入</a>
					<a class="easyui-linkbutton close-btncssbg" href="javascript:void(0)" onclick="javascript:ButtonFun.findFun()">查找</a>
				</div>
				<div class="parceldivs" style="border:0">
					<div class="parceldiv_tow" style="width: 505px;height: 317px;">
						<div id="editgridlist" data-options="region:'center'" style="width: 505px;height: 319px;margin:13px 0 0 19px; overflow: auto;overflow-x: hidden;"></div>
					</div>
				</div>
				<div id="southToolDiv" data-options="region:'south'" style="height:42px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
					<!-- 底部功能栏 -->
			    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" onclick="$('#edit_panel').window('close')">取消</a>
			    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn"  onclick="saveInitialBalancemx()">确定</a>
				</div>
			</div>

		</div>
		
		<!-- 导入弹出框 -->
		<div id="import_panel" class="easyui-window" style="width: 400px;height:200px;display:none" title="导入" data-options="closed:true,collapsible:false,minimizable:false,maximizable:false">
			<div class="bgdivtit" style="height: 117px;border: 0;">
				<form id="importFileForm" method="post" enctype="multipart/form-data" data-options="novalidate:true">
					<input type="hidden" id="type" name="type" >
					<ul class="tolead_ul">
					    <li>
					    	<p>选择文件：</p>
					        <input class="easyui-filebox" id="file_name" name="file_name" data-options="buttonText:'浏览'" style="width:200px;height:27px">
					    </li>
					</ul>
				</form>
			</div>
			<div style="height:43px;text-align:right;background:#ecf5fa;    border-radius: 0 0 5px 5px;">
		    	<a href="javascript:void(0)" class="easyui-linkbutton cancel-btn qxNew_btn" style="margin: 0px 13px 0 0;" onclick="$('#import_panel').window('close')">取消</a>
		    	<a href="javascript:void(0)" class="easyui-linkbutton sure-btn sureNew_btn" style="margin: 0px 13px 0 0;" onclick="importExcel()">确定</a>
		    </div>
		</div>
		
	</div>

<script type="text/javascript">
var width = $(window).width()-60;
var w1 = width/30;
var heights = $(window).height();
var mxData = {},mainIndex,pre_val='';

$(function(){
	$("#parcel_southToolDiv").height(heights-54);
	$("#gridlist").height(heights-54);
	//$("#gridlist").width(w1*14+70);
	var obj = new Object();
	obj["position"] = '#gridlist';
	obj["idField"] = 'id';
	obj["url"] = 'initialbalance/list';
	obj["pagination"] = false;
	obj["columns"] = [[ 
						{field:'',checkbox:true},
						{field:'accsubjects_code',title:'科目代码',align: 'left', halign: 'center',width:w1*3},
						{field:'accsubjects_name',title:'科目名称',align: 'left', halign: 'center',width:w1*4},
						{field:'sub_direction',title:'余额方向',align: 'left', halign: 'center',width:w1*2},
						{field:'initialbalance',title:'期初余额',align: 'right', halign: 'center',width:w1*3+5,
							editor:{type:'numberbox',
								options: {
									precision:2,
									onChange:sumParentJe
								}
							},
							formatter:function(value,row,index){
								return formatMoney(value);
							}
						},
						{field:'auxbus',title:'辅助核算',width:w1*2,align: 'center',
							formatter:function(value,row,index){
								var s='';
								if (value != null && value != ''){
									s = '<a href="javascript:void(0)" class="mxqd_btntx" style="margin: 4px;" onclick="showDoc('+index+',\''+value+'\','+row.id+')">'+value+'</a> ';
									return s;
								} else {
									return s = '';
								}
							}
						}
					]];
	
	//生成可编辑表格
	Grid.edit_row_grid(obj);
	//启动行编辑
	$('#gridlist').datagrid({
		onClickRow: function(index,row){
			var isaccount_1 = $('#isaccount').val();
			if(row.isEdit && isaccount_1==0){
				$(this).datagrid('beginEdit', index);
				var ed = $(this).datagrid('getEditor', {index:index,field:'initialbalance'});
				if($(ed.target).next().find("input").length>0){
					$(ed.target).next().find("input").focus();
				}
			}
		},
		onDblClickRow:function(index,row){
			if(row["accsubjects_name"] == '固定资产'){
				openIndexTabs({code:'fixedassetscard',url:'fixedassetscard/goList.do?sourcetype=1&source=initialbalance&sourceid='+row["accsubjectsid"],title:'固定资产卡片'});
			}else if(row["accsubjects_name"] == '累计折旧'){
				openIndexTabs({code:'fixedassetscard',url:'fixedassetscard/goList.do?sourcetype=2&source=initialbalance&sourceid='+row["accsubjectsid"],title:'固定资产卡片'});
			}
		},
		onBeforeSelect:function(index, row){
			$(this).datagrid('acceptChanges');
		},
		rowStyler: function (index, row) {
	        if(row.isEdit=='0'){
	            return 'background-color:#fdfdda;';
	        }else{
	        	return 'background-color:white;';
	        }
	    }
	});
	
	//初始状态
	var accinfo = findAccInfo();
	var isaccount = accinfo.initbalanceisacc;
	$('#isaccount').val(isaccount);
	if($("#isaccount").val() == 0){
		enableControl(['#tallyBtn']);
		disableControl(['#untallyBtn']);
	}else{
		enableControl(['#untallyBtn']);
		disableControl(['#tallyBtn']);
	}
	
});


//合计父亲期初余额
function sumParentJe(newValue, oldValue){
	var index = getRowIndex(this);
	changOneRow(index,newValue,oldValue);
}

function changOneRow(index,newValue,oldValue){
	var row = $('#gridlist').datagrid('getChecked')[0];
	var isChange = false;
	if((oldValue == '' && newValue != row["initialbalance"]) || (oldValue != '' &&  oldValue != newValue) ){
		isChange = true;
	}
	
	if(!isChange ||(!row.isEdit && checkEm(row["auxbus"]))){
		return;
	}
	
	//if(!row.isEdit || !isChange){return;}
	while(row.parentid != null){
		//获取列表数据
		var rows = $('#gridlist').datagrid('getRows'),copy_val = 0;
		//获取父亲
		var parentRow = jsonUtils.findRowByColAndVal(rows,"accsubjectsid",row["parentid"]);
		var parent_rowindex = $('#gridlist').datagrid('getRowIndex',parentRow);
		
		if(newValue == ''){
			newValue = 0;
		}
		//计算列合计
		if(!row.isEdit){
			copy_val = 0;
		}else{
			if(oldValue != '') 
				copy_val = parseFloat(newValue) - parseFloat(oldValue);
			else 
				copy_val = newValue;
		}
		
		var sumJe = jsonUtils.getSumByCol(rows,'initialbalance','parentid',row["parentid"],copy_val);
		if(sumJe == 0){
			parentRow["initialbalance"] = '';
		}else{
			parentRow["initialbalance"] = sumJe;
		}
		
		//更新父亲行数据
		if(parentRow != null){
			$('#gridlist').datagrid('refreshRow',parent_rowindex);
			row = parentRow;
			//保存父亲行
			saveRow(parent_rowindex);
		}
	}
	var row = $('#gridlist').datagrid('getRows')[index];
	
	if(newValue == 0){
		row["initialbalance"] = '';
	}else{
		row["initialbalance"] = newValue;
	}
	
	$('#gridlist').datagrid('refreshRow',index);
	//保存自己行
	saveRow(index);
}

var qcye = '';
function showDoc(index,type,id){
	$('#edit_panel').show().dialog({
		title: '期初余额',    
	    width: 600,    
	    height: 400,    
	    closed: false,    
	    cache: false,
	    modal: true
	});
	var gridObj = new Object();
	gridObj["position"] = "#editgridlist";
	gridObj["url"] = 'initialbalance/getInitialbalancemx.do?initialbalanceid='+id;
	gridObj["idField"] = 'id';
	gridObj["columns"] = [[
							{field:'',checkbox:true},
							{field:'recordname',title:'档案名称',align: 'left', halign: 'center',width:223,
								formatter:function(value,row,index){
									var val = row.recordname || '',
									    lx = row.lx == null?type:row.lx;
									
									return  '<div style="width: 73px;height: 28px;text-align:left;">'+val + '<div class="choose_btnimg" style="margin:0px 0 0 0" onclick="showRecord('+index+',\''+lx+'\')"></div>'+'</div>';
								}
							},
							{field:'initialbalance',title:'期初余额',align: 'right', halign: 'center',width:223,
								editor:{type:'numberbox',
									options: {
										precision:2,
										onChange:sumQcye
									}
								},formatter:function(value,row,index){
									return  '<p style="width:100%;height:100%;margin: 0 0 0 -19px;">'+formatMoney(value)+'</p>';
							}
							}
	                     ]];
	gridObj["pagination"] = false;
	Grid.edit_row_grid(gridObj);
	$('#type').val(type);
	$('#editgridlist').datagrid({
		onClickRow: function(index,row){
			if(formVailate()){return}
			
			$(this).datagrid('beginEdit', index);
			var ed = $(this).datagrid('getEditor', {index:index,field:'initialbalance'});
			$(ed.target).focus();
			
			qcye = '';
		},
		onBeforeSelect:function(index, row){
			$(this).datagrid('acceptChanges');
		},
	});
	//记录回写Index
	mainIndex = index;
	var pre_row = $('#gridlist').datagrid('getSelected');
	if(!checkEm(pre_row["initialbalance"])){
		pre_val = pre_row["initialbalance"]
	}else{
		pre_val = ''
	}
	
}

//指定位置添加行
function addrow(){
	if(formVailate()){return}
	
	var row = $('#editgridlist').datagrid('getSelected');
	if(row != null){
		var index = $('#editgridlist').datagrid('getRowIndex',row);
		$('#editgridlist').datagrid('insertRow',{index: index + 1,row: {rowid:Math.round(Math.random() * 100000)}});
		editrow(index + 1);
	}else{
		$('#editgridlist').datagrid('appendRow',{rowid:Math.round(Math.random() * 100000)});
		editrow($('#editgridlist').datagrid('getRows').length - 1);
	}
}

//启动编辑行
function editrow(index){
	$('#editgridlist').datagrid('beginEdit', index);
}

//删除多行
function removerow(){
	if(formVailate()){return}
	var rows = $('#editgridlist').datagrid('getSelections');
	var copyrows = new Array();
    for(var i in rows){
    	copyrows.push(rows[i]);
    }
	for(var i in copyrows){
		var index = $('#editgridlist').datagrid('getRowIndex',copyrows[i]);
		$('#editgridlist').datagrid('deleteRow', index);
	}
	$('#editgridlist').datagrid('clearSelections');
	
}

/**
 * 表单验证
 */
function formVailate(){
	var isaccount_1 = $('#isaccount').val();
	if(isaccount_1 == 1){
		return true;
	}else{
		return false;
	}
}

/**
 * 明细 辅助核算 选择_替换
 */
function showRecord(index,lx){
	//如果已经记账，不能选择
	
	var isaccount= $('#isaccount').val();
	if(Number(isaccount)==1){
		return;
	}
	var code = '';
	var name = '';
	switch(lx){
	case '客户':
		code = 'customer';
		name = '客户';
		break;
	case '供应商':
		code = 'supplier';
		name = '供应商';
		break;
	default:
		break;
	}
	var obj = new Object();
	obj["code"] = code;
	obj["name"] = name;
	obj["okFun"] = 'updateRecord_ok(\''+code+'\',\''+name+'\','+index+')';
	obj["dbFun"] = function (){updateRecord_ok(code,name,index)};
	Dialog.archives_selector(obj);
}

/**
 * 明细更新档案选择  确定
 */

function updateRecord_ok(code,lx,index){
	var node = $('#' + code + '_gridlist').datagrid('getSelected');
	$('#editgridlist').datagrid('acceptChanges');
	var id = node['id'];		
	var name = node['name'];
	
	var mxRow = $('#editgridlist').datagrid('getSelected');
	mxRow["recordid"] = id;
	mxRow["recordname"] = name;
	mxRow["lx"] = lx;
	mxRow["initialbalance"] = qcye;
	$('#editgridlist').datagrid('refreshRow', index);
	$('#'+code+'_dialog').dialog('close');
}

/**
 * 计算合计明细期初余额
 */
function sumQcye(newvalue,oldvalue){
	qcye = newvalue;
}

function saveInitialBalancemx(){
	//如果已记账,点击确定不要有动作
	var isaccount = $('#isaccount').val();
	 if(Number(isaccount) == 1){
		return;
	} 
	$('#editgridlist').datagrid('acceptChanges');
	var rows = $('#editgridlist').datagrid('getRows');
	//将客户或者供应商为空的   删除
	for(var i=0;i<rows.length;i++){
		if(checkEm(rows[i].recordname)){
			$('#editgridlist').datagrid('deleteRow',i);
			i--;
			continue;
		}	
	}
	mxData[mainIndex] = rows;
	//关闭编辑面板
	$('#edit_panel').dialog('close');
	//获取选择的数据
	var row = $('#gridlist').datagrid('getSelected');
	/* row["initialbalance"] =  */
	var initialbalance = jsonUtils.sumRowsByCol(rows,'initialbalance');
	//刷新数据
	//$('#gridlist').datagrid('refreshRow', mainIndex);
	changOneRow(mainIndex,initialbalance,pre_val);
}

//保存一行数据
function saveRow(index){
	var row = $('#gridlist').datagrid('getRows')[index];
	
	//如果 存在管制对象 保存管制对象明细
	if(mxData[mainIndex]!= null){
		row["initialbalancemx"] = JSON.stringify(mxData[mainIndex]);
	}

	$.ajax({
	    url: getUrl('initialbalance/edit'),
	    type: 'post', async: false, data: row, dataType:'json',
	    success:function(data){
   			if(data.state == 'success'){
    			row.id = data.id;
    			$('#gridlist').datagrid('refreshRow', index);	//刷新行
			}else {$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');}
		}
    });
}


//获得标记行下标
function getRowIndex(target){
    var tr = $(target).closest('tr.datagrid-row');
    return parseInt(tr.attr('datagrid-row-index'));
}

/**
 * 记账逻辑
 */
function checkInitialbalance(obj){
	if(obj != null){
		if(obj["estatus"] == '1'){
			var rows = $('#gridlist').datagrid('getRows');
			var d = 0;
			var c = 0;
			for(var i in rows){
				if(rows[i].sub_direction == '借' && checkEm(rows[i].parentid) ){
					if(!checkEm(rows[i].initialbalance)){
						d += parseFloat(rows[i].initialbalance);
					}
				}
				if(rows[i].sub_direction == '贷' && checkEm(rows[i].parentid) ){
					if(!checkEm(rows[i].initialbalance)){
						c += parseFloat(rows[i].initialbalance);
					}
				}
			}
			if( d.toFixed(2) != c.toFixed(2) ){
				$.messager.alert('提示','<span class="hintsp_e">提示</span>期初余额借贷不平，请检查后重新记账','error');
				return;
			}
		}
		
		var estatus1 = $('#estatus').val(),
			isaccount = $('#isaccount').val();
		
		sendAjax('initialbalance/accountAll',{isaccount:obj["estatus"]},checkOrUnchek_callback);
		
	}
	
	function checkOrUnchek_callback(data){
		if(data.state == 'success'){
			/* $.messager.show({
				title:'操作成功',
				msg:data.message,
				showType:'show',
				timeout:1000,
			}); */
			promptbox('success','操作成功！');
			refresh();
		}else {
			$.messager.alert('提示','<span class="hintsp_e">提示</span>'+data.message,'error');
		}
	}
}

/**
 * 撤账
 */
function beforeVoucherRevoke(obj){
	voucherRevoke(obj);		//撤账
}

//导入excel
function importExcel(){
	$.messager.progress();	// 显示进度条
	$('#importFileForm').form('submit', {
			url: getUrl('initialbalance/importExcel'),
			onSubmit: function(){
				var file_name = $('#file_name').filebox('getText');
				if(file_name == ''){
					$.messager.alert('提示','<span class="hintsp_e">提示</span>请选择要导入的文件！','error');
					$.messager.progress('close');
					return false;
				}
			},
			success: function (result) {
				var res = JSON.parse(result);
				if(res.state == 'success'){
					var rows = res.rows;
					for(var i in rows){
						$('#editgridlist').datagrid('appendRow',rows[i]);
					}
					$.messager.alert('提示','<span class="hintsp_w">提示</span>导入成功！','success');
					$('#import_panel').window('close');
				}else{
					$.messager.alert('提示','<span class="hintsp_w">提示</span>'+res.message,'success');
				}
				$('#gridlist').datagrid('load');
				$.messager.progress('close');// 如果表单是无效的则隐藏进度条
			}
	});
}

</script>

</body>
</html>