var jsonUtils = {
	"version_":'',
	"getSumByCol":function(rows,colname,condition,value,initval){
		
		var row = null,sumval = 0,oneval = 0;
		
		if(checkEm(initval)||isNaN(initval)){
			sumval = 0;
		}else{
			sumval = initval;
		}
		if(rows != null){
			for(var i=0,n=rows.length;i<n;i++){
				row = rows[i];
				if(row[condition] == value){
					if(checkEm(row[colname])||isNaN(row[colname])){
						oneval = 0;
					}else{
						oneval = row[colname];
					}
					sumval =parseFloat(sumval) + parseFloat(oneval);
				}
			}
		}
		return sumval;
	},
	"findRowByColAndVal":function(rows,colname,value){
		if(rows != null){
			for(var i=0,n=rows.length;i<n;i++){
				row = rows[i];
				if(row[colname] == value){
					return row;
				}
			}
		}
		return null;
	},
	"sumRowsByCol":function(rows,colname){
		var sumval = 0,oneval=0;
		if(rows != null){
			for(var i=0,n=rows.length;i<n;i++){
				row = rows[i];
				oneval = row[colname];
				if(oneval == null || oneval == ''){
					oneval = 0;
				}
				sumval =parseFloat(sumval) + parseFloat(oneval);
			}
		}
		return sumval;
	}
}