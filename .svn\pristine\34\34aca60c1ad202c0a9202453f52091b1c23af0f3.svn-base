<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="MenuMapper">

	<!-- 新增 -->
	<insert id="save" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${ database }.c_menu(
			 code,
			 name,
			 ioc,
			 url,
			 order,
			 pid,
			 createby,createtime
		) values (
			 #{code},
			 #{name},
			 #{ioc},
			 #{url},
			 #{order},
			 <choose>
				<when test="pid != null and pid !=''">#{ pid }</when>
				<otherwise>null</otherwise>
			 </choose>
			 #{createby},now()
		)
	</insert>
	<!-- 删除 -->
	<delete id="delete" parameterType="pd">
		delete from ${ database }.c_menu
		where
		id = #{ id } and estatus = 1 and closestatus = 0
	</delete>

	<!-- 修改 -->
	<update id="edit" parameterType="pd">
		update ${ database }.c_menu
		set 
			code = #{code},
 			name = #{name},
 			ioc = #{ioc},
 			url = #{url},
 			order = #{order},
 			<choose>
				<when test="pid != null and pid !=''">pid = #{ pid },</when>
				<otherwise>pid = null,</otherwise>
			 </choose>
			modifyby = #{ modifyby },
			modifytime = now(),
			estatus=1
		where 
			id = #{ id }
	</update>

	<!-- 通过ID获取数据 -->
	<select id="findById" parameterType="pd" resultType="pd">
		select 
			a.code,	
			a.name,	
			a.ioc,	
			a.url,	
			a.order,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.id
		from 
			${ database }.c_menu as a
			left join ${ database }.c_menu as b on b.id = a.pid
		where 
			a.id = #{ id }
	</select>
	<!-- 列表 -->
	<select id="datalistPage" parameterType="page" resultType="pd">
		select
			a.code,	
			a.name,	
			a.name as text,
			a.ioc,	
			a.url,
			a.type,
			a.xh,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.pid,
			a.pid as parentId,
			p.name as parent_name,
			<if test="pd.jurisdiction != null and pd.jurisdiction != ''">
				case when LOCATE(concat(',',a.id,','),#{pd.jurisdiction}) &gt; 0 then true else false end as checked,
			</if>
			a.id
		from 
			${ pd.database }.c_menu a
			left join ${ pd.database }.c_menu p on p.id = a.pid
		order by parentId,xh
	</select>
	
	<!-- 列表(全部) -->
	<select id="alllistPage" parameterType="pd" resultType="pd">
		select
			a.code,	
			a.name,	
			a.name as text,
			a.ioc,	
			a.url,
			a.type,	
			a.xh,	
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.pid,
			a.pid as parentId,
			p.name as parent_name,
			a.id
		from 
			${ database }.c_menu a
			left join ${ database }.c_menu p on p.id = a.pid
		
		<where>
			<if test="jurisdiction != null and jurisdiction != ''">
				LOCATE(concat(',',a.id,','),concat(',',#{jurisdiction},',')) &gt; 0 
			</if>
			
			<if test="belongsto != null and belongsto != ''">
				and a.belongsto = #{belongsto}
			</if>
		</where>
		order by parentId,xh
	</select>
	
	
	
	<select id="findMenuQxByUser" parameterType="pd" resultType="pd">
		select
			a.code,	
			a.name,	
			a.name as text,
			a.ioc,	
			a.url,
			a.type,	
			a.xh,	
			case when LOCATE(CONCAT(',',a.id,','),CONCAT(',',#{jurisdiction},','))!=0 then 1 else 0 end as has_qx,
			a.createby,
			a.createtime,
			a.auditby,
			a.audittime,
			a.estatus,
			a.closestatus,
			a.version,
			a.pid,
			a.pid as parentId,
			p.name as parent_name,
			a.id
		from 
			${ database }.c_menu a
			left join ${ database }.c_menu p on p.id = a.pid
		where 1=1
			<if test="belongsto != null and belongsto != ''">
				and a.belongsto = #{belongsto}
			</if>
		order by parentId,xh
	</select>
	<select id="findPurchaseByPeriod" parameterType="pd" resultType="pd">
		select 
			id
		from 
			${ database }.${tablename} 
		where 
			left(${createdate},7) = #{accountperiod}
			<if test="source !=null and source !='' ">
			and source in 
			<foreach collection="source" item="item" index="index" 
				open="(" separator="," close=")">#{item}</foreach>
			</if>
	</select>
	
	<select id="findMenuUrlByCode" parameterType="pd" resultType="pd">
		select a.url as menu_url,a.code as menucode from ${ database }.c_menu as a where a.code = #{menucode}
	</select>
	
	<!-- 通过NAME获取数据 -->
	<select id="findEncodingByName" parameterType="pd" resultType="pd">
		select
			b.code,
	    	b.name menu_name,
			a.bsf,
			a.gz,
			a.lshcd,
			a.qslsh,
			a.sxf,
			a.tranmemo
		from 
			${database}.c_menumx as a,${database}.c_menu b
	  	where a.menuid = b.id 
	  	<if test="menu_code != null and menu_code != ''">
	  		and b.code = #{menu_code} 
	  	</if>
	  	<if test="menuid != null and menuid != ''">
	  		and b.id = #{menuid}
	  	</if>
	  	and a.sxf = 1 and b.closestatus = 0
	</select>
	<select id="findLshFromMenuNumber" parameterType="pd" resultType="pd">
		select * from  ${database}.sys_menu_number where menu_code = #{menu_code} and current_rq=#{rq}
	</select>


	<update id="updateMenuNumber" parameterType="pd">
		update ${database}.sys_menu_number set number = #{k} where menu_code = #{menu_code} and current_rq = #{rq}
	</update>
	
	<insert id="insertMenuNumber" parameterType="pd" useGeneratedKeys="true" keyProperty="id">
		insert into ${database}.sys_menu_number (menu_code,current_rq,number) 
		values(#{menu_code},#{rq},1)
	</insert>
</mapper>