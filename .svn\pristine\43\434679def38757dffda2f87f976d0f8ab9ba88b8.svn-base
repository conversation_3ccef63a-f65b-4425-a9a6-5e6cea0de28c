<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Keyboard Navigation in MenuButton - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Keyboard Navigation in MenuButton</h2>
	<p>Press Alt+W to focus the menubutton. Once the menubutton get focus, you will be able to navigate menubutton using keyboard keys.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="padding:5px;">
		<a id="btn-home" href="#" class="easyui-linkbutton" data-options="plain:true">Home</a>
		<a href="#" class="easyui-menubutton" data-options="menu:'#mm1',iconCls:'icon-edit'">Edit</a>
		<a href="#" class="easyui-menubutton" data-options="menu:'#mm2',iconCls:'icon-help'">Help</a>
		<a href="#" class="easyui-menubutton" data-options="menu:'#mm3'">About</a>
	</div>
	<div id="mm1" style="width:150px;">
		<div data-options="iconCls:'icon-undo'">Undo</div>
		<div data-options="iconCls:'icon-redo'">Redo</div>
		<div class="menu-sep"></div>
		<div>Cut</div>
		<div>Copy</div>
		<div>Paste</div>
		<div class="menu-sep"></div>
		<div>
			<span>Toolbar</span>
			<div>
				<div>Address</div>
				<div>Link</div>
				<div>Navigation Toolbar</div>
				<div>Bookmark Toolbar</div>
				<div class="menu-sep"></div>
				<div>New Toolbar...</div>
			</div>
		</div>
		<div data-options="iconCls:'icon-remove'">Delete</div>
		<div>Select All</div>
	</div>
	<div id="mm2" style="width:100px;">
		<div>Help</div>
		<div>Update</div>
		<div>About</div>
	</div>
	<div id="mm3" class="menu-content" style="background:#f0f0f0;padding:10px;text-align:left">
		<img src="http://www.jeasyui.com/images/logo1.png" style="width:150px;height:50px">
		<p style="font-size:14px;color:#444;">Try jQuery EasyUI to build your modern, interactive, javascript applications.</p>
	</div>

	<script type="text/javascript">
		(function($){
			function getParentMenu(rootMenu, menu){
				return findParent(rootMenu);

				function findParent(pmenu){
					var p = undefined;
					$(pmenu).find('.menu-item').each(function(){
						if (!p && this.submenu){
							if ($(this.submenu)[0] == $(menu)[0]){
								p = pmenu;
							} else {
								p = findParent(this.submenu);
							}
						}
					});
					return p;
				}
			}

			$.extend($.fn.menubutton.methods, {
				enableNav: function(enabled){
					var curr;
					$(document).unbind('.menubutton');
					if (enabled == undefined){enabled = true;}
					if (enabled){
						$(document).bind('keydown.menubutton', function(e){
							var currButton = $(this).find('.m-btn-active,.m-btn-plain-active,.l-btn:focus');
							if (!currButton.length){
								return;
							}

							if (!curr || curr.button != currButton[0]){
								curr = {
									menu: currButton.data('menubutton') ? $(currButton.menubutton('options').menu) : $(),
									button: currButton[0]
								};
							}
							var item = curr.menu.find('.menu-active');

							switch(e.keyCode){
								case 13:  // enter
									item.trigger('click');
									break;
								case 27:  // esc
									currButton.trigger('mouseleave');
									break;
								case 38:  // up
									var prev = !item.length ? curr.menu.find('.menu-item:last') : item.prevAll('.menu-item:first');
									prev.trigger('mouseenter');
									return false;
								case 40:  // down
									var next = !item.length ? curr.menu.find('.menu-item:first') : item.nextAll('.menu-item:first');
									next.trigger('mouseenter');
									return false;
								case 37:  // left
									var pmenu = getParentMenu(currButton.data('menubutton') ? $(currButton.menubutton('options').menu) : $(), curr.menu);
									if (pmenu){
										curr.menu = pmenu;
										item.triggerHandler('mouseleave');
									} else {
										var prev = currButton.prevAll('.l-btn:first');
										if (prev.length){
											currButton.trigger('mouseleave');
											prev.focus();
										}
									}
									return false;
								case 39:  // right
									if (item.length && item[0].submenu){
										curr.menu = $(item[0].submenu);
										curr.button = currButton[0];
										curr.menu.find('.menu-item:first').trigger('mouseenter');
									} else {
										var next = currButton.nextAll('.l-btn:first');
										if (next.length){
											currButton.trigger('mouseleave');
											next.focus();
										}
									}
									return false;
							}
						});						
					}
				}
			});
		})(jQuery);

		$(function(){
			$.fn.menubutton.methods.enableNav();
			$(document).keydown(function(e){
				if (e.altKey && e.keyCode == 87){
					$('#btn-home').focus();
				}
			})
		});
	</script>
</body>
</html>